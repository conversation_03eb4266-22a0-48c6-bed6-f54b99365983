# Go REST API Tech Stack & Architecture Guide

> **Comprehensive template for building production-ready Go REST APIs with clean architecture**

## 🚀 Technology Stack

### Core Technologies
- **Go 1.23+** - Primary programming language
- **Gin** - High-performance HTTP web framework
- **PostgreSQL 12+** - Primary database with JSON support
- **PGX v5** - Native PostgreSQL driver (100% compatibility)
- **Docker & Docker Compose** - Containerization and orchestration
- **JWT** - Token-based authentication

### Development Tools
- **golangci-lint** - Code linting and static analysis
- **Air** - Hot reload for development
- **K6** - Load testing and API testing
- **Make** - Build automation and task management

### Infrastructure & Deployment
- **DigitalOcean PostgreSQL** - Managed database service
- **PgBouncer** - Connection pooling (auto-optimized)
- **Docker** - Production containerization
- **Background Workers** - Async processing with Go routines

## 📁 Project Structure

```
project-root/
├── cmd/                              # Application entry points
│   ├── server/                       # HTTP server
│   │   └── main.go                   # Server startup and initialization
│   ├── migration/                    # Database migration tool
│   │   └── main.go                   # Migration runner with PGX
│   └── worker/                       # Background worker processes
│       ├── main.go                   # Worker coordinator
│       └── commission_worker.go      # Specific worker implementations
│
├── internal/                         # Private application code
│   ├── app/                          # Application layer
│   │   └── app.go                    # App initialization and dependency injection
│   │
│   ├── config/                       # Configuration management
│   │   └── config.go                 # Viper-based config with env support
│   │
│   ├── domain/                       # Business entities and core logic
│   │   ├── user/                     # User aggregate
│   │   │   ├── user.go               # Entity definitions and methods
│   │   │   └── user_test.go          # Unit tests
│   │   ├── agent/                    # Agent hierarchy management
│   │   ├── commission/               # Commission calculation logic
│   │   └── common.go                 # Shared domain types
│   │
│   ├── handler/                      # Request handlers
│   │   └── http/                     # HTTP handlers (Gin-based)
│   │       ├── user.go               # User CRUD operations
│   │       ├── agent.go              # Agent management
│   │       ├── commission.go         # Commission processing
│   │       └── response.go           # Standardized response helpers
│   │
│   ├── middleware/                   # HTTP middleware
│   │   ├── auth.go                   # JWT authentication
│   │   ├── cors.go                   # CORS configuration
│   │   ├── logger.go                 # Request logging
│   │   └── error.go                  # Error handling
│   │
│   ├── repository/                   # Data access layer
│   │   ├── interfaces/               # Repository contracts
│   │   │   ├── user.go               # User repository interface
│   │   │   ├── agent.go              # Agent repository interface
│   │   │   └── commission.go         # Commission repository interface
│   │   ├── postgres/                 # PostgreSQL implementations
│   │   │   ├── user.go               # User data access (PGX)
│   │   │   ├── agent.go              # Agent data access (PGX)
│   │   │   └── commission.go         # Commission data access (PGX)
│   │   └── redis/                    # Redis implementations (optional)
│   │
│   ├── router/                       # Route definitions
│   │   ├── router.go                 # Main router setup
│   │   └── user.go                   # User route groups
│   │
│   └── service/                      # Business logic layer
│       ├── user.go                   # User business operations
│       ├── agent.go                  # Agent management logic
│       └── commission.go             # Commission calculation
│
├── infrastructure/                   # External service integrations
│   ├── database/                     # Database connections
│   │   └── database.go               # PGX pool management
│   ├── cache/                        # Redis/memory cache
│   ├── messaging/                    # Message queue integrations
│   └── monitoring/                   # Observability tools
│
├── migrations/                       # Database schema management
│   ├── list/                         # Migration files
│   │   ├── 01_create_init_tables.go  # Initial schema (PGX native)
│   │   ├── 02_create_game_system.go  # Feature-specific migrations
│   │   └── ...                       # Incremental migrations
│   ├── seeders/                      # Data seeding
│   │   ├── 01_seed_default_data.go   # Default system data
│   │   └── 02_seed_game_providers.go # Business-specific data
│   └── README.md                     # Migration documentation
│
├── pkg/                              # Shared/reusable packages
│   ├── errors/                       # Custom error types
│   │   └── errors.go                 # Structured error handling
│   ├── logger/                       # Logging interface
│   │   └── logger.go                 # Logger abstraction
│   ├── validator/                    # Input validation
│   │   └── validator.go              # Request validation helpers
│   └── dbutil/                       # Database utilities
│       └── schema.go                 # Schema management helpers
│
├── test/                             # Testing infrastructure
│   ├── api_test.js                   # K6 API integration tests
│   ├── mocks/                        # Generated mocks
│   └── testdata/                     # Test fixtures
│
├── scripts/                          # Utility scripts
│   ├── hash_password.go              # Password hashing utility
│   └── seed_root_agent.sql           # Database seeding scripts
│
├── docs/                             # Documentation
│   ├── api/                          # API documentation
│   ├── architecture/                 # Architecture decisions
│   └── srs/                          # Software requirements
│
├── Makefile                          # Build and development commands
├── Dockerfile                        # Production container
├── docker-compose.yml                # Development environment
├── go.mod                            # Go module dependencies
└── go.sum                            # Dependency checksums
```

## 🏗️ Architecture Layers

### 1. Domain Layer (`internal/domain/`)
**Purpose**: Core business entities and rules
```go
// Example: User entity with business methods
type User struct {
    ID        string    `json:"id"`
    Username  string    `json:"username"`
    Email     string    `json:"email"`
    Status    string    `json:"status"`
    CreatedAt time.Time `json:"createdAt"`
}

// Business methods on entities
func (u *User) IsActive() bool {
    return u.Status == "active"
}

func (u *User) Activate() {
    u.Status = "active"
}
```

### 2. Service Layer (`internal/service/`)
**Purpose**: Business logic orchestration
```go
type UserService struct {
    userRepo interfaces.UserRepository
    logger   logger.Logger
}

func (s *UserService) CreateUser(ctx context.Context, req CreateUserRequest) (*User, error) {
    // Validation
    if err := s.validateCreateUser(req); err != nil {
        return nil, err
    }
    
    // Business logic
    user := &User{
        ID:       uuid.New().String(),
        Username: req.Username,
        Email:    req.Email,
        Status:   "active",
    }
    
    // Repository call
    return s.userRepo.Create(ctx, user)
}
```

### 3. Repository Layer (`internal/repository/`)
**Purpose**: Data access abstraction
```go
// Interface definition
type UserRepository interface {
    Create(ctx context.Context, user *User) (*User, error)
    GetByID(ctx context.Context, id string) (*User, error)
    List(ctx context.Context, filters UserFilters) ([]User, error)
}

// PGX implementation
type userRepository struct {
    pool *pgxpool.Pool
}

func (r *userRepository) Create(ctx context.Context, user *User) (*User, error) {
    query := `INSERT INTO users (id, username, email, status) VALUES ($1, $2, $3, $4) RETURNING created_at`
    
    err := r.pool.QueryRow(ctx, query, user.ID, user.Username, user.Email, user.Status).Scan(&user.CreatedAt)
    if err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err)
    }
    
    return user, nil
}
```

### 4. Handler Layer (`internal/handler/http/`)
**Purpose**: HTTP request/response handling
```go
type UserHandler struct {
    userService *service.UserService
    logger      logger.Logger
}

func (h *UserHandler) CreateUser(c *gin.Context) {
    var req CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": "Invalid request"})
        return
    }
    
    user, err := h.userService.CreateUser(c.Request.Context(), req)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(201, gin.H{"success": true, "data": user})
}
```

## 🗄️ Database Architecture

### Native PGX Migration System
```go
// Migration interface
type Migration interface {
    GetName() string
    Up(con pgx.Tx)
    Down(con pgx.Tx)
}

// Example migration
type CreateUsersTable struct{}

func (m *CreateUsersTable) GetName() string {
    return "CreateUsersTable"
}

func (m *CreateUsersTable) Up(con pgx.Tx) {
    ctx := context.Background()
    _, err := con.Exec(ctx, `
        CREATE TABLE IF NOT EXISTS users (
            id UUID PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    `)
    if err != nil {
        panic(err)
    }
}
```

### Connection Management
```go
// DigitalOcean optimized connection
func NewDatabase(config *Config) (*Database, error) {
    poolConfig, err := pgxpool.ParseConfig(config.DatabaseURL)
    if err != nil {
        return nil, err
    }
    
    // Auto-detect DigitalOcean and apply optimizations
    if strings.Contains(config.DatabaseHost, ".ondigitalocean.com") {
        poolConfig.ConnConfig.RuntimeParams["default_query_exec_mode"] = "simple_protocol"
        poolConfig.ConnConfig.RuntimeParams["statement_cache_capacity"] = "0"
    }
    
    pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
    if err != nil {
        return nil, err
    }
    
    return &Database{PGXPool: pool}, nil
}
```

## 🔧 Development Commands

### Essential Make Commands
```makefile
# Build and run
build:                    # Build all binaries (server, migration, worker)
run ENV=local:           # Run server locally
run-migration ENV=local: # Run database migrations
run-worker ENV=local:    # Run background worker

# Testing
test:                    # Run unit tests
test-coverage:          # Run tests with coverage report
api-test:               # Run K6 API tests

# Code quality
fmt:                     # Format code with go fmt
lint:                   # Run golangci-lint
check:                  # Run fmt, lint, and test

# Docker
docker-build:           # Build Docker image
docker-run:             # Start all services with docker-compose
dev-start:              # Full development environment setup
```

### Environment Setup
```bash
# 1. Clone and setup
git clone <repository>
cd <project>
cp .env.example .env

# 2. Install dependencies
go mod download
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 3. Setup database
make docker-run  # Start PostgreSQL with Docker
make run-migration ENV=local  # Run migrations

# 4. Start development
make run ENV=local  # Start server
make run-worker ENV=local  # Start background worker (separate terminal)
```

## 🛡️ Security & Best Practices

### Authentication Pattern
```go
// JWT middleware
func (m *AuthMiddleware) NewUserJWT() *jwt.GinJWTMiddleware {
    return &jwt.GinJWTMiddleware{
        Realm:       "user zone",
        Key:         []byte(m.config.JWTSecret),
        Timeout:     time.Hour * 24,
        MaxRefresh:  time.Hour * 24 * 7,
        PayloadFunc: m.userPayloadFunc,
        LoginResponse: m.loginResponseFunc,
        Authenticator: m.userAuthenticator,
        Authorizator: m.userAuthorizator,
    }
}
```

### Password Security
```go
func HashPassword(password string) (string, error) {
    bytes, err := bcrypt.GenerateFromPassword([]byte(password), 14)
    return string(bytes), err
}

func CheckPasswordHash(password, hash string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
    return err == nil
}
```

### Input Validation
```go
type CreateUserRequest struct {
    Username string `json:"username" binding:"required,min=3,max=50"`
    Email    string `json:"email" binding:"required,email"`
    Password string `json:"password" binding:"required,min=8"`
}
```

## 📊 API Response Standards

### Consistent Response Format
```go
type APIResponse struct {
    Success bool        `json:"success"`
    Data    interface{} `json:"data,omitempty"`
    Message string      `json:"message,omitempty"`
    Error   *APIError   `json:"error,omitempty"`
}

type PaginatedResponse struct {
    Success    bool         `json:"success"`
    Data       interface{}  `json:"data"`
    Pagination *Pagination  `json:"pagination"`
}

type Pagination struct {
    CurrentPage  int `json:"currentPage"`
    TotalPages   int `json:"totalPages"`
    TotalRecords int `json:"totalRecords"`
    PerPage      int `json:"perPage"`
}
```

### Error Handling
```go
type APIError struct {
    Code    string                 `json:"code"`
    Message string                 `json:"message"`
    Details map[string]interface{} `json:"details,omitempty"`
}

// Usage in handlers
func handleError(c *gin.Context, err error) {
    switch e := err.(type) {
    case *ValidationError:
        c.JSON(400, APIResponse{
            Success: false,
            Error: &APIError{
                Code:    "VALIDATION_ERROR",
                Message: e.Message,
                Details: e.Fields,
            },
        })
    case *NotFoundError:
        c.JSON(404, APIResponse{
            Success: false,
            Error: &APIError{
                Code:    "NOT_FOUND",
                Message: e.Message,
            },
        })
    default:
        c.JSON(500, APIResponse{
            Success: false,
            Error: &APIError{
                Code:    "INTERNAL_ERROR",
                Message: "Internal server error",
            },
        })
    }
}
```

## 🔄 Background Processing

### Worker Pattern
```go
type CommissionWorker struct {
    betRepo        interfaces.BetTransactionRepository
    commissionRepo interfaces.CommissionDistributionRepository
    logger         logger.Logger
    stopCh         chan struct{}
}

func (w *CommissionWorker) Start(ctx context.Context) {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            if err := w.processPendingCommissions(ctx); err != nil {
                w.logger.WithError(err).Error("Failed to process commissions")
            }
        case <-w.stopCh:
            return
        case <-ctx.Done():
            return
        }
    }
}
```

## 📝 Configuration Management

### Environment-based Config
```go
type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    JWT      JWTConfig      `mapstructure:"jwt"`
    Logger   LoggerConfig   `mapstructure:"logger"`
}

func LoadConfig() (*Config, error) {
    viper.SetConfigName("config")
    viper.SetConfigType("yaml")
    viper.AddConfigPath(".")
    viper.AddConfigPath("./configs")
    
    // Environment variables
    viper.SetEnvPrefix("BLACKING")
    viper.AutomaticEnv()
    viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
    
    if err := viper.ReadInConfig(); err != nil {
        return nil, err
    }
    
    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        return nil, err
    }
    
    return &config, nil
}
```

## 🧪 Testing Strategy

### Unit Tests
```go
func TestUserService_CreateUser(t *testing.T) {
    // Setup
    mockRepo := mocks.NewUserRepository(t)
    service := service.NewUserService(mockRepo, logger.NewNoop())
    
    req := CreateUserRequest{
        Username: "testuser",
        Email:    "<EMAIL>",
        Password: "password123",
    }
    
    // Mock expectations
    mockRepo.On("Create", mock.Anything, mock.AnythingOfType("*User")).
        Return(&User{ID: "123", Username: "testuser"}, nil)
    
    // Execute
    user, err := service.CreateUser(context.Background(), req)
    
    // Assert
    assert.NoError(t, err)
    assert.Equal(t, "testuser", user.Username)
    mockRepo.AssertExpectations(t)
}
```

### API Tests (K6)
```javascript
import http from 'k6/http';
import { check } from 'k6';

export let options = {
    stages: [
        { duration: '30s', target: 10 },
        { duration: '1m', target: 50 },
        { duration: '30s', target: 0 },
    ],
};

export default function() {
    let payload = JSON.stringify({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
    });
    
    let params = {
        headers: { 'Content-Type': 'application/json' },
    };
    
    let response = http.post('http://localhost:8080/api/v1/users', payload, params);
    
    check(response, {
        'status is 201': (r) => r.status === 201,
        'response has success': (r) => JSON.parse(r.body).success === true,
    });
}
```

## 🚀 Deployment

### Docker Production Build
```dockerfile
# Multi-stage build
FROM golang:1.23-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o server ./cmd/server
RUN CGO_ENABLED=0 GOOS=linux go build -o migration ./cmd/migration
RUN CGO_ENABLED=0 GOOS=linux go build -o worker ./cmd/worker

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/server .
COPY --from=builder /app/migration .
COPY --from=builder /app/worker .

EXPOSE 8080
CMD ["./server"]
```

### Environment Variables
```bash
# Database
BLACKING_DATABASE_HOST=localhost
BLACKING_DATABASE_PORT=5432
BLACKING_DATABASE_USERNAME=postgres
BLACKING_DATABASE_PASSWORD=password
BLACKING_DATABASE_DBNAME=blacking_db

# Server
BLACKING_SERVER_PORT=8080
BLACKING_SERVER_MODE=production

# JWT
BLACKING_JWT_SECRET=your-super-secret-key
BLACKING_JWT_EXPIRES_IN=24h

# Logging
BLACKING_LOG_LEVEL=info
BLACKING_LOG_FORMAT=json
```

## 🎯 Performance Optimizations

### Database Query Optimization
```go
// Use native PGX for complex queries
func (r *agentRepository) GetHierarchyWithMetrics(ctx context.Context, rootCode string) ([]AgentWithMetrics, error) {
    query := `
        WITH RECURSIVE agent_tree AS (
            SELECT a.*, ah.depth, 0 as level
            FROM agents a
            JOIN agent_hierarchy ah ON a.line_code = ah.child_code
            WHERE a.line_code = $1
            
            UNION ALL
            
            SELECT a.*, ah.depth, at.level + 1
            FROM agents a
            JOIN agent_hierarchy ah ON a.line_code = ah.child_code
            JOIN agent_tree at ON a.parent_code = at.line_code
        )
        SELECT 
            at.*,
            COALESCE(apm.total_bets, 0) as total_bets,
            COALESCE(apm.total_commission, 0) as total_commission
        FROM agent_tree at
        LEFT JOIN agent_performance_metrics apm ON at.line_code = apm.agent_code
        ORDER BY at.level, at.line_code
    `
    
    rows, err := r.pool.Query(ctx, query, rootCode)
    if err != nil {
        return nil, fmt.Errorf("failed to get agent hierarchy: %w", err)
    }
    defer rows.Close()
    
    var agents []AgentWithMetrics
    for rows.Next() {
        var agent AgentWithMetrics
        err := rows.Scan(
            &agent.ID, &agent.LineCode, &agent.ParentCode,
            &agent.FullName, &agent.AgentType, &agent.Status,
            &agent.Depth, &agent.Level,
            &agent.TotalBets, &agent.TotalCommission,
        )
        if err != nil {
            return nil, fmt.Errorf("failed to scan agent: %w", err)
        }
        agents = append(agents, agent)
    }
    
    return agents, nil
}
```

### Connection Pool Tuning
```go
func setupDatabasePool(config *DatabaseConfig) (*pgxpool.Pool, error) {
    poolConfig, err := pgxpool.ParseConfig(config.URL)
    if err != nil {
        return nil, err
    }
    
    // Production optimizations
    poolConfig.MaxConns = 25
    poolConfig.MinConns = 5
    poolConfig.MaxConnLifetime = time.Hour
    poolConfig.MaxConnIdleTime = time.Minute * 30
    poolConfig.HealthCheckPeriod = time.Minute * 5
    
    // DigitalOcean specific optimizations
    if strings.Contains(config.Host, ".ondigitalocean.com") {
        poolConfig.ConnConfig.RuntimeParams["default_query_exec_mode"] = "simple_protocol"
        poolConfig.ConnConfig.RuntimeParams["statement_cache_capacity"] = "0"
    }
    
    return pgxpool.NewWithConfig(context.Background(), poolConfig)
}
```

---

**Template Version**: 1.0  
**Last Updated**: 2024-07-24  
**Go Version**: 1.23+  
**Database**: PostgreSQL 12+ with PGX v5