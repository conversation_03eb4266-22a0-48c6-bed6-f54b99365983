-- Login Attempts Query Examples
-- After adding is_admin and is_member columns

-- 1. View table structure
\d login_attempts;

-- 2. View all indexes
\di login_attempts*;

-- 3. Insert test data (for testing purposes)
INSERT INTO login_attempts (username, ip_address, success, user_agent, is_admin, is_member, created_at) VALUES
('admin_user', '*************', true, 'Mozilla/5.0 Admin Browser', true, false, NOW()),
('admin_user', '*************', false, 'Mozilla/5.0 Admin Browser', true, false, NOW() - INTERVAL '1 hour'),
('member_user', '*************', true, 'Mozilla/5.0 Member Browser', false, true, NOW()),
('member_user', '*************', false, 'Mozilla/5.0 Member Browser', false, true, NOW() - INTERVAL '30 minutes');

-- 4. Basic queries

-- Get all admin login attempts
SELECT username, ip_address, success, created_at 
FROM login_attempts 
WHERE is_admin = true 
ORDER BY created_at DESC;

-- Get all member login attempts
SELECT username, ip_address, success, created_at 
FROM login_attempts 
WHERE is_member = true 
ORDER BY created_at DESC;

-- Get failed admin login attempts
SELECT username, ip_address, user_agent, created_at 
FROM login_attempts 
WHERE is_admin = true AND success = false 
ORDER BY created_at DESC;

-- Get failed member login attempts
SELECT username, ip_address, user_agent, created_at 
FROM login_attempts 
WHERE is_member = true AND success = false 
ORDER BY created_at DESC;

-- 5. Statistics queries

-- Login attempts summary by user type
SELECT 
    CASE 
        WHEN is_admin = true THEN 'Admin'
        WHEN is_member = true THEN 'Member'
        ELSE 'Unknown'
    END as user_type,
    success,
    COUNT(*) as attempt_count
FROM login_attempts 
GROUP BY is_admin, is_member, success
ORDER BY user_type, success DESC;

-- Daily login attempts by user type
SELECT 
    DATE(created_at) as login_date,
    CASE 
        WHEN is_admin = true THEN 'Admin'
        WHEN is_member = true THEN 'Member'
        ELSE 'Unknown'
    END as user_type,
    COUNT(*) as total_attempts,
    SUM(CASE WHEN success = true THEN 1 ELSE 0 END) as successful_attempts,
    SUM(CASE WHEN success = false THEN 1 ELSE 0 END) as failed_attempts
FROM login_attempts 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(created_at), is_admin, is_member
ORDER BY login_date DESC, user_type;

-- 6. Security monitoring queries

-- Users with multiple failed login attempts (Admin)
SELECT 
    username,
    COUNT(*) as failed_attempts,
    MAX(created_at) as last_failed_attempt,
    MIN(created_at) as first_failed_attempt
FROM login_attempts 
WHERE is_admin = true AND success = false 
    AND created_at >= NOW() - INTERVAL '24 hours'
GROUP BY username
HAVING COUNT(*) >= 3
ORDER BY failed_attempts DESC;

-- Users with multiple failed login attempts (Member)
SELECT 
    username,
    COUNT(*) as failed_attempts,
    MAX(created_at) as last_failed_attempt,
    MIN(created_at) as first_failed_attempt
FROM login_attempts 
WHERE is_member = true AND success = false 
    AND created_at >= NOW() - INTERVAL '24 hours'
GROUP BY username
HAVING COUNT(*) >= 5
ORDER BY failed_attempts DESC;

-- Suspicious IP addresses (multiple failed attempts from same IP)
SELECT 
    ip_address,
    CASE 
        WHEN is_admin = true THEN 'Admin'
        WHEN is_member = true THEN 'Member'
        ELSE 'Unknown'
    END as target_user_type,
    COUNT(DISTINCT username) as unique_usernames_targeted,
    COUNT(*) as total_failed_attempts,
    MAX(created_at) as last_attempt
FROM login_attempts 
WHERE success = false 
    AND created_at >= NOW() - INTERVAL '24 hours'
GROUP BY ip_address, is_admin, is_member
HAVING COUNT(*) >= 10
ORDER BY total_failed_attempts DESC;

-- 7. Performance testing queries

-- Test index performance on is_admin
EXPLAIN ANALYZE 
SELECT * FROM login_attempts WHERE is_admin = true;

-- Test index performance on is_member
EXPLAIN ANALYZE 
SELECT * FROM login_attempts WHERE is_member = true;

-- Test composite index performance
EXPLAIN ANALYZE 
SELECT * FROM login_attempts WHERE is_admin = true AND is_member = false;

-- 8. Data migration verification

-- Check for records with both is_admin and is_member true (should be none)
SELECT COUNT(*) as invalid_records
FROM login_attempts 
WHERE is_admin = true AND is_member = true;

-- Check for records with both is_admin and is_member false (legacy records)
SELECT COUNT(*) as legacy_records
FROM login_attempts 
WHERE is_admin = false AND is_member = false;

-- 9. Cleanup queries (use with caution)

-- Delete old failed login attempts (older than 30 days)
-- DELETE FROM login_attempts 
-- WHERE success = false AND created_at < NOW() - INTERVAL '30 days';

-- Delete all login attempts for a specific user
-- DELETE FROM login_attempts WHERE username = 'specific_username';

-- 10. Reporting queries

-- Monthly login success rate by user type
SELECT 
    DATE_TRUNC('month', created_at) as month,
    CASE 
        WHEN is_admin = true THEN 'Admin'
        WHEN is_member = true THEN 'Member'
        ELSE 'Unknown'
    END as user_type,
    COUNT(*) as total_attempts,
    ROUND(
        (SUM(CASE WHEN success = true THEN 1 ELSE 0 END)::DECIMAL / COUNT(*)) * 100, 
        2
    ) as success_rate_percent
FROM login_attempts 
WHERE created_at >= DATE_TRUNC('month', NOW() - INTERVAL '6 months')
GROUP BY DATE_TRUNC('month', created_at), is_admin, is_member
ORDER BY month DESC, user_type;

-- Top 10 most active users by user type
SELECT 
    username,
    CASE 
        WHEN is_admin = true THEN 'Admin'
        WHEN is_member = true THEN 'Member'
        ELSE 'Unknown'
    END as user_type,
    COUNT(*) as total_login_attempts,
    SUM(CASE WHEN success = true THEN 1 ELSE 0 END) as successful_logins,
    MAX(created_at) as last_login_attempt
FROM login_attempts 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY username, is_admin, is_member
ORDER BY total_login_attempts DESC
LIMIT 10;
