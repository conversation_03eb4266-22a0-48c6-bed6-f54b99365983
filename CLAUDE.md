# CLAUDE.md - Project Memory & Migration Guide

## Project Overview

**Blacking API** is a backend service built with Go (Gin framework) that provides:
- User management and authentication 
- Member management system
- Banking and financial operations
- System settings and configurations
- Multi-tier architecture with repository pattern

## Recent Major Changes

### 🚀 **SQL to PGX v5 Migration (COMPLETED - July 2025)**

Successfully migrated from `database/sql` to `PGX v5` native PostgreSQL driver for better performance and features.

#### Migration Summary:
- **31 repository files** fully migrated to PGX v5
- **Application layer** updated to use PGX pool
- **Transaction handling** converted from `*sql.Tx` to `pgx.Tx`
- **Error handling** updated for PGX error types
- **Zero compilation errors** - project builds successfully
- **Full CRUD operations tested** and working

#### Key Changes Made:

**Database Operations:**
```go
// OLD (database/sql)
r.db.QueryRowContext(ctx, query, args...)
r.db.QueryContext(ctx, query, args...)
r.db.ExecContext(ctx, query, args...)

// NEW (PGX v5)
dbutil.QueryRowWithSchema(ctx, r.pool, query, args...)
dbutil.QueryWithSchema(ctx, r.pool, query, args...)
dbutil.ExecWithSchema(ctx, r.pool, query, args...)
```

**Transaction Handling:**
```go
// OLD
r.db.BeginTx(ctx)
tx.QueryRowContext(ctx, ...)
tx.ExecContext(ctx, ...)
tx.Rollback()
tx.Commit()

// NEW
r.pool.Begin(ctx)
tx.QueryRow(ctx, ...)
tx.Exec(ctx, ...)
tx.Rollback(ctx)
tx.Commit(ctx)
```

**Error Handling:**
```go
// OLD
sql.ErrNoRows
*pq.Error

// NEW
pgx.ErrNoRows
*pgconn.PgError
```

**Repository Constructors:**
```go
// OLD
func NewRepository(db *database.Database, logger logger.Logger) interfaces.Repository

// NEW
func NewRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.Repository
```

#### Migration Verification:
- ✅ Database connection pool working (PostgreSQL 17.5)
- ✅ Connection pooling: 13 idle connections, max 25
- ✅ Transaction support working
- ✅ Schema path support working (schema: public)
- ✅ DigitalOcean PostgreSQL compatibility confirmed
- ✅ Query parameter binding working
- ✅ Error handling working correctly

## Architecture

### Directory Structure
```
blacking-api/
├── cmd/                     # Application entry points
├── internal/
│   ├── app/                # Application layer
│   ├── config/             # Configuration management
│   ├── domain/             # Business logic and entities
│   ├── handler/            # HTTP handlers
│   ├── middleware/         # HTTP middleware
│   ├── repository/         # Data access layer (PGX v5)
│   ├── router/             # Route definitions (modular)
│   └── service/            # Business logic services
├── infrastructure/
│   └── database/           # Database connection (PGX pool)
├── migrations/             # Database migrations (Go-based)
├── pkg/                    # Shared utilities
│   ├── dbutil/            # Database utilities for PGX
│   ├── logger/            # Logging utilities
│   └── errors/            # Custom error types
└── DB_PROGRESS.md         # Migration tracking file
```

### Key Technologies
- **Framework**: Gin HTTP framework
- **Database**: PostgreSQL with PGX v5 native driver
- **Database Provider**: DigitalOcean Managed PostgreSQL
- **Connection Pooling**: pgxpool.Pool
- **Migrations**: Go-based migrations (custom system)
- **Architecture**: Repository pattern with dependency injection
- **Logging**: Structured logging with custom logger
- **Configuration**: Environment-based configuration

## Database

### Connection Details
- **Driver**: PGX v5 native PostgreSQL driver
- **Provider**: DigitalOcean Managed PostgreSQL
- **Connection Pooling**: Yes (pgxpool.Pool)
- **Schema**: public (default)
- **Migration System**: Custom Go-based migrations

### Database Utilities (`pkg/dbutil/`)
```go
// Schema-aware query functions
dbutil.QueryWithSchema(ctx, pool, query, args...)
dbutil.QueryRowWithSchema(ctx, pool, query, args...)
dbutil.ExecWithSchema(ctx, pool, query, args...)
```

### Repository Pattern
All repositories follow consistent pattern:
```go
type Repository struct {
    pool   *pgxpool.Pool
    logger logger.Logger
}

func NewRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.Repository {
    return &Repository{
        pool:   pool,
        logger: logger,
    }
}
```

## Router Architecture

### Modular Router Structure
Routes are organized into separate files for better maintainability:

```
internal/router/
├── router.go               # Main router setup
├── user.go                 # User authentication routes
├── member.go               # Member management routes
├── banking.go              # Banking system routes
├── system-setting.go       # System configuration routes
├── member-auth.go          # Member authentication
├── permission.go           # Permission management
├── audit-log.go           # Audit logging routes
├── banner.go              # Banner management
├── channel.go             # Channel management
├── commission-group.go    # Commission groups
├── deposit-account.go     # Deposit accounts
├── faq.go                 # FAQ management
├── holding-account.go     # Holding accounts
├── login-report.go        # Login reporting
├── member-group.go        # Member groups
├── otp.go                 # OTP management
├── payment-gateway.go     # Payment gateways
├── referral-group.go      # Referral groups
└── withdraw-account.go    # Withdrawal accounts
```

## Development Guidelines

### Database Operations
- Always use `dbutil` functions for schema-aware queries
- Use `pgx.Tx` for transactions, not `*sql.Tx`
- Handle `pgx.ErrNoRows` for not found errors
- Use connection pool (`*pgxpool.Pool`) for all operations

### Repository Development
```go
// Template for new repositories
type NewRepository struct {
    pool   *pgxpool.Pool
    logger logger.Logger
}

func NewNewRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.NewRepository {
    return &NewRepository{
        pool:   pool,
        logger: logger,
    }
}

// CRUD operations using dbutil
func (r *NewRepository) Create(ctx context.Context, entity *domain.Entity) error {
    query := `INSERT INTO table_name (...) VALUES (...) RETURNING id`
    err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&entity.ID)
    if err != nil {
        r.logger.WithError(err).Error("failed to create entity")
        return errors.NewDatabaseError("failed to create entity")
    }
    return nil
}
```

### Testing Database Operations
- Use the PGX pool directly for database tests
- Test connection with `pool.Ping(ctx)`
- Test transactions with `pool.Begin(ctx)`
- Verify schema path with `SELECT current_schema()`

### Error Handling
```go
// PGX error handling patterns
if err != nil {
    if err == pgx.ErrNoRows {
        return nil, errors.NewNotFoundError("entity not found")
    }
    if pgErr, ok := err.(*pgconn.PgError); ok {
        switch pgErr.Code {
        case "23505": // unique_violation
            return errors.NewValidationError("duplicate entry")
        }
    }
    return errors.NewDatabaseError("database operation failed")
}
```

## Build and Deployment

### Build Commands
```bash
# Build the application
go build -o bin/blacking-api ./cmd/api

# Run tests
go test ./...

# Run with specific config
go run ./cmd/api
```

### Environment Variables
```bash
# Database
DATABASE_HOST=your-db-host
DATABASE_PORT=25061
DATABASE_NAME=your-db-name
DATABASE_USERNAME=your-username
DATABASE_PASSWORD=your-password
DATABASE_SSL_MODE=require
DATABASE_SCHEMA=public

# Server
SERVER_PORT=8080
SERVER_MODE=release

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
```

## Performance Optimizations

### PGX v5 Benefits
- **20-30% faster** than database/sql
- **Lower memory usage** with native protocol
- **Better connection pooling** with pgxpool
- **Native PostgreSQL features** support
- **Improved error handling** with detailed error information

### Connection Pool Settings
```go
// Current settings (in database/database.go)
poolConfig.MaxConns = int32(cfg.Database.MaxOpenConns)       // 25
poolConfig.MinConns = int32(cfg.Database.MaxIdleConns / 2)   // ~10
poolConfig.MaxConnLifetime = time.Hour                       // 1 hour
poolConfig.MaxConnIdleTime = 30 * time.Minute              // 30 min
poolConfig.HealthCheckPeriod = 5 * time.Minute             // 5 min
```

## Migration History

### Major Migrations Completed:
1. **UUID to SERIAL Migration** - Changed primary keys from UUID to auto-increment integers
2. **Router Modularization** - Split monolithic router into 19 separate route files
3. **SQL to PGX v5 Migration** - Complete migration from database/sql to PGX v5 native driver

### Migration Tracking
- Migration progress tracked in `DB_PROGRESS.md`
- All 31 repository files successfully migrated
- Zero compilation errors after migration
- Full functionality testing completed

## Troubleshooting

### Common Issues

**Connection Issues:**
- Ensure DigitalOcean database allows connections from your IP
- Verify SSL mode is set correctly
- Check if custom runtime parameters are supported

**PGX Migration Issues:**
- Use `pgx.ErrNoRows` instead of `sql.ErrNoRows`
- Transaction methods require context: `tx.Commit(ctx)`
- RowsAffected returns single value: `result.RowsAffected()`

**Query Issues:**
- Always use `dbutil` functions for schema path support
- Use proper parameter binding: `$1, $2, $3...`
- Handle PostgreSQL-specific error codes

### Debug Commands
```bash
# Test database connection
go run -c 'fmt.Println("Testing connection..."); /* your test code */'

# Check PGX pool stats
pool.Stat().TotalConns()
pool.Stat().IdleConns()
pool.Stat().AcquiredConns()

# Test schema path
SELECT current_schema();
```

---

**Last Updated**: July 25, 2025
**Migration Status**: ✅ COMPLETE - SQL to PGX v5 migration successful
**Build Status**: ✅ PASSING - Zero compilation errors
**Test Status**: ✅ VERIFIED - All core functionality tested and working