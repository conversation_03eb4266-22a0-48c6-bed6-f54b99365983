package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"blacking-api/internal/config"
	"blacking-api/pkg/logger"
)

func main() {

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	appLogger := logger.New(logger.Config{
		Level:  cfg.Log.Level,
		Format: cfg.Log.Format,
	})

	appLogger.Info("Starting worker...")

	// TODO: Initialize worker services here
	// Example: message queue consumers, scheduled tasks, etc.

	appLogger.Info("Worker started successfully")

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	appLogger.Info("Received shutdown signal")

	// TODO: Graceful shutdown of worker services

	appLogger.Info("Worker stopped successfully")
}