package auth

import (
	"github.com/gin-gonic/gin"
)

// GetUsernameFromContext extracts username from JWT claims
func GetUsernameFromContext(c *gin.Context) string {
	// Try to get from auth-user set by JWT middleware
	if authUser, exists := c.Get("auth-user"); exists {
		if loginClaims, ok := authUser.(*LoginClaims); ok {
			return loginClaims.Username
		}
	}

	// Fallback
	return "system"
}

// GetUserIDFromContext extracts user ID from JWT claims
func GetUserIDFromContext(c *gin.Context) int {
	// Try to get from auth-user set by JWT middleware
	if authUser, exists := c.Get("auth-user"); exists {
		if loginClaims, ok := authUser.(*LoginClaims); ok {
			return loginClaims.ID
		}
	}

	// Fallback
	return 0
}

// GetMemberFromContext extracts member claims from JWT
func GetMemberFromContext(c *gin.Context) *MemberLoginClaims {
	if authMember, exists := c.Get("auth-member"); exists {
		if memberClaims, ok := authMember.(*MemberLoginClaims); ok {
			return memberClaims
		}
	}
	return nil
}

// GetUserRoleIDFromContext extracts user role ID from JWT claims
func GetUserRoleIDFromContext(c *gin.Context) int {
	if authUser, exists := c.Get("auth-user"); exists {
		if loginClaims, ok := authUser.(*LoginClaims); ok {
			return loginClaims.AdminRoleID
		}
	}
	return 0
}

// GetUserRoleNameFromContext extracts user role name from JWT claims
func GetUserRoleNameFromContext(c *gin.Context) string {
	if authUser, exists := c.Get("auth-user"); exists {
		if loginClaims, ok := authUser.(*LoginClaims); ok {
			return loginClaims.AdminRoleName
		}
	}
	return ""
}

// IsUserEnabled checks if current user is enabled
func IsUserEnabled(c *gin.Context) bool {
	if authUser, exists := c.Get("auth-user"); exists {
		if loginClaims, ok := authUser.(*LoginClaims); ok {
			return loginClaims.IsEnable
		}
	}
	return false
}
