package errors

import (
	"encoding/json"
	"fmt"
	"net/http"
)

type ErrorType string

const (
	ValidationError   ErrorType = "VALIDATION_ERROR"
	NotFoundError     ErrorType = "NOT_FOUND"
	UnauthorizedError ErrorType = "UNAUTHORIZED"
	ForbiddenError    ErrorType = "FORBIDDEN"
	ConflictError     ErrorType = "CONFLICT"
	InternalError     ErrorType = "INTERNAL_ERROR"
	DatabaseError     ErrorType = "DATABASE_ERROR"
	ExternalAPIError  ErrorType = "EXTERNAL_API_ERROR"
)

type AppError struct {
	Type       ErrorType              `json:"type"`
	Message    string                 `json:"message"`
	Details    map[string]interface{} `json:"details,omitempty"`
	StatusCode int                    `json:"-"`
	Cause      error                  `json:"-"`
}

func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

func (e *AppError) Unwrap() error {
	return e.Cause
}

func (e *AppError) WithDetails(details map[string]interface{}) *AppError {
	e.Details = details
	return e
}

func (e *AppError) WithCause(cause error) *AppError {
	e.Cause = cause
	return e
}

func NewValidationError(message string) *AppError {
	return &AppError{
		Type:       ValidationError,
		Message:    message,
		StatusCode: http.StatusBadRequest,
	}
}

func NewNotFoundError(message string) *AppError {
	return &AppError{
		Type:       NotFoundError,
		Message:    message,
		StatusCode: http.StatusNotFound,
	}
}

func NewUnauthorizedError(message string) *AppError {
	return &AppError{
		Type:       UnauthorizedError,
		Message:    message,
		StatusCode: http.StatusUnauthorized,
	}
}

func NewForbiddenError(message string) *AppError {
	return &AppError{
		Type:       ForbiddenError,
		Message:    message,
		StatusCode: http.StatusForbidden,
	}
}

func NewConflictError(message string) *AppError {
	return &AppError{
		Type:       ConflictError,
		Message:    message,
		StatusCode: http.StatusConflict,
	}
}

func NewInternalError(message string) *AppError {
	return &AppError{
		Type:       InternalError,
		Message:    message,
		StatusCode: http.StatusInternalServerError,
	}
}

func NewDatabaseError(message string) *AppError {
	return &AppError{
		Type:       DatabaseError,
		Message:    message,
		StatusCode: http.StatusInternalServerError,
	}
}

// IsNotFoundError checks if the error is a not found error
func IsNotFoundError(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Type == NotFoundError
	}
	return false
}

// IsValidationError checks if the error is a validation error
func IsValidationError(err error) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Type == ValidationError
	}
	return false
}

func NewExternalAPIError(message string) *AppError {
	return &AppError{
		Type:       ExternalAPIError,
		Message:    message,
		StatusCode: http.StatusBadGateway,
	}
}

type ErrorResponse struct {
	Error   ErrorType              `json:"error"`
	Message string                 `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
}

func (e *AppError) ToResponse() ErrorResponse {
	return ErrorResponse{
		Error:   e.Type,
		Message: e.Message,
		Details: e.Details,
	}
}

func (e *AppError) ToJSON() string {
	response := e.ToResponse()
	data, _ := json.Marshal(response)
	return string(data)
}

func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

func GetAppError(err error) *AppError {
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}
	return nil
}

func WrapError(err error, errorType ErrorType, message string) *AppError {
	statusCode := http.StatusInternalServerError
	switch errorType {
	case ValidationError:
		statusCode = http.StatusBadRequest
	case NotFoundError:
		statusCode = http.StatusNotFound
	case UnauthorizedError:
		statusCode = http.StatusUnauthorized
	case ForbiddenError:
		statusCode = http.StatusForbidden
	case ConflictError:
		statusCode = http.StatusConflict
	}

	return &AppError{
		Type:       errorType,
		Message:    message,
		StatusCode: statusCode,
		Cause:      err,
	}
}
