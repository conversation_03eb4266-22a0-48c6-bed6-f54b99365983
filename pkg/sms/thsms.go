package sms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

// ThSMSConfig holds configuration for ThSMS API
type ThSMSConfig struct {
	Token   string // For send SMS
	BaseURL string
	Sender  string
	Timeout time.Duration
}

// ThSMSClient represents ThSMS API client
type ThSMSClient struct {
	config ThSMSConfig
	client *http.Client
	logger logger.Logger
}

// CreditResponse represents credit check response
type CreditResponse struct {
	Status  string  `json:"status"`
	Message string  `json:"message"`
	Credit  float64 `json:"credit"`
}

// SendSMSRequest represents SMS sending request
type SendSMSRequest struct {
	Phone   string `json:"phone" validate:"required"`
	Message string `json:"message" validate:"required"`
}

// SendSMSResponse represents SMS sending response
type SendSMSResponse struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		CreditUsage     int `json:"credit_usage"`
		RemainingCredit int `json:"remaining_credit"`
	} `json:"data"`
}

type SendSMSThRequest struct {
	Sender  string   `json:"sender" validate:"required"`
	Msisdn  []string `json:"msisdn" validate:"required"`
	Message string   `json:"message" validate:"required"`
}

// NewThSMSClient creates a new ThSMS client
func NewThSMSClient(config ThSMSConfig, logger logger.Logger) *ThSMSClient {
	if config.BaseURL == "" {
		config.BaseURL = "https://thsms.com/api/rest"
	}
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	return &ThSMSClient{
		config: config,
		client: &http.Client{
			Timeout: config.Timeout,
		},
		logger: logger,
	}
}

// CheckCredit checks remaining SMS credit
func (c *ThSMSClient) CheckCredit() (*CreditResponse, error) {
	log := c.logger.WithField("operation", "CheckCredit")

	url := fmt.Sprintf("%s/credit", c.config.BaseURL)

	payload := map[string]string{
		// "username": c.config.Username,
		// "password": c.config.Password,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		log.WithError(err).Error("failed to marshal credit request")
		return nil, errors.NewInternalError("failed to prepare credit request")
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.WithError(err).Error("failed to create credit request")
		return nil, errors.NewInternalError("failed to create credit request")
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.client.Do(req)
	if err != nil {
		log.WithError(err).Error("failed to send credit request")
		return nil, errors.NewInternalError("failed to check SMS credit")
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read credit response")
		return nil, errors.NewInternalError("failed to read credit response")
	}

	var creditResp CreditResponse
	if err := json.Unmarshal(body, &creditResp); err != nil {
		log.WithError(err).WithField("response_body", string(body)).Error("failed to parse credit response")
		return nil, errors.NewInternalError("failed to parse credit response")
	}

	log.WithField("credit", creditResp.Credit).WithField("status", creditResp.Status).Info("credit check completed")
	return &creditResp, nil
}

// SendSMS sends SMS message
func (c *ThSMSClient) SendSMS(req SendSMSRequest) (*SendSMSResponse, error) {
	log := c.logger.WithField("operation", "SendSMS").WithField("phone", req.Phone)

	url := fmt.Sprintf("%ssend-sms", c.config.BaseURL)
	log.WithField("request_url", url).Info("Sending SMS request to URL")
	payload := SendSMSThRequest{
		Sender:  c.config.Sender,
		Msisdn:  []string{req.Phone},
		Message: req.Message,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		log.WithError(err).Error("failed to marshal SMS request")
		return nil, errors.NewInternalError("failed to prepare SMS request")
	}

	// Print request payload for debugging
	log.WithField("request_payload", string(jsonData)).Info("ThSMS request payload")

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		log.WithError(err).Error("failed to create SMS request")
		return nil, errors.NewInternalError("failed to create SMS request")
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.config.Token)

	resp, err := c.client.Do(httpReq)
	if err != nil {
		log.WithError(err).Error("failed to send SMS request")
		return nil, errors.NewInternalError("failed to send SMS")
	}
	defer resp.Body.Close()

	// Print response status for debugging
	log.WithField("response_status", resp.Status).WithField("response_code", resp.StatusCode).Info("ThSMS response status")

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.WithError(err).Error("failed to read SMS response")
		return nil, errors.NewInternalError("failed to read SMS response")
	}

	// Print raw response body for debugging
	log.WithField("raw_response", string(body)).Info("ThSMS send SMS raw response")

	var smsResp SendSMSResponse
	if err := json.Unmarshal(body, &smsResp); err != nil {
		log.WithError(err).WithField("response_body", string(body)).Error("failed to parse SMS response")
		return nil, errors.NewInternalError("failed to parse SMS response")
	}

	if !smsResp.Success || smsResp.Code != 200 {
		log.WithField("success", smsResp.Success).WithField("code", smsResp.Code).WithField("message", smsResp.Message).Error("SMS sending failed")
		return nil, errors.NewValidationError(fmt.Sprintf("SMS sending failed: %s", smsResp.Message))
	}

	log.WithField("credit_usage", smsResp.Data.CreditUsage).WithField("remaining_credit", smsResp.Data.RemainingCredit).Info("SMS sent successfully")
	return &smsResp, nil
}

// ValidatePhone validates Thai phone number format
func ValidatePhone(phone string) error {
	if len(phone) < 10 {
		return errors.NewValidationError("phone number must be at least 10 digits")
	}

	// Remove common prefixes and validate
	if phone[0:2] == "66" && len(phone) == 11 {
		// International format: 66xxxxxxxxx
		return nil
	} else if phone[0:1] == "0" && len(phone) == 10 {
		// Local format: 0xxxxxxxxx
		return nil
	} else if len(phone) == 9 {
		// Without leading 0: xxxxxxxxx
		return nil
	}

	return errors.NewValidationError("invalid Thai phone number format")
}

// FormatPhone formats phone number to international format
func FormatPhone(phone string) string {
	// Remove any spaces or special characters
	phone = removeNonDigits(phone)

	// Convert to international format (66xxxxxxxxx)
	if phone[0:1] == "0" && len(phone) == 10 {
		return "66" + phone[1:]
	} else if len(phone) == 9 {
		return "66" + phone
	} else if phone[0:2] == "66" && len(phone) == 11 {
		return phone
	}

	return phone
}

// removeNonDigits removes all non-digit characters
func removeNonDigits(s string) string {
	result := ""
	for _, char := range s {
		if char >= '0' && char <= '9' {
			result += string(char)
		}
	}
	return result
}
