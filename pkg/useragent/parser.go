package useragent

import (
	"regexp"
	"strings"
)

// DeviceInfo represents parsed device information from user agent
type DeviceInfo struct {
	DeviceType     *string
	Browser        *string
	BrowserVersion *string
	OS             *string
	OSVersion      *string
	Platform       *string
	IsMobile       bool
	IsTablet       bool
	IsDesktop      bool
}

// ParseUserAgent parses user agent string and extracts device information
func ParseUserAgent(userAgent string) *DeviceInfo {
	if userAgent == "" {
		return &DeviceInfo{}
	}

	info := &DeviceInfo{}
	ua := strings.ToLower(userAgent)

	// Detect device type and platform
	info.detectDeviceType(ua)
	info.detectBrowser(userAgent)
	info.detectOS(userAgent)

	return info
}

// detectDeviceType detects if device is mobile, tablet, or desktop
func (d *DeviceInfo) detectDeviceType(ua string) {
	// Mobile patterns
	mobilePatterns := []string{
		"mobile", "android", "iphone", "ipod", "blackberry", "windows phone",
		"palm", "symbian", "nokia", "samsung", "htc", "lg", "motorola",
	}

	// Tablet patterns
	tabletPatterns := []string{
		"ipad", "tablet", "kindle", "playbook", "nexus 7", "nexus 10",
		"galaxy tab", "xoom", "transformer",
	}

	// Check for tablet first (more specific)
	for _, pattern := range tabletPatterns {
		if strings.Contains(ua, pattern) {
			d.IsTablet = true
			d.DeviceType = stringPtr("tablet")
			d.Platform = stringPtr("tablet")
			return
		}
	}

	// Check for mobile
	for _, pattern := range mobilePatterns {
		if strings.Contains(ua, pattern) {
			d.IsMobile = true
			d.DeviceType = stringPtr("mobile")
			d.Platform = stringPtr("mobile")
			return
		}
	}

	// Default to desktop
	d.IsDesktop = true
	d.DeviceType = stringPtr("desktop")
	d.Platform = stringPtr("desktop")
}

// detectBrowser detects browser name and version
func (d *DeviceInfo) detectBrowser(userAgent string) {
	browsers := []struct {
		name    string
		pattern *regexp.Regexp
	}{
		{"Chrome", regexp.MustCompile(`Chrome/(\d+\.\d+)`)},
		{"Firefox", regexp.MustCompile(`Firefox/(\d+\.\d+)`)},
		{"Safari", regexp.MustCompile(`Safari/(\d+\.\d+)`)},
		{"Edge", regexp.MustCompile(`Edge/(\d+\.\d+)`)},
		{"Opera", regexp.MustCompile(`Opera/(\d+\.\d+)`)},
		{"Internet Explorer", regexp.MustCompile(`MSIE (\d+\.\d+)`)},
	}

	for _, browser := range browsers {
		if matches := browser.pattern.FindStringSubmatch(userAgent); len(matches) > 1 {
			d.Browser = stringPtr(browser.name)
			d.BrowserVersion = stringPtr(matches[1])
			return
		}
	}

	// Fallback for Chrome (sometimes version is in different format)
	if strings.Contains(userAgent, "Chrome") {
		d.Browser = stringPtr("Chrome")
		if matches := regexp.MustCompile(`Chrome/(\d+)`).FindStringSubmatch(userAgent); len(matches) > 1 {
			d.BrowserVersion = stringPtr(matches[1])
		}
	}
}

// detectOS detects operating system and version
func (d *DeviceInfo) detectOS(userAgent string) {
	osPatterns := []struct {
		name    string
		pattern *regexp.Regexp
	}{
		{"Windows", regexp.MustCompile(`Windows NT (\d+\.\d+)`)},
		{"macOS", regexp.MustCompile(`Mac OS X (\d+[._]\d+[._]?\d*)`)},
		{"Linux", regexp.MustCompile(`Linux`)},
		{"Android", regexp.MustCompile(`Android (\d+\.\d+)`)},
		{"iOS", regexp.MustCompile(`OS (\d+_\d+)`)},
		{"Ubuntu", regexp.MustCompile(`Ubuntu`)},
	}

	for _, os := range osPatterns {
		if matches := os.pattern.FindStringSubmatch(userAgent); len(matches) > 0 {
			d.OS = stringPtr(os.name)
			if len(matches) > 1 {
				version := strings.ReplaceAll(matches[1], "_", ".")
				d.OSVersion = stringPtr(version)
			}
			return
		}
	}

	// Simple OS detection without version
	if strings.Contains(userAgent, "Windows") {
		d.OS = stringPtr("Windows")
	} else if strings.Contains(userAgent, "Mac") {
		d.OS = stringPtr("macOS")
	} else if strings.Contains(userAgent, "Linux") {
		d.OS = stringPtr("Linux")
	} else if strings.Contains(userAgent, "Android") {
		d.OS = stringPtr("Android")
	} else if strings.Contains(userAgent, "iPhone") || strings.Contains(userAgent, "iPad") {
		d.OS = stringPtr("iOS")
	}
}

// stringPtr returns a pointer to string
func stringPtr(s string) *string {
	return &s
}
