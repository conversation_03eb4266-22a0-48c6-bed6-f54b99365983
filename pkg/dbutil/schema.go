package dbutil

import (
	"context"
	"fmt"
	"os"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

// EnsureSchemaPath ensures the correct search_path is set for the connection
// This is a utility function to handle DigitalOcean PostgreSQL schema issues
func EnsureSchemaPath(ctx context.Context, pool *pgxpool.Pool) error {
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" || schemaName == "public" {
		return nil // No need to set schema for public
	}

	_, err := pool.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		return fmt.Errorf("failed to set search_path to %s: %w", schemaName, err)
	}

	return nil
}

// WithSchemaPath executes a function with the correct schema path set
func WithSchemaPath(ctx context.Context, pool *pgxpool.Pool, fn func() error) error {
	// Ensure schema path is set
	if err := EnsureSchemaPath(ctx, pool); err != nil {
		return err
	}

	// Execute the function
	return fn()
}

// ExecWithSchema executes a query with schema path ensured
func ExecWithSchema(ctx context.Context, pool *pgxpool.Pool, query string, args ...interface{}) (pgconn.CommandTag, error) {
	var result pgconn.CommandTag
	err := WithSchemaPath(ctx, pool, func() error {
		var execErr error
		result, execErr = pool.Exec(ctx, query, args...)
		return execErr
	})
	return result, err
}

// QueryRowWithSchema queries a single row with schema path ensured
func QueryRowWithSchema(ctx context.Context, pool *pgxpool.Pool, query string, args ...interface{}) pgx.Row {
	// Best effort to set schema path - ignore errors for this utility
	_ = EnsureSchemaPath(ctx, pool)
	return pool.QueryRow(ctx, query, args...)
}

// QueryWithSchema queries multiple rows with schema path ensured
func QueryWithSchema(ctx context.Context, pool *pgxpool.Pool, query string, args ...interface{}) (pgx.Rows, error) {
	if err := EnsureSchemaPath(ctx, pool); err != nil {
		return nil, err
	}
	return pool.Query(ctx, query, args...)
}