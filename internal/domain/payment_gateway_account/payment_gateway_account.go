package payment_gateway_account

import (
	"blacking-api/internal/domain/request"
	"time"
)

type PaymentGatewayAccount struct {
	ID                                 int64     `json:"id" db:"id"`
	AccountName                        string    `json:"account_name" db:"account_name"`
	Code                               string    `json:"code" db:"code"`
	Provider                           string    `json:"provider" db:"provider"`
	MerchantCode                       string    `json:"merchant_code" db:"merchant_code"`
	SecretKey                          string    `json:"secret_key" db:"secret_key"`
	SecretKeyTwo                       string    `json:"secret_key_two" db:"secret_key_two"`
	FirstUsername                      string    `json:"first_username" db:"first_username"`
	SecondUsername                     string    `json:"second_username" db:"second_username"`
	FirstPassword                      string    `json:"first_password" db:"first_password"`
	SecondPassword                     string    `json:"second_password" db:"second_password"`
	MinimumWithdraw                    float64   `json:"minimum_withdraw" db:"minimum_withdraw"`
	MaximumWithdraw                    float64   `json:"maximum_withdraw" db:"maximum_withdraw"`
	WithdrawSplit                      bool      `json:"withdraw_split" db:"withdraw_split"`
	MaximumWithdrawPerTransaction      float64   `json:"maximum_withdraw_per_transaction" db:"maximum_withdraw_per_transaction"`
	MaximumSplitWithdrawPerTransaction float64   `json:"maximum_split_withdraw_per_transaction" db:"maximum_split_withdraw_per_transaction"`
	IsDeposit                          bool      `json:"is_deposit" db:"is_deposit"`
	IsWithdraw                         bool      `json:"is_withdraw" db:"is_withdraw"`
	IsTransfer                         bool      `json:"is_transfer" db:"is_transfer"`
	Active                             bool      `json:"active" db:"active"`
	Inactive                           bool      `json:"inactive" db:"inactive"`
	CreatedAt                          time.Time `json:"created_at" db:"created_at"`
	UpdatedAt                          time.Time `json:"updated_at" db:"updated_at"`
}

type PaymentGatewayAccountRequest struct {
	AccountName                        string  `json:"accountName" validate:"required,min=1,max=255"`
	Code                               string  `json:"code" validate:"required,min=1,max=255"`
	Provider                           string  `json:"provider" validate:"required,min=1,max=255"`
	MerchantCode                       string  `json:"merchantCode" validate:"required,min=1,max=255"`
	SecretKey                          string  `json:"secretKey" validate:"required,min=1,max=255"`
	SecretKeyTwo                       string  `json:"secretKeyTwo" validate:"required,min=1,max=255"`
	FirstUsername                      string  `json:"firstUsername" validate:"required,min=1,max=255"`
	SecondUsername                     string  `json:"secondUsername" validate:"required,min=1,max=255"`
	FirstPassword                      string  `json:"firstPassword" validate:"required,min=1,max=255"`
	SecondPassword                     string  `json:"secondPassword" validate:"required,min=1,max=255"`
	MinimumWithdraw                    float64 `json:"minimumWithdraw" validate:"required"`
	MaximumWithdraw                    float64 `json:"maximumWithdraw" validate:"required"`
	WithdrawSplit                      bool    `json:"withdrawSplit" default:"false"`
	MaximumWithdrawPerTransaction      float64 `json:"maximumWithdrawPerTransaction" validate:"required"`
	MaximumSplitWithdrawPerTransaction float64 `json:"maximumSplitWithdrawPerTransaction" validate:"required"`
	IsDeposit                          bool    `json:"isDeposit" default:"false"`
	IsWithdraw                         bool    `json:"isWithdraw" default:"false"`
	IsTransfer                         bool    `json:"isTransfer" default:"false"`
}

type PaymentGatewayAccountListResponse struct {
	ID              int64     `json:"id"`
	AccountName     string    `json:"accountName"`
	MerchantCode    string    `json:"merchantCode"`
	Provider        string    `json:"provider"`
	MinimumWithdraw float64   `json:"minimumWithdraw"`
	MaximumWithdraw float64   `json:"maximumWithdraw"`
	IsDeposit       bool      `json:"isDeposit"`
	IsWithdraw      bool      `json:"isWithdraw"`
	IsTransfer      bool      `json:"isTransfer"`
	Active          bool      `json:"active"`
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

type PaymentGatewayAccountByIdResponse struct {
	ID                                 int64   `json:"id"`
	AccountName                        string  `json:"accountName"`
	Code                               string  `json:"code"`
	Provider                           string  `json:"provider" `
	MerchantCode                       string  `json:"merchantCode"`
	SecretKey                          string  `json:"secretKey"`
	SecretKeyTwo                       string  `json:"secretKeyTwo"`
	FirstUsername                      string  `json:"firstUsername"`
	SecondUsername                     string  `json:"secondUsername"`
	FirstPassword                      string  `json:"firstPassword"`
	SecondPassword                     string  `json:"secondPassword"`
	MinimumWithdraw                    float64 `json:"minimumWithdraw"`
	MaximumWithdraw                    float64 `json:"maximumWithdraw"`
	WithdrawSplit                      bool    `json:"withdrawSplit"`
	MaximumWithdrawPerTransaction      float64 `json:"maximumWithdrawPerTransaction"`
	MaximumSplitWithdrawPerTransaction float64 `json:"maximumSplitWithdrawPerTransaction"`
	IsDeposit                          bool    `json:"isDeposit"`
	IsWithdraw                         bool    `json:"isWithdraw"`
	IsTransfer                         bool    `json:"isTransfer"`
}

type PaymentGatewayAccountSearchRequest struct {
	request.RequestPagination
	AccountName *string `form:"accountName"`
}

type UpdateStatusRequest struct {
	Name   string `form:"name" validate:"required,min=1,max=255"`
	Status bool   `form:"status" default:"false"`
}

func (p *PaymentGatewayAccount) ToListResponse() *PaymentGatewayAccountListResponse {
	return &PaymentGatewayAccountListResponse{
		ID:              p.ID,
		AccountName:     p.AccountName,
		MerchantCode:    p.MerchantCode,
		Provider:        p.Provider,
		MinimumWithdraw: p.MinimumWithdraw,
		MaximumWithdraw: p.MaximumWithdraw,
		IsDeposit:       p.IsDeposit,
		IsWithdraw:      p.IsWithdraw,
		IsTransfer:      p.IsTransfer,
		Active:          p.Active,
		CreatedAt:       p.CreatedAt,
		UpdatedAt:       p.UpdatedAt,
	}
}

func (p *PaymentGatewayAccount) ToByIdResponse() *PaymentGatewayAccountByIdResponse {
	return &PaymentGatewayAccountByIdResponse{
		ID:                                 p.ID,
		AccountName:                        p.AccountName,
		Code:                               p.Code,
		Provider:                           p.Provider,
		MerchantCode:                       p.MerchantCode,
		SecretKey:                          p.SecretKey,
		SecretKeyTwo:                       p.SecretKeyTwo,
		FirstUsername:                      p.FirstUsername,
		SecondUsername:                     p.SecondUsername,
		FirstPassword:                      p.FirstPassword,
		SecondPassword:                     p.SecondPassword,
		MinimumWithdraw:                    p.MinimumWithdraw,
		MaximumWithdraw:                    p.MaximumWithdraw,
		WithdrawSplit:                      p.WithdrawSplit,
		MaximumWithdrawPerTransaction:      p.MaximumWithdrawPerTransaction,
		MaximumSplitWithdrawPerTransaction: p.MaximumSplitWithdrawPerTransaction,
		IsDeposit:                          p.IsDeposit,
		IsWithdraw:                         p.IsWithdraw,
		IsTransfer:                         p.IsTransfer,
	}
}
