package user_audit_log

import (
	"blacking-api/pkg/errors"
	"time"
)

// ActionType represents the type of action performed on user
type ActionType string

const (
	ActionCreate     ActionType = "create"
	ActionUpdate     ActionType = "update"
	ActionDelete     ActionType = "delete"
	ActionActivate   ActionType = "activate"
	ActionDeactivate ActionType = "deactivate"
	ActionSuspend    ActionType = "suspend"
	ActionPassword   ActionType = "password_change"
)

// ActionTypeInfo represents action type with Thai translation
type ActionTypeInfo struct {
	Key   string `json:"key"`
	Value string `json:"value"`
	Label string `json:"label"`
}

// GetAllActionTypes returns all available action types with Thai translations
func GetAllActionTypes() []ActionTypeInfo {
	return []ActionTypeInfo{
		{Key: string(ActionCreate), Value: string(ActionCreate), Label: "สร้างผู้ใช้"},
		{Key: string(ActionUpdate), Value: string(ActionUpdate), Label: "แก้ไขข้อมูล"},
		{Key: string(ActionDelete), Value: string(ActionDelete), Label: "ลบผู้ใช้"},
		{Key: string(ActionActivate), Value: string(ActionActivate), Label: "เปิดใช้งาน"},
		{Key: string(ActionDeactivate), Value: string(ActionDeactivate), Label: "ปิดใช้งาน"},
		{Key: string(ActionSuspend), Value: string(ActionSuspend), Label: "ระงับการใช้งาน"},
		{Key: string(ActionPassword), Value: string(ActionPassword), Label: "เปลี่ยนรหัสผ่าน"},
	}
}

// GetActionTypeLabel returns Thai label for given action type
func GetActionTypeLabel(actionType ActionType) string {
	actionTypes := GetAllActionTypes()
	for _, at := range actionTypes {
		if at.Key == string(actionType) {
			return at.Label
		}
	}
	return string(actionType) // fallback to original value
}

// UserAuditLog represents an audit log entry for user operations
type UserAuditLog struct {
	ID            int        `json:"id" db:"id"`
	UserID        int        `json:"user_id" db:"user_id"`
	Username      string     `json:"username" db:"username"`
	Action        ActionType `json:"action" db:"action"`
	OldValues     *string    `json:"old_values,omitempty" db:"old_values"`
	NewValues     *string    `json:"new_values,omitempty" db:"new_values"`
	ChangedBy     int        `json:"changed_by" db:"changed_by"`
	ChangedByName string     `json:"changed_by_name" db:"changed_by_name"`
	ChangedAt     time.Time  `json:"changed_at" db:"changed_at"`
}

// CreateAuditLogRequest represents the request to create an audit log
type CreateAuditLogRequest struct {
	UserID        int        `json:"user_id" validate:"required"`
	Username      string     `json:"username" validate:"required"`
	Action        ActionType `json:"action" validate:"required"`
	OldValues     *string    `json:"old_values,omitempty"`
	NewValues     *string    `json:"new_values,omitempty"`
	ChangedBy     int        `json:"changed_by" validate:"required"`
	ChangedByName string     `json:"changed_by_name" validate:"required"`
}

// UserAuditLogResponse represents the response structure for audit log
type UserAuditLogResponse struct {
	ID            int        `json:"id"`
	UserID        int        `json:"user_id"`
	Username      string     `json:"username"`
	Action        ActionType `json:"action"`
	ActionLabel   string     `json:"action_label"`
	OldValues     *string    `json:"old_values,omitempty"`
	NewValues     *string    `json:"new_values,omitempty"`
	ChangedBy     int        `json:"changed_by"`
	ChangedByName string     `json:"changed_by_name"`
	ChangedAt     time.Time  `json:"changed_at"`
}

// NewUserAuditLog creates a new user audit log entry
func NewUserAuditLog(req CreateAuditLogRequest) (*UserAuditLog, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	auditLog := &UserAuditLog{
		UserID:        req.UserID,
		Username:      req.Username,
		Action:        req.Action,
		OldValues:     req.OldValues,
		NewValues:     req.NewValues,
		ChangedBy:     req.ChangedBy,
		ChangedByName: req.ChangedByName,
		ChangedAt:     now,
	}

	return auditLog, nil
}

// ToResponse converts UserAuditLog to UserAuditLogResponse
func (u *UserAuditLog) ToResponse() UserAuditLogResponse {
	return UserAuditLogResponse{
		ID:            u.ID,
		UserID:        u.UserID,
		Username:      u.Username,
		Action:        u.Action,
		ActionLabel:   GetActionTypeLabel(u.Action),
		OldValues:     u.OldValues,
		NewValues:     u.NewValues,
		ChangedBy:     u.ChangedBy,
		ChangedByName: u.ChangedByName,
		ChangedAt:     u.ChangedAt,
	}
}

// IsValidAction checks if the action type is valid
func IsValidAction(action ActionType) bool {
	switch action {
	case ActionCreate, ActionUpdate, ActionDelete, ActionActivate, ActionDeactivate, ActionSuspend, ActionPassword:
		return true
	default:
		return false
	}
}

// GetActionDescription returns a human-readable description for the action
func (a ActionType) GetActionDescription() string {
	switch a {
	case ActionCreate:
		return "User created"
	case ActionUpdate:
		return "User updated"
	case ActionDelete:
		return "User deleted"
	case ActionActivate:
		return "User activated"
	case ActionDeactivate:
		return "User deactivated"
	case ActionSuspend:
		return "User suspended"
	case ActionPassword:
		return "User password changed"
	default:
		return "Unknown action"
	}
}

// validateCreateRequest validates the create audit log request
func validateCreateRequest(req CreateAuditLogRequest) error {
	if req.UserID == 0 {
		return errors.NewValidationError("user_id is required")
	}
	if req.Username == "" {
		return errors.NewValidationError("username is required")
	}
	if req.Action == "" {
		return errors.NewValidationError("action is required")
	}
	if !IsValidAction(req.Action) {
		return errors.NewValidationError("invalid action type")
	}
	if req.ChangedBy == 0 {
		return errors.NewValidationError("changed_by is required")
	}
	if req.ChangedByName == "" {
		return errors.NewValidationError("changed_by_name is required")
	}
	return nil
}
