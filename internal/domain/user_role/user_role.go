package userrole

import (
	"blacking-api/pkg/errors"
	"time"
)

type UserRole struct {
	ID        int       `json:"id"`
	Position  *int      `json:"position"`
	Name      *string   `json:"name"`
	Is2FA     bool      `json:"is_2fa"`
	IsLockIP  bool      `json:"is_lock_ip"`
	IsEnable  bool      `json:"is_enable"`
	Status    Status    `json:"status"`
	UpdatedAt time.Time `json:"updated_at"`
	CreatedAt time.Time `json:"created_at"`
}

type Status string

const (
	StatusActive    Status = "active"
	StatusInactive  Status = "inactive"
	StatusSuspended Status = "suspended"
)

type CreateUserRoleRequest struct {
	Name     string `json:"name" validate:"required"`
	IsEnable bool   `json:"is_enable" validate:"required"`
	Is2FA    *bool  `json:"is_2fa" `
	IsLockIP *bool  `json:"is_lock_ip" `
}

type UpdateUserRoleRequest struct {
	Name     *string `json:"name" validate:"required"`
	IsEnable *bool   `json:"is_enable" validate:"required"`
}

type LockIPToggle struct {
	ID       int  `json:"id" validate:"required"`
	IsLockIP bool `json:"is_lock_ip"`
}

type Toggle2FA struct {
	ID    int  `json:"id" validate:"required"`
	Is2FA bool `json:"is_2fa"`
}

type BulkToggleLockIPRequest struct {
	Toggles []LockIPToggle `json:"toggles" validate:"required,min=1"`
}

type BulkToggle2FARequest struct {
	Toggles []Toggle2FA `json:"toggles" validate:"required,min=1"`
}

type ReorderRequest struct {
	UserRoleIDs []int `json:"user_role_ids" validate:"required,min=1"`
}

type UserRoleResponse struct {
	ID        int       `json:"id"`
	Position  *int      `json:"position"`
	Name      *string   `json:"name"`
	Is2FA     bool      `json:"is_2fa"`
	IsLockIP  bool      `json:"is_lock_ip"`
	IsEnable  bool      `json:"is_enable"`
	Status    Status    `json:"status"`
	UpdatedAt time.Time `json:"updated_at"`
	CreatedAt time.Time `json:"created_at"`
}

// UserRoleDropdownResponse represents user role dropdown response
type UserRoleDropdownResponse struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type BulkLockIPResponse struct {
	ID       int     `json:"id"`
	Position *int    `json:"position"`
	Name     *string `json:"name"`
	Is2FA    bool    `json:"is_2fa"`
	IsLockIP bool    `json:"is_lock_ip"`
}

type Bulk2FAResponse struct {
	ID       int     `json:"id"`
	Position *int    `json:"position"`
	Name     *string `json:"name"`
	Status   Status  `json:"status"`
	Is2FA    bool    `json:"is_2fa"`
	IsLockIP bool    `json:"is_lock_ip"`
}

func NewUserRole(req CreateUserRoleRequest) (*UserRole, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	user := &UserRole{
		Name:      &req.Name,
		IsEnable:  req.IsEnable,
		Status:    StatusActive,
		CreatedAt: now,
		UpdatedAt: now,
	}
	if req.IsLockIP != nil {
		user.IsLockIP = *req.IsLockIP
	}
	if req.Is2FA != nil {
		user.Is2FA = *req.Is2FA
	}
	return user, nil
}

func (u *UserRole) Update(req UpdateUserRoleRequest) error {
	if err := validateUpdateRequest(req); err != nil {
		return err
	}

	if req.Name != nil {
		u.Name = req.Name
	}
	u.IsEnable = *req.IsEnable

	u.UpdatedAt = time.Now()
	return nil
}

func (u *UserRole) IsActive() bool {
	return u.Status == StatusActive
}

func (u *UserRole) Deactivate() {
	u.Status = StatusInactive
	u.UpdatedAt = time.Now()
}

func (u *UserRole) Suspend() {
	u.Status = StatusSuspended
	u.UpdatedAt = time.Now()
}

func (u *UserRole) Activate() {
	u.Status = StatusActive
	u.UpdatedAt = time.Now()
}

func (u *UserRole) ToggleLockIP(isLockIP bool) {
	u.IsLockIP = isLockIP
	u.UpdatedAt = time.Now()
}

func (u *UserRole) Toggle2FA(is2FA bool) {
	u.Is2FA = is2FA
	u.UpdatedAt = time.Now()
}

func (u *UserRole) ToResponse() UserRoleResponse {
	return UserRoleResponse{
		ID:        u.ID,
		Name:      u.Name,
		Position:  u.Position,
		Is2FA:     u.Is2FA,
		IsLockIP:  u.IsLockIP,
		IsEnable:  u.IsEnable,
		Status:    u.Status,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
	}
}

func (u *UserRole) ToBulkLockIPResponse() BulkLockIPResponse {
	return BulkLockIPResponse{
		ID:       u.ID,
		Position: u.Position,
		Name:     u.Name,
		Is2FA:    u.Is2FA,
		IsLockIP: u.IsLockIP,
	}
}

func (u *UserRole) ToBulk2FAResponse() Bulk2FAResponse {
	return Bulk2FAResponse{
		ID:       u.ID,
		Position: u.Position,
		Name:     u.Name,
		Status:   u.Status,
		Is2FA:    u.Is2FA,
		IsLockIP: u.IsLockIP,
	}
}

func validateCreateRequest(req CreateUserRoleRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}
	return nil
}

func validateUpdateRequest(req UpdateUserRoleRequest) error {
	if req.Name != nil && *req.Name == "" {
		return errors.NewValidationError("name cannot be empty")
	}
	if req.IsEnable == nil {
		return errors.NewValidationError("is_enable cannot be empty")
	}

	return nil
}
