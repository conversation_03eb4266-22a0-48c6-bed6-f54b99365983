package system_setting

import (
	"blacking-api/pkg/errors"
	"time"
)

// SettingOption represents a setting option for dropdown
type SettingOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// SettingOptionsResponse represents the response for setting options
type SettingOptionsResponse struct {
	Options []SettingOption `json:"options"`
}

// SystemSetting represents a system configuration setting
type SystemSetting struct {
	ID          int       `json:"id" db:"id"`
	Key         string    `json:"key" db:"key"`
	Value       string    `json:"value" db:"value"`
	Description *string   `json:"description,omitempty" db:"description"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
}

// Predefined system setting keys
const (
	KeyMaxLoginAttempts = "max_login_attempts"

	// Group: General
	KeyEnableDeposit              = "enable_deposit"
	KeyEnableDepositRemark        = "enable_deposit_remark"
	KeyEnableWithdraw             = "enable_withdraw"
	KeyEnableWithdrawRemark       = "enable_withdraw_remark"
	KeyEnableGame                 = "enable_game"
	KeyEnableGameRemark           = "enable_game_remark"
	KeyEnableTruemoney            = "enable_truemoney"
	KeyEnableTruemoneyRemark      = "enable_truemoney_remark"
	KeyEnableMemberRegister       = "enable_member_register"
	KeyEnableMemberRegisterRemark = "enable_member_register_remark"

	// Group: Deposit/Withdraw
	KeyEnableSlipVerify               = "enable_slip_verify"
	KeyEnableSlipVerifyRemark         = "enable_slip_verify_remark"
	KeyEnablePaymentGatewayOnly       = "enable_payment_gateway_only"
	KeyEnablePaymentGatewayOnlyRemark = "enable_payment_gateway_only_remark"
	KeyEnableHideDepositChannel       = "enable_hide_deposit_channel"
	KeyEnableHideDepositChannelRemark = "enable_hide_deposit_channel_remark"
	KeyEnableShowDepositDecimal       = "enable_show_deposit_decimal"
	KeyEnableShowDepositDecimalRemark = "enable_show_deposit_decimal_remark"
	KeyEnableWithdrawPincode          = "enable_withdraw_pincode"
	KeyEnableWithdrawPincodeRemark    = "enable_withdraw_pincode_remark"
	KeyPlayerAttemptLimit             = "player_attempt_limit"
	KeyTimeoutDeposit                 = "timeout_deposit"
	KeyTimeoutWithdraw                = "timeout_withdraw"

	// Group: Analytics
	KeyEnableGoogleAnalytics = "enable_gg_analytics"
	KeyGoogleAnalyticsKey    = "gg_analytics_key"
	KeyEnableFacebookPixel   = "enable_fb_pixel_id"
	KeyFacebookPixelID       = "fb_pixel_id"

	// Group: Other
	KeyEnableSettingUsername       = "enable_setting_username"
	KeyEnableSettingUsernameRemark = "enable_setting_username_remark"
	KeyEnableSettingGender         = "enable_setting_gender"
	KeyEnableSettingGenderRemark   = "enable_setting_gender_remark"
	KeyOTPOption                   = "otp_option"
	KeyEnableOTPBypass             = "enable_otp_bypass"
	KeyEnableOTPBypassRemark       = "enable_otp_bypass_remark"
	KeyCaptchaConfigPublic         = "captcha_config_public"
	KeyCaptchaConfigPrivate        = "captcha_config_private"
	KeyAdminLang                   = "admin_lang"
	KeyPlayerLang                  = "player_lang"

	// Group: SEO
	KeySEOSiteTitle       = "seo_site_title"
	KeySEOTitle           = "seo_title"
	KeySEOMetaDescription = "seo_meta_description"
	KeySEOFavicon         = "seo_favicon"
	KeySEOFeaturedImage   = "seo_featured_image"

	// Group: SiteImage
	KeySiteImageLogoBeforeLogin             = "image_logo_before_login"
	KeySiteImageLogoAfterLogin              = "image_logo_after_login"
	KeySiteImageGameLoading                 = "image_game_loading"
	KeySiteImageLevelIcon                   = "image_level_icon"
	KeySiteImageNotificationText            = "image_notification_text"
	KeySiteImageNotificationPopup           = "image_notification_popup"
	KeySiteImageDepositSuccess              = "image_deposit_success"
	KeySiteImageWithdrawSuccess             = "image_withdraw_success"
	KeySiteImageDepositFail                 = "image_deposit_fail"
	KeySiteImageWithdrawFail                = "image_withdraw_fail"
	KeySiteImageEnableVideoTutorial         = "image_enable_video_tutorial"
	KeySiteImageVideoDeposit                = "image_video_deposit"
	KeySiteImageVideoDepositSmall           = "image_video_deposit_small"
	KeySiteImageVideoDepositDecimal         = "image_video_deposit_decimal"
	KeySiteImageVideoDepositDecimalSmall    = "image_video_deposit_decimal_small"
	KeySiteImageVideoDepositQR              = "image_video_deposit_qr"
	KeySiteImageVideoDepositQRSmall         = "image_video_deposit_qr_small"
	KeySiteImageVideoDepositTruewallet      = "image_video_deposit_truewallet"
	KeySiteImageVideoDepositTruewalletSmall = "image_video_deposit_truewallet_small"

	// Group: Network
	KeySiteNetworkTutorialImage          = "network_tutorial_image"
	KeySiteNetworkTutorialText           = "network_tutorial_text"
	KeySiteNetworkTutorialMakeMoneyImage = "network_tutorial_make_money_image"

	// Commission Settings Keys
	KeyCommissionAutoApprovalThreshold = "commission_auto_approval_threshold"
	KeyCommissionMinWithdrawAmount     = "commission_min_withdraw_amount"

	// Referral Settings Keys
	KeyReferralAutoApprovalThreshold = "referral_auto_approval_threshold"
	KeyReferralMinWithdrawAmount     = "referral_min_withdraw_amount"

	// Member Level Calculation Settings Keys
	KeyMemberLevelUpgradeType   = "member_level_upgrade_type"   // daily, monthly, immediate
	KeyMemberLevelUpgradeTime   = "member_level_upgrade_time"   // HH:MM format (e.g., "03:00")
	KeyMemberLevelUpgradeDay    = "member_level_upgrade_day"    // 1-31 for monthly type
	KeyMemberLevelDowngradeType = "member_level_downgrade_type" // daily, monthly, immediate
	KeyMemberLevelDowngradeTime = "member_level_downgrade_time" // HH:MM format (e.g., "04:30")
	KeyMemberLevelDowngradeDay  = "member_level_downgrade_day"  // 1-31 for monthly type

	// Member settings
	KeyMemberPrefix = "setting_member_prefix"
)

// UpdateSystemSettingRequest represents the request to update a system setting
type UpdateSystemSettingRequest struct {
	Value string `json:"value" validate:"required"`
}

// SystemSettingResponse represents the response structure for system setting
type SystemSettingResponse struct {
	ID          int       `json:"id"`
	Key         string    `json:"key"`
	Value       string    `json:"value"`
	Description *string   `json:"description,omitempty"`
	UpdatedAt   time.Time `json:"updated_at"`
	CreatedAt   time.Time `json:"created_at"`
}

// LoginAttemptLimitResponse represents the response for login attempt limit
type LoginAttemptLimitResponse struct {
	MaxAttempts int    `json:"max_attempts"`
	Description string `json:"description"`
}

// UpdateLoginAttemptLimitRequest represents the request to update login attempt limit
type UpdateLoginAttemptLimitRequest struct {
	MaxAttempts int `json:"max_attempts" validate:"min=0"`
}

// GeneralSettingsResponse represents the response for general settings
type GeneralSettingsResponse struct {
	General         GeneralGroup         `json:"general"`
	DepositWithdraw DepositWithdrawGroup `json:"deposit_withdraw"`
	Analytics       AnalyticsGroup       `json:"analytics"`
	Other           OtherGroup           `json:"other"`
}

// GeneralGroup represents general settings group
type GeneralGroup struct {
	EnableDeposit              bool   `json:"enable_deposit"`
	EnableDepositRemark        string `json:"enable_deposit_remark"`
	EnableWithdraw             bool   `json:"enable_withdraw"`
	EnableWithdrawRemark       string `json:"enable_withdraw_remark"`
	EnableGame                 bool   `json:"enable_game"`
	EnableGameRemark           string `json:"enable_game_remark"`
	EnableTruemoney            bool   `json:"enable_truemoney"`
	EnableTruemoneyRemark      string `json:"enable_truemoney_remark"`
	EnableMemberRegister       bool   `json:"enable_member_register"`
	EnableMemberRegisterRemark string `json:"enable_member_register_remark"`
}

// DepositWithdrawGroup represents deposit/withdraw settings group
type DepositWithdrawGroup struct {
	EnableSlipVerify               bool   `json:"enable_slip_verify"`
	EnableSlipVerifyRemark         string `json:"enable_slip_verify_remark"`
	EnablePaymentGatewayOnly       bool   `json:"enable_payment_gateway_only"`
	EnablePaymentGatewayOnlyRemark string `json:"enable_payment_gateway_only_remark"`
	EnableHideDepositChannel       bool   `json:"enable_hide_deposit_channel"`
	EnableHideDepositChannelRemark string `json:"enable_hide_deposit_channel_remark"`
	EnableShowDepositDecimal       bool   `json:"enable_show_deposit_decimal"`
	EnableShowDepositDecimalRemark string `json:"enable_show_deposit_decimal_remark"`
	EnableWithdrawPincode          bool   `json:"enable_withdraw_pincode"`
	EnableWithdrawPincodeRemark    string `json:"enable_withdraw_pincode_remark"`
	PlayerAttemptLimit             int    `json:"player_attempt_limit"`
	TimeoutDeposit                 int    `json:"timeout_deposit"`
	TimeoutWithdraw                int    `json:"timeout_withdraw"`
}

// AnalyticsGroup represents analytics settings group
type AnalyticsGroup struct {
	EnableGoogleAnalytics bool   `json:"enable_gg_analytics"`
	GoogleAnalyticsKey    string `json:"gg_analytics_key"`
	EnableFacebookPixel   bool   `json:"enable_fb_pixel_id"`
	FacebookPixelID       string `json:"fb_pixel_id"`
}

// OtherGroup represents other settings group
type OtherGroup struct {
	EnableSettingUsername       bool   `json:"enable_setting_username"`
	EnableSettingUsernameRemark string `json:"enable_setting_username_remark"`
	EnableSettingGender         bool   `json:"enable_setting_gender"`
	EnableSettingGenderRemark   string `json:"enable_setting_gender_remark"`
	OTPOption                   string `json:"otp_option"`
	EnableOTPBypass             bool   `json:"enable_otp_bypass"`
	EnableOTPBypassRemark       string `json:"enable_otp_bypass_remark"`
	CaptchaConfigPublic         string `json:"captcha_config_public"`
	CaptchaConfigPrivate        string `json:"captcha_config_private"`
	AdminLang                   string `json:"admin_lang"`
	PlayerLang                  string `json:"player_lang"`
}

// UpdateGeneralSettingsRequest represents the request to update general settings
type UpdateGeneralSettingsRequest struct {
	General         *GeneralGroup         `json:"general,omitempty"`
	DepositWithdraw *DepositWithdrawGroup `json:"deposit_withdraw,omitempty"`
	Analytics       *AnalyticsGroup       `json:"analytics,omitempty"`
	Other           *OtherGroup           `json:"other,omitempty"`
}

// SEOSettingsResponse represents the response for SEO settings
type SEOSettingsResponse struct {
	SiteTitle       string `json:"site_title"`
	Title           string `json:"title"`
	MetaDescription string `json:"meta_description"`
	Favicon         string `json:"favicon"`
	FeaturedImage   string `json:"featured_image"`
}

// UpdateSEOSettingsRequest represents the request to update SEO settings
type UpdateSEOSettingsRequest struct {
	SiteTitle       string  `json:"site_title"`
	Title           string  `json:"title"`
	MetaDescription string  `json:"meta_description"`
	Favicon         *string `json:"favicon"`
	FeaturedImage   *string `json:"featured_image"`
}

// SiteImageSettingsResponse represents the response for SiteImage settings
type SiteImageSettingsResponse struct {
	LogoBeforeLogin             string `json:"logo_before_login"`
	LogoAfterLogin              string `json:"logo_after_login"`
	GameLoading                 string `json:"game_loading"`
	LevelIcon                   string `json:"level_icon"`
	NotificationText            string `json:"notification_text"`
	NotificationPopup           string `json:"notification_popup"`
	DepositSuccess              string `json:"deposit_success"`
	WithdrawSuccess             string `json:"withdraw_success"`
	DepositFail                 string `json:"deposit_fail"`
	WithdrawFail                string `json:"withdraw_fail"`
	EnableVideoTutorial         string `json:"enable_video_tutorial"`
	VideoDeposit                string `json:"video_deposit"`
	VideoDepositSmall           string `json:"video_deposit_small"`
	VideoDepositDecimal         string `json:"video_deposit_decimal"`
	VideoDepositDecimalSmall    string `json:"video_deposit_decimal_small"`
	VideoDepositQR              string `json:"video_deposit_qr"`
	VideoDepositQRSmall         string `json:"video_deposit_qr_small"`
	VideoDepositTruewallet      string `json:"video_deposit_truewallet"`
	VideoDepositTruewalletSmall string `json:"video_deposit_truewallet_small"`

	// Group: Network
	NetworkTutorialImage          string `json:"network_tutorial_image"`
	NetworkTutorialText           string `json:"network_tutorial_text"`
	NetworkTutorialMakeMoneyImage string `json:"network_tutorial_make_money_image"`
}

// UpdateSiteImageSettingsRequest represents the request to update SiteImage settings
type UpdateSiteImageSettingsRequest struct {
	LogoBeforeLogin               *string `json:"logo_before_login"`
	LogoAfterLogin                *string `json:"logo_after_login"`
	GameLoading                   *string `json:"game_loading"`
	LevelIcon                     *string `json:"level_icon"`
	NotificationText              *string `json:"notification_text"`
	NotificationPopup             *string `json:"notification_popup"`
	DepositSuccess                *string `json:"deposit_success"`
	WithdrawSuccess               *string `json:"withdraw_success"`
	DepositFail                   *string `json:"deposit_fail"`
	WithdrawFail                  *string `json:"withdraw_fail"`
	EnableVideoTutorial           *string `json:"enable_video_tutorial"`
	VideoDeposit                  *string `json:"video_deposit"`
	VideoDepositSmall             *string `json:"video_deposit_small"`
	VideoDepositDecimal           *string `json:"video_deposit_decimal"`
	VideoDepositDecimalSmall      *string `json:"video_deposit_decimal_small"`
	VideoDepositQR                *string `json:"video_deposit_qr"`
	VideoDepositQRSmall           *string `json:"video_deposit_qr_small"`
	VideoDepositTruewallet        *string `json:"video_deposit_truewallet"`
	VideoDepositTruewalletSmall   *string `json:"video_deposit_truewallet_small"`
	NetworkTutorialImage          *string `json:"network_tutorial_image"`
	NetworkTutorialText           *string `json:"network_tutorial_text"`
	NetworkTutorialMakeMoneyImage *string `json:"network_tutorial_make_money_image"`
}

// UpdateMemberLevelSettingsRequest represents the request to update member level calculation settings
type UpdateMemberLevelSettingsRequest struct {
	UpgradeType   string `json:"upgrade_type" validate:"required,oneof=daily monthly immediate"`   // daily, monthly, immediate
	UpgradeTime   string `json:"upgrade_time"`                                                     // HH:MM format (optional, for display only)
	UpgradeDay    *int   `json:"upgrade_day" validate:"omitempty,min=1,max=31"`                    // 1-31 for monthly type
	DowngradeType string `json:"downgrade_type" validate:"required,oneof=daily monthly immediate"` // daily, monthly, immediate
	DowngradeTime string `json:"downgrade_time"`                                                   // HH:MM format (optional, for display only)
	DowngradeDay  *int   `json:"downgrade_day" validate:"omitempty,min=1,max=31"`                  // 1-31 for monthly type
}

// MemberLevelSettingsResponse represents the response for member level calculation settings
type MemberLevelSettingsResponse struct {
	UpgradeType   string `json:"upgrade_type"`   // daily, monthly, immediate
	UpgradeTime   string `json:"upgrade_time"`   // HH:MM format
	UpgradeDay    *int   `json:"upgrade_day"`    // 1-31 for monthly type
	DowngradeType string `json:"downgrade_type"` // daily, monthly, immediate
	DowngradeTime string `json:"downgrade_time"` // HH:MM format
	DowngradeDay  *int   `json:"downgrade_day"`  // 1-31 for monthly type
}

// NewSystemSetting creates a new system setting
func NewSystemSetting(key, value string, description *string) *SystemSetting {
	now := time.Now()
	return &SystemSetting{
		Key:         key,
		Value:       value,
		Description: description,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// Update updates the system setting value
func (s *SystemSetting) Update(req UpdateSystemSettingRequest) error {
	if err := validateUpdateRequest(req); err != nil {
		return err
	}

	s.Value = req.Value
	s.UpdatedAt = time.Now()
	return nil
}

// ToResponse converts SystemSetting to SystemSettingResponse
func (s *SystemSetting) ToResponse() SystemSettingResponse {
	return SystemSettingResponse{
		ID:          s.ID,
		Key:         s.Key,
		Value:       s.Value,
		Description: s.Description,
		UpdatedAt:   s.UpdatedAt,
		CreatedAt:   s.CreatedAt,
	}
}

// GetIntValue returns the value as integer
func (s *SystemSetting) GetIntValue() (int, error) {
	if s.Value == "" {
		return 0, nil
	}

	// Simple string to int conversion for numeric values
	var intValue int
	switch s.Value {
	case "0":
		intValue = 0
	case "1":
		intValue = 1
	case "2":
		intValue = 2
	case "3":
		intValue = 3
	case "4":
		intValue = 4
	case "5":
		intValue = 5
	case "6":
		intValue = 6
	case "7":
		intValue = 7
	case "8":
		intValue = 8
	case "9":
		intValue = 9
	case "10":
		intValue = 10
	default:
		// For values > 10, we can add more cases or use strconv
		return 0, errors.NewValidationError("invalid numeric value")
	}

	return intValue, nil
}

// validateUpdateRequest validates the update system setting request
func validateUpdateRequest(req UpdateSystemSettingRequest) error {
	// if req.Value == "" {
	// 	return errors.NewValidationError("value is required")
	// }
	return nil
}

// GetDefaultMaxLoginAttempts returns the default max login attempts setting
func GetDefaultMaxLoginAttempts() *SystemSetting {
	description := "Maximum number of failed login attempts allowed. Set to 0 for unlimited attempts."
	return NewSystemSetting(KeyMaxLoginAttempts, "5", &description)
}

// CommissionSettingsResponse represents the response for commission settings
type CommissionSettingsResponse struct {
	AutoApprovalThreshold float64 `json:"auto_approval_threshold"` // อนุมัติการโอนรายได้คอมมิชชั่นเมื่อจำนวนไม่
	MinWithdrawAmount     float64 `json:"min_withdraw_amount"`     // จำนวนถอนขั้นต่ำ
}

// UpdateCommissionSettingsRequest represents the request to update commission settings
type UpdateCommissionSettingsRequest struct {
	AutoApprovalThreshold *float64 `json:"auto_approval_threshold,omitempty" validate:"omitempty,min=0"` // อนุมัติการโอนรายได้คอมมิชชั่นเมื่อจำนวนไม่
	MinWithdrawAmount     *float64 `json:"min_withdraw_amount,omitempty" validate:"omitempty,min=0"`     // จำนวนถอนขั้นต่ำ
}

// GetDefaultCommissionAutoApprovalThreshold returns the default commission auto approval threshold setting
func GetDefaultCommissionAutoApprovalThreshold() *SystemSetting {
	description := "อนุมัติการโอนรายได้คอมมิชชั่นเมื่อจำนวนไม่ (บาท)"
	return NewSystemSetting(KeyCommissionAutoApprovalThreshold, "10000.00", &description)
}

// GetDefaultCommissionMinWithdrawAmount returns the default commission minimum withdraw amount setting
func GetDefaultCommissionMinWithdrawAmount() *SystemSetting {
	description := "จำนวนถอนขั้นต่ำคอมมิชชั่น (บาท)"
	return NewSystemSetting(KeyCommissionMinWithdrawAmount, "100.00", &description)
}

// ReferralSettingsResponse represents the response for referral settings
type ReferralSettingsResponse struct {
	AutoApprovalThreshold float64 `json:"auto_approval_threshold"` // อนุมัติการโอนรายได้จากการแนะนำเมื่อจำนวนไม่
	MinWithdrawAmount     float64 `json:"min_withdraw_amount"`     // จำนวนถอนขั้นต่ำ
}

// UpdateReferralSettingsRequest represents the request to update referral settings
type UpdateReferralSettingsRequest struct {
	AutoApprovalThreshold *float64 `json:"auto_approval_threshold,omitempty" validate:"omitempty,min=0"` // อนุมัติการโอนรายได้จากการแนะนำเมื่อจำนวนไม่
	MinWithdrawAmount     *float64 `json:"min_withdraw_amount,omitempty" validate:"omitempty,min=0"`     // จำนวนถอนขั้นต่ำ
}

// GetDefaultReferralAutoApprovalThreshold returns the default referral auto approval threshold setting
func GetDefaultReferralAutoApprovalThreshold() *SystemSetting {
	description := "อนุมัติการโอนรายได้จากการแนะนำเมื่อจำนวนไม่ (บาท)"
	return NewSystemSetting(KeyReferralAutoApprovalThreshold, "5000.00", &description)
}

// GetDefaultReferralMinWithdrawAmount returns the default referral minimum withdraw amount setting
func GetDefaultReferralMinWithdrawAmount() *SystemSetting {
	description := "จำนวนถอนขั้นต่ำจากการแนะนำ (บาท)"
	return NewSystemSetting(KeyReferralMinWithdrawAmount, "50.00", &description)
}

// FileUploadResponse represents file upload response for system setting
type FileUploadResponse struct {
	FileUrl string `json:"fileUrl"`
}

// DeleteFileRequest represents file deletion request for system setting
type DeleteFileRequest struct {
	FileUrl string `json:"fileUrl" validate:"required"`
}

// GetDefaultMemberPrefix returns the default member prefix setting
func GetDefaultMemberPrefix() *SystemSetting {
	description := "Prefix for member username generation"
	return NewSystemSetting(KeyMemberPrefix, "AG001", &description)
}
