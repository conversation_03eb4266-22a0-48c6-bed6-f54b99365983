package platform

import (
	"blacking-api/pkg/errors"
)

// PlatformType represents the type of platform
type PlatformType string

const (
	PlatformTypeFacebook PlatformType = "Facebook"
	PlatformTypeYoutube  PlatformType = "Youtube"
	PlatformTypeGoogle   PlatformType = "Google"
	PlatformTypeLine     PlatformType = "Line"
	PlatformTypeTiktok   PlatformType = "Tiktok"
	PlatformTypeBanner   PlatformType = "Banner"
	PlatformTypeTwitter  PlatformType = "Twitter"
)

// Platform represents a platform/channel (fixed data)
type Platform struct {
	ID   string       `json:"id"` // Use string ID like "facebook", "youtube"
	Name string       `json:"name"`
	Type PlatformType `json:"type"`
}

// PlatformResponse represents platform API response
type PlatformResponse struct {
	ID   string       `json:"id"`
	Name string       `json:"name"`
	Type PlatformType `json:"type"`
}

// GetFixedPlatforms returns all fixed platforms
func GetFixedPlatforms() []*Platform {
	return []*Platform{
		{ID: "facebook", Name: "Facebook Official", Type: PlatformTypeFacebook},
		{ID: "youtube", Name: "YouTube Channel", Type: PlatformTypeYoutube},
		{ID: "google", Name: "Google Ads", Type: PlatformTypeGoogle},
		{ID: "line", Name: "LINE Official", Type: PlatformTypeLine},
		{ID: "tiktok", Name: "TikTok Official", Type: PlatformTypeTiktok},
		{ID: "banner", Name: "Website Banner", Type: PlatformTypeBanner},
		{ID: "twitter", Name: "Twitter Official", Type: PlatformTypeTwitter},
	}
}

// GetPlatformByID returns platform by ID
func GetPlatformByID(id string) (*Platform, error) {
	platforms := GetFixedPlatforms()
	for _, platform := range platforms {
		if platform.ID == id {
			return platform, nil
		}
	}
	return nil, errors.NewNotFoundError("platform not found")
}

// ToResponse converts platform to response format
func (p *Platform) ToResponse() PlatformResponse {
	return PlatformResponse{
		ID:   p.ID,
		Name: p.Name,
		Type: p.Type,
	}
}

// ValidatePlatformType validates platform type
func ValidatePlatformType(platformType PlatformType) error {
	validTypes := []PlatformType{
		PlatformTypeFacebook,
		PlatformTypeYoutube,
		PlatformTypeGoogle,
		PlatformTypeLine,
		PlatformTypeTiktok,
		PlatformTypeBanner,
		PlatformTypeTwitter,
	}

	for _, validType := range validTypes {
		if platformType == validType {
			return nil
		}
	}

	return errors.NewValidationError("invalid platform type")
}

// GetPlatformTypes returns all available platform types
func GetPlatformTypes() []PlatformType {
	return []PlatformType{
		PlatformTypeFacebook,
		PlatformTypeYoutube,
		PlatformTypeGoogle,
		PlatformTypeLine,
		PlatformTypeTiktok,
		PlatformTypeBanner,
		PlatformTypeTwitter,
	}
}

// IsValidPlatformID validates if platform ID exists
func IsValidPlatformID(id string) bool {
	_, err := GetPlatformByID(id)
	return err == nil
}
