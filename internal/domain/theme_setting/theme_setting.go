package theme_setting

import (
	"time"
)

// ThemeSetting represents a theme configuration setting
type ThemeSetting struct {
	ID         int       `json:"id" db:"id"`
	ThemeValue string    `json:"theme_value" db:"theme_value"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
	UpdatedBy  int       `json:"updated_by" db:"updated_by"`
}

// SaveThemeRequest represents the request to save theme setting
type SaveThemeRequest struct {
	ThemeValue string `json:"theme_value" validate:"required"`
}

// ThemeResponse represents the response structure for theme setting
type ThemeResponse struct {
	ID         int       `json:"id"`
	ThemeValue string    `json:"theme_value"`
	UpdatedAt  time.Time `json:"updated_at"`
	UpdatedBy  int       `json:"updated_by"`
}

// NewThemeSetting creates a new theme setting
func NewThemeSetting(themeValue string, updatedBy int) *ThemeSetting {
	now := time.Now()
	return &ThemeSetting{
		ThemeValue: themeValue,
		UpdatedBy:  updatedBy,
		UpdatedAt:  now,
	}
}

// Update updates the theme setting
func (t *ThemeSetting) Update(req SaveThemeRequest, updatedBy int) {
	t.ThemeValue = req.ThemeValue
	t.UpdatedBy = updatedBy
	t.UpdatedAt = time.Now()
}

// ToResponse converts ThemeSetting to ThemeResponse
func (t *ThemeSetting) ToResponse() ThemeResponse {
	return ThemeResponse{
		ID:         t.ID,
		ThemeValue: t.ThemeValue,
		UpdatedAt:  t.UpdatedAt,
		UpdatedBy:  t.UpdatedBy,
	}
}
