package login_attempt

import (
	"strings"
	"time"
)

// LoginAttempt represents a login attempt record
type LoginAttempt struct {
	ID             int       `json:"id" db:"id"`
	Username       string    `json:"username" db:"username"`
	IPAddress      string    `json:"ip_address" db:"ip_address"`
	Success        bool      `json:"success" db:"success"`
	UserAgent      string    `json:"user_agent" db:"user_agent"`
	DeviceType     *string   `json:"device_type" db:"device_type"`
	Browser        *string   `json:"browser" db:"browser"`
	BrowserVersion *string   `json:"browser_version" db:"browser_version"`
	OS             *string   `json:"os" db:"os"`
	OSVersion      *string   `json:"os_version" db:"os_version"`
	Platform       *string   `json:"platform" db:"platform"`
	IsMobile       bool      `json:"is_mobile" db:"is_mobile"`
	IsTablet       bool      `json:"is_tablet" db:"is_tablet"`
	IsDesktop      bool      `json:"is_desktop" db:"is_desktop"`
	IsA<PERSON><PERSON>        bool      `json:"is_admin" db:"is_admin"`
	IsM<PERSON>ber       bool      `json:"is_member" db:"is_member"`
	IsCleared      bool      `json:"is_cleared" db:"is_cleared"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
}

// LoginAttemptSummary represents login attempt statistics for a user
type LoginAttemptSummary struct {
	Username           string     `json:"username"`
	FailedAttempts     int        `json:"failed_attempts"`
	LastFailedAttempt  *time.Time `json:"last_failed_attempt,omitempty"`
	IsLocked           bool       `json:"is_locked"`
	MaxAttemptsAllowed int        `json:"max_attempts_allowed"`
}

// NewLoginAttempt creates a new login attempt record with device information
func NewLoginAttempt(username, ipAddress, userAgent string, success, isAdmin bool, isMember bool) *LoginAttempt {
	// Parse device information from user agent
	deviceInfo := parseUserAgent(userAgent)

	return &LoginAttempt{
		Username:       username,
		IPAddress:      ipAddress,
		UserAgent:      userAgent,
		DeviceType:     deviceInfo.DeviceType,
		Browser:        deviceInfo.Browser,
		BrowserVersion: deviceInfo.BrowserVersion,
		OS:             deviceInfo.OS,
		OSVersion:      deviceInfo.OSVersion,
		Platform:       deviceInfo.Platform,
		IsMobile:       deviceInfo.IsMobile,
		IsTablet:       deviceInfo.IsTablet,
		IsDesktop:      deviceInfo.IsDesktop,
		Success:        success,
		IsAdmin:        isAdmin,
		IsMember:       isMember,
		IsCleared:      false,
		CreatedAt:      time.Now(),
	}
}

// deviceInfo represents parsed device information
type deviceInfo struct {
	DeviceType     *string
	Browser        *string
	BrowserVersion *string
	OS             *string
	OSVersion      *string
	Platform       *string
	IsMobile       bool
	IsTablet       bool
	IsDesktop      bool
}

// parseUserAgent parses user agent string and extracts device information
func parseUserAgent(userAgent string) *deviceInfo {
	if userAgent == "" {
		return &deviceInfo{}
	}

	info := &deviceInfo{}
	ua := strings.ToLower(userAgent)

	// Detect device type
	if strings.Contains(ua, "mobile") || strings.Contains(ua, "android") ||
		strings.Contains(ua, "iphone") || strings.Contains(ua, "ipod") {
		info.IsMobile = true
		info.DeviceType = stringPtr("mobile")
		info.Platform = stringPtr("mobile")
	} else if strings.Contains(ua, "ipad") || strings.Contains(ua, "tablet") {
		info.IsTablet = true
		info.DeviceType = stringPtr("tablet")
		info.Platform = stringPtr("tablet")
	} else {
		info.IsDesktop = true
		info.DeviceType = stringPtr("desktop")
		info.Platform = stringPtr("desktop")
	}

	// Detect browser
	if strings.Contains(ua, "chrome") {
		info.Browser = stringPtr("Chrome")
	} else if strings.Contains(ua, "firefox") {
		info.Browser = stringPtr("Firefox")
	} else if strings.Contains(ua, "safari") && !strings.Contains(ua, "chrome") {
		info.Browser = stringPtr("Safari")
	} else if strings.Contains(ua, "edge") {
		info.Browser = stringPtr("Edge")
	} else if strings.Contains(ua, "opera") {
		info.Browser = stringPtr("Opera")
	}

	// Detect OS
	if strings.Contains(ua, "windows") {
		info.OS = stringPtr("Windows")
	} else if strings.Contains(ua, "mac") {
		info.OS = stringPtr("macOS")
	} else if strings.Contains(ua, "linux") {
		info.OS = stringPtr("Linux")
	} else if strings.Contains(ua, "android") {
		info.OS = stringPtr("Android")
	} else if strings.Contains(ua, "iphone") || strings.Contains(ua, "ipad") {
		info.OS = stringPtr("iOS")
	}

	return info
}

// stringPtr returns a pointer to string
func stringPtr(s string) *string {
	return &s
}
