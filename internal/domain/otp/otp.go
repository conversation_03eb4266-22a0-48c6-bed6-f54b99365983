package otp

import (
	"crypto/rand"
	"fmt"
	"time"

	"blacking-api/pkg/errors"
)

// OTPOption represents an OTP option for dropdown
type OTPOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// OTPOptionsResponse represents the response for OTP options
type OTPOptionsResponse struct {
	Options []OTPOption `json:"options"`
}

// OTPType represents the type of OTP
type OTPType string

const (
	OTPTypePhone OTPType = "phone"
	OTPTypeEmail OTPType = "email"
)

// OTPPurpose represents the purpose of OTP
type OTPPurpose string

const (
	OTPPurposeMemberRegistration OTPPurpose = "member_registration"
	OTPPurposePasswordReset      OTPPurpose = "password_reset"
	OTPPurpose2FA                OTPPurpose = "2fa"
)

// OTP represents an OTP record
type OTP struct {
	ID        int        `json:"id" db:"id"`
	Type      OTPType    `json:"type" db:"type"`
	Purpose   OTPPurpose `json:"purpose" db:"purpose"`
	Recipient string     `json:"recipient" db:"recipient"` // phone number or email
	Code      string     `json:"code" db:"code"`
	Reference string     `json:"reference" db:"reference"` // 6-char reference for verification
	IsUsed    bool       `json:"is_used" db:"is_used"`
	ExpiresAt time.Time  `json:"expires_at" db:"expires_at"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	UsedAt    *time.Time `json:"used_at" db:"used_at"`
}

// SendOTPRequest represents request to send OTP
type SendOTPRequest struct {
	Type      OTPType    `json:"type" validate:"required,oneof=phone email"`
	Purpose   OTPPurpose `json:"purpose" validate:"required"`
	Recipient string     `json:"recipient" validate:"required"`
}

// VerifyOTPRequest represents request to verify OTP
type VerifyOTPRequest struct {
	Type      OTPType    `json:"type" validate:"required,oneof=phone email"`
	Purpose   OTPPurpose `json:"purpose" validate:"required"`
	Recipient string     `json:"recipient" validate:"required"`
	Code      string     `json:"code" validate:"required,len=6"`
}

type GetLastVerifyOTPRequest struct {
	Type      OTPType    `json:"type" validate:"required,oneof=phone email"`
	Purpose   OTPPurpose `json:"purpose" validate:"required"`
	Recipient string     `json:"recipient" validate:"required"`
}

type GetInUsedOTPRequest struct {
	Type      OTPType    `json:"type" validate:"required,oneof=phone email"`
	Purpose   OTPPurpose `json:"purpose" validate:"required"`
	Recipient string     `json:"recipient" validate:"required"`
}

// OTPResponse represents OTP API response
type OTPResponse struct {
	ID        int        `json:"id"`
	Type      OTPType    `json:"type"`
	Purpose   OTPPurpose `json:"purpose"`
	Recipient string     `json:"recipient"`
	Reference string     `json:"reference"`
	Message   string     `json:"message"`
	ExpiresAt time.Time  `json:"expires_at"`
	CreatedAt time.Time  `json:"created_at"`
}

// AdminOTPListRequest represents request to list OTPs for admin
type AdminOTPListRequest struct {
	Limit        int      `json:"limit" validate:"min=1,max=100"`
	Offset       int      `json:"offset" validate:"min=0"`
	Search       string   `json:"search"`         // Search by phone number
	Username     string   `json:"username"`       // Filter by username
	UserID       string   `json:"user_id"`        // Filter by user ID
	ReferUserIDs []string `json:"refer_user_ids"` // Filter by refer user IDs (array)
	Purpose      string   `json:"purpose"`        // Filter by OTP purpose
	DateFrom     string   `json:"date_from"`      // Filter from date (YYYY-MM-DD)
	DateTo       string   `json:"date_to"`        // Filter to date (YYYY-MM-DD)
}

// AdminOTPResponse represents OTP response for admin with member info
type AdminOTPResponse struct {
	ID        int        `json:"id"`
	Type      OTPType    `json:"type"`
	Purpose   OTPPurpose `json:"purpose"`
	Recipient string     `json:"recipient"` // Full phone number for admin
	Code      string     `json:"code"`      // OTP code for admin/support use
	Reference string     `json:"reference"`
	IsUsed    bool       `json:"is_used"`
	ExpiresAt time.Time  `json:"expires_at"`
	CreatedAt time.Time  `json:"created_at"`
	UsedAt    *time.Time `json:"used_at"`

	// Member information (if found and verified)
	MemberID          *int       `json:"member_id"`
	Username          *string    `json:"username"`
	FirstName         *string    `json:"first_name"`
	LastName          *string    `json:"last_name"`
	Gender            *string    `json:"gender"`
	ReferCode         *string    `json:"refer_code"`
	RegisterReferCode *string    `json:"register_refer_code"`
	IsEnable          *bool      `json:"is_enable"`
	RegisterStatus    *bool      `json:"register_status"`
	CreatedMemberAt   *time.Time `json:"created_member_at"`
}

// NewOTP creates a new OTP
func NewOTP(otpType OTPType, purpose OTPPurpose, recipient string) (*OTP, error) {
	if err := validateOTPRequest(otpType, purpose, recipient); err != nil {
		return nil, err
	}

	code, err := generateOTPCode()
	if err != nil {
		return nil, errors.NewInternalError("failed to generate OTP code")
	}

	reference, err := generateReference()
	if err != nil {
		return nil, errors.NewInternalError("failed to generate reference")
	}

	now := time.Now()
	expiresAt := now.Add(5 * time.Minute) // OTP expires in 5 minutes

	otp := &OTP{
		Type:      otpType,
		Purpose:   purpose,
		Recipient: recipient,
		Code:      code,
		Reference: reference,
		IsUsed:    false,
		ExpiresAt: expiresAt,
		CreatedAt: now,
	}

	return otp, nil
}

// IsExpired checks if OTP is expired
func (o *OTP) IsExpired() bool {
	return time.Now().UTC().After(o.ExpiresAt.UTC())
}

// IsValid checks if OTP is valid for verification
func (o *OTP) IsValid() bool {
	return !o.IsUsed && !o.IsExpired()
}

// MarkAsUsed marks OTP as used
func (o *OTP) MarkAsUsed() {
	o.IsUsed = true
	now := time.Now().UTC()
	o.UsedAt = &now
}

// Verify verifies the OTP code
func (o *OTP) Verify(code string) error {
	if !o.IsValid() {
		if o.IsUsed {
			return errors.NewValidationError("OTP has already been used")
		}
		if o.IsExpired() {
			return errors.NewValidationError("OTP has expired")
		}
	}

	if o.Code != code {
		return errors.NewValidationError("invalid OTP code")
	}

	o.MarkAsUsed()
	return nil
}

// ToResponse converts OTP to response format
func (o *OTP) ToResponse() OTPResponse {
	return OTPResponse{
		ID:        o.ID,
		Type:      o.Type,
		Purpose:   o.Purpose,
		Recipient: maskRecipient(o.Recipient, o.Type),
		Reference: o.Reference,
		ExpiresAt: o.ExpiresAt,
		CreatedAt: o.CreatedAt,
	}
}

// generateOTPCode generates a 6-digit OTP code
func generateOTPCode() (string, error) {
	bytes := make([]byte, 3)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}

	// Convert to 6-digit number
	code := int(bytes[0])<<16 | int(bytes[1])<<8 | int(bytes[2])
	code = code % 1000000 // Ensure 6 digits max

	return fmt.Sprintf("%06d", code), nil
}

// generateReference generates a 6-character reference code
func generateReference() (string, error) {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	bytes := make([]byte, 6)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}

	for i := range bytes {
		bytes[i] = charset[bytes[i]%byte(len(charset))]
	}

	return string(bytes), nil
}

// validateOTPRequest validates OTP request parameters
func validateOTPRequest(otpType OTPType, purpose OTPPurpose, recipient string) error {
	if otpType != OTPTypePhone && otpType != OTPTypeEmail {
		return errors.NewValidationError("invalid OTP type")
	}

	if purpose != OTPPurposeMemberRegistration && purpose != OTPPurposePasswordReset && purpose != OTPPurpose2FA {
		return errors.NewValidationError("invalid OTP purpose")
	}

	if recipient == "" {
		return errors.NewValidationError("recipient is required")
	}

	// Basic validation for phone/email format
	if otpType == OTPTypePhone {
		if len(recipient) < 10 {
			return errors.NewValidationError("invalid phone number format")
		}
	} else if otpType == OTPTypeEmail {
		if len(recipient) < 5 || !contains(recipient, "@") {
			return errors.NewValidationError("invalid email format")
		}
	}

	return nil
}

// maskRecipient masks sensitive parts of recipient for response
func maskRecipient(recipient string, otpType OTPType) string {
	if otpType == OTPTypePhone {
		if len(recipient) >= 4 {
			return recipient[:3] + "****" + recipient[len(recipient)-2:]
		}
		return "***"
	} else if otpType == OTPTypeEmail {
		if atIndex := indexOf(recipient, "@"); atIndex > 0 {
			username := recipient[:atIndex]
			domain := recipient[atIndex:]
			if len(username) >= 2 {
				return username[:2] + "****" + domain
			}
			return "****" + domain
		}
		return "****"
	}
	return "****"
}

// Helper functions
func contains(s, substr string) bool {
	return indexOf(s, substr) >= 0
}

func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}
