package user_transaction_type

import "time"

// UserTransactionType represents a user transaction type
type UserTransactionType struct {
	ID        int       `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	Detail    *string   `json:"detail" db:"detail"`
	CreatedAt time.Time `json:"createdAt" db:"created_at"`
}

// UserTransactionTypeResponse represents the API response for user transaction type
type UserTransactionTypeResponse struct {
	ID        int       `json:"id"`
	Name      string    `json:"name"`
	Detail    *string   `json:"detail"`
	CreatedAt time.Time `json:"createdAt"`
}

// ToResponse converts UserTransactionType to UserTransactionTypeResponse
func (u *UserTransactionType) ToResponse() UserTransactionTypeResponse {
	return UserTransactionTypeResponse{
		ID:        u.ID,
		Name:      u.Name,
		Detail:    u.Detail,
		CreatedAt: u.CreatedAt,
	}
}
