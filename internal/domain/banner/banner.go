package banner

import (
	"blacking-api/pkg/errors"
	"time"
)

type Banner struct {
	ID                int        `json:"id"`
	Position          *int       `json:"position"`
	Name              *string    `json:"name"`
	Type              Type       `json:"type"`
	LinkURL           *string    `json:"link_url"`
	ImageURL          *string    `json:"image_url"`
	Status            Status     `json:"status"`
	DeletedByUsername *string    `json:"deleted_by_user_id"`
	DeletedAt         *time.Time `json:"deleted_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	CreatedAt         time.Time  `json:"created_at"`
}

type Type string
type Status string

const (
	TypeImage Type = "image"
	TypeLink  Type = "link"
)

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

type CreateBannerRequest struct {
	Name     string  `json:"name"`
	Type     Type    `json:"type"`
	LinkURL  *string `json:"link_url"`
	ImageURL *string `json:"image_url"`
}

type UpdateBannerRequest struct {
	Name     string  `json:"name"`
	Type     Type    `json:"type"`
	LinkURL  *string `json:"link_url"`
	ImageURL *string `json:"image_url"`
}

type ReorderRequest struct {
	BannerIDs []int `json:"banner_ids" validate:"required,min=1"`
}

type BannerResponse struct {
	ID        int       `json:"id"`
	Position  *int      `json:"position"`
	Name      *string   `json:"name"`
	Type      Type      `json:"type"`
	LinkURL   *string   `json:"link_url"`
	ImageURL  *string   `json:"image_url"`
	Status    Status    `json:"status"`
	UpdatedAt time.Time `json:"updated_at"`
	CreatedAt time.Time `json:"created_at"`
}

func NewBanner(req CreateBannerRequest) (*Banner, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	banner := &Banner{
		Name:      &req.Name,
		Type:      req.Type,
		Status:    StatusActive,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// Set URL based on type
	if req.Type == TypeLink {
		banner.ImageURL = req.ImageURL
		banner.LinkURL = req.LinkURL
	} else if req.Type == TypeImage {
		banner.ImageURL = req.ImageURL
		banner.LinkURL = nil
	}

	return banner, nil
}

func (b *Banner) Update(req UpdateBannerRequest) error {
	if err := validateUpdateRequest(req); err != nil {
		return err
	}

	b.Name = &req.Name

	// Only update ImageURL if it's not empty
	if req.ImageURL != nil && *req.ImageURL != "" {
		b.ImageURL = req.ImageURL
	}

	// Handle type change
	if req.Type != b.Type {
		b.Type = req.Type

		// Clear URLs when changing type
		if req.Type == TypeLink {
			b.LinkURL = req.LinkURL
		} else if req.Type == TypeImage {
			b.LinkURL = nil
		}
	} else {
		// Update LinkURL only if type is Link and not changing type
		if req.Type == TypeLink {
			b.LinkURL = req.LinkURL
		}
	}

	b.UpdatedAt = time.Now()
	return nil
}

func (b *Banner) IsActive() bool {
	return b.Status == StatusActive
}

func (b *Banner) Deactivate(username string) {
	b.Status = StatusInactive
	b.DeletedByUsername = &username
	now := time.Now()
	b.DeletedAt = &now
	b.UpdatedAt = now
}

func (b *Banner) Activate() {
	b.Status = StatusActive
	b.DeletedByUsername = nil
	b.DeletedAt = nil
	b.UpdatedAt = time.Now()
}

func (b *Banner) ToResponse() BannerResponse {
	return BannerResponse{
		ID:        b.ID,
		Position:  b.Position,
		Name:      b.Name,
		Type:      b.Type,
		LinkURL:   b.LinkURL,
		ImageURL:  b.ImageURL,
		Status:    b.Status,
		CreatedAt: b.CreatedAt,
		UpdatedAt: b.UpdatedAt,
	}
}

func validateCreateRequest(req CreateBannerRequest) error {
	if req.Type == "" {
		return errors.NewValidationError("type is required")
	}

	if req.Type != TypeImage && req.Type != TypeLink {
		return errors.NewValidationError("type must be 'image' or 'link'")
	}

	if req.Type == TypeLink && (req.LinkURL == nil || *req.LinkURL == "") {
		return errors.NewValidationError("link_url is required when type is 'link'")
	}

	// if req.Type == TypeImage && (req.ImageURL == nil || *req.ImageURL == "") {
	// 	return errors.NewValidationError("image_url is required when type is 'image'")
	// }

	return nil
}

func validateUpdateRequest(req UpdateBannerRequest) error {
	if req.Type != TypeImage && req.Type != TypeLink {
		return errors.NewValidationError("type must be 'image' or 'link'")
	}

	if req.Type == TypeLink && (req.LinkURL == nil || *req.LinkURL == "") {
		return errors.NewValidationError("link_url is required when type is 'link'")
	}

	// if req.Type == TypeImage && (req.ImageURL == nil || *req.ImageURL == "") {
	// 	return errors.NewValidationError("image_url is required when type is 'image'")
	// }

	return nil
}

// FileUploadResponse represents file upload response for banner
type FileUploadResponse struct {
	FileUrl string `json:"fileUrl"`
}

// DeleteFileRequest represents file deletion request for banner
type DeleteFileRequest struct {
	FileUrl string `json:"fileUrl" validate:"required"`
}
