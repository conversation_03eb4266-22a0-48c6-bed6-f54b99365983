package withdraw_account

import (
	"blacking-api/internal/domain/request"
	"time"
)

type WithdrawAccount struct {
	ID                                 int64     `json:"id" db:"id"`
	BankingID                          int64     `json:"banking_id" db:"banking_id"`
	BankingName                        string    `json:"banking_name" db:"banking_name"`
	BankingImageUrl                    string    `json:"banking_image_url" db:"banking_image_url"`
	AlgorithmID                        *int64    `json:"algorithm_id" db:"algorithm_id"`
	AlgorithmName                      *string   `json:"algorithm_name" db:"algorithm_name"`
	AutoBotID                          int64     `json:"auto_bot_id" db:"auto_bot_id"`
	AccountName                        string    `json:"account_name" db:"account_name"`
	AccountNameDisplay                 string    `json:"account_name_display" db:"account_name_display"`
	AccountNumber                      string    `json:"account_number" db:"account_number"`
	PhoneNumber                        string    `json:"phone_number" db:"phone_number"`
	MinimumWithdraw                    float64   `json:"minimum_withdraw" db:"min_withdraw"`
	MaximumWithdraw                    float64   `json:"maximum_withdraw" db:"max_withdraw"`
	WithdrawSplitting                  bool      `json:"withdraw_splitting" db:"withdraw_splitting"`
	MaximumWithdrawPerTransaction      float64   `json:"maximum_withdraw_per_transaction" db:"max_withdraw_per_transaction"`
	MaximumSplitWithdrawPerTransaction float64   `json:"maximum_split_withdraw_per_transaction" db:"max_split_withdraw_per_transaction"`
	LimitAutoTransfer                  float64   `json:"limit_auto_transfer" db:"limit_auto_transfer"`
	PushBulletNickname                 *string   `json:"push_bullet_nickname" db:"push_bullet_nickname"`
	PushBulletToken                    *string   `json:"push_bullet_token" db:"push_bullet_token"`
	IdentificationNumber               *string   `json:"identification_number" db:"identification_number"`
	LaserId                            *string   `json:"laser_id" db:"laser_id"`
	Pin                                *string   `json:"pin" db:"pin"`
	EncryptionKey                      *string   `json:"encryption_key" db:"encryption_key"`
	DeviceId                           *string   `json:"device_id" db:"device_id"`
	FileJsonUrl                        *string   `json:"file_json_url" db:"file_json_url"`
	BirthDay                           *string   `json:"birthday" db:"birthday"`
	Active                             bool      `json:"active" db:"active"`
	Inactive                           bool      `json:"inactive" db:"inactive"`
	CreatedAt                          time.Time `json:"created_at" db:"created_at"`
	UpdatedAt                          time.Time `json:"updated_at" db:"updated_at"`
}

type WithdrawAccountRequest struct {
	AccountName                        string  `json:"accountName" validate:"required,min=1,max=255"`
	AccountNameDisplay                 string  `json:"accountNameDisplay" validate:"required,min=1,max=255"`
	AccountNumber                      string  `json:"accountNumber" validate:"required,min=13,max=13"`
	BankingID                          uint    `json:"bankingId" validate:"required,min=1"`
	PhoneNumber                        string  `json:"phoneNumber" validate:"required,min=10,max=10"`
	MinimumWithdraw                    float64 `json:"minimumWithdraw" validate:"required,min=0"`
	MaximumWithdraw                    float64 `json:"maximumWithdraw" validate:"required,min=0"`
	WithdrawSplitting                  bool    `json:"withdrawSplitting" validate:"required"`
	MaximumWithdrawPerTransaction      float64 `json:"maximumWithdrawPerTransaction" validate:"required,min=0"`
	MaximumSplitWithdrawPerTransaction float64 `json:"maximumSplitWithdrawPerTransaction" validate:"required,min=0"`
	LimitAutoTransfer                  float64 `json:"limitAutoTransfer" validate:"required,min=0"`
}

type WithdrawAccountSettingAlgorithmRequest struct {
	AlgorithmID          uint   `json:"algorithmId" validate:"required,min=1"`
	IdentificationNumber string `json:"identificationNumber" validate:"required,min=13,max=13"`
	LaserId              string `json:"laserId" validate:"omitempty,min=1,max=12"`
	BirthDay             string `json:"birthday" validate:"required,min=10,max=10"`
	Pin                  string `json:"pin" validate:"required,min=4,max=6"`
	EncryptionKey        string `json:"encryptionKey" validate:"required,min=1,max=255"`
	DeviceId             string `json:"deviceId" validate:"required,min=1,max=255"`
	FileJsonUrl          string `json:"fileJsonUrl" validate:"required,url"`
	PushBulletToken      string `json:"pushBulletToken" validate:"required,min=1,max=255"`
	PushBulletNickname   string `json:"pushBulletNickname" validate:"required,min=1,max=255"`
}

type WithdrawAccountSearchRequest struct {
	request.RequestPagination
	AccountName *string `form:"accountName"`
	BankingID   *int64  `form:"banking"`
	AlgorithmID *int64  `form:"algorithm"`
	AutoBotID   *int64  `form:"bot"`
}

type DeleteFileRequest struct {
	FileUrl string `json:"fileUrl" validate:"required,url"`
}

type WithdrawAccountListResponse struct {
	ID                 int64     `json:"id" db:"id"`
	BankingID          int64     `json:"bankingId" db:"banking_id"`
	BankingName        string    `json:"bankingName" db:"banking_name"`
	BankingImageUrl    string    `json:"bankingImageUrl" db:"banking_image_url"`
	AlgorithmID        *int64    `json:"algorithmId" db:"algorithm_id"`
	AlgorithmName      *string   `json:"algorithmName" db:"algorithm_name"`
	AutoBotID          int64     `json:"autoBotId" db:"auto_bot_id"`
	AccountName        string    `json:"accountName" db:"account_name"`
	AccountNameDisplay string    `json:"accountNameDisplay" db:"account_name_display"`
	AccountNumber      string    `json:"accountNumber" db:"account_number"`
	CreatedAt          time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt          time.Time `json:"updatedAt" db:"updated_at"`
}

type WithdrawAccountByIdResponse struct {
	ID                                 int64   `json:"id" db:"id"`
	AccountName                        string  `json:"accountName" db:"account_name"`
	AccountNameDisplay                 string  `json:"accountName_display" db:"account_name_display"`
	AccountNumber                      string  `json:"accountNumber" db:"account_number"`
	BankingID                          int64   `json:"bankingId" db:"banking_id"`
	PhoneNumber                        string  `json:"phoneNumber" db:"phone_number"`
	MinimumWithdraw                    float64 `json:"minimumWithdraw" db:"min_withdraw"`
	MaximumWithdraw                    float64 `json:"maximumWithdraw" db:"max_withdraw"`
	WithdrawSplitting                  bool    `json:"withdrawSplitting" db:"withdraw_splitting"`
	MaximumWithdrawPerTransaction      float64 `json:"maximumWithdrawPerTransaction" db:"max_withdraw_per_transaction"`
	MaximumSplitWithdrawPerTransaction float64 `json:"maximumSplitWithdrawPerTransaction" db:"max_split_withdraw_per_transaction"`
	LimitAutoTransfer                  float64 `json:"limitAutoTransfer" db:"limit_auto_transfer"`
}

type WithdrawAccountSettingAlgorithmResponse struct {
	ID                   int64   `json:"id" db:"id"`
	AlgorithmID          *int64  `json:"algorithmId" db:"algorithm_id"`
	IdentificationNumber *string `json:"identificationNumber" db:"identification_number"`
	LaserId              *string `json:"laserId" db:"laser_id"`
	BirthDay             *string `json:"birthday" db:"birthday"`
	Pin                  *string `json:"pin" db:"pin"`
	EncryptionKey        *string `json:"encryptionKey" db:"encryption_key"`
	DeviceId             *string `json:"deviceId" db:"device_id"`
	FileJsonUrl          *string `json:"fileJsonUrl" db:"file_json_url"`
	PushBulletToken      *string `json:"pushBulletToken" db:"push_bullet_token"`
	PushBulletNickname   *string `json:"pushBulletNickname" db:"push_bullet_nickname"`
}

type FileUploadResponse struct {
	FileUrl string `json:"fileUrl" db:"file_url"`
}

func (w *WithdrawAccount) ToListResponse() *WithdrawAccountListResponse {
	return &WithdrawAccountListResponse{
		ID:                 w.ID,
		BankingID:          w.BankingID,
		BankingName:        w.BankingName,
		BankingImageUrl:    w.BankingImageUrl,
		AlgorithmID:        w.AlgorithmID,
		AlgorithmName:      w.AlgorithmName,
		AutoBotID:          w.AutoBotID,
		AccountName:        w.AccountName,
		AccountNameDisplay: w.AccountNameDisplay,
		AccountNumber:      w.AccountNumber,
		CreatedAt:          w.CreatedAt,
		UpdatedAt:          w.UpdatedAt,
	}
}

func (w *WithdrawAccount) ToByIdResponse() *WithdrawAccountByIdResponse {
	return &WithdrawAccountByIdResponse{
		ID:                                 w.ID,
		AccountName:                        w.AccountName,
		AccountNameDisplay:                 w.AccountNameDisplay,
		AccountNumber:                      w.AccountNumber,
		BankingID:                          w.BankingID,
		PhoneNumber:                        w.PhoneNumber,
		MinimumWithdraw:                    w.MinimumWithdraw,
		MaximumWithdraw:                    w.MaximumWithdraw,
		WithdrawSplitting:                  w.WithdrawSplitting,
		MaximumWithdrawPerTransaction:      w.MaximumWithdrawPerTransaction,
		MaximumSplitWithdrawPerTransaction: w.MaximumSplitWithdrawPerTransaction,
		LimitAutoTransfer:                  w.LimitAutoTransfer,
	}
}

func (w *WithdrawAccount) ToSettingAlgorithmResponse() *WithdrawAccountSettingAlgorithmResponse {
	return &WithdrawAccountSettingAlgorithmResponse{
		ID:                   w.ID,
		AlgorithmID:          w.AlgorithmID,
		IdentificationNumber: w.IdentificationNumber,
		LaserId:              w.LaserId,
		BirthDay:             w.BirthDay,
		Pin:                  w.Pin,
		EncryptionKey:        w.EncryptionKey,
		DeviceId:             w.DeviceId,
		FileJsonUrl:          w.FileJsonUrl,
		PushBulletToken:      w.PushBulletToken,
		PushBulletNickname:   w.PushBulletNickname,
	}
}
