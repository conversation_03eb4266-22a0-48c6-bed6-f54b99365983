package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewUser(t *testing.T) {
	tests := []struct {
		name    string
		req     CreateUserRequest
		wantErr bool
	}{
		{
			name: "valid user creation",
			req: CreateUserRequest{
				Username:  "<EMAIL>",
				Password:  "password123",
				FirstName: "<PERSON>",
				LastName:  "Doe",
			},
			wantErr: false,
		},
		{
			name: "empty Username",
			req: CreateUserRequest{
				Username:  "",
				Password:  "password123",
				FirstName: "<PERSON>",
				LastName:  "Doe",
			},
			wantErr: true,
		},
		{
			name: "short password",
			req: CreateUserRequest{
				Username:  "<EMAIL>",
				Password:  "short",
				FirstName: "John",
				LastName:  "Doe",
			},
			wantErr: true,
		},
		{
			name: "empty first name",
			req: CreateUserRequest{
				Username:  "<EMAIL>",
				Password:  "password123",
				FirstName: "",
				LastName:  "Doe",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			user, err := NewUser(tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, user)
			} else {
				require.NoError(t, err)
				require.NotNil(t, user)

				assert.NotEmpty(t, user.ID)
				assert.Equal(t, tt.req.Username, user.Username)
				assert.Equal(t, tt.req.FirstName, user.FirstName)
				assert.Equal(t, tt.req.LastName, user.LastName)
				assert.Equal(t, StatusActive, user.Status)
				assert.NotEmpty(t, user.Password)
				assert.NotEqual(t, tt.req.Password, user.Password) // Password should be hashed
				assert.False(t, user.CreatedAt.IsZero())
				assert.False(t, user.UpdatedAt.IsZero())
			}
		})
	}
}

func TestUser_CheckPassword(t *testing.T) {
	req := CreateUserRequest{
		Username:  "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
	}

	user, err := NewUser(req)
	require.NoError(t, err)

	// Test correct password
	assert.True(t, user.CheckPassword("password123"))

	// Test incorrect password
	assert.False(t, user.CheckPassword("wrongpassword"))
}

func TestUser_SetPassword(t *testing.T) {
	req := CreateUserRequest{
		Username:  "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
	}

	user, err := NewUser(req)
	require.NoError(t, err)

	originalPassword := user.Password

	// Test setting new password
	err = user.SetPassword("newpassword123")
	require.NoError(t, err)

	assert.NotEqual(t, originalPassword, user.Password)
	assert.True(t, user.CheckPassword("newpassword123"))
	assert.False(t, user.CheckPassword("password123"))

	// Test setting short password
	err = user.SetPassword("short")
	assert.Error(t, err)
}

func TestUser_Update(t *testing.T) {
	req := CreateUserRequest{
		Username:  "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
	}

	user, err := NewUser(req)
	require.NoError(t, err)

	originalUpdatedAt := user.UpdatedAt

	// Test updating first name
	newFirstName := "Jane"
	updateReq := UpdateUserRequest{
		FirstName: &newFirstName,
	}

	err = user.Update(updateReq)
	require.NoError(t, err)

	assert.Equal(t, "Jane", user.FirstName)
	assert.Equal(t, "Doe", user.LastName) // Should remain unchanged
	assert.True(t, user.UpdatedAt.After(originalUpdatedAt))
}

func TestUser_StatusMethods(t *testing.T) {
	req := CreateUserRequest{
		Username:  "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
	}

	user, err := NewUser(req)
	require.NoError(t, err)

	// Test initial active status
	assert.True(t, user.IsActive())
	assert.Equal(t, StatusActive, user.Status)

	// Test deactivation
	user.Deactivate()
	assert.False(t, user.IsActive())
	assert.Equal(t, StatusInactive, user.Status)

	// Test suspension
	user.Suspend()
	assert.False(t, user.IsActive())
	assert.Equal(t, StatusSuspended, user.Status)

	// Test activation
	user.Activate()
	assert.True(t, user.IsActive())
	assert.Equal(t, StatusActive, user.Status)
}

func TestUser_ToResponse(t *testing.T) {
	req := CreateUserRequest{
		Username:  "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
	}

	user, err := NewUser(req)
	require.NoError(t, err)

	response := user.ToResponse()

	assert.Equal(t, user.ID, response.ID)
	assert.Equal(t, user.Username, response.Username)
	assert.Equal(t, user.FirstName, response.FirstName)
	assert.Equal(t, user.LastName, response.LastName)
	assert.Equal(t, user.Status, response.Status)
	assert.Equal(t, user.CreatedAt, response.CreatedAt)
	assert.Equal(t, user.UpdatedAt, response.UpdatedAt)
}

func TestUser_GetFullName(t *testing.T) {
	req := CreateUserRequest{
		Username:  "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
	}

	user, err := NewUser(req)
	require.NoError(t, err)

	assert.Equal(t, "John Doe", user.GetFullName())
}
