package user

import (
	"blacking-api/pkg/errors"
	"time"

	"golang.org/x/crypto/bcrypt"
)

type User struct {
	ID           int       `json:"id" db:"id"`
	Username     string    `json:"username" db:"username"`
	Password     string    `json:"-" db:"password"`
	FirstName    string    `json:"first_name" db:"first_name"`
	LastName     string    `json:"last_name" db:"last_name"`
	Status       Status    `json:"status" db:"status"`
	UserRoleID   int       `json:"user_role_id" db:"user_role_id"`
	UserRoleName string    `json:"user_role_name" db:"user_role_name"`
	IsEnable     *bool     `json:"is_enable" db:"is_enable"`
	Is2FAEnabled bool      `json:"is_2fa_enabled" db:"is_2fa_enabled"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

type Status string

const (
	StatusActive    Status = "active"
	StatusInactive  Status = "inactive"
	StatusSuspended Status = "suspended"
)

type CreateUserRequest struct {
	UserRoleID int    `json:"user_role_id" validate:"required"`
	Username   string `json:"username" validate:"required"`
	Password   string `json:"password" validate:"required,min=8"`
	FirstName  string `json:"first_name" validate:"required,min=2,max=50"`
	LastName   string `json:"last_name" validate:"required,min=2,max=50"`
	IsEnable   *bool  `json:"is_enable" validate:"required"`
}

// UpdateUserRequest represents the request to update a user
// If user_role_id is sent with the same value as current user, no validation will be performed
type UpdateUserRequest struct {
	Username   *string `json:"username,omitempty" validate:"omitempty,min=3,max=50"`
	FirstName  *string `json:"first_name,omitempty" validate:"omitempty,min=2,max=50"`
	LastName   *string `json:"last_name,omitempty" validate:"omitempty,min=2,max=50"`
	UserRoleID *int    `json:"user_role_id,omitempty" validate:"omitempty"` // Only validates if value changes
	IsEnable   *bool   `json:"is_enable,omitempty" validate:"omitempty"`
}

type UserResponse struct {
	ID           int       `json:"id"`
	Username     string    `json:"username"`
	FirstName    string    `json:"first_name"`
	LastName     string    `json:"last_name"`
	Status       Status    `json:"status"`
	UserRoleName string    `json:"user_role_name"`
	UserRoleID   int       `json:"user_role_id"`
	IsEnable     bool      `json:"is_enable" `
	Is2FAEnabled bool      `json:"is_2fa_enabled"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

func NewUser(req CreateUserRequest) (*User, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		return nil, errors.NewInternalError("failed to hash password")
	}

	now := time.Now()
	user := &User{
		Username:   req.Username,
		UserRoleID: req.UserRoleID,
		Password:   hashedPassword,
		FirstName:  req.FirstName,
		LastName:   req.LastName,
		IsEnable:   req.IsEnable,
		Status:     StatusActive,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
	return user, nil
}

func (u *User) Update(req UpdateUserRequest) error {
	if err := validateUpdateRequest(req); err != nil {
		return err
	}

	if req.Username != nil {
		u.Username = *req.Username
	}
	if req.FirstName != nil {
		u.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		u.LastName = *req.LastName
	}
	if req.UserRoleID != nil {
		u.UserRoleID = *req.UserRoleID
	}
	if req.IsEnable != nil {
		u.IsEnable = req.IsEnable
	}

	u.UpdatedAt = time.Now()
	return nil
}

func (u *User) SetPassword(password string) error {
	if len(password) < 8 {
		return errors.NewValidationError("password must be at least 8 characters long")
	}

	hashedPassword, err := hashPassword(password)
	if err != nil {
		return errors.NewInternalError("failed to hash password")
	}

	u.Password = hashedPassword
	u.UpdatedAt = time.Now()
	return nil
}

func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

func (u *User) IsActive() bool {
	return u.Status == StatusActive
}

func (u *User) Deactivate() {
	u.Status = StatusInactive
	u.UpdatedAt = time.Now()
}

func (u *User) Suspend() {
	u.Status = StatusSuspended
	u.UpdatedAt = time.Now()
}

func (u *User) Activate() {
	u.Status = StatusActive
	u.UpdatedAt = time.Now()
}

func (u *User) ToResponse() UserResponse {
	isEnable := false
	if u.IsEnable != nil {
		isEnable = *u.IsEnable
	}

	return UserResponse{
		ID:           u.ID,
		Username:     u.Username,
		FirstName:    u.FirstName,
		LastName:     u.LastName,
		Status:       u.Status,
		UserRoleID:   u.UserRoleID,
		UserRoleName: u.UserRoleName,
		IsEnable:     isEnable,
		Is2FAEnabled: u.Is2FAEnabled,
		CreatedAt:    u.CreatedAt,
		UpdatedAt:    u.UpdatedAt,
	}
}

func (u *User) GetFullName() string {
	return u.FirstName + " " + u.LastName
}

func hashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

func validateCreateRequest(req CreateUserRequest) error {
	if req.Username == "" {
		return errors.NewValidationError("Username is required")
	}
	if req.Password == "" {
		return errors.NewValidationError("password is required")
	}
	if len(req.Password) < 8 {
		return errors.NewValidationError("password must be at least 8 characters long")
	}
	if req.FirstName == "" {
		return errors.NewValidationError("first name is required")
	}
	if req.LastName == "" {
		return errors.NewValidationError("last name is required")
	}
	if req.UserRoleID == 0 {
		return errors.NewValidationError("user_role_id is required")
	}
	if req.IsEnable == nil {
		return errors.NewValidationError("is_enable is required")
	}

	return nil
}

func validateUpdateRequest(req UpdateUserRequest) error {
	if req.Username != nil && *req.Username == "" {
		return errors.NewValidationError("username cannot be empty")
	}
	if req.FirstName != nil && *req.FirstName == "" {
		return errors.NewValidationError("first name cannot be empty")
	}
	if req.LastName != nil && *req.LastName == "" {
		return errors.NewValidationError("last name cannot be empty")
	}
	if req.UserRoleID != nil && *req.UserRoleID == 0 {
		return errors.NewValidationError("user role id cannot be 0")
	}
	return nil
}

func isValidStatus(status Status) bool {
	switch status {
	case StatusActive, StatusInactive, StatusSuspended:
		return true
	default:
		return false
	}
}
