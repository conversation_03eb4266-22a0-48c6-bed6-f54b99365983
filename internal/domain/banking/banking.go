package banking

import (
	"blacking-api/internal/domain/request"
	"time"
)

type Banking struct {
	ID        int64     `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	ShortName string    `json:"shortName" db:"short_name"`
	Code      string    `json:"code" db:"code"`
	ImageURL  string    `json:"imageUrl" db:"image_url"`
	CreatedAt time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt time.Time `json:"updatedAt" db:"updated_at"`
}

type BankingResponse struct {
	ID        int64     `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	ShortName string    `json:"shortName" db:"short_name"`
	Code      string    `json:"code" db:"code"`
	ImageURL  string    `json:"imageUrl" db:"image_url"`
	CreatedAt time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt time.Time `json:"updatedAt" db:"updated_at"`
}

type BankingSearchRequest struct {
	request.RequestPagination
	Name      *string `form:"name"`
	ShortName *string `form:"sname"`
	Code      *string `form:"code"`
}

func (b *Banking) ToResponse() BankingResponse {
	return BankingResponse{
		ID:        b.ID,
		Name:      b.Name,
		ShortName: b.ShortName,
		Code:      b.Code,
		ImageURL:  b.ImageURL,
		CreatedAt: b.CreatedAt,
		UpdatedAt: b.UpdatedAt,
	}
}
