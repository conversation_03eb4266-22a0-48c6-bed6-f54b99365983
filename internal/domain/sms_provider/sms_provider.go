package sms_provider

import (
	"blacking-api/internal/domain/request"
	"time"
)

type SMSProvider struct {
	ID             int64     `json:"id" db:"id"`
	Name           string    `json:"name" db:"name"`
	ProviderNameID int64     `json:"providerNameId" db:"provider_name_id"`
	APIKey         string    `json:"apiKey" db:"api_key"`
	SecretKey      string    `json:"secretKey" db:"secret_key"`
	OTPSender      string    `json:"otpSender" db:"otp_sender"`
	SMSSender      string    `json:"smsSender" db:"sms_sender"`
	PrefixOTP      string    `json:"prefixOtp" db:"prefix_otp"`
	Balance        *float64  `json:"balance" db:"balance"`
	IsSMS          bool      `json:"isSMS" db:"is_sms"`
	IsOTP          bool      `json:"isOTP" db:"is_otp"`
	Active         bool      `json:"active" db:"active"`
	Inactive       bool      `json:"inactive" db:"inactive"`
	CreatedAt      time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt      time.Time `json:"updatedAt" db:"updated_at"`
}

type SMSProviderRequest struct {
	Name           string `json:"name" validate:"required,min=1,max=255"`
	ProviderNameID int64  `json:"providerNameId" validate:"required,min=1"`
	APIKey         string `json:"apiKey" validate:"required,min=1,max=255"`
	SecretKey      string `json:"secretKey" validate:"required,min=1,max=255"`
	OTPSender      string `json:"otpSender" validate:"required,min=1,max=255"`
	SMSSender      string `json:"smsSender" validate:"required,min=1,max=255"`
	PrefixOTP      string `json:"prefixOtp" validate:"required,min=1,max=255"`
}

type SMSProviderSearchRequest struct {
	request.RequestPagination
	Name *string `form:"name"`
}

type UpdateStatusRequest struct {
	Name   string `form:"name" validate:"required,min=1,max=255"`
	Status bool   `form:"status" default:"false"`
}

type SMSProviderListResponse struct {
	ID             int64    `json:"id"`
	Name           string   `json:"name"`
	ProviderNameID int64    `json:"providerNameId"`
	ProviderName   string   `json:"providerName"`
	Balance        *float64 `json:"balance"`
	IsSMS          bool     `json:"isSMS"`
	IsOTP          bool     `json:"isOTP"`
}

type SMSProviderByIdResponse struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	ProviderNameID int64  `json:"providerNameId"`
	APIKey         string `json:"apiKey"`
	SecretKey      string `json:"secretKey"`
	OTPSender      string `json:"otpSender"`
	SMSSender      string `json:"smsSender"`
	PrefixOTP      string `json:"prefixOtp"`
}

func (s *SMSProvider) ToByIdResponse() *SMSProviderByIdResponse {
	return &SMSProviderByIdResponse{
		ID:             s.ID,
		Name:           s.Name,
		ProviderNameID: s.ProviderNameID,
		APIKey:         s.APIKey,
		SecretKey:      s.SecretKey,
		OTPSender:      s.OTPSender,
		SMSSender:      s.SMSSender,
		PrefixOTP:      s.PrefixOTP,
	}
}

func (s *SMSProvider) ToListResponse(providerName string) *SMSProviderListResponse {
	return &SMSProviderListResponse{
		ID:             s.ID,
		Name:           s.Name,
		ProviderNameID: s.ProviderNameID,
		ProviderName:   providerName,
		Balance:        s.Balance,
		IsSMS:          s.IsSMS,
		IsOTP:          s.IsOTP,
	}
}
