package user_transaction_status

import "time"

// UserTransactionStatus represents a user transaction status
type UserTransactionStatus struct {
	ID        int       `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	Detail    string    `json:"detail" db:"detail"`
	CreatedAt time.Time `json:"createdAt" db:"created_at"`
}

// UserTransactionStatusResponse represents the API response for user transaction status
type UserTransactionStatusResponse struct {
	ID        int       `json:"id"`
	Name      string    `json:"name"`
	Detail    string    `json:"detail"`
	CreatedAt time.Time `json:"createdAt"`
}

// ToResponse converts UserTransactionStatus to UserTransactionStatusResponse
func (u *UserTransactionStatus) ToResponse() UserTransactionStatusResponse {
	return UserTransactionStatusResponse{
		ID:        u.ID,
		Name:      u.Name,
		Detail:    u.Detail,
		CreatedAt: u.CreatedAt,
	}
}
