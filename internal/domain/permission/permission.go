package permission

import (
	"blacking-api/pkg/errors"
	"time"
)

type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

// User Role Permission structures
type UserRolePermission struct {
	ID            int       `json:"id"`
	UserRoleID    int       `json:"user_role_id"`
	PermissionKey string    `json:"permission_key"`
	CanCreate     bool      `json:"can_create"`
	CanView       bool      `json:"can_view"`
	CanEdit       bool      `json:"can_edit"`
	CanDelete     bool      `json:"can_delete"`
	Status        Status    `json:"status"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

type UpdateUserRolePermissionsRequest struct {
	Permissions []UserRolePermissionUpdate `json:"permissions" validate:"required,min=1"`
}

type UserRolePermissionUpdate struct {
	PermissionKey string `json:"permission_key" validate:"required"`
	CanCreate     bool   `json:"can_create"`
	CanView       bool   `json:"can_view"`
	CanEdit       bool   `json:"can_edit"`
	CanDelete     bool   `json:"can_delete"`
}

// Permission Matrix structures for UI
type PermissionMatrix struct {
	ID             int                `json:"id"`
	Name           string             `json:"name"`
	Key            string             `json:"key"`
	Level          int                `json:"level"`
	SupportsCreate bool               `json:"supports_create"`
	SupportsView   bool               `json:"supports_view"`
	SupportsEdit   bool               `json:"supports_edit"`
	SupportsDelete bool               `json:"supports_delete"`
	IsTab          bool               `json:"is_tab"`
	IsButton       bool               `json:"is_button"`
	CanCreate      bool               `json:"can_create"`
	CanView        bool               `json:"can_view"`
	CanEdit        bool               `json:"can_edit"`
	CanDelete      bool               `json:"can_delete"`
	SubPermissions []PermissionMatrix `json:"sub_permissions,omitempty"`
}

type PermissionMatrixResponse struct {
	UserRole         UserRoleInfo            `json:"user_role"`
	PermissionGroups []PermissionGroupMatrix `json:"permission_groups"`
}

type UserRoleInfo struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type PermissionGroupMatrix struct {
	ID          int                `json:"id"`
	Name        string             `json:"name"`
	Key         string             `json:"key"`
	Permissions []PermissionMatrix `json:"permissions"`
}

type Permission struct {
	ID                 int                     `json:"id"`
	ParentID           *int                    `json:"parent_id"`
	Name               string                  `json:"name"`
	Key                string                  `json:"key"`
	Description        *string                 `json:"description"`
	Position           *int                    `json:"position"`
	Level              int                     `json:"level"`
	SupportsCreate     bool                    `json:"supports_create"`
	SupportsView       bool                    `json:"supports_view"`
	SupportsEdit       bool                    `json:"supports_edit"`
	SupportsDelete     bool                    `json:"supports_delete"`
	IsTab              bool                    `json:"is_tab"`
	IsButton           bool                    `json:"is_button"`
	Enabled            bool                    `json:"enabled"`
	Status             Status                  `json:"status"`
	SubPermissions     []Permission            `json:"sub_permissions,omitempty"`
	UserRolePermission *UserRolePermissionData `json:"user_role_permission,omitempty"`
	CreatedAt          time.Time               `json:"created_at"`
	UpdatedAt          time.Time               `json:"updated_at"`
}

type UserRolePermissionData struct {
	CanCreate bool `json:"can_create"`
	CanView   bool `json:"can_view"`
	CanEdit   bool `json:"can_edit"`
	CanDelete bool `json:"can_delete"`
}

type CreatePermissionRequest struct {
	ParentID       *int    `json:"parent_id"`
	Name           string  `json:"name" validate:"required"`
	Key            string  `json:"key" validate:"required"`
	Description    *string `json:"description"`
	Position       *int    `json:"position"`
	SupportsCreate *bool   `json:"supports_create"`
	SupportsView   *bool   `json:"supports_view"`
	SupportsEdit   *bool   `json:"supports_edit"`
	SupportsDelete *bool   `json:"supports_delete"`
	IsTab          *bool   `json:"is_tab"`
	IsButton       *bool   `json:"is_button"`
	Enabled        *bool   `json:"enabled"`
}

type UpdatePermissionRequest struct {
	ParentID       *int    `json:"parent_id"`
	Name           *string `json:"name"`
	Key            *string `json:"key"`
	Description    *string `json:"description"`
	Position       *int    `json:"position"`
	SupportsCreate *bool   `json:"supports_create"`
	SupportsView   *bool   `json:"supports_view"`
	SupportsEdit   *bool   `json:"supports_edit"`
	SupportsDelete *bool   `json:"supports_delete"`
	IsTab          *bool   `json:"is_tab"`
	IsButton       *bool   `json:"is_button"`
	Enabled        *bool   `json:"enabled"`
}

type ReorderPermissionsRequest struct {
	PermissionIDs []int `json:"permission_ids" validate:"required,min=1"`
}

type PermissionResponse struct {
	ID             int                  `json:"id"`
	ParentID       *int                 `json:"parent_id"`
	Name           string               `json:"name"`
	Key            string               `json:"key"`
	Description    *string              `json:"description"`
	Position       *int                 `json:"position"`
	Level          int                  `json:"level"`
	SupportsCreate bool                 `json:"supports_create"`
	SupportsView   bool                 `json:"supports_view"`
	SupportsEdit   bool                 `json:"supports_edit"`
	SupportsDelete bool                 `json:"supports_delete"`
	IsTab          bool                 `json:"is_tab"`
	IsButton       bool                 `json:"is_button"`
	Enabled        bool                 `json:"enabled"`
	Status         Status               `json:"status"`
	SubPermissions []PermissionResponse `json:"sub_permissions,omitempty"`
	CreatedAt      time.Time            `json:"created_at"`
	UpdatedAt      time.Time            `json:"updated_at"`
}

func NewPermission(req CreatePermissionRequest) (*Permission, error) {
	if err := validateCreatePermissionRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	level := 1
	if req.ParentID != nil {
		level = 2 // Sub-permission
	}

	permission := &Permission{
		ParentID:       req.ParentID,
		Name:           req.Name,
		Key:            req.Key,
		Description:    req.Description,
		Position:       req.Position,
		Level:          level,
		SupportsCreate: getBoolValue(req.SupportsCreate, true),
		SupportsView:   getBoolValue(req.SupportsView, true),
		SupportsEdit:   getBoolValue(req.SupportsEdit, true),
		SupportsDelete: getBoolValue(req.SupportsDelete, true),
		IsTab:          getBoolValue(req.IsTab, false),
		IsButton:       getBoolValue(req.IsButton, false),
		Enabled:        getBoolValue(req.Enabled, true),
		Status:         StatusActive,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	return permission, nil
}

func (p *Permission) Update(req UpdatePermissionRequest) error {
	if err := validateUpdatePermissionRequest(req); err != nil {
		return err
	}

	// GroupID removed - no longer needed
	if req.ParentID != nil {
		p.ParentID = req.ParentID
		p.Level = 2 // Sub-permission
	}
	if req.Name != nil {
		p.Name = *req.Name
	}
	if req.Key != nil {
		p.Key = *req.Key
	}
	if req.Description != nil {
		p.Description = req.Description
	}
	if req.Position != nil {
		p.Position = req.Position
	}
	if req.SupportsCreate != nil {
		p.SupportsCreate = *req.SupportsCreate
	}
	if req.SupportsView != nil {
		p.SupportsView = *req.SupportsView
	}
	if req.SupportsEdit != nil {
		p.SupportsEdit = *req.SupportsEdit
	}
	if req.SupportsDelete != nil {
		p.SupportsDelete = *req.SupportsDelete
	}
	if req.IsTab != nil {
		p.IsTab = *req.IsTab
	}
	if req.IsButton != nil {
		p.IsButton = *req.IsButton
	}
	if req.Enabled != nil {
		p.Enabled = *req.Enabled
	}

	p.UpdatedAt = time.Now()
	return nil
}

func (p *Permission) IsActive() bool {
	return p.Status == StatusActive
}

func (p *Permission) Deactivate() {
	p.Status = StatusInactive
	p.UpdatedAt = time.Now()
}

func (p *Permission) Activate() {
	p.Status = StatusActive
	p.UpdatedAt = time.Now()
}

func (p *Permission) ToResponse() PermissionResponse {
	var subPermissions []PermissionResponse
	for _, subPerm := range p.SubPermissions {
		subPermissions = append(subPermissions, subPerm.ToResponse())
	}

	return PermissionResponse{
		ID:             p.ID,
		ParentID:       p.ParentID,
		Name:           p.Name,
		Key:            p.Key,
		Description:    p.Description,
		Position:       p.Position,
		Level:          p.Level,
		SupportsCreate: p.SupportsCreate,
		SupportsView:   p.SupportsView,
		SupportsEdit:   p.SupportsEdit,
		SupportsDelete: p.SupportsDelete,
		IsTab:          p.IsTab,
		IsButton:       p.IsButton,
		Enabled:        p.Enabled,
		Status:         p.Status,
		SubPermissions: subPermissions,
		CreatedAt:      p.CreatedAt,
		UpdatedAt:      p.UpdatedAt,
	}
}

func getBoolValue(ptr *bool, defaultValue bool) bool {
	if ptr != nil {
		return *ptr
	}
	return defaultValue
}

func getStringValue(ptr *string, defaultValue string) string {
	if ptr != nil {
		return *ptr
	}
	return defaultValue
}

func validateCreatePermissionRequest(req CreatePermissionRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}
	if req.Key == "" {
		return errors.NewValidationError("key is required")
	}
	return nil
}

func validateUpdatePermissionRequest(req UpdatePermissionRequest) error {
	if req.Name != nil && *req.Name == "" {
		return errors.NewValidationError("name cannot be empty")
	}
	if req.Key != nil && *req.Key == "" {
		return errors.NewValidationError("key cannot be empty")
	}
	return nil
}
