package user_transaction_direction

import "time"

// UserTransactionDirection represents a user transaction direction
type UserTransactionDirection struct {
	ID        int       `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	Detail    *string   `json:"detail" db:"detail"`
	CreatedAt time.Time `json:"createdAt" db:"created_at"`
}

// UserTransactionDirectionResponse represents the API response for user transaction direction
type UserTransactionDirectionResponse struct {
	ID        int       `json:"id"`
	Name      string    `json:"name"`
	Detail    *string   `json:"detail"`
	CreatedAt time.Time `json:"createdAt"`
}

// ToResponse converts UserTransactionDirection to UserTransactionDirectionResponse
func (u *UserTransactionDirection) ToResponse() UserTransactionDirectionResponse {
	return UserTransactionDirectionResponse{
		ID:        u.ID,
		Name:      u.Name,
		Detail:    u.Detail,
		CreatedAt: u.CreatedAt,
	}
}
