package promotion_web

import "time"

// PROMOTION_WEB_OPTION_MODEL
const (
	PROMOTION_WEB_USER_STATUS_ON_PROCESS  = int64(1)
	PROMOTION_WEB_USER_STATUS_CANCELED    = int64(3)
	PROMOTION_WEB_USER_STATUS_SUCCESS     = int64(2)
	PROMOTION_WEB_USER_STATUS_ON_WITHDRAW = int64(4)
)

const (
	PROMOTION_WEB_STATUS_DISABLE_WEB = int64(1)
	PROMOTION_WEB_STATUS_ACTIVE      = int64(2)
	PROMOTION_WEB_STATUS_CANCELED    = int64(3)
	PROMOTION_WEB_STATUS_ONLY_SHOW   = int64(4)
	PROMOTION_WEB_STATUS_ONLY_URL    = int64(5)
)

type PromotionWebGetListRequest struct {
	Page                 int    `form:"page" default:"1"`
	Limit                int    `form:"limit" default:"10"`
	StartDate            string `form:"startDate" time_format:"2006-01-02"`
	EndDate              string `form:"end" time_format:"2006-01-02"`
	Search               string `form:"search"`
	PromotionWebStatusId *int64 `form:"promotionWebStatusId"`
}

type PromotionWebGetListResponse struct {
	Id                     int64     `json:"id"`
	PromotionWebTypeId     int64     `json:"promotionWebTypeId"`
	PromotionWebTypeTh     string    `json:"promotionWebTypeTh"`
	PromotionWebStatusId   int64     `json:"promotionWebStatusId"`
	PromotionWebStatusTh   string    `json:"promotionWebStatusTh"`
	Name                   string    `json:"name"`
	PromotionWebDateTypeId int64     `json:"promotionWebDateTypeId"`
	StartDate              string    `json:"startDate"`
	EndDate                string    `json:"endDate"`
	TimeStart              string    `json:"timeStart"`
	TimeEnd                string    `json:"timeEnd"`
	CreatedByAdminId       int64     `json:"createdByAdminId"`
	CreatedByAdminName     string    `json:"createdByAdminName"`
	UpdatedByAdminId       *int64    `json:"updatedByAdminId"`
	UpdatedByAdminName     string    `json:"updatedByAdminName"`
	CanceledByAdminId      *int64    `json:"canceledByAdminId"`
	CanceledByAdminName    string    `json:"canceledByAdminName"`
	HiddenUrlLink          string    `json:"hiddenUrlLink"`
	UpdatedAt              time.Time `json:"updatedAt"`
}

type PromotionWebCreateRequest struct {
	Id                           int64     `json:"-"`
	PromotionWebTypeId           int64     `json:"promotionWebTypeId" binding:"required"`
	PromotionWebStatusId         int64     `json:"promotionWebStatusId" binding:"required"`
	ConditionDetail              string    `json:"conditionDetail"`
	ImageUrl                     string    `json:"imageUrl"`
	Name                         string    `json:"name" binding:"required"`
	ShortDescription             string    `json:"shortDescription" binding:"required"`
	Description                  string    `json:"description" binding:"required"`
	PromotionWebDateTypeId       int64     `json:"promotionWebDateTypeId" binding:"required"`
	StartDate                    *string   `json:"startDate"`
	EndDate                      *string   `json:"endDate"`
	FreeBonusAmount              float64   `json:"freeBonusAmount"`
	PrivilegePerDay              int64     `json:"privilegePerDay"`
	AbleWithdrawMorethan         float64   `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId *int64    `json:"promotionWebBonusConditionId"`
	BonusConditionAmount         float64   `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId      *int64    `json:"promotionWebBonusTypeId"`
	BonusTypeAmount              float64   `json:"bonusTypeAmount"`
	BonusTypeAmountMax           float64   `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime          float64   `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId   *int64    `json:"promotionWebTurnoverTypeId"`
	TurnoverAmount               float64   `json:"turnoverAmount"`
	Monday                       bool      `json:"monday"`
	Tuesday                      bool      `json:"tuesday"`
	Wednesday                    bool      `json:"wednesday"`
	Thursday                     bool      `json:"thursday"`
	Friday                       bool      `json:"friday"`
	Saturday                     bool      `json:"saturday"`
	Sunday                       bool      `json:"sunday"`
	TimeStart                    *string   `json:"timeStart"`
	TimeEnd                      *string   `json:"timeEnd"`
	HiddenUrlLink                string    `json:"hiddenUrlLink"`
	CreatedByAdminId             int64     `json:"-"`
	UpdatedAt                    time.Time `json:"-"`
}

type PromotionWebGetByIdResponse struct {
	Id                               int64   `json:"id"`
	PromotionWebTypeId               int64   `json:"promotionWebTypeId"`
	PromotionWebTypeTh               string  `json:"promotionWebTypeTh"`
	PromotionWebStatusId             int64   `json:"promotionWebStatusId"`
	PromotionWebStatusTh             string  `json:"promotionWebStatusTh"`
	ConditionDetail                  string  `json:"conditionDetail"`
	ImageUrl                         string  `json:"imageUrl"`
	Name                             string  `json:"name"`
	ShortDescription                 string  `json:"shortDescription"`
	Description                      string  `json:"description"`
	PromotionWebDateTypeId           int64   `json:"promotionWebDateTypeId"`
	StartDate                        string  `json:"startDate"`
	EndDate                          string  `json:"endDate"`
	FreeBonusAmount                  float64 `json:"freeBonusAmount"`
	PrivilegePerDay                  int64   `json:"privilegePerDay"`
	AbleWithdrawMorethan             float64 `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId     int64   `json:"promotionWebBonusConditionId"`
	PromotionWebBonusConditionTh     string  `json:"promotionWebBonusConditionTh"`
	PromotionWebBonusConditionSyntax string  `json:"promotionWebBonusConditionSyntax"`
	BonusConditionAmount             float64 `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId          int64   `json:"promotionWebBonusTypeId"`
	PromotionWebBonusTypeTh          string  `json:"promotionWebBonusTypeTh"`
	BonusTypeAmount                  float64 `json:"bonusTypeAmount"`
	BonusTypeAmountMax               float64 `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime              float64 `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId       int64   `json:"promotionWebTurnoverTypeId"`
	PromotionWebTurnoverTypeTh       string  `json:"promotionWebTurnoverTypeTh"`
	TurnoverAmount                   float64 `json:"turnoverAmount"`
	Monday                           bool    `json:"monday"`
	Tuesday                          bool    `json:"tuesday"`
	Wednesday                        bool    `json:"wednesday"`
	Thursday                         bool    `json:"thursday"`
	Friday                           bool    `json:"friday"`
	Saturday                         bool    `json:"saturday"`
	Sunday                           bool    `json:"sunday"`
	TimeStart                        string  `json:"timeStart"`
	TimeEnd                          string  `json:"timeEnd"`
}

type PromotionWebUpdateRequest struct {
	Id                           int64    `json:"-"`
	PromotionWebTypeId           *int64   `json:"promotionWebTypeId"`
	PromotionWebStatusId         *int64   `json:"promotionWebStatusId"`
	ConditionDetail              *string  `json:"conditionDetail"`
	ImageUrl                     *string  `json:"imageUrl"`
	Name                         *string  `json:"name"`
	ShortDescription             *string  `json:"shortDescription"`
	Description                  *string  `json:"description"`
	PromotionWebDateTypeId       int64    `json:"promotionWebDateTypeId"`
	StartDate                    *string  `json:"startDate"`
	EndDate                      *string  `json:"endDate"`
	FreeBonusAmount              *float64 `json:"freeBonusAmount"`
	PrivilegePerDay              *int64   `json:"privilegePerDay"`
	AbleWithdrawMorethan         *float64 `json:"ableWithdrawMorethan"`
	PromotionWebBonusConditionId *int64   `json:"promotionWebBonusConditionId"`
	BonusConditionAmount         *float64 `json:"bonusConditionAmount"`
	PromotionWebBonusTypeId      *int64   `json:"promotionWebBonusTypeId"`
	BonusTypeAmount              *float64 `json:"bonusTypeAmount"`
	BonusTypeAmountMax           *float64 `json:"bonusTypeAmountMax"`
	AbleWithdrawPertime          *float64 `json:"ableWithdrawPertime"`
	PromotionWebTurnoverTypeId   *int64   `json:"promotionWebTurnoverTypeId"`
	TurnoverAmount               *float64 `json:"turnoverAmount"`
	Monday                       *bool    `json:"monday"`
	Tuesday                      *bool    `json:"tuesday"`
	Wednesday                    *bool    `json:"wednesday"`
	Thursday                     *bool    `json:"thursday"`
	Friday                       *bool    `json:"friday"`
	Saturday                     *bool    `json:"saturday"`
	Sunday                       *bool    `json:"sunday"`
	TimeStart                    *string  `json:"timeStart"`
	TimeEnd                      *string  `json:"timeEnd"`
	HiddenUrlLink                *string  `json:"hiddenUrlLink"`
	UpdatedByAdminId             int64    `json:"-"`
}

type GetPromotionWebIdToCancel struct {
	Id                       int64 `json:"id"`
	PromotionWebUserStatusId int64 `json:"promotionWebUserStatusId"`
}

type CancelPromotionWebRequest struct {
	Id                   int64     `json:"id"`
	CanceledByAdminId    int64     `json:"-"`
	CanceledAt           time.Time `json:"-"`
	PromotionWebStatusId int64     `json:"-"`
}

type DeletePromotionWebRequest struct {
	Id                   int64     `json:"id"`
	DeletedByAdminId     int64     `json:"-"`
	DeletedAt            time.Time `json:"-"`
	PromotionWebStatusId int64     `json:"-"`
}

type PromotionWebExpired struct {
	Id int64 `json:"id"`
}

type CancelPromotionWebUserById struct {
	Id                int64 `json:"-"`
	CanceledByAdminId int64 `json:"canceledByAdminId"`
}

type CancelPromotionWebUserByPromotionWebId struct {
	PromotionWebId    int64     `json:"promotionWebId"`
	CanceledByAdminId int64     `json:"canceledByAdminId"`
	CanceledAt        time.Time `json:"canceledAt"`
}

type PromotionWebUserByUserIdResponse struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	PromotionName            string    `json:"promotionName"`
	UserId                   int64     `json:"userId"`
	MemberCode               string    `json:"memberCode"`
	FullName                 string    `json:"fullName"`
	Phone                    string    `json:"phone"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string    `json:"promotionWebUserStatusTh"`
	TotalAmount              float64   `json:"totalAmount"`
	CreatedAt                time.Time `json:"createdAt"`
}

type PromotionWebUserGetListRequest struct {
	PromotionWebId           *int64 `form:"promotionWebId"`
	Page                     int    `form:"page" default:"1"`
	Limit                    int    `form:"limit" default:"10"`
	StartDate                string `form:"startDate" time_format:"2006-01-02"`
	EndDate                  string `form:"end" time_format:"2006-01-02"`
	PromotionWebUserStatusId *int64 `form:"promotionWebUserStatusId"`
	Search                   string `form:"search"`
	TypeList                 string `form:"typeList"`
}

type PromotionWebUserGetListResponse struct {
	Id                       int64      `json:"id"`
	PromotionWebId           int64      `json:"promotionWebId"`
	PromotionName            string     `json:"promotionName"`
	UserId                   int64      `json:"userId"`
	MemberCode               string     `json:"memberCode"`
	FullName                 string     `json:"fullName"`
	Phone                    string     `json:"phone"`
	PromotionWebUserStatusId int64      `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string     `json:"promotionWebUserStatusTh"`
	TotalAmount              float64    `json:"totalAmount"`
	IsLocked                 bool       `json:"isLocked"`
	AbleWithdrawPertime      float64    `json:"ableWithdrawPertime"`
	AbleWithdrawMorethan     float64    `json:"ableWithdrawMorethan"`
	CreatedAt                time.Time  `json:"createdAt"`
	CanceledByAdminId        *int64     `json:"canceledByAdminId"`
	CanceledByAdminName      *string    `json:"canceledByAdminName"`
	CanceledAt               *time.Time `json:"canceledAt"`
	ApproveCreditByAdminId   *int64     `json:"approveCreditByAdminId"`
	ApproveCreditByAdminName string     `json:"approveCreditByAdminName"`
	ApproveCreditAt          *time.Time `json:"approveCreditAt"`
}

type GetPromotionWebUserById struct {
	Id int64 `json:"id"`
}

type GetPromotionWebUserByIdResponse struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	PromotionName            string    `json:"promotionName"`
	UserId                   int64     `json:"userId"`
	MemberCode               string    `json:"memberCode"`
	FullName                 string    `json:"fullName"`
	Phone                    string    `json:"phone"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string    `json:"promotionWebUserStatusTh"`
	TotalAmount              float64   `json:"totalAmount"`
	CreatedAt                time.Time `json:"createdAt"`
}

type PromotionWebUserGetListByUserIdRequest struct {
	UserId                   int64  `form:"userId" binding:"required"`
	PromotionWebUserStatusId *int64 `form:"promotionWebUserStatusId"`
	OfDate                   string `form:"ofDate"`
	DateType                 string `form:"dateType"`
	FromDate                 string `form:"fromDate"`
	ToDate                   string `form:"toDate"`
	Search                   string `form:"search"`
	Page                     int    `form:"page" default:"1"`
	Limit                    int    `form:"limit" default:"10"`
	SortCol                  string `form:"sortCol"`
	SortAsc                  string `form:"sortAsc"`
}

type PromotionWebUserGetListByUserIdResponse struct {
	Id                       int64     `json:"id"`
	PromotionWebId           int64     `json:"promotionWebId"`
	PromotionName            string    `json:"promotionName"`
	UserId                   int64     `json:"userId"`
	MemberCode               string    `json:"memberCode"`
	FullName                 string    `json:"fullName"`
	Phone                    string    `json:"phone"`
	PromotionWebUserStatusId int64     `json:"promotionWebUserStatusId"`
	PromotionWebUserStatusTh string    `json:"promotionWebUserStatusTh"`
	TotalAmount              float64   `json:"totalAmount"`
	CreatedAt                time.Time `json:"createdAt"`
	CanceledByAdminId        *int64    `json:"canceledByAdminId"`
	CanceledByAdminName      *string   `json:"canceledByAdminName"`
}

type PromotionWebGetSildeListOnlyActive struct {
	Id                     int64  `json:"id"`
	PromotionWebTypeId     int64  `json:"promotionWebTypeId"`
	PromotionWebTypeTh     string `json:"promotionWebTypeTh"`
	PromotionWebStatusId   int64  `json:"promotionWebStatusId"`
	PromotionWebStatusTh   string `json:"promotionWebStatusTh"`
	Name                   string `json:"name"`
	PromotionWebDateTypeId int64  `json:"promotionWebDateTypeId"`
	StartDate              string `json:"startDate"`
	EndDate                string `json:"endDate"`
	TimeStart              string `json:"timeStart"`
	TimeEnd                string `json:"timeEnd"`
}

type CloudFlareResult struct {
	Id                string    `json:"id"`
	Filename          string    `json:"filename"`
	Uploaded          time.Time `json:"uploaded"`
	RequireSignedURLs bool      `json:"requireSignedURLs"`
	Variants          []string  `json:"variants"`
}

type CloudFlareUploadResponse struct {
	Result   CloudFlareResult `json:"result"`
	Success  bool             `json:"success"`
	Errors   []interface{}    `json:"errors"`
	Messages []interface{}    `json:"messages"`
}

type CloudFlareUploadCreateBody struct {
	FileTypeId        int64     `json:"fileTypeId"`
	ImageId           string    `json:"imageId"`
	Filename          string    `json:"filename"`
	Uploaded          time.Time `json:"uploaded"`
	RequireSignedURLs bool      `json:"requireSignedURLs"`
	FileUrl           string    `json:"fileUrl"`
	ImageDimensions   string    `json:"imageDimensions"`
	Kilobytes         int64     `json:"kilobytes"`
}

type DragSortRequest struct {
	FromItemId int64 `form:"fromItemId" binding:"required"`
	ToItemId   int64 `form:"toItemId" binding:"required"`
}

type PrioritySortResponse struct {
	Id            int64 `json:"id"`
	PriorityOrder int64 `json:"priorityOrder"`
}
