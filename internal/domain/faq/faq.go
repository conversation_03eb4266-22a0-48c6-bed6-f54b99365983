package faq

import (
	"time"
)

type FAQ struct {
	ID        int       `json:"id"`
	Position  *int      `json:"position"`
	Title     *string   `json:"title"`
	Answer    *string   `json:"answer"`
	Status    Status    `json:"status"`
	UpdatedAt time.Time `json:"updated_at"`
	CreatedAt time.Time `json:"created_at"`
}

type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

type CreateFAQRequest struct {
	Title  *string `json:"title" validate:"required"`
	Answer *string `json:"answer" validate:"required"`
}

type UpdateFAQRequest struct {
	Title  *string `json:"title" validate:"required"`
	Answer *string `json:"answer" validate:"required"`
}

type ReorderRequest struct {
	FAQIDs []int `json:"faq_ids" validate:"required,min=1"`
}

type FAQResponse struct {
	ID        int       `json:"id"`
	Position  *int      `json:"position"`
	Title     *string   `json:"title"`
	Answer    *string   `json:"answer"`
	Status    Status    `json:"status"`
	UpdatedAt time.Time `json:"updated_at"`
	CreatedAt time.Time `json:"created_at"`
}

func NewFAQ(req CreateFAQRequest) (*FAQ, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	user := &FAQ{
		Title:     req.Title,
		Answer:    req.Answer,
		Status:    StatusActive,
		CreatedAt: now,
		UpdatedAt: now,
	}
	return user, nil
}

func (u *FAQ) Update(req UpdateFAQRequest) error {
	if err := validateUpdateRequest(req); err != nil {
		return err
	}

	if req.Title != nil {
		u.Title = req.Title
	}

	if req.Answer != nil {
		u.Answer = req.Answer
	}

	u.UpdatedAt = time.Now()
	return nil
}

func (u *FAQ) IsActive() bool {
	return u.Status == StatusActive
}

func (u *FAQ) Deactivate() {
	u.Status = StatusInactive
	u.UpdatedAt = time.Now()
}

func (u *FAQ) Activate() {
	u.Status = StatusActive
	u.UpdatedAt = time.Now()
}

func (u *FAQ) ToResponse() FAQResponse {
	return FAQResponse{
		ID:        u.ID,
		Position:  u.Position,
		Title:     u.Title,
		Answer:    u.Answer,
		Status:    u.Status,
		CreatedAt: u.CreatedAt,
		UpdatedAt: u.UpdatedAt,
	}
}

func validateCreateRequest(req CreateFAQRequest) error {
	// if req.Title != nil && *req.Title == "" {
	// 	return errors.NewValidationError("title is required")
	// }
	// if req.Answer != nil && *req.Answer == "" {
	// 	return errors.NewValidationError("answer is required")
	// }
	return nil
}

func validateUpdateRequest(req UpdateFAQRequest) error {
	// if req.Title != nil && *req.Title == "" {
	// 	return errors.NewValidationError("title is required")
	// }
	// if req.Answer != nil && *req.Answer == "" {
	// 	return errors.NewValidationError("answer is required")
	// }

	return nil
}
