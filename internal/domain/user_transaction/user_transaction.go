package user_transaction

import "time"

type UserTransaction struct {
	ID          int64     `json:"id"`
	BillNumber  string    `json:"billNumber" db:"bill_number"`
	UserAccount string    `json:"userAccount" db:"user_account"`
	Amount      float64   `json:"amount" db:"amount"`
	Date        time.Time `json:"date" db:"date"`
	StartTime   time.Time `json:"startTime" db:"start_time"`
	EndTime     time.Time `json:"endTime" db:"end_time"`
	CreatedAt   time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt   time.Time `json:"updatedAt" db:"updated_at"`
}

type DepositTransactionRequest struct {
	UserCode    string  `json:"userCode" validate:"required"`
	Amount      float64 `json:"amount" validate:"required,gt=0"`
	BankingID   int64   `json:"bankingId" validate:"required,gt=0"`
	Date        string  `json:"date" validate:"required"`
	Time        string  `json:"time" validate:"required"`
	PromotionID *int64  `json:"promotionId"`
	Description string  `json:"description"`
}

type WithdrawTransactionRequest struct {
	UserCode    string  `json:"userCode" validate:"required"`
	Amount      float64 `json:"amount" validate:"required,gt=0"`
	Description string  `json:"description"`
}
