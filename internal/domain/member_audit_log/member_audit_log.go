package member_audit_log

import (
	"blacking-api/pkg/errors"
	"time"
)

// ActionType represents the type of action performed on member
type ActionType string

const (
	ActionCreate          ActionType = "create"
	ActionUpdate          ActionType = "update"
	ActionDelete          ActionType = "delete"
	ActionActivate        ActionType = "activate"
	ActionDeactivate      ActionType = "deactivate"
	ActionSuspend         ActionType = "suspend"
	ActionPassword        ActionType = "password_change"
	ActionGameCredentials ActionType = "game_credentials_change"
	ActionBalanceUpdate   ActionType = "balance_update"
)

// MemberAuditLog represents an audit log entry for member operations
type MemberAuditLog struct {
	ID            int        `json:"id" db:"id"`
	MemberID      int        `json:"member_id" db:"member_id"`
	Username      string     `json:"username" db:"username"`
	Action        ActionType `json:"action" db:"action"`
	OldValues     *string    `json:"old_values,omitempty" db:"old_values"`
	NewValues     *string    `json:"new_values,omitempty" db:"new_values"`
	ChangedBy     int        `json:"changed_by" db:"changed_by"`
	ChangedByName string     `json:"changed_by_name" db:"changed_by_name"`
	ChangedAt     time.Time  `json:"changed_at" db:"changed_at"`
}

// CreateAuditLogRequest represents the request to create an audit log
type CreateAuditLogRequest struct {
	MemberID      int        `json:"member_id" validate:"required"`
	Username      string     `json:"username" validate:"required"`
	Action        ActionType `json:"action" validate:"required"`
	OldValues     *string    `json:"old_values,omitempty"`
	NewValues     *string    `json:"new_values,omitempty"`
	ChangedBy     int        `json:"changed_by" validate:"required"`
	ChangedByName string     `json:"changed_by_name" validate:"required"`
}

// MemberAuditLogResponse represents the response structure for audit log
type MemberAuditLogResponse struct {
	ID            int        `json:"id"`
	MemberID      int        `json:"member_id"`
	Username      string     `json:"username"`
	Action        ActionType `json:"action"`
	OldValues     *string    `json:"old_values,omitempty"`
	NewValues     *string    `json:"new_values,omitempty"`
	ChangedBy     int        `json:"changed_by"`
	ChangedByName string     `json:"changed_by_name"`
	ChangedAt     time.Time  `json:"changed_at"`
}

// NewMemberAuditLog creates a new member audit log entry
func NewMemberAuditLog(req CreateAuditLogRequest) (*MemberAuditLog, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	auditLog := &MemberAuditLog{
		MemberID:      req.MemberID,
		Username:      req.Username,
		Action:        req.Action,
		OldValues:     req.OldValues,
		NewValues:     req.NewValues,
		ChangedBy:     req.ChangedBy,
		ChangedByName: req.ChangedByName,
		ChangedAt:     now,
	}

	return auditLog, nil
}

// ToResponse converts MemberAuditLog to MemberAuditLogResponse
func (m *MemberAuditLog) ToResponse() MemberAuditLogResponse {
	return MemberAuditLogResponse{
		ID:            m.ID,
		MemberID:      m.MemberID,
		Username:      m.Username,
		Action:        m.Action,
		OldValues:     m.OldValues,
		NewValues:     m.NewValues,
		ChangedBy:     m.ChangedBy,
		ChangedByName: m.ChangedByName,
		ChangedAt:     m.ChangedAt,
	}
}

// IsValidAction checks if the action type is valid
func IsValidAction(action ActionType) bool {
	switch action {
	case ActionCreate, ActionUpdate, ActionDelete, ActionActivate, ActionDeactivate, ActionSuspend, ActionPassword, ActionGameCredentials, ActionBalanceUpdate:
		return true
	default:
		return false
	}
}

// GetActionDescription returns a human-readable description for the action
func (a ActionType) GetActionDescription() string {
	switch a {
	case ActionCreate:
		return "Member created"
	case ActionUpdate:
		return "Member updated"
	case ActionDelete:
		return "Member deleted"
	case ActionActivate:
		return "Member activated"
	case ActionDeactivate:
		return "Member deactivated"
	case ActionSuspend:
		return "Member suspended"
	case ActionPassword:
		return "Member password changed"
	case ActionGameCredentials:
		return "Member game credentials changed"
	case ActionBalanceUpdate:
		return "Member balance updated"
	default:
		return "Unknown action"
	}
}

// validateCreateRequest validates the create audit log request
func validateCreateRequest(req CreateAuditLogRequest) error {
	if req.MemberID == 0 {
		return errors.NewValidationError("member_id is required")
	}
	if req.Username == "" {
		return errors.NewValidationError("username is required")
	}
	if req.Action == "" {
		return errors.NewValidationError("action is required")
	}
	if !IsValidAction(req.Action) {
		return errors.NewValidationError("invalid action type")
	}
	if req.ChangedBy == 0 {
		return errors.NewValidationError("changed_by is required")
	}
	if req.ChangedByName == "" {
		return errors.NewValidationError("changed_by_name is required")
	}
	return nil
}
