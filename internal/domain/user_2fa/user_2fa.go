package user_2fa

import (
	"blacking-api/pkg/errors"
	"crypto/rand"
	"time"

	"github.com/pquerna/otp"
	"github.com/pquerna/otp/totp"
)

// User2FA represents a user's 2FA configuration
type User2FA struct {
	UserID      int       `json:"user_id" db:"user_id"`
	SecretKey   string    `json:"secret_key" db:"secret_key"`
	IsEnabled   bool      `json:"is_enabled" db:"is_enabled"`
	BackupCodes []string  `json:"backup_codes" db:"backup_codes"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Setup2FARequest represents the request to setup 2FA
type Setup2FARequest struct {
	UserID int `json:"user_id" validate:"required"`
}

// Setup2FAResponse represents the response for 2FA setup
type Setup2FAResponse struct {
	SecretKey string `json:"secret_key"`
	QRCodeURL string `json:"qr_code_url"`
	QRCode    string `json:"qr_code_base64"` // Base64 encoded QR code image
}

// Verify2FARequest represents the request to verify 2FA code
type Verify2FARequest struct {
	UserID int    `json:"user_id" validate:"required"`
	Code   string `json:"code" validate:"required,len=6"`
}

// Enable2FARequest represents the request to enable 2FA
type Enable2FARequest struct {
	UserID int    `json:"user_id" validate:"required"`
	Code   string `json:"code" validate:"required,len=6"`
}

// Disable2FARequest represents the request to disable 2FA
type Disable2FARequest struct {
	UserID int    `json:"user_id" validate:"required"`
	Code   string `json:"code" validate:"required,len=6"`
}

// Login2FARequest represents the request for 2FA login step
type Login2FARequest struct {
	Token string `json:"token" validate:"required"`
	Code  string `json:"code" validate:"required,len=6"`
}

// Reset2FARequest represents the request to reset 2FA (admin only)
type Reset2FARequest struct {
	UserID  int    `json:"user_id" validate:"required"`
	AdminID string `json:"admin_id" validate:"required"`
}

// User2FAResponse represents the response structure for 2FA
type User2FAResponse struct {
	UserID      int       `json:"user_id"`
	IsEnabled   bool      `json:"is_enabled"`
	BackupCodes []string  `json:"backup_codes,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// User2FAStatusResponse represents the 2FA status response
type User2FAStatusResponse struct {
	UserID    int  `json:"user_id"`
	IsEnabled bool `json:"is_enabled"`
	HasSetup  bool `json:"has_setup"`
}

// NewUser2FA creates a new User2FA instance
func NewUser2FA(userID int, secretKey string) *User2FA {
	now := time.Now()
	return &User2FA{
		UserID:      userID,
		SecretKey:   secretKey,
		IsEnabled:   false,
		BackupCodes: nil,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// GenerateSecret generates a new TOTP secret key
func GenerateSecret(issuer, accountName string) (*otp.Key, error) {
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      issuer,
		AccountName: accountName,
		SecretSize:  32,
	})
	if err != nil {
		return nil, errors.NewInternalError("failed to generate TOTP secret")
	}
	return key, nil
}

// ValidateCode validates a TOTP code against the secret
func ValidateCode(secret, code string) bool {
	return totp.Validate(code, secret)
}

// GenerateQRCode generates a QR code for the TOTP URL
func GenerateQRCode(key *otp.Key) (string, error) {
	// For now, we'll return the URL - QR code generation can be added later
	return key.URL(), nil
}

// Enable enables 2FA for the user
func (u *User2FA) Enable() {
	u.IsEnabled = true
	u.UpdatedAt = time.Now()
}

// Disable disables 2FA for the user
func (u *User2FA) Disable() {
	u.IsEnabled = false
	u.UpdatedAt = time.Now()
}

// UpdateSecret updates the secret key
func (u *User2FA) UpdateSecret(secretKey string) {
	u.SecretKey = secretKey
	u.UpdatedAt = time.Now()
}

// Reset resets 2FA configuration (admin function)
func (u *User2FA) Reset() {
	u.IsEnabled = false
	u.SecretKey = ""
	u.BackupCodes = []string{}
	u.UpdatedAt = time.Now()
}

// GenerateBackupCodes generates backup codes for emergency access
func (u *User2FA) GenerateBackupCodes() []string {
	codes := make([]string, 10) // Generate 10 backup codes
	for i := 0; i < 10; i++ {
		// Generate random 8-character backup code using only alphanumeric characters
		code := generateRandomCode(8)
		codes[i] = code
	}
	u.BackupCodes = codes
	u.UpdatedAt = time.Now()
	return codes
}

// generateRandomCode generates a random alphanumeric code of specified length
func generateRandomCode(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		// Use crypto/rand for secure random generation
		randomByte := make([]byte, 1)
		_, err := rand.Read(randomByte)
		if err != nil {
			// Fallback to math/rand if crypto/rand fails
			b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
		} else {
			b[i] = charset[randomByte[0]%byte(len(charset))]
		}
	}
	return string(b)
}

// ValidateBackupCode validates a backup code
func (u *User2FA) ValidateBackupCode(code string) bool {
	for i, backupCode := range u.BackupCodes {
		if backupCode == code {
			// Remove used backup code
			u.BackupCodes = append(u.BackupCodes[:i], u.BackupCodes[i+1:]...)
			u.UpdatedAt = time.Now()
			return true
		}
	}
	return false
}

// ToResponse converts User2FA to User2FAResponse
func (u *User2FA) ToResponse() User2FAResponse {
	return User2FAResponse{
		UserID:      u.UserID,
		IsEnabled:   u.IsEnabled,
		BackupCodes: u.BackupCodes,
		CreatedAt:   u.CreatedAt,
		UpdatedAt:   u.UpdatedAt,
	}
}

// ToStatusResponse converts User2FA to User2FAStatusResponse
func (u *User2FA) ToStatusResponse() User2FAStatusResponse {
	return User2FAStatusResponse{
		UserID:    u.UserID,
		IsEnabled: u.IsEnabled,
		HasSetup:  u.SecretKey != "",
	}
}

// ValidateSetupRequest validates the setup 2FA request
func ValidateSetupRequest(req Setup2FARequest) error {
	if req.UserID == 0 {
		return errors.NewValidationError("user_id is required")
	}
	return nil
}

// ValidateReset2FARequest validates the reset 2FA request
func ValidateReset2FARequest(req Reset2FARequest) error {
	if req.UserID == 0 {
		return errors.NewValidationError("user_id is required")
	}
	return nil
}

// ValidateVerifyRequest validates the verify 2FA request
func ValidateVerifyRequest(req Verify2FARequest) error {
	if req.UserID == 0 {
		return errors.NewValidationError("user_id is required")
	}
	if req.Code == "" {
		return errors.NewValidationError("code is required")
	}
	if len(req.Code) != 6 {
		return errors.NewValidationError("code must be 6 digits")
	}
	return nil
}

// ValidateEnableRequest validates the enable 2FA request
func ValidateEnableRequest(req Enable2FARequest) error {
	if req.UserID == 0 {
		return errors.NewValidationError("user_id is required")
	}
	if req.Code == "" {
		return errors.NewValidationError("code is required")
	}
	if len(req.Code) != 6 {
		return errors.NewValidationError("code must be 6 digits")
	}
	return nil
}

// ValidateDisableRequest validates the disable 2FA request
func ValidateDisableRequest(req Disable2FARequest) error {
	if req.UserID == 0 {
		return errors.NewValidationError("user_id is required")
	}
	if req.Code == "" {
		return errors.NewValidationError("code is required")
	}
	if len(req.Code) != 6 {
		return errors.NewValidationError("code must be 6 digits")
	}
	return nil
}

// ValidateLogin2FARequest validates the 2FA login request
func ValidateLogin2FARequest(req Login2FARequest) error {
	if req.Token == "" {
		return errors.NewValidationError("token is required")
	}
	if req.Code == "" {
		return errors.NewValidationError("code is required")
	}
	if len(req.Code) != 6 {
		return errors.NewValidationError("code must be 6 digits")
	}
	return nil
}
