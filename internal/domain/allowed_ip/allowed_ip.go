package allowed_ip

import (
	"blacking-api/pkg/errors"
	"net"
	"time"

	"github.com/google/uuid"
)

// Status represents the status of an allowed IP
type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

// AllowedIP represents an allowed IP address entry
type AllowedIP struct {
	ID          string    `json:"id" db:"id"`
	IPAddress   string    `json:"ip_address" db:"ip_address"`
	Description *string   `json:"description,omitempty" db:"description"`
	Status      Status    `json:"status" db:"status"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// CreateAllowedIPRequest represents the request to create/toggle an allowed IP
type CreateAllowedIPRequest struct {
	IPAddress   string  `json:"ip_address" validate:"required,ip"`
	Description *string `json:"description,omitempty"`
}

// ToggleIPsRequest represents the request to toggle multiple IPs
type ToggleIPsRequest struct {
	IPAddresses []string `json:"ip_addresses" validate:"required,dive,ip"`
}

// IPAddressItem represents an IP address item with name
type IPAddressItem struct {
	Name string `json:"name" validate:"required,ip"`
}

// SyncIPsRequest represents the request to sync multiple IPs with object structure
type SyncIPsRequest struct {
	IPAddresses []IPAddressItem `json:"ip_addresses" validate:"required,dive"`
}

// AllowedIPResponse represents the response structure for allowed IP
type AllowedIPResponse struct {
	ID          string    `json:"id"`
	IPAddress   string    `json:"ip_address"`
	Description *string   `json:"description,omitempty"`
	Status      Status    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// NewAllowedIP creates a new allowed IP entry
func NewAllowedIP(req CreateAllowedIPRequest) (*AllowedIP, error) {
	if err := validateCreateRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	allowedIP := &AllowedIP{
		ID:          uuid.New().String(),
		IPAddress:   req.IPAddress,
		Description: req.Description,
		Status:      StatusActive,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	return allowedIP, nil
}

// ToResponse converts AllowedIP to AllowedIPResponse
func (a *AllowedIP) ToResponse() AllowedIPResponse {
	return AllowedIPResponse{
		ID:          a.ID,
		IPAddress:   a.IPAddress,
		Description: a.Description,
		Status:      a.Status,
		CreatedAt:   a.CreatedAt,
		UpdatedAt:   a.UpdatedAt,
	}
}

// Activate activates the IP address
func (a *AllowedIP) Activate() {
	a.Status = StatusActive
	a.UpdatedAt = time.Now()
}

// Deactivate deactivates the IP address
func (a *AllowedIP) Deactivate() {
	a.Status = StatusInactive
	a.UpdatedAt = time.Now()
}

// Toggle toggles the active status of the IP address
func (a *AllowedIP) Toggle() {
	if a.Status == StatusActive {
		a.Status = StatusInactive
	} else {
		a.Status = StatusActive
	}
	a.UpdatedAt = time.Now()
}

// IsActive checks if the IP address is active
func (a *AllowedIP) IsActive() bool {
	return a.Status == StatusActive
}

// validateCreateRequest validates the create allowed IP request
func validateCreateRequest(req CreateAllowedIPRequest) error {
	if req.IPAddress == "" {
		return errors.NewValidationError("ip_address is required")
	}

	// Validate IP address format
	if net.ParseIP(req.IPAddress) == nil {
		return errors.NewValidationError("invalid IP address format")
	}

	return nil
}

// IsValidIP checks if the given string is a valid IP address
func IsValidIP(ip string) bool {
	return net.ParseIP(ip) != nil
}
