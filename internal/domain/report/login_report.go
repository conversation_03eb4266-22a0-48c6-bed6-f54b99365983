package report

import (
	"time"
)

// MemberLoginReport represents member login report data
type MemberLoginReport struct {
	UserID         string    `json:"user_id" db:"user_id"`
	Username       string    `json:"username" db:"username"`
	FirstName      string    `json:"first_name" db:"first_name"`
	LastName       string    `json:"last_name" db:"last_name"`
	Phone          *string   `json:"phone" db:"phone"`
	LoginDate      time.Time `json:"login_date" db:"login_date"`
	IPAddress      string    `json:"ip_address" db:"ip_address"`
	UserAgent      string    `json:"user_agent" db:"user_agent"`
	DeviceType     *string   `json:"device_type" db:"device_type"`
	Browser        *string   `json:"browser" db:"browser"`
	BrowserVersion *string   `json:"browser_version" db:"browser_version"`
	OS             *string   `json:"os" db:"os"`
	OSVersion      *string   `json:"os_version" db:"os_version"`
	Platform       *string   `json:"platform" db:"platform"`
	IsMobile       bool      `json:"is_mobile" db:"is_mobile"`
	IsTablet       bool      `json:"is_tablet" db:"is_tablet"`
	IsDesktop      bool      `json:"is_desktop" db:"is_desktop"`
	Success        bool      `json:"success" db:"success"`
	LoginCount     int       `json:"login_count,omitempty" db:"login_count"` // For aggregated data
}

// AdminLoginReport represents admin/user login report data
type AdminLoginReport struct {
	UserID         string    `json:"user_id" db:"user_id"`
	Username       string    `json:"username" db:"username"`
	FirstName      string    `json:"first_name" db:"first_name"`
	LastName       string    `json:"last_name" db:"last_name"`
	LoginDate      time.Time `json:"login_date" db:"login_date"`
	IPAddress      string    `json:"ip_address" db:"ip_address"`
	UserAgent      string    `json:"user_agent" db:"user_agent"`
	DeviceType     *string   `json:"device_type" db:"device_type"`
	Browser        *string   `json:"browser" db:"browser"`
	BrowserVersion *string   `json:"browser_version" db:"browser_version"`
	OS             *string   `json:"os" db:"os"`
	OSVersion      *string   `json:"os_version" db:"os_version"`
	Platform       *string   `json:"platform" db:"platform"`
	IsMobile       bool      `json:"is_mobile" db:"is_mobile"`
	IsTablet       bool      `json:"is_tablet" db:"is_tablet"`
	IsDesktop      bool      `json:"is_desktop" db:"is_desktop"`
	Success        bool      `json:"success" db:"success"`
}

// LoginReportFilter represents filter parameters for login reports
type LoginReportFilter struct {
	Username    string     `json:"username,omitempty"`
	Phone       string     `json:"phone,omitempty"`
	UserID      string     `json:"user_id,omitempty"`
	StartDate   *time.Time `json:"start_date,omitempty"`
	EndDate     *time.Time `json:"end_date,omitempty"`
	IPAddress   string     `json:"ip_address,omitempty"`
	UserAgent   string     `json:"user_agent,omitempty"`
	OnlySuccess *bool      `json:"only_success,omitempty"`
	DuplicateIP bool       `json:"duplicate_ip,omitempty"` // Filter for duplicate IPs
	Limit       int        `json:"limit,omitempty"`
	Offset      int        `json:"offset,omitempty"`
}

// MemberLoginReportResponse represents the API response for member login report
type MemberLoginReportResponse struct {
	Reports         []*MemberLoginReport `json:"reports"`
	Total           int64                `json:"total"`
	Limit           int                  `json:"limit"`
	Offset          int                  `json:"offset"`
	HasDuplicateIPs bool                 `json:"has_duplicate_ips,omitempty"`
}

// AdminLoginReportResponse represents the API response for admin login report
type AdminLoginReportResponse struct {
	Reports []*AdminLoginReport `json:"reports"`
	Total   int64               `json:"total"`
	Limit   int                 `json:"limit"`
	Offset  int                 `json:"offset"`
}

// DuplicateIPReport represents IPs that have multiple login attempts
type DuplicateIPReport struct {
	IPAddress    string                    `json:"ip_address" db:"ip_address"`
	UserCount    int                       `json:"user_count" db:"user_count"`
	LoginCount   int                       `json:"login_count" db:"login_count"`
	LastLogin    time.Time                 `json:"last_login" db:"last_login"`
	Usernames    []string                  `json:"usernames,omitempty"`
	LoginDetails []*DuplicateIPLoginDetail `json:"login_details,omitempty"`
}

// DuplicateIPLoginDetail represents individual login attempts for duplicate IP
type DuplicateIPLoginDetail struct {
	UserID    string    `json:"user_id" db:"user_id"`
	Username  string    `json:"username" db:"username"`
	Phone     *string   `json:"phone" db:"phone"`
	LoginDate time.Time `json:"login_date" db:"login_date"`
	UserAgent string    `json:"user_agent" db:"user_agent"`
	Success   bool      `json:"success" db:"success"`
}

// GetDefaultDateRange returns today's date range (start and end of day)
func GetDefaultDateRange() (time.Time, time.Time) {
	now := time.Now()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, time.Local)
	return startOfDay, endOfDay
}

// ValidateFilter validates and sets default values for the filter
func (f *LoginReportFilter) ValidateAndSetDefaults() {
	// Set default date range to today if not provided
	if f.StartDate == nil || f.EndDate == nil {
		start, end := GetDefaultDateRange()
		if f.StartDate == nil {
			f.StartDate = &start
		}
		if f.EndDate == nil {
			f.EndDate = &end
		}
	}

	// Set default pagination
	if f.Limit <= 0 {
		f.Limit = 50
	}
	if f.Limit > 1000 {
		f.Limit = 1000 // Max limit
	}
	if f.Offset < 0 {
		f.Offset = 0
	}
}

// CSVHeader returns CSV headers for member login report
func (m *MemberLoginReport) CSVHeader() []string {
	return []string{
		"User ID",
		"Username",
		"First Name",
		"Last Name",
		"Phone",
		"Login Date",
		"IP Address",
		"User Agent",
		"Device Type",
		"Browser",
		"Browser Version",
		"OS",
		"OS Version",
		"Platform",
		"Is Mobile",
		"Is Tablet",
		"Is Desktop",
		"Success",
	}
}

// CSVRow returns CSV row data for member login report
func (m *MemberLoginReport) CSVRow() []string {
	phone := ""
	if m.Phone != nil {
		phone = *m.Phone
	}

	deviceType := ""
	if m.DeviceType != nil {
		deviceType = *m.DeviceType
	}

	browser := ""
	if m.Browser != nil {
		browser = *m.Browser
	}

	browserVersion := ""
	if m.BrowserVersion != nil {
		browserVersion = *m.BrowserVersion
	}

	os := ""
	if m.OS != nil {
		os = *m.OS
	}

	osVersion := ""
	if m.OSVersion != nil {
		osVersion = *m.OSVersion
	}

	platform := ""
	if m.Platform != nil {
		platform = *m.Platform
	}

	success := "Failed"
	if m.Success {
		success = "Success"
	}

	return []string{
		m.UserID,
		m.Username,
		m.FirstName,
		m.LastName,
		phone,
		m.LoginDate.Format("2006-01-02 15:04:05"),
		m.IPAddress,
		m.UserAgent,
		deviceType,
		browser,
		browserVersion,
		os,
		osVersion,
		platform,
		boolToString(m.IsMobile),
		boolToString(m.IsTablet),
		boolToString(m.IsDesktop),
		success,
	}
}

// CSVHeader returns CSV headers for admin login report
func (a *AdminLoginReport) CSVHeader() []string {
	return []string{
		"User ID",
		"Username",
		"First Name",
		"Last Name",
		"Login Date",
		"IP Address",
		"User Agent",
		"Device Type",
		"Browser",
		"Browser Version",
		"OS",
		"OS Version",
		"Platform",
		"Is Mobile",
		"Is Tablet",
		"Is Desktop",
		"Success",
	}
}

// CSVRow returns CSV row data for admin login report
func (a *AdminLoginReport) CSVRow() []string {
	deviceType := ""
	if a.DeviceType != nil {
		deviceType = *a.DeviceType
	}

	browser := ""
	if a.Browser != nil {
		browser = *a.Browser
	}

	browserVersion := ""
	if a.BrowserVersion != nil {
		browserVersion = *a.BrowserVersion
	}

	os := ""
	if a.OS != nil {
		os = *a.OS
	}

	osVersion := ""
	if a.OSVersion != nil {
		osVersion = *a.OSVersion
	}

	platform := ""
	if a.Platform != nil {
		platform = *a.Platform
	}

	success := "Failed"
	if a.Success {
		success = "Success"
	}

	return []string{
		a.UserID,
		a.Username,
		a.FirstName,
		a.LastName,
		a.LoginDate.Format("2006-01-02 15:04:05"),
		a.IPAddress,
		a.UserAgent,
		deviceType,
		browser,
		browserVersion,
		os,
		osVersion,
		platform,
		boolToString(a.IsMobile),
		boolToString(a.IsTablet),
		boolToString(a.IsDesktop),
		success,
	}
}

// boolToString converts boolean to string for CSV export
func boolToString(b bool) string {
	if b {
		return "Yes"
	}
	return "No"
}
