package payment_method

import "time"

type PaymentMethod struct {
	ID             int64     `json:"id"`
	Name           string    `json:"name"`
	MiniminDeposit float64   `json:"minimumDeposit"`
	MaximinDeposit float64   `json:"maximumDeposit"`
	Fee            float64   `json:"fee"`
	IsFee          bool      `json:"isFee"`
	Active         bool      `json:"active"`
	IsLobby        bool      `json:"isLobby"`
	Inactive       bool      `json:"inactive" default:"false"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

type PaymentMethodRequest struct {
	Name           string  `json:"name" validate:"required,min=1,max=255"`
	MiniminDeposit float64 `json:"minimumDeposit" validate:"required,gt=0"`
	MaximinDeposit float64 `json:"maximumDeposit" validate:"required,gt=0"`
	Fee            float64 `json:"fee" validate:"required,gt=0"`
	IsFee          bool    `json:"isFee" default:"false"`
	Active         bool    `json:"active" default:"true"`
	IsL<PERSON>by        bool    `json:"isLobby" default:"true"`
}

type UpdateStatusRequest struct {
	Name   string `form:"name" validate:"required,min=1,max=255"`
	Status bool   `form:"status" default:"false"`
}

type PaymentMethodResponse struct {
	ID             int64     `json:"id"`
	Name           string    `json:"name"`
	MiniminDeposit float64   `json:"minimumDeposit"`
	MaximinDeposit float64   `json:"maximumDeposit"`
	Fee            float64   `json:"fee"`
	IsFee          bool      `json:"isFee"`
	Active         bool      `json:"active"`
	IsLobby        bool      `json:"isLobby"`
	CreatedAt      time.Time `json:"createdAt"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

func (pm *PaymentMethod) ToResponse() *PaymentMethodResponse {
	return &PaymentMethodResponse{
		ID:             pm.ID,
		Name:           pm.Name,
		MiniminDeposit: pm.MiniminDeposit,
		MaximinDeposit: pm.MaximinDeposit,
		Fee:            pm.Fee,
		IsFee:          pm.IsFee,
		Active:         pm.Active,
		IsLobby:        pm.IsLobby,
		CreatedAt:      pm.CreatedAt,
		UpdatedAt:      pm.UpdatedAt,
	}
}
