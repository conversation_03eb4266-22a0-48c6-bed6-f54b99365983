package commission_group

import (
	"time"

	"blacking-api/pkg/errors"
)

// Status represents the commission group status
type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

// CommissionGroup represents a commission group
type CommissionGroup struct {
	ID              int       `json:"id"`
	Name            string    `json:"name"`             // ชื่อกลุ่มคอมมิชชั่น
	IsDefault       bool      `json:"is_default"`       // รายการสามารถ enable ได้รายการเดียว
	TurnoverSports  float64   `json:"turnover_sports"`  // 0 - 100%
	TurnoverCasino  float64   `json:"turnover_casino"`  // 0 - 100%
	TurnoverFishing float64   `json:"turnover_fishing"` // 0 - 100%
	TurnoverSlot    float64   `json:"turnover_slot"`    // 0 - 100%
	TurnoverLottery float64   `json:"turnover_lottery"` // 0 - 100%
	TurnoverCard    float64   `json:"turnover_card"`    // 0 - 100%
	TurnoverOther   float64   `json:"turnover_other"`   // 0 - 100%
	Status          Status    `json:"status"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CreateCommissionGroupRequest for creating commission group
type CreateCommissionGroupRequest struct {
	Name            string  `json:"name" validate:"required,min=1,max=100"`
	IsDefault       bool    `json:"is_default"`
	TurnoverSports  float64 `json:"turnover_sports" validate:"min=0,max=100"`
	TurnoverCasino  float64 `json:"turnover_casino" validate:"min=0,max=100"`
	TurnoverFishing float64 `json:"turnover_fishing" validate:"min=0,max=100"`
	TurnoverSlot    float64 `json:"turnover_slot" validate:"min=0,max=100"`
	TurnoverLottery float64 `json:"turnover_lottery" validate:"min=0,max=100"`
	TurnoverCard    float64 `json:"turnover_card" validate:"min=0,max=100"`
	TurnoverOther   float64 `json:"turnover_other" validate:"min=0,max=100"`
}

// UpdateCommissionGroupRequest for updating commission group
type UpdateCommissionGroupRequest struct {
	Name            string  `json:"name" validate:"required,min=1,max=100"`
	IsDefault       bool    `json:"is_default"`
	TurnoverSports  float64 `json:"turnover_sports" validate:"min=0,max=100"`
	TurnoverCasino  float64 `json:"turnover_casino" validate:"min=0,max=100"`
	TurnoverFishing float64 `json:"turnover_fishing" validate:"min=0,max=100"`
	TurnoverSlot    float64 `json:"turnover_slot" validate:"min=0,max=100"`
	TurnoverLottery float64 `json:"turnover_lottery" validate:"min=0,max=100"`
	TurnoverCard    float64 `json:"turnover_card" validate:"min=0,max=100"`
	TurnoverOther   float64 `json:"turnover_other" validate:"min=0,max=100"`
}

// CommissionGroupResponse represents commission group API response
type CommissionGroupResponse struct {
	ID              int       `json:"id"`
	Name            string    `json:"name"`
	IsDefault       bool      `json:"is_default"`
	TurnoverSports  float64   `json:"turnover_sports"`
	TurnoverCasino  float64   `json:"turnover_casino"`
	TurnoverFishing float64   `json:"turnover_fishing"`
	TurnoverSlot    float64   `json:"turnover_slot"`
	TurnoverLottery float64   `json:"turnover_lottery"`
	TurnoverCard    float64   `json:"turnover_card"`
	TurnoverOther   float64   `json:"turnover_other"`
	Status          Status    `json:"status"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CommissionGroupDropdownResponse represents commission group dropdown API response
type CommissionGroupDropdownResponse struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// NewCommissionGroup creates a new commission group
func NewCommissionGroup(req CreateCommissionGroupRequest) (*CommissionGroup, error) {
	if err := validateCommissionGroupRequest(req); err != nil {
		return nil, err
	}

	now := time.Now()
	commissionGroup := &CommissionGroup{
		// ID will be generated by database
		Name:            req.Name,
		IsDefault:       req.IsDefault,
		TurnoverSports:  req.TurnoverSports,
		TurnoverCasino:  req.TurnoverCasino,
		TurnoverFishing: req.TurnoverFishing,
		TurnoverSlot:    req.TurnoverSlot,
		TurnoverLottery: req.TurnoverLottery,
		TurnoverCard:    req.TurnoverCard,
		TurnoverOther:   req.TurnoverOther,
		Status:          StatusActive,
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	return commissionGroup, nil
}

// Update updates commission group information
func (cg *CommissionGroup) Update(req UpdateCommissionGroupRequest) error {
	if err := validateUpdateCommissionGroupRequest(req); err != nil {
		return err
	}

	cg.Name = req.Name
	cg.IsDefault = req.IsDefault
	cg.TurnoverSports = req.TurnoverSports
	cg.TurnoverCasino = req.TurnoverCasino
	cg.TurnoverFishing = req.TurnoverFishing
	cg.TurnoverSlot = req.TurnoverSlot
	cg.TurnoverLottery = req.TurnoverLottery
	cg.TurnoverCard = req.TurnoverCard
	cg.TurnoverOther = req.TurnoverOther
	cg.UpdatedAt = time.Now()

	return nil
}

// ToResponse converts commission group to response format
func (cg *CommissionGroup) ToResponse() CommissionGroupResponse {
	return CommissionGroupResponse{
		ID:              cg.ID,
		Name:            cg.Name,
		IsDefault:       cg.IsDefault,
		TurnoverSports:  cg.TurnoverSports,
		TurnoverCasino:  cg.TurnoverCasino,
		TurnoverFishing: cg.TurnoverFishing,
		TurnoverSlot:    cg.TurnoverSlot,
		TurnoverLottery: cg.TurnoverLottery,
		TurnoverCard:    cg.TurnoverCard,
		TurnoverOther:   cg.TurnoverOther,
		Status:          cg.Status,
		CreatedAt:       cg.CreatedAt,
		UpdatedAt:       cg.UpdatedAt,
	}
}

// validateCommissionGroupRequest validates commission group creation request
func validateCommissionGroupRequest(req CreateCommissionGroupRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}

	// Validate turnover percentages (0-100)
	turnovers := map[string]float64{
		"turnover_sports":  req.TurnoverSports,
		"turnover_casino":  req.TurnoverCasino,
		"turnover_fishing": req.TurnoverFishing,
		"turnover_slot":    req.TurnoverSlot,
		"turnover_lottery": req.TurnoverLottery,
		"turnover_card":    req.TurnoverCard,
		"turnover_other":   req.TurnoverOther,
	}

	for field, value := range turnovers {
		if value < 0 || value > 100 {
			return errors.NewValidationError(field + " must be between 0 and 100")
		}
	}

	return nil
}

// validateUpdateCommissionGroupRequest validates commission group update request
func validateUpdateCommissionGroupRequest(req UpdateCommissionGroupRequest) error {
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}

	// Validate turnover percentages (0-100)
	turnovers := map[string]float64{
		"turnover_sports":  req.TurnoverSports,
		"turnover_casino":  req.TurnoverCasino,
		"turnover_fishing": req.TurnoverFishing,
		"turnover_slot":    req.TurnoverSlot,
		"turnover_lottery": req.TurnoverLottery,
		"turnover_card":    req.TurnoverCard,
		"turnover_other":   req.TurnoverOther,
	}

	for field, value := range turnovers {
		if value < 0 || value > 100 {
			return errors.NewValidationError(field + " must be between 0 and 100")
		}
	}

	return nil
}
