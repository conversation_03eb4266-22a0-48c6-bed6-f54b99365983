package holding_account

import (
	"blacking-api/internal/domain/request"
	"time"
)

type HoldingAccount struct {
	ID                 int64     `json:"id" db:"id"`
	AccountName        string    `json:"accountName" db:"account_name"`
	AccountNameDisplay string    `json:"accountName_display" db:"account_name_display"`
	AccountNumber      string    `json:"accountNumber" db:"account_number"`
	BankingID          int64     `json:"bankingId" db:"banking_id"`
	BankingName        string    `json:"bankingName" db:"banking_name"`
	BankingImageUrl    string    `json:"bankingImageUrl" db:"banking_image_url"`
	PhoneNumber        string    `json:"phoneNumber" db:"phone_number"`
	Active             bool      `json:"active" db:"active"`
	Inactive           bool      `json:"inactive" db:"inactive"`
	CreatedAt          time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt          time.Time `json:"updatedAt" db:"updated_at"`
}

type HoldingAccountRequest struct {
	AccountName        string `json:"accountName" validate:"required,min=1,max=255"`
	AccountNameDisplay string `json:"accountNameDisplay" validate:"required,min=1,max=255"`
	AccountNumber      string `json:"accountNumber" validate:"required,min=13,max=13"`
	BankingID          uint   `json:"bankingId" validate:"required,min=1"`
	PhoneNumber        string `json:"phoneNumber" validate:"omitempty,min=10,max=10"`
}

type HoldingAccountListResponse struct {
	ID                 int64     `json:"id" db:"id"`
	AccountName        string    `json:"accountName"`
	AccountNameDisplay string    `json:"accountName_display"`
	AccountNumber      string    `json:"accountNumber"`
	BankingID          int64     `json:"bankingId"`
	BankingName        string    `json:"bankingName"`
	BankingImageUrl    string    `json:"bankingImageUrl"`
	PhoneNumber        string    `json:"phoneNumber"`
	Active             bool      `json:"active"`
	Inactive           bool      `json:"inactive"`
	CreatedAt          time.Time `json:"createdAt"`
	UpdatedAt          time.Time `json:"updatedAt"`
}

type HoldingAccountByIdResponse struct {
	ID                 int64  `json:"id"`
	AccountName        string `json:"accountName"`
	AccountNameDisplay string `json:"accountName_display"`
	AccountNumber      string `json:"accountNumber"`
	BankingID          int64  `json:"bankingId"`
	PhoneNumber        string `json:"phoneNumber"`
}

type HoldingAccountSearchRequest struct {
	request.RequestPagination
	AccountName *string `form:"accountName"`
	BankingID   *int64  `form:"banking"`
}

func (h *HoldingAccount) ToListResponse() *HoldingAccountListResponse {
	return &HoldingAccountListResponse{
		ID:                 h.ID,
		AccountName:        h.AccountName,
		AccountNameDisplay: h.AccountNameDisplay,
		AccountNumber:      h.AccountNumber,
		BankingID:          h.BankingID,
		BankingName:        h.BankingName,
		BankingImageUrl:    h.BankingImageUrl,
		PhoneNumber:        h.PhoneNumber,
		Active:             h.Active,
		Inactive:           h.Inactive,
		CreatedAt:          h.CreatedAt,
		UpdatedAt:          h.UpdatedAt,
	}
}

func (h *HoldingAccount) ToByIdResponse() *HoldingAccountByIdResponse {
	return &HoldingAccountByIdResponse{
		ID:                 h.ID,
		AccountName:        h.AccountName,
		AccountNameDisplay: h.AccountNameDisplay,
		AccountNumber:      h.AccountNumber,
		BankingID:          h.BankingID,
		PhoneNumber:        h.PhoneNumber,
	}
}
