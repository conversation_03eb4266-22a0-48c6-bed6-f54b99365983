package member_group

import (
	"time"

	"blacking-api/pkg/errors"
)

// Status represents the member group status
type Status string

const (
	StatusActive   Status = "active"
	StatusInactive Status = "inactive"
)

// DepositTurnoverType represents deposit turnover calculation type
type DepositTurnoverType string

const (
	DepositTurnoverTypeNil        DepositTurnoverType = "nil"
	DepositTurnoverTypeMultiplier DepositTurnoverType = "multiplier"
	DepositTurnoverTypePercentage DepositTurnoverType = "percentage"
)

// DepositTurnoverReleaseType represents deposit turnover release type
type DepositTurnoverReleaseType string

const (
	DepositTurnoverReleaseTypeFixed      DepositTurnoverReleaseType = "fixed"
	DepositTurnoverReleaseTypePercentage DepositTurnoverReleaseType = "percentage"
)

// MemberGroup represents a member group
type MemberGroup struct {
	ID                           int                        `json:"id"`
	Code                         string                     `json:"code"`                                      // รหัสกลุ่ม (ข้อความ)
	Name                         string                     `json:"name"`                                      // ชื่อกลุ่ม
	MemberGroupTypeID            *int                       `json:"member_group_type_id"`                      // ประเภทกลุ่ม (nullable สำหรับ VIP)
	MinDeposit                   float64                    `json:"min_deposit"`                               // เงินฝากขั้นต่ำ
	MinWithdraw                  float64                    `json:"min_withdraw"`                              // เงินถอนขั้นต่ำ
	MaxDeposit                   float64                    `json:"max_deposit"`                               // เงินฝากสูงสุด
	IsDefault                    bool                       `json:"is_default"`                                // ค่าเริ่มต้น
	IsVIP                        bool                       `json:"is_vip"`                                    // VIP (ไม่เก็บข้อมูลบางส่วน)
	DailyWithdrawLimit           *int                       `json:"daily_withdraw_limit"`                      // จำกัดจำนวนครั้งถอนต่อวัน (nullable สำหรับ VIP)
	DailyWithdrawAmountLimit     *float64                   `json:"daily_withdraw_amount_limit"`               // จำกัดจำนวนเงินถอนต่อวัน (nullable สำหรับ VIP)
	CommissionGroupID            int                        `json:"commission_group_id"`                       // กลุ่มคอมมิชชั่น
	CommissionGroupName          *string                    `json:"commission_group_name,omitempty"`           // ชื่อกลุ่มคอมมิชชั่น (joined)
	MemberGroupTypeShowInLobby   *bool                      `json:"member_group_type_show_in_lobby,omitempty"` // แสดงใน lobby (joined)
	DepositTurnoverType          DepositTurnoverType        `json:"deposit_turnover_type"`                     // ยอดฝากติดเทิร์น_ประเภท
	DepositTurnoverAmount        float64                    `json:"deposit_turnover_amount"`                   // ยอดฝากติดเทิร์น_จำนวน
	DepositTurnoverReleaseType   DepositTurnoverReleaseType `json:"deposit_turnover_release_type"`             // ยอดฝากติดเทิร์น_หลุดโปร
	DepositTurnoverReleaseAmount float64                    `json:"deposit_turnover_release_amount"`           // ยอดฝากติดเทิร์น_หลุดโปร_จำนวน
	CalculateMinDeposit          bool                       `json:"calculate_min_deposit"`                     // ยอดฝากคำนวนฝากขั้นต่ำ
	Image                        *string                    `json:"image"`                                     // รูปภาพ 1000x333
	Status                       Status                     `json:"status"`
	CreatedAt                    time.Time                  `json:"created_at"`
	UpdatedAt                    time.Time                  `json:"updated_at"`

	// Related data
	WithdrawalApprovals []MemberGroupWithdrawalApproval `json:"withdrawal_approvals,omitempty"`
	DepositAccounts     []MemberGroupDepositAccount     `json:"deposit_accounts,omitempty"`
}

// MemberGroupWithdrawalApproval represents withdrawal approval settings
type MemberGroupWithdrawalApproval struct {
	ID            int       `json:"id"`
	MemberGroupID int       `json:"member_group_id"`
	MinAmount     float64   `json:"min_amount"`   // จำนวนเงินถอนขั้นต่ำ
	MaxAmount     float64   `json:"max_amount"`   // จำนวนเงินถอนสูงสุด
	UserRoleID    int       `json:"user_role_id"` // บทบาทผู้อนุมัติ
	OrderIndex    int       `json:"order_index"`  // ลำดับการอนุมัติ
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// MemberGroupDepositAccount represents deposit account assignment
type MemberGroupDepositAccount struct {
	ID               int       `json:"id"`
	MemberGroupID    int       `json:"member_group_id"`
	AccountDepositID int       `json:"account_deposit_id"` // บัญชีฝาก
	CreatedAt        time.Time `json:"created_at"`
}

// CreateMemberGroupRequest for creating member group
type CreateMemberGroupRequest struct {
	Code                         string                            `json:"code" validate:"required,min=1,max=20"`
	Name                         string                            `json:"name" validate:"required,min=1,max=100"`
	MemberGroupTypeID            *int                              `json:"member_group_type_id"`
	MinDeposit                   float64                           `json:"min_deposit" validate:"min=0"`
	MinWithdraw                  float64                           `json:"min_withdraw" validate:"min=0"`
	MaxDeposit                   float64                           `json:"max_deposit" validate:"min=0"`
	IsDefault                    bool                              `json:"is_default"`
	IsVIP                        bool                              `json:"is_vip"`
	DailyWithdrawLimit           *int                              `json:"daily_withdraw_limit"`
	DailyWithdrawAmountLimit     *float64                          `json:"daily_withdraw_amount_limit"`
	CommissionGroupID            int                               `json:"commission_group_id" validate:"required"`
	DepositTurnoverType          DepositTurnoverType               `json:"deposit_turnover_type" validate:"required"`
	DepositTurnoverAmount        float64                           `json:"deposit_turnover_amount" validate:"min=0"`
	DepositTurnoverReleaseType   DepositTurnoverReleaseType        `json:"deposit_turnover_release_type" validate:"required"`
	DepositTurnoverReleaseAmount float64                           `json:"deposit_turnover_release_amount" validate:"min=0"`
	CalculateMinDeposit          bool                              `json:"calculate_min_deposit"`
	Image                        *string                           `json:"image"`
	WithdrawalApprovals          []CreateWithdrawalApprovalRequest `json:"withdrawal_approvals"`
	DepositAccountIDs            []int                             `json:"deposit_account_ids"`
}

// FileUploadResponse represents file upload response
type FileUploadResponse struct {
	FileUrl string `json:"file_url"`
}

// DeleteFileRequest represents delete file request
type DeleteFileRequest struct {
	FileUrl string `json:"file_url" validate:"required"`
}

// MemberGroupDropdownResponse represents member group dropdown response
type MemberGroupDropdownResponse struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// CreateWithdrawalApprovalRequest for creating withdrawal approval
type CreateWithdrawalApprovalRequest struct {
	MinAmount  float64 `json:"min_amount" validate:"min=0"`
	MaxAmount  float64 `json:"max_amount" validate:"min=0"`
	UserRoleID int     `json:"user_role_id" validate:"required"`
}

// UpdateMemberGroupRequest for updating member group
type UpdateMemberGroupRequest struct {
	Code                         string                            `json:"code" validate:"required,min=1,max=20"`
	Name                         string                            `json:"name" validate:"required,min=1,max=100"`
	MemberGroupTypeID            *int                              `json:"member_group_type_id"`
	MinDeposit                   float64                           `json:"min_deposit" validate:"min=0"`
	MinWithdraw                  float64                           `json:"min_withdraw" validate:"min=0"`
	MaxDeposit                   float64                           `json:"max_deposit" validate:"min=0"`
	IsDefault                    bool                              `json:"is_default"`
	IsVIP                        bool                              `json:"is_vip"`
	DailyWithdrawLimit           *int                              `json:"daily_withdraw_limit"`
	DailyWithdrawAmountLimit     *float64                          `json:"daily_withdraw_amount_limit"`
	CommissionGroupID            int                               `json:"commission_group_id" validate:"required"`
	DepositTurnoverType          DepositTurnoverType               `json:"deposit_turnover_type" validate:"required"`
	DepositTurnoverAmount        float64                           `json:"deposit_turnover_amount" validate:"min=0"`
	DepositTurnoverReleaseType   DepositTurnoverReleaseType        `json:"deposit_turnover_release_type" validate:"required"`
	DepositTurnoverReleaseAmount float64                           `json:"deposit_turnover_release_amount" validate:"min=0"`
	CalculateMinDeposit          bool                              `json:"calculate_min_deposit"`
	Image                        *string                           `json:"image"`
	ImageDelete                  bool                              `json:"image_delete"`
	ImageProvided                bool                              `json:"-"` // Internal flag to track if image was provided
	WithdrawalApprovals          []CreateWithdrawalApprovalRequest `json:"withdrawal_approvals"`
	DepositAccountIDs            []int                             `json:"deposit_account_ids"`
}

// MemberGroupResponse represents member group API response
type MemberGroupResponse struct {
	ID                           int                             `json:"id"`
	Code                         string                          `json:"code"`
	Name                         string                          `json:"name"`
	MemberGroupTypeID            *int                            `json:"member_group_type_id"`
	MinDeposit                   float64                         `json:"min_deposit"`
	MinWithdraw                  float64                         `json:"min_withdraw"`
	MaxDeposit                   float64                         `json:"max_deposit"`
	IsDefault                    bool                            `json:"is_default"`
	IsVIP                        bool                            `json:"is_vip"`
	DailyWithdrawLimit           *int                            `json:"daily_withdraw_limit"`
	DailyWithdrawAmountLimit     *float64                        `json:"daily_withdraw_amount_limit"`
	CommissionGroupID            int                             `json:"commission_group_id"`
	CommissionGroupName          *string                         `json:"commission_group_name,omitempty"`
	MemberGroupTypeShowInLobby   *bool                           `json:"member_group_type_show_in_lobby,omitempty"`
	DepositTurnoverType          DepositTurnoverType             `json:"deposit_turnover_type"`
	DepositTurnoverAmount        float64                         `json:"deposit_turnover_amount"`
	DepositTurnoverReleaseType   DepositTurnoverReleaseType      `json:"deposit_turnover_release_type"`
	DepositTurnoverReleaseAmount float64                         `json:"deposit_turnover_release_amount"`
	CalculateMinDeposit          bool                            `json:"calculate_min_deposit"`
	Image                        *string                         `json:"image"`
	Status                       Status                          `json:"status"`
	CreatedAt                    time.Time                       `json:"created_at"`
	UpdatedAt                    time.Time                       `json:"updated_at"`
	WithdrawalApprovals          []MemberGroupWithdrawalApproval `json:"withdrawal_approvals,omitempty"`
	DepositAccounts              []MemberGroupDepositAccount     `json:"deposit_accounts,omitempty"`
}

// NewMemberGroup creates a new member group
func NewMemberGroup(req CreateMemberGroupRequest) (*MemberGroup, error) {
	if err := validateMemberGroupRequest(&req); err != nil {
		return nil, err
	}

	now := time.Now()
	memberGroup := &MemberGroup{
		// ID will be generated by database
		Code:                         req.Code,
		Name:                         req.Name,
		MemberGroupTypeID:            req.MemberGroupTypeID,
		MinDeposit:                   req.MinDeposit,
		MinWithdraw:                  req.MinWithdraw,
		MaxDeposit:                   req.MaxDeposit,
		IsDefault:                    req.IsDefault,
		IsVIP:                        req.IsVIP,
		DailyWithdrawLimit:           req.DailyWithdrawLimit,
		DailyWithdrawAmountLimit:     req.DailyWithdrawAmountLimit,
		CommissionGroupID:            req.CommissionGroupID,
		DepositTurnoverType:          req.DepositTurnoverType,
		DepositTurnoverAmount:        req.DepositTurnoverAmount,
		DepositTurnoverReleaseType:   req.DepositTurnoverReleaseType,
		DepositTurnoverReleaseAmount: req.DepositTurnoverReleaseAmount,
		CalculateMinDeposit:          req.CalculateMinDeposit,
		Image:                        req.Image,
		Status:                       StatusActive,
		CreatedAt:                    now,
		UpdatedAt:                    now,
	}

	return memberGroup, nil
}

// Update updates member group information
func (mg *MemberGroup) Update(req UpdateMemberGroupRequest) error {
	if err := validateUpdateMemberGroupRequest(&req); err != nil {
		return err
	}

	mg.Code = req.Code
	mg.Name = req.Name
	mg.MemberGroupTypeID = req.MemberGroupTypeID
	mg.MinDeposit = req.MinDeposit
	mg.MinWithdraw = req.MinWithdraw
	mg.MaxDeposit = req.MaxDeposit
	mg.IsDefault = req.IsDefault
	mg.IsVIP = req.IsVIP
	mg.DailyWithdrawLimit = req.DailyWithdrawLimit
	mg.DailyWithdrawAmountLimit = req.DailyWithdrawAmountLimit
	mg.CommissionGroupID = req.CommissionGroupID
	mg.DepositTurnoverType = req.DepositTurnoverType
	mg.DepositTurnoverAmount = req.DepositTurnoverAmount
	mg.DepositTurnoverReleaseType = req.DepositTurnoverReleaseType
	mg.DepositTurnoverReleaseAmount = req.DepositTurnoverReleaseAmount
	mg.CalculateMinDeposit = req.CalculateMinDeposit

	// Only update Image if ImageDelete is true or if new Image is provided
	if req.ImageDelete {
		mg.Image = nil
	} else if req.Image != nil {
		mg.Image = req.Image
	}
	// If neither ImageDelete nor Image is provided, keep existing image

	mg.UpdatedAt = time.Now()

	return nil
}

// ToResponse converts member group to response format
func (mg *MemberGroup) ToResponse() MemberGroupResponse {
	return MemberGroupResponse{
		ID:                           mg.ID,
		Code:                         mg.Code,
		Name:                         mg.Name,
		MemberGroupTypeID:            mg.MemberGroupTypeID,
		MinDeposit:                   mg.MinDeposit,
		MinWithdraw:                  mg.MinWithdraw,
		MaxDeposit:                   mg.MaxDeposit,
		IsDefault:                    mg.IsDefault,
		IsVIP:                        mg.IsVIP,
		DailyWithdrawLimit:           mg.DailyWithdrawLimit,
		DailyWithdrawAmountLimit:     mg.DailyWithdrawAmountLimit,
		CommissionGroupID:            mg.CommissionGroupID,
		CommissionGroupName:          mg.CommissionGroupName,
		MemberGroupTypeShowInLobby:   mg.MemberGroupTypeShowInLobby,
		DepositTurnoverType:          mg.DepositTurnoverType,
		DepositTurnoverAmount:        mg.DepositTurnoverAmount,
		DepositTurnoverReleaseType:   mg.DepositTurnoverReleaseType,
		DepositTurnoverReleaseAmount: mg.DepositTurnoverReleaseAmount,
		CalculateMinDeposit:          mg.CalculateMinDeposit,
		Image:                        mg.Image,
		Status:                       mg.Status,
		CreatedAt:                    mg.CreatedAt,
		UpdatedAt:                    mg.UpdatedAt,
		WithdrawalApprovals:          mg.WithdrawalApprovals,
		DepositAccounts:              mg.DepositAccounts,
	}
}

// validateMemberGroupRequest validates member group creation request and auto-corrects VIP conflicts
func validateMemberGroupRequest(req *CreateMemberGroupRequest) error {
	if req.Code == "" {
		return errors.NewValidationError("code is required")
	}
	if req.Name == "" {
		return errors.NewValidationError("name is required")
	}
	if req.CommissionGroupID == 0 {
		return errors.NewValidationError("commission_group_id is required")
	}
	if req.MinDeposit < 0 {
		return errors.NewValidationError("min_deposit must be >= 0")
	}
	if req.MinWithdraw < 0 {
		return errors.NewValidationError("min_withdraw must be >= 0")
	}
	if req.MaxDeposit < 0 {
		return errors.NewValidationError("max_deposit must be >= 0")
	}
	if req.MaxDeposit > 0 && req.MinDeposit > req.MaxDeposit {
		return errors.NewValidationError("min_deposit cannot be greater than max_deposit")
	}

	// VIP validation - auto-correct conflicting fields instead of returning errors
	if req.IsVIP {
		// Reset fields that VIP groups cannot have
		if req.MemberGroupTypeID != nil {
			req.MemberGroupTypeID = nil
		}
		if req.DailyWithdrawLimit != nil {
			req.DailyWithdrawLimit = nil
		}
		if req.DailyWithdrawAmountLimit != nil {
			req.DailyWithdrawAmountLimit = nil
		}
	} else {
		if req.MemberGroupTypeID == nil {
			return errors.NewValidationError("non-VIP groups must have member_group_type_id")
		}
	}

	// Validate deposit turnover type
	validTurnoverTypes := map[DepositTurnoverType]bool{
		DepositTurnoverTypeNil:        true,
		DepositTurnoverTypeMultiplier: true,
		DepositTurnoverTypePercentage: true,
	}
	if !validTurnoverTypes[req.DepositTurnoverType] {
		return errors.NewValidationError("invalid deposit_turnover_type")
	}

	// Validate deposit turnover release type
	validReleaseTypes := map[DepositTurnoverReleaseType]bool{
		DepositTurnoverReleaseTypeFixed:      true,
		DepositTurnoverReleaseTypePercentage: true,
	}
	if !validReleaseTypes[req.DepositTurnoverReleaseType] {
		return errors.NewValidationError("invalid deposit_turnover_release_type")
	}

	// Validate withdrawal approvals
	for i, approval := range req.WithdrawalApprovals {
		if approval.MinAmount < 0 {
			return errors.NewValidationError("withdrawal approval min_amount must be >= 0")
		}
		if approval.MaxAmount < 0 {
			return errors.NewValidationError("withdrawal approval max_amount must be >= 0")
		}
		if approval.MinAmount > approval.MaxAmount {
			return errors.NewValidationError("withdrawal approval min_amount cannot be greater than max_amount")
		}
		if approval.UserRoleID == 0 {
			return errors.NewValidationError("withdrawal approval user_role_id is required")
		}

		// Check for overlapping ranges
		for j := i + 1; j < len(req.WithdrawalApprovals); j++ {
			other := req.WithdrawalApprovals[j]
			if approval.MinAmount <= other.MaxAmount && approval.MaxAmount >= other.MinAmount {
				return errors.NewValidationError("withdrawal approval ranges cannot overlap")
			}
		}
	}

	return nil
}

// validateUpdateMemberGroupRequest validates member group update request
func validateUpdateMemberGroupRequest(req *UpdateMemberGroupRequest) error {
	createReq := CreateMemberGroupRequest{
		Code:                         req.Code,
		Name:                         req.Name,
		MemberGroupTypeID:            req.MemberGroupTypeID,
		MinDeposit:                   req.MinDeposit,
		MinWithdraw:                  req.MinWithdraw,
		MaxDeposit:                   req.MaxDeposit,
		IsDefault:                    req.IsDefault,
		IsVIP:                        req.IsVIP,
		DailyWithdrawLimit:           req.DailyWithdrawLimit,
		DailyWithdrawAmountLimit:     req.DailyWithdrawAmountLimit,
		CommissionGroupID:            req.CommissionGroupID,
		DepositTurnoverType:          req.DepositTurnoverType,
		DepositTurnoverAmount:        req.DepositTurnoverAmount,
		DepositTurnoverReleaseType:   req.DepositTurnoverReleaseType,
		DepositTurnoverReleaseAmount: req.DepositTurnoverReleaseAmount,
		CalculateMinDeposit:          req.CalculateMinDeposit,
		Image:                        req.Image,
		WithdrawalApprovals:          req.WithdrawalApprovals,
		DepositAccountIDs:            req.DepositAccountIDs,
	}

	if err := validateMemberGroupRequest(&createReq); err != nil {
		return err
	}

	// Copy back the auto-corrected values
	req.MemberGroupTypeID = createReq.MemberGroupTypeID
	req.DailyWithdrawLimit = createReq.DailyWithdrawLimit
	req.DailyWithdrawAmountLimit = createReq.DailyWithdrawAmountLimit

	return nil
}
