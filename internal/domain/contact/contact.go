package contact

import (
	"blacking-api/internal/domain/request"
	"time"
)

type Contact struct {
	ID           int64     `json:"id" db:"id"`
	Name         string    `json:"name" db:"name"`
	ImageURL     string    `json:"imageUrl" db:"image_url"`
	Link         *string   `json:"link" db:"link"`
	Account      *string   `json:"account" db:"account"`
	MainContract bool      `json:"mainContract" db:"main_contract"`
	Birthday     bool      `json:"birthday" db:"brithday"`
	FirstColor   string    `json:"firstColor" db:"first_color"`
	SecondColor  string    `json:"secondColor" db:"second_color"`
	Active       bool      `json:"active" db:"active"`
	Inactive     bool      `json:"inactive" db:"inactive"`
	CreatedAt    time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt    time.Time `json:"updatedAt" db:"updated_at"`
}

type CreateContactRequest struct {
	Name        string `json:"name" validate:"required,min=1,max=255"`
	ImageURL    string `json:"imageUrl" validate:"required,url"`
	Link        string `json:"link"`
	Account     string `json:"account"`
	FirstColor  string `json:"firstColor" validate:"required,len=7"`
	SecondColor string `json:"secondColor" validate:"required,len=7"`
}

type ContactSearchRequest struct {
	request.RequestPagination
	Name *string `form:"name"`
}

type ListContactResponse struct {
	ID           int64   `json:"id"`
	Name         string  `json:"name"`
	ImageURL     string  `json:"imageUrl"`
	Link         *string `json:"link"`
	Account      *string `json:"account"`
	MainContract bool    `json:"mainContract"`
	Birthday     bool    `json:"birthday"`
	FirstColor   string  `json:"firstColor"`
	SecondColor  string  `json:"secondColor"`
	Active       bool    `json:"active"`
}

type ByIdContactRequest struct {
	Name        string  `json:"name"`
	ImageURL    string  `json:"imageUrl"`
	Link        *string `json:"link"`
	Account     *string `json:"account"`
	FirstColor  string  `json:"firstColor"`
	SecondColor string  `json:"secondColor"`
}

type UpdateStatusRequest struct {
	Name   string `form:"name" validate:"required,min=1,max=255"`
	Status bool   `form:"status" default:"false"`
}

type FileUploadResponse struct {
	FileUrl string `json:"fileUrl"`
}

type DeleteFileRequest struct {
	FileUrl string `json:"fileUrl" validate:"required,url"`
}

func (c *Contact) ToListResponse() *ListContactResponse {
	return &ListContactResponse{
		ID:           c.ID,
		Name:         c.Name,
		ImageURL:     c.ImageURL,
		Link:         c.Link,
		Account:      c.Account,
		MainContract: c.MainContract,
		Birthday:     c.Birthday,
		FirstColor:   c.FirstColor,
		SecondColor:  c.SecondColor,
		Active:       c.Active,
	}
}

func (c *Contact) ToByIdResponse() *ByIdContactRequest {
	return &ByIdContactRequest{
		Name:        c.Name,
		ImageURL:    c.ImageURL,
		Link:        c.Link,
		Account:     c.Account,
		FirstColor:  c.FirstColor,
		SecondColor: c.SecondColor,
	}
}
