package middleware

import (
	"time"

	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func Logger(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate request ID
		requestID := uuid.New().String()
		c.Set("request_id", requestID)

		// Start timer
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Build log fields
		fields := map[string]interface{}{
			"request_id":   requestID,
			"method":       c.Request.Method,
			"path":         path,
			"status":       c.Writer.Status(),
			"latency":      latency.String(),
			"latency_ms":   latency.Milliseconds(),
			"ip":           c.ClientIP(),
			"user_agent":   c.Request.UserAgent(),
			"response_size": c.<PERSON>.<PERSON>(),
		}

		if raw != "" {
			fields["query"] = raw
		}

		if len(c.Errors) > 0 {
			fields["errors"] = c.Errors.String()
		}

		// Log based on status code
		switch {
		case c.Writer.Status() >= 500:
			log.WithFields(fields).Error("server error")
		case c.Writer.Status() >= 400:
			log.WithFields(fields).Warn("client error")
		default:
			log.WithFields(fields).Info("request completed")
		}
	}
}