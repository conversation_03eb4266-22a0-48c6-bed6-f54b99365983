package middleware

import (
	"blacking-api/internal/service"
	"errors"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
)

type LoginInput struct {
	Username string `form:"username" json:"username" binding:"required"`
	Password string `form:"password" json:"password" binding:"required"`
}

type LoginClaims struct {
	ID            int
	Username      string
	AdminRoleName string
	AdminRoleID   int
	IsEnable      bool
	LoginTime     int64 // Unix timestamp of login
}

type MemberLoginClaims struct {
	ID        int
	Username  string
	IsEnable  bool
	LoginTime int64 // Unix timestamp of login
}

type AuthMiddleware struct {
	userService     service.UserService
	userRoleService service.UserRoleService
	user2FAService  service.User2FAService
	memberService   service.MemberService
}

func NewAuthMiddleware(userService service.UserService, userRoleService service.UserRoleService, user2FAService service.User2FAService, memberService service.MemberService) *AuthMiddleware {
	return &AuthMiddleware{
		userService:     userService,
		userRoleService: userRoleService,
		user2FAService:  user2FAService,
		memberService:   memberService,
	}
}

func (a *AuthMiddleware) HandlerMiddleware(authMiddleware *jwt.GinJWTMiddleware) gin.HandlerFunc {
	return authMiddleware.MiddlewareFunc()
}

func (a *AuthMiddleware) NewUserJWT() *jwt.GinJWTMiddleware {
	// Get JWT secret from environment variable, fallback to default if not set
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "icqHh7zR3.yLZdKUimV7" // fallback to original hardcoded key
	}
	
	m, _ := jwt.New(&jwt.GinJWTMiddleware{
		Realm:       "user zone",
		Key:         []byte(jwtSecret),
		IdentityKey: "username",
		Timeout:     time.Minute * 30, // Shorter timeout to force re-login
		MaxRefresh:  time.Minute * 30,

		// Force re-authentication by making tokens single-use
		DisabledAbort: false,
		PayloadFunc:   a.PayloadFunc(),

		IdentityHandler: a.AdminIdentityHandler(),
		Authenticator:   a.authenticator(),
		Authorizator:    a.authorizator(),
		Unauthorized:    a.unauthorized(),

		SendCookie:     true,
		CookieName:     "auth-token",
		CookieHTTPOnly: true,
		// CookieSecure:   true,                 // ต้องใช้ HTTPS
		//CookieSameSite: http.SameSiteLaxMode, // Strict / None ตาม
		//CookieDomain:   "yourdomain.com",     // ถ้าจำเป็น

		// TokenLookup:     "header: Authorization, query: token, cookie: jwt",
		// TokenLookup: "query:token",
		// TokenLookup:   "cookie:token",
		TokenLookup:   "header: Authorization, cookie: auth-token",
		TokenHeadName: "Bearer",
		TimeFunc:      time.Now,
		LoginResponse: func(c *gin.Context, code int, token string, expire time.Time) {
			// Check if response was already sent (e.g., for 2FA flow)
			if c.Writer.Written() {
				return
			}
			c.JSON(code, gin.H{
				"status":  "success",
				"message": "Login successful",
				"token":   token,
				"expire":  expire.Unix(),
			})
		},
	})
	return m
}

func (a *AuthMiddleware) NewMemberJWT() *jwt.GinJWTMiddleware {
	// Get JWT secret from environment variable, fallback to default if not set
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		jwtSecret = "memberSecretKey123" // fallback to original hardcoded key
	}
	
	m, _ := jwt.New(&jwt.GinJWTMiddleware{
		Realm:       "member zone",
		Key:         []byte(jwtSecret),
		IdentityKey: "username",
		Timeout:     time.Hour * 2, // Shorter timeout for members too
		MaxRefresh:  time.Hour * 2,
		PayloadFunc: a.MemberPayloadFunc(),

		IdentityHandler: a.MemberIdentityHandler(),
		Authenticator:   a.memberAuthenticator(),
		Authorizator:    a.memberAuthorizator(),
		Unauthorized:    a.unauthorized(),

		SendCookie:     true,
		CookieName:     "member_jwt", // Different cookie name
		CookieHTTPOnly: true,
		CookieSameSite: http.SameSiteLaxMode,
		CookieDomain:   "yourdomain.com",

		TokenLookup:   "cookie:member_jwt",
		TokenHeadName: "Bearer",
		TimeFunc:      time.Now,
		LoginResponse: func(c *gin.Context, code int, token string, expire time.Time) {
			if c.Writer.Written() {
				return
			}
			c.JSON(code, gin.H{
				"status":  "success",
				"message": "Member login successful",
				"token":   token,
				"expire":  expire.Unix(),
			})
		},
	})
	return m
}

func (a *AuthMiddleware) MemberPayloadFunc() func(data interface{}) jwt.MapClaims {
	return func(data interface{}) jwt.MapClaims {
		log.Printf("MemberPayloadFunc called with data: %+v, type: %T", data, data)

		if v, ok := data.(*MemberLoginClaims); ok {
			log.Printf("Successfully cast to MemberLoginClaims: %+v", v)

			claims := jwt.MapClaims{
				"username":   v.Username,
				"id":         v.ID,
				"is_enable":  v.IsEnable,
				"login_time": time.Now().Unix(), // Add current login timestamp
			}
			log.Printf("Generated member claims: %+v", claims)
			return claims
		}

		log.Printf("Failed to cast to MemberLoginClaims, returning empty claims")
		return jwt.MapClaims{}
	}
}

func (a *AuthMiddleware) PayloadFunc() func(data interface{}) jwt.MapClaims {
	return func(data interface{}) jwt.MapClaims {
		// Try pointer first
		if v, ok := data.(*LoginClaims); ok {
			log.Printf("Successfully cast to *LoginClaims: %+v", v)
			claims := jwt.MapClaims{
				"username":       v.Username,
				"id":             v.ID,
				"user_role_id":   v.AdminRoleID,
				"user_role_name": v.AdminRoleName,
				"is_enable":      v.IsEnable,
				"login_time":     time.Now().Unix(), // Add current login timestamp
			}
			log.Printf("Generated claims: %+v", claims)
			return claims
		}

		// Try value type
		if v, ok := data.(LoginClaims); ok {
			log.Printf("Successfully cast to LoginClaims: %+v", v)
			claims := jwt.MapClaims{
				"username":       v.Username,
				"id":             v.ID,
				"user_role_id":   v.AdminRoleID,
				"user_role_name": v.AdminRoleName,
				"is_enable":      v.IsEnable,
			}
			log.Printf("Generated claims: %+v", claims)
			return claims
		}

		log.Printf("Failed to cast to LoginClaims, returning empty claims")
		return jwt.MapClaims{}
	}
}

func (a *AuthMiddleware) AdminIdentityHandler() func(c *gin.Context) interface{} {
	return func(c *gin.Context) interface{} {

		claims := jwt.ExtractClaims(c)
		username, _ := claims["username"].(string)
		idFloat, _ := claims["id"].(float64)
		userRoleFloat, ok := claims["user_role_id"].(float64)
		isEnable, isEnableExists := claims["is_enable"].(bool)

		// If is_enable claim doesn't exist in JWT (old token), default to false for security
		if !isEnableExists {
			isEnable = false
		}

		if !ok {
			log.Println("cannot convert role id to float64")

		}

		userRoleID := int(userRoleFloat)
		userRole, _ := claims["user_role_name"].(string)
		id := int(idFloat)

		authUser := LoginClaims{
			Username:      username,
			ID:            id,
			AdminRoleName: userRole,
			AdminRoleID:   userRoleID,
			IsEnable:      isEnable,
		}

		c.Set("auth-user", &authUser)
		c.Set("auth-username", username)
		c.Set("user_id", id)

		return &authUser
	}
}

func (a *AuthMiddleware) MemberIdentityHandler() func(c *gin.Context) interface{} {
	return func(c *gin.Context) interface{} {
		claims := jwt.ExtractClaims(c)
		username, _ := claims["username"].(string)
		idStr, _ := claims["id"].(string)
		isEnable, _ := claims["is_enable"].(bool)

		id, err := strconv.Atoi(idStr)
		if err != nil {
			id = 0 // fallback to 0 if conversion fails
		}

		authMember := MemberLoginClaims{
			Username: username,
			ID:       id,
			IsEnable: isEnable,
		}

		c.Set("auth-member", &authMember)
		return &authMember
	}
}

func (a *AuthMiddleware) authorizator() func(data interface{}, c *gin.Context) bool {
	return func(data interface{}, c *gin.Context) bool {
		if v, ok := data.(*LoginClaims); ok {
			// Check if user is enabled

			if !v.IsEnable {
				return false
			}

			if v.ID == -1 {
				return true
			}
			_, err := a.userRoleService.GetUserRoleByID(c.Request.Context(), strconv.Itoa(v.AdminRoleID))
			if err != nil {
				return false
			}
			return true
		}
		return false
	}
}

func (a *AuthMiddleware) memberAuthorizator() func(data interface{}, c *gin.Context) bool {
	return func(data interface{}, c *gin.Context) bool {
		if v, ok := data.(*MemberLoginClaims); ok {
			// Check if member is enabled
			if !v.IsEnable {
				return false
			}
			// Additional member authorization logic can be added here
			return true
		}
		return false
	}
}

func (a *AuthMiddleware) unauthorized() func(c *gin.Context, code int, message string) {
	return func(c *gin.Context, code int, message string) {
		c.JSON(code, gin.H{
			"code":    code,
			"message": message,
		})
	}
}

func (a *AuthMiddleware) authenticator() func(c *gin.Context) (interface{}, error) {
	return func(c *gin.Context) (interface{}, error) {
		log.Printf("🔐 Authenticator called - new login attempt")

		var input LoginInput
		if err := c.ShouldBindJSON(&input); err != nil {
			return "", jwt.ErrMissingLoginValues
		}

		username := input.Username
		password := input.Password

		log.Printf("🔐 Login attempt for username: %s", username)

		// ✅ dev login
		if os.Getenv("GIN_MODE") != "release" &&
			username == os.Getenv("DEV_USERNAME") &&
			password == os.Getenv("DEV_PASSWORD") {
			return &LoginClaims{
				ID:            1, // Developer ID
				Username:      username,
				AdminRoleID:   1,
				AdminRoleName: "Super Admin Dev",
				IsEnable:      true, // Developer is always enabled
			}, nil
		}

		// ✅ ค้นหา user จาก DB ( logic ตรง้)
		user, err := a.userService.ValidateUserCredentials(c, username, password)

		if err != nil {
			return nil, errors.New("authentication failed")
		}

		// Check if user is enabled
		if !user.IsEnable {
			return nil, errors.New("user account is disabled")
		}

		// Check if user role requires 2FA
		userRole, err := a.userRoleService.GetUserRoleByID(c.Request.Context(), strconv.Itoa(user.UserRoleID))
		if err != nil {
			// If error getting user role, continue without 2FA (backward compatibility)
			return &LoginClaims{
				Username:      user.Username,
				AdminRoleName: user.UserRoleName,
				AdminRoleID:   user.UserRoleID,
				ID:            user.ID,
				IsEnable:      user.IsEnable, // UserResponse.IsEnable is bool, not *bool
			}, nil
		}

		// If user role requires 2FA, check if user has 2FA setup and enabled
		if userRole.Is2FA {

			claims := &LoginClaims{
				Username:      "__2fa_setup__",
				AdminRoleName: "__2fa_setup__",
				AdminRoleID:   0,
				ID:            user.ID,
				IsEnable:      user.IsEnable, // UserResponse.IsEnable is bool, not *bool
			}

			is2FAEnabled, err := a.user2FAService.Get2FAStatus(c.Request.Context(), strconv.Itoa(user.ID))

			if err != nil || !is2FAEnabled.IsEnabled {

				// Generate temporary JWT token for 2FA setup
				setupToken, err := a.generateTempToken(claims)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error":   "failed_to_generate_token",
						"message": "Failed to generate setup token. Please try again.",
					})
					c.Abort()
					return nil, nil
				}

				// If error getting 2FA status, auto setup 2FA
				setupResponse, setupErr := a.autoSetup2FA(c, user.ID, user.Username, setupToken)
				if setupErr != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error":   "failed_to_setup_2fa",
						"message": "Failed to setup 2FA automatically. Please try again.",
					})
					c.Abort()
					return nil, nil
				}

				c.JSON(http.StatusPreconditionRequired, gin.H{
					"requires_2fa_setup": true,
					"setup_data":         setupResponse,
					"message":            "2FA is required for your role. Please scan the QR code and enable 2FA.",
				})
				c.Abort()
				return nil, nil
			}
			if is2FAEnabled.IsEnabled {

				tempToken, _ := a.generateTempToken(claims)

				c.JSON(http.StatusAccepted, gin.H{
					"requires_2fa": true,
					"temp_token":   tempToken,
					"message":      "2FA verification required",
				})

			}
			c.Abort()
			return nil, nil
		}

		return &LoginClaims{
			Username:      user.Username,
			AdminRoleName: user.UserRoleName,
			AdminRoleID:   user.UserRoleID,
			ID:            user.ID,
			IsEnable:      user.IsEnable, // UserResponse.IsEnable is bool, not *bool
		}, nil
	}
}

func (a *AuthMiddleware) memberAuthenticator() func(c *gin.Context) (interface{}, error) {
	return func(c *gin.Context) (interface{}, error) {
		log.Printf("👤 Member Authenticator called - new member login attempt")

		var input LoginInput
		if err := c.ShouldBindJSON(&input); err != nil {
			return "", jwt.ErrMissingLoginValues
		}

		username := input.Username
		password := input.Password // TODO: Implement password verification

		log.Printf("👤 Member login attempt for username: %s", username)

		member, err := a.memberService.ValidateMemberCredentials(c, username, password)

		if err != nil {
			return nil, errors.New("authentication failed")
		}

		// Check if member is enabled
		if !member.IsEnable {
			// Record failed member login attempt
			return nil, errors.New("member account is disabled")
		}

		return &MemberLoginClaims{
			Username: member.Username,
			ID:       member.ID,
			IsEnable: member.IsEnable,
		}, nil
	}
}

// generateTempToken generates a temporary JWT token for 2FA step
func (a *AuthMiddleware) generateTempToken(claims *LoginClaims) (string, error) {
	// Create JWT middleware instance for token generation
	jwtMiddleware := a.NewUserJWT()

	// Generate JWT token manually
	token, _, err := jwtMiddleware.TokenGenerator(claims)
	if err != nil {
		return "", err
	}

	log.Println(token)

	return token, nil
}

// autoSetup2FA automatically sets up 2FA for a user
func (a *AuthMiddleware) autoSetup2FA(c *gin.Context, userID int, username string, setupToken string) (interface{}, error) {
	// For now, return a mock response that tells user to use the setup API
	// In production, this should call the actual 2FA service
	return map[string]interface{}{
		"message":        "Please use the 2FA setup API to complete setup",
		"setup_endpoint": "/api/v1/auth/2fa/my-setup",
		"user_id":        userID,
		"setup_token":    setupToken,
	}, nil
}
