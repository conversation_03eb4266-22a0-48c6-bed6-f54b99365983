package router

import (
	"github.com/gin-gonic/gin"
)

func SetupBankingRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Banking related endpoints
	banking := r.Group("/banking")
	{
		banking.GET("", handlers.BankingHandler.ListBankings)
	}

	// Algorithm endpoints
	algorithm := r.Group("/algorithm")
	{
		algorithm.GET("", handlers.AlgorithmHandler.ListAlgorithms)
	}

	// Auto Bot endpoints
	autoBot := r.Group("/auto-bot")
	{
		autoBot.GET("", handlers.AutoBotHandler.ListAutoBots)
	}
}