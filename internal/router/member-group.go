package router

import (
	"github.com/gin-gonic/gin"
)

func SetupMemberGroupRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Member Group Type management (Admin access only)
	memberGroupTypes := r.Group("/member-group-types")
	{
		memberGroupTypes.Use(handlers.JwtMiddleware.MiddlewareFunc())
		memberGroupTypes.POST("", handlers.MemberGroupTypeHandler.CreateMemberGroupType)
		memberGroupTypes.GET("", handlers.MemberGroupTypeHandler.ListMemberGroupTypes)
		memberGroupTypes.GET("/active", handlers.MemberGroupTypeHandler.ListActiveMemberGroupTypes)
		memberGroupTypes.GET("/dropdown", handlers.MemberGroupTypeHandler.ListMemberGroupTypesForDropdown)
		memberGroupTypes.GET("/:id", handlers.MemberGroupTypeHandler.GetMemberGroupType)
		memberGroupTypes.PUT("/:id", handlers.MemberGroupTypeHandler.UpdateMemberGroupType)
		memberGroupTypes.PUT("/:id/reorder", handlers.MemberGroupTypeHandler.ReorderMemberGroupType)
		memberGroupTypes.DELETE("/:id", handlers.MemberGroupTypeHandler.DeleteMemberGroupType)
		memberGroupTypes.POST("/upload", handlers.MemberGroupTypeHandler.FileUpload)
		memberGroupTypes.DELETE("/file", handlers.MemberGroupTypeHandler.DeleteFile)
	}

	// Member Group management (Admin access only)
	memberGroups := r.Group("/member-groups")
	{
		memberGroups.Use(handlers.JwtMiddleware.MiddlewareFunc())
		memberGroups.POST("", handlers.MemberGroupHandler.CreateMemberGroup)
		memberGroups.GET("", handlers.MemberGroupHandler.ListMemberGroups)
		memberGroups.GET("/active", handlers.MemberGroupHandler.ListActiveMemberGroups)
		memberGroups.GET("/dropdown", handlers.MemberGroupHandler.ListMemberGroupsForDropdown)
		memberGroups.GET("/default", handlers.MemberGroupHandler.GetDefaultMemberGroup)
		memberGroups.GET("/code/:code", handlers.MemberGroupHandler.GetMemberGroupByCode)
		memberGroups.GET("/:id", handlers.MemberGroupHandler.GetMemberGroup)
		memberGroups.PUT("/:id", handlers.MemberGroupHandler.UpdateMemberGroup)
		memberGroups.PUT("/:id/set-default", handlers.MemberGroupHandler.SetDefaultMemberGroup)
		memberGroups.DELETE("/:id", handlers.MemberGroupHandler.DeleteMemberGroup)
	}
}
