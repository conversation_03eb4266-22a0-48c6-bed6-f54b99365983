package router

import (
	"github.com/gin-gonic/gin"
)

func SetupAuditLogRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Member audit logs (Admin access only)
	memberAuditLogs := r.Group("/member-audit-logs")
	{
		memberAuditLogs.Use(handlers.JwtMiddleware.MiddlewareFunc())
		memberAuditLogs.GET("", handlers.MemberAuditLogHandler.ListMemberAuditLogs)
	}

	// User Audit Log endpoints
	auditLogs := r.Group("/user-audit-logs")
	{
		auditLogs.Use(handlers.JwtMiddleware.MiddlewareFunc())
		auditLogs.GET("", handlers.UserAuditLogHandler.ListUserAuditLogs)
		auditLogs.GET("/action-types", handlers.UserAuditLogHandler.GetActionTypes)
	}
}
