package router

import (
	"blacking-api/internal/handler/http"
	"blacking-api/internal/middleware"
	"blacking-api/pkg/logger"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
)

type Handlers struct {
	JwtMiddleware                   *jwt.GinJWTMiddleware
	MemberJwtMiddleware             *jwt.GinJWTMiddleware
	UserHandler                     *http.UserHandler
	HealthHandler                   *http.HealthHandler
	UserRoleHandler                 *http.UserRoleHandler
	MemberHandler                   *http.MemberHandler
	MemberAuditLogHandler           *http.MemberAuditLogHandler
	UserAuditLogHandler             *http.UserAuditLogHandler
	MemberRegistrationHandler       *http.MemberRegistrationHandler
	LoginReportHandler              *http.LoginReportHandler
	AllowedIPHandler                *http.AllowedIPHandler
	SystemSettingHandler            *http.SystemSettingHandler
	ThemeSettingHandler          *http.ThemeSettingHandler
	User2FAHandler                  *http.User2FAHandler
	LanguageHandler                 *http.LanguageHandler
	BannerHandler                   *http.BannerHandler
	PermissionHandler               *http.PermissionHandler
	BankingHandler                  *http.BankingHandler
	PaymentMethodHandler            *http.PaymentMethodHandler
	AlgorithmHandler                *http.AlgorithmHandler
	AutoBotHandler                  *http.AutoBotHandler
	FAQHandler                      *http.FAQHandler
	OTPHandler                      *http.OTPHandler
	PlatformHandler                 *http.PlatformHandler
	ChannelHandler                  *http.ChannelHandler
	MemberGroupTypeHandler          *http.MemberGroupTypeHandler
	CommissionGroupHandler          *http.CommissionGroupHandler
	MemberGroupHandler              *http.MemberGroupHandler
	ReferralGroupHandler            *http.ReferralGroupHandler
	PartnerHandler                  *http.PartnerHandler
	DepositAccountHandler           *http.DepositAccountHandler
	WithdrawAccountHandler          *http.WithdrawAccountHandler
	HoldingAccountHandler           *http.HoldingAccountHandler
	PaymentGatewayAccountHandler    *http.PaymentGatewayAccountHandler
	SMSProviderNameHandler          *http.SMSProviderNameHandler
	SMSProviderHandler              *http.SMSProviderHandler
	ContactHandler                  *http.ContactHandler
	PromotionWebHandler             *http.PromotionWebHandler
	UserTransactionTypeHandler      *http.UserTransactionTypeHandler
	UserTransactionDirectionHandler *http.UserTransactionDirectionHandler
	UserTransactionStatusHandler    *http.UserTransactionStatusHandler
}

func Setup(handlers *Handlers, log logger.Logger) *gin.Engine {
	// Create Gin router
	r := gin.New()

	// Add middleware
	r.Use(middleware.Recovery(log))
	r.Use(middleware.Logger(log))
	r.Use(middleware.CORS())
	r.Use(middleware.ErrorHandler(log))

	// Health endpoints
	r.GET("/health", handlers.HealthHandler.Health)
	r.GET("/health/ready", handlers.HealthHandler.Ready)
	r.GET("/health/live", handlers.HealthHandler.Live)

	// Static file serving for uploads
	r.Static("/uploads", "./uploads")

	// API v1 routes
	v1 := r.Group("/api/v1")
	{
		// User management routes
		SetupUserRoutes(v1, handlers)

		// User role management routes
		SetupUserRoleRoutes(v1, handlers)

		// Member management routes (Admin)
		SetupMemberRoutes(v1, handlers)

		// Member authentication routes
		SetupMemberAuthRoutes(v1, handlers)

		// Member area routes (Member protected)
		SetupMemberAreaRoutes(v1, handlers)

		// Audit log routes
		SetupAuditLogRoutes(v1, handlers)

		// OTP and Platform routes
		SetupOTPRoutes(v1, handlers)
		SetupPlatformRoutes(v1, handlers)

		// Channel management routes
		SetupChannelRoutes(v1, handlers)

		// Member group management routes
		SetupMemberGroupRoutes(v1, handlers)

		// Commission and referral routes
		SetupCommissionRoutes(v1, handlers)
		SetupReferralRoutes(v1, handlers)

		// Partner management routes
		SetupPartnerRoutes(v1, handlers)

		// Report routes
		SetupReportRoutes(v1, handlers)

		// System management routes
		SetupSystemRoutes(v1, handlers)

		// Language and banner routes
		SetupLanguageRoutes(v1, handlers)
		SetupBannerRoutes(v1, handlers)

		// Permission management routes
		SetupPermissionRoutes(v1, handlers)

		// 2FA authentication routes
		Setup2FARoutes(v1, handlers)

		// Banking system routes
		SetupBankingRoutes(v1, handlers)

		// Payment system routes
		SetupPaymentRoutes(v1, handlers)

		// Account management routes
		SetupAccountRoutes(v1, handlers)

		// Communication routes
		SetupCommunicationRoutes(v1, handlers)

		// Promotion Web routes
		SetupPromotionWebRoutes(v1, handlers)

		// User transaction type routes
		SetupUserTransactionTypeRoutes(v1, handlers)

		// User transaction direction routes
		SetupUserTransactionDirectionRoutes(v1, handlers)

		// User transaction status routes
		SetupUserTransactionStatusRoutes(v1, handlers)
	}

	return r
}
