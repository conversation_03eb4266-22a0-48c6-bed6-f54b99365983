package router

import (
	"github.com/gin-gonic/gin"
)

func SetupUserRoleRoutes(r *gin.RouterGroup, handlers *Handlers) {
	userRoles := r.Group("/user-roles")
	{
		userRoles.Use(handlers.JwtMiddleware.MiddlewareFunc())
		userRoles.POST("", handlers.UserRoleHandler.CreateUserRole)
		userRoles.GET("", handlers.UserRoleHandler.ListUserRoles)
		userRoles.GET("/dropdown", handlers.UserRoleHandler.ListUserRolesForDropdown)
		userRoles.GET("/:id", handlers.UserRoleHandler.GetUserRole)
		userRoles.PUT("/:id", handlers.UserRoleHandler.UpdateUserRole)
		userRoles.DELETE("/:id", handlers.UserRoleHandler.DeleteUserRole)

		userRoles.GET("/bulk/lock-ip", handlers.UserRoleHandler.BulkListLockIP)
		userRoles.GET("/bulk/2fa", handlers.UserRoleHandler.BulkList2FA)
		userRoles.PATCH("/bulk/lock-ip", handlers.UserRoleHandler.BulkToggleLockIP)
		userRoles.PATCH("/bulk/2fa", handlers.UserRoleHandler.BulkToggle2FA)
		userRoles.PATCH("/reorder", handlers.UserRoleHandler.ReorderUserRoles)
		userRoles.POST("/:id/clone", handlers.UserRoleHandler.CloneUserRole)
	}
}
