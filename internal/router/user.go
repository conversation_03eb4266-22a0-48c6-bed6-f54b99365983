package router

import (
	"github.com/gin-gonic/gin"
)

func SetupUserRoutes(r *gin.RouterGroup, handlers *Handlers) {
	users := r.Group("/users")
	{
		// Public endpoints (no middleware required)
		users.POST("/login", handlers.JwtMiddleware.LoginHandler)

		// Protected endpoints (require authentication)
		users.Use(handlers.JwtMiddleware.MiddlewareFunc())
		users.POST("", handlers.UserHandler.CreateUser)
		users.GET("", handlers.UserHandler.ListUsers)
		users.GET("/:id", handlers.UserHandler.GetUser)
		users.PUT("/:id", handlers.UserHandler.UpdateUser)
		users.DELETE("/:id", handlers.UserHandler.DeleteUser)
		users.PATCH("/:id/activate", handlers.UserHandler.ActivateUser)
		users.PATCH("/:id/deactivate", handlers.UserHandler.DeactivateUser)
		users.PATCH("/:id/suspend", handlers.UserHandler.SuspendUser)
		users.PATCH("/:id/password", handlers.UserHandler.ChangePassword)
		users.GET("/refresh_token", handlers.JwtMiddleware.RefreshHandler)
	}
}
