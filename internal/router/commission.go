package router

import (
	"github.com/gin-gonic/gin"
)

func SetupCommissionRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Commission Group management (Admin access only)
	commissionGroups := r.Group("/commission-groups")
	{
		commissionGroups.Use(handlers.JwtMiddleware.MiddlewareFunc())
		commissionGroups.POST("", handlers.CommissionGroupHandler.CreateCommissionGroup)
		commissionGroups.GET("", handlers.CommissionGroupHandler.ListCommissionGroups)
		commissionGroups.GET("/active", handlers.CommissionGroupHandler.ListActiveCommissionGroups)
		commissionGroups.GET("/dropdown", handlers.CommissionGroupHandler.ListCommissionGroupsForDropdown)
		commissionGroups.GET("/default", handlers.CommissionGroupHandler.GetDefaultCommissionGroup)
		commissionGroups.GET("/:id", handlers.CommissionGroupHandler.GetCommissionGroup)
		commissionGroups.PUT("/:id", handlers.CommissionGroupHandler.UpdateCommissionGroup)
		commissionGroups.PUT("/:id/set-default", handlers.CommissionGroupHandler.SetDefaultCommissionGroup)
		commissionGroups.DELETE("/:id", handlers.CommissionGroupHandler.DeleteCommissionGroup)
	}
}

func SetupReferralRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Referral Group management (Admin access only)
	referralGroups := r.Group("/referral-groups")
	{
		referralGroups.Use(handlers.JwtMiddleware.MiddlewareFunc())
		referralGroups.POST("", handlers.ReferralGroupHandler.CreateReferralGroup)
		referralGroups.GET("", handlers.ReferralGroupHandler.ListReferralGroups)
		referralGroups.GET("/active", handlers.ReferralGroupHandler.ListActiveReferralGroups)
		referralGroups.GET("/dropdown", handlers.ReferralGroupHandler.ListReferralGroupsForDropdown)
		referralGroups.GET("/default", handlers.ReferralGroupHandler.GetDefaultReferralGroup)
		referralGroups.GET("/:id", handlers.ReferralGroupHandler.GetReferralGroup)
		referralGroups.PUT("/:id", handlers.ReferralGroupHandler.UpdateReferralGroup)
		referralGroups.PUT("/:id/set-default", handlers.ReferralGroupHandler.SetDefaultReferralGroup)
		referralGroups.DELETE("/:id", handlers.ReferralGroupHandler.DeleteReferralGroup)
	}
}