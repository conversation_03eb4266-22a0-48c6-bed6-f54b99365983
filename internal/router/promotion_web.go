package router

import (
	"github.com/gin-gonic/gin"
)

func SetupPromotionWebRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Promotion Web management endpoints
	promotionWebs := r.Group("/promotion-webs")
	{
		promotionWebs.Use(handlers.JwtMiddleware.MiddlewareFunc())

		// Basic CRUD operations
		promotionWebs.POST("", handlers.PromotionWebHandler.CreatePromotionWeb)
		promotionWebs.GET("", handlers.PromotionWebHandler.GetPromotionWebList)
		promotionWebs.GET("/:id", handlers.PromotionWebHandler.GetPromotionWebById)
		promotionWebs.PUT("/:id", handlers.PromotionWebHandler.UpdatePromotionWeb)
		promotionWebs.DELETE("/:id", handlers.PromotionWebHandler.DeletePromotionWeb)

		// Promotion web specific operations
		promotionWebs.POST("/:id/cancel", handlers.PromotionWebHandler.CancelPromotionWeb)
		promotionWebs.GET("/:id/users-to-cancel", handlers.PromotionWebHandler.GetPromotionWebUserToCancel)

		// Priority order management
		promotionWebs.PUT("/:id/priority-order", handlers.PromotionWebHandler.UpdatePromotionWebPriorityOrder)
		promotionWebs.POST("/sort-priority", handlers.PromotionWebHandler.SortPromotionWebPriorityOrder)

		// File upload
		promotionWebs.POST("/upload-image", handlers.PromotionWebHandler.UploadImageToCloudflare)

		// Public endpoints (slides for frontend)
		promotionWebs.GET("/slides/active", handlers.PromotionWebHandler.GetActivePromotionWebSlides)
	}

	// Promotion Web User management endpoints
	promotionWebUsers := r.Group("/promotion-web-users")
	{
		promotionWebUsers.Use(handlers.JwtMiddleware.MiddlewareFunc())

		// User promotion web operations
		promotionWebUsers.GET("", handlers.PromotionWebHandler.GetUserPromotionWebList)
		promotionWebUsers.GET("/:id", handlers.PromotionWebHandler.GetPromotionWebUserById)
		promotionWebUsers.POST("/:id/cancel", handlers.PromotionWebHandler.CancelPromotionWebUserById)
	}

	// User-specific promotion web endpoints
	users := r.Group("/users-promotion-webs")
	{
		users.Use(handlers.JwtMiddleware.MiddlewareFunc())

		// User promotion web operations
		users.GET("/:user_id/promotion-web", handlers.PromotionWebHandler.GetUserPromotionWebByUserId)
		users.GET("/:user_id/promotion-web-users", handlers.PromotionWebHandler.PromotionWebUserGetListByUserId)
	}
}
