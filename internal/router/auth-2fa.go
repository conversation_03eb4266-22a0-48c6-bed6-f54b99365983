package router

import (
	"github.com/gin-gonic/gin"
)

func Setup2FARoutes(r *gin.RouterGroup, handlers *Handlers) {
	// 2FA endpoints
	auth2FA := r.Group("/auth/2fa")
	{
		auth2FA.Use(handlers.JwtMiddleware.MiddlewareFunc())
		auth2FA.POST("/disable", handlers.User2FAHandler.Disable2FA)
		auth2FA.GET("/status/:user_id", handlers.User2FAHandler.Get2FAStatus)
		auth2FA.POST("/backup-codes/:user_id", handlers.User2FAHandler.GenerateBackupCodes)
		auth2FA.POST("/validate-backup", handlers.User2FAHandler.ValidateBackupCode)

		// Admin 2FA endpoints
		auth2FA.POST("/admin/reset", handlers.User2FAHandler.Reset2FA)

		// Personal 2FA endpoints (for current logged-in user)
		auth2FA.GET("/my-status", handlers.User2FAHandler.GetMyStatus)
		auth2FA.POST("/my-setup", handlers.User2FAHandler.SetupMy2FA)
		auth2FA.POST("/my-enable", handlers.User2FAHandler.EnableMy2FA)
		auth2FA.POST("/my-disable", handlers.User2FAHandler.DisableMy2FA)
	}

	// 2FA Login endpoint (no auth required)
	r.POST("/auth/login-2fa", handlers.User2FAHandler.Login2FA)

	// 2FA Setup endpoints (no auth required, use setup_token)
	r.POST("/auth/2fa-setup/setup", handlers.User2FAHandler.SetupWithToken)
	r.POST("/auth/2fa-setup/verify", handlers.User2FAHandler.Verify2FA)
	r.POST("/auth/2fa-setup/enable", handlers.User2FAHandler.EnableWithToken)
}
