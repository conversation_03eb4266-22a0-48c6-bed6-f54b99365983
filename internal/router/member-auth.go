package router

import (
	"github.com/gin-gonic/gin"
)

func SetupMemberAuthRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Member authentication endpoints
	memberAuth := r.Group("/member-auth")
	{
		// OTP-based registration (3-step process)
		memberAuth.POST("/register/send-otp", handlers.MemberRegistrationHandler.SendRegistrationOTP)
		memberAuth.POST("/register/verify-otp", handlers.MemberRegistrationHandler.VerifyRegistrationOTP)
		memberAuth.POST("/register/complete", handlers.MemberRegistrationHandler.CompleteRegistration)

		memberAuth.POST("/login", handlers.MemberJwtMiddleware.LoginHandler)
		memberAuth.POST("/logout", handlers.MemberJwtMiddleware.LogoutHandler)
		memberAuth.POST("/refresh", handlers.MemberJwtMiddleware.RefreshHandler)
	}
}