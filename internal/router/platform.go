package router

import (
	"github.com/gin-gonic/gin"
)

func SetupOTPRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// OTP management (Admin access only)
	otps := r.Group("/otps")
	{
		otps.Use(handlers.JwtMiddleware.MiddlewareFunc())
		otps.GET("", handlers.OTPHandler.ListOTPs)
	}
}

func SetupPlatformRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Platform management (Admin access only)
	platforms := r.Group("/platforms")
	{
		platforms.Use(handlers.JwtMiddleware.MiddlewareFunc())
		platforms.GET("", handlers.PlatformHandler.ListPlatforms)
		platforms.GET("/:id", handlers.PlatformHandler.GetPlatform)
		platforms.GET("/types", handlers.PlatformHandler.GetPlatformTypes)
		platforms.GET("/type/:type", handlers.PlatformHandler.GetPlatformsByType)
	}
}

func SetupChannelRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Channel management (Admin access only)
	channels := r.Group("/channels")
	{
		channels.Use(handlers.JwtMiddleware.MiddlewareFunc())
		channels.POST("", handlers.ChannelHandler.CreateChannel)
		channels.GET("", handlers.ChannelHandler.ListChannels)
		channels.GET("/active", handlers.ChannelHandler.ListActiveChannels)
		channels.GET("/platform/:platformId", handlers.ChannelHandler.ListChannelsByPlatform)
		channels.GET("/:id", handlers.ChannelHandler.GetChannel)
		channels.PUT("/:id", handlers.ChannelHandler.UpdateChannel)
		channels.DELETE("/:id", handlers.ChannelHandler.DeleteChannel)
	}
}
