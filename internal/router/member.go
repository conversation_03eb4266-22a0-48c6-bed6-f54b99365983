package router

import (
	"github.com/gin-gonic/gin"
)

func SetupMemberRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Member endpoints (Admin access)
	members := r.Group("/members")
	{
		members.Use(handlers.JwtMiddleware.MiddlewareFunc()) // Admin JWT for managing members
		members.POST("", handlers.MemberHandler.CreateMember)
		members.GET("", handlers.MemberHandler.ListMembersWithFilter)
		members.GET("/:id", handlers.MemberHandler.GetMember)
		members.PUT("/:id", handlers.MemberHandler.UpdateMember)
		members.DELETE("/:id", handlers.MemberHandler.DeleteMember)
		members.GET("/username/:username", handlers.MemberHandler.GetMemberByUsername)
		members.GET("/game-username/:game_username", handlers.MemberHandler.GetMemberByGameUsername)
	}
}

func SetupMemberAreaRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Member protected endpoints (Member JWT required)
	memberArea := r.Group("/member")
	{
		memberArea.Use(handlers.MemberJwtMiddleware.MiddlewareFunc())
		// Add member-specific endpoints here
		// memberArea.GET("/profile", handlers.MemberHandler.GetProfile)
		// memberArea.PUT("/profile", handlers.MemberHandler.UpdateProfile)
	}
}
