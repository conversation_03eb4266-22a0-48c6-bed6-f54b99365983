package router

import (
	"github.com/gin-gonic/gin"
)

func SetupAccountRoutes(r *gin.RouterGroup, handlers *Handlers) {
	// Deposit Account endpoints
	depositAccount := r.Group("/deposit-account")
	{
		depositAccount.POST("", handlers.DepositAccountHandler.CreateDepositAccount)
		depositAccount.GET("", handlers.DepositAccountHandler.GetDepositAccounts)
		depositAccount.GET("/:id", handlers.DepositAccountHandler.GetDepositAccountByID)
		depositAccount.GET("/:id/algorithm", handlers.DepositAccountHandler.GetDepositAccountSettingAlgorithmByID)
		depositAccount.PUT("/:id", handlers.DepositAccountHandler.UpdateDepositAccount)
		depositAccount.PUT("/:id/auto-bot", handlers.DepositAccountHandler.UpdateDepositAccountAutoBot)
		depositAccount.PUT("/:id/algorithm", handlers.DepositAccountHandler.UpdateDepositAccountAlgorithm)
		depositAccount.PUT("/:id/active", handlers.DepositAccountHandler.ActiveDepositAccount)
		depositAccount.DELETE("/:id", handlers.DepositAccountHandler.DeleteDepositAccount)
	}

	// Withdraw Account endpoints
	withdrawAccount := r.Group("/withdraw-account")
	{
		withdrawAccount.POST("/upload-file", handlers.WithdrawAccountHandler.FileUpload)
		withdrawAccount.POST("/delete-file", handlers.WithdrawAccountHandler.DeleteFile)
		withdrawAccount.POST("", handlers.WithdrawAccountHandler.CreateWithdrawAccount)
		withdrawAccount.GET("", handlers.WithdrawAccountHandler.GetWithdrawAccounts)
		withdrawAccount.GET("/:id", handlers.WithdrawAccountHandler.GetWithdrawAccountByID)
		withdrawAccount.GET("/:id/algorithm", handlers.WithdrawAccountHandler.GetWithdrawAccountSettingAlgorithmByID)
		withdrawAccount.PUT("/:id", handlers.WithdrawAccountHandler.UpdateWithdrawAccount)
		withdrawAccount.PUT("/:id/auto-bot", handlers.WithdrawAccountHandler.UpdateWithdrawAccountAutoBot)
		withdrawAccount.PUT("/:id/algorithm", handlers.WithdrawAccountHandler.UpdateWithdrawAccountAlgorithm)
		withdrawAccount.PUT("/:id/active", handlers.WithdrawAccountHandler.ActiveWithdrawAccount)
		withdrawAccount.DELETE("/:id", handlers.WithdrawAccountHandler.DeleteWithdrawAccount)
	}

	// Holding Account endpoints
	holdingAccount := r.Group("/holding-account")
	{
		holdingAccount.POST("", handlers.HoldingAccountHandler.CreateHoldingAccount)
		holdingAccount.GET("", handlers.HoldingAccountHandler.GetHoldingAccounts)
		holdingAccount.GET("/:id", handlers.HoldingAccountHandler.GetHoldingAccountByID)
		holdingAccount.PUT("/:id", handlers.HoldingAccountHandler.UpdateHoldingAccount)
		holdingAccount.PUT("/:id/active", handlers.HoldingAccountHandler.ActiveHoldingAccount)
		holdingAccount.DELETE("/:id", handlers.HoldingAccountHandler.DeleteHoldingAccount)
	}

	// Payment Gateway Account endpoints
	paymentGatewayAccount := r.Group("/payment-gateway-account")
	{
		paymentGatewayAccount.POST("", handlers.PaymentGatewayAccountHandler.CreatePaymentGatewayAccount)
		paymentGatewayAccount.GET("", handlers.PaymentGatewayAccountHandler.GetPaymentGatewayAccounts)
		paymentGatewayAccount.GET("/:id", handlers.PaymentGatewayAccountHandler.GetPaymentGatewayAccountByID)
		paymentGatewayAccount.PUT("/:id", handlers.PaymentGatewayAccountHandler.UpdatePaymentGatewayAccount)
		paymentGatewayAccount.PUT("/:id/status", handlers.PaymentGatewayAccountHandler.UpdatePaymentGatewayAccountStatus)
		paymentGatewayAccount.DELETE("/:id", handlers.PaymentGatewayAccountHandler.DeletePaymentGatewayAccount)
	}
}