package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/user"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	userService service.UserService
	logger      logger.Logger
}

func NewUserHandler(userService service.UserService, logger logger.Logger) *UserHandler {
	return &UserHandler{
		userService: userService,
		logger:      logger,
	}
}

func (h *UserHandler) CreateUser(c *gin.Context) {
	var req user.CreateUserRequest
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	userResp, err := h.userService.CreateUser(c.Request.Context(), req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    userResp,
	})
}

func (h *UserHandler) GetUser(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	userResp, err := h.userService.GetUserByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}

func (h *UserHandler) UpdateUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	var req user.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	userResp, err := h.userService.UpdateUser(c.Request.Context(), id, req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}

func (h *UserHandler) DeleteUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)
	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	if err := h.userService.DeleteUser(c.Request.Context(), id, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user deleted successfully",
	})
}

func (h *UserHandler) ListUsers(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search")              // รับพารามิเตอร์ search
	userRoleIDStr := c.Query("user_role_id") // รับพารามิเตอร์ user_role_id

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid limit parameter"))
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid offset parameter"))
		return
	}

	// Convert user_role_id to integer if provided
	var userRoleID *int
	if userRoleIDStr != "" {
		roleID, err := strconv.Atoi(userRoleIDStr)
		if err != nil {
			c.Error(errors.NewValidationError("invalid user_role_id parameter"))
			return
		}
		userRoleID = &roleID
	}

	users, err := h.userService.ListUsers(c.Request.Context(), limit, offset, search, userRoleID)
	if err != nil {
		c.Error(err)
		return
	}

	count, err := h.userService.GetUsersCount(c.Request.Context(), search, userRoleID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"users": users,
			"pagination": gin.H{
				"total":  count,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

func (h *UserHandler) ActivateUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	if err := h.userService.ActivateUser(c.Request.Context(), id, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user activated successfully",
	})
}

func (h *UserHandler) DeactivateUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	if err := h.userService.DeactivateUser(c.Request.Context(), id, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user deactivated successfully",
	})
}

func (h *UserHandler) SuspendUser(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	if err := h.userService.SuspendUser(c.Request.Context(), id, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user suspended successfully",
	})
}

type ChangePasswordRequest struct {
	NewPassword     string `json:"new_password" validate:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" validate:"required,min=8"`
}

func (h *UserHandler) ChangePassword(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("user ID is required"))
		return
	}

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	if req.NewPassword != req.ConfirmPassword {
		c.Error(errors.NewValidationError("new password and confirm password do not match"))
		return
	}

	if err := h.userService.ChangePassword(c.Request.Context(), id, req.NewPassword, strconv.Itoa(adminId), adminName); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "password changed successfully",
	})
}
