package http

import (
	"blacking-api/internal/domain/payment_gateway_account"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"strconv"
)

type PaymentGatewayAccountHandler struct {
	paymentGatewayAccountService service.PaymentGatewayAccountService
	logger                       logger.Logger
}

func NewPaymentGatewayAccountHandler(paymentGatewayAccountService service.PaymentGatewayAccountService, logger logger.Logger) *PaymentGatewayAccountHandler {
	return &PaymentGatewayAccountHandler{
		paymentGatewayAccountService: paymentGatewayAccountService,
		logger:                       logger,
	}
}

func (h *PaymentGatewayAccountHandler) CreatePaymentGatewayAccount(c *gin.Context) {
	var req *payment_gateway_account.PaymentGatewayAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentGatewayAccountService.CreatePaymentGatewayAccount(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create payment gateway account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(201, gin.H{
		"success": true,
		"message": "Payment gateway account created successfully",
	})
}

func (h *PaymentGatewayAccountHandler) GetPaymentGatewayAccounts(c *gin.Context) {
	var req *payment_gateway_account.PaymentGatewayAccountSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	accounts, err := h.paymentGatewayAccountService.FindAllPaymentGatewayAccount(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get payment gateway accounts", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    accounts,
	})
}

func (h *PaymentGatewayAccountHandler) GetPaymentGatewayAccountByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment gateway account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	account, err := h.paymentGatewayAccountService.FindPaymentGatewayAccountByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to get payment gateway account by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    account,
	})
}

func (h *PaymentGatewayAccountHandler) UpdatePaymentGatewayAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment gateway account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *payment_gateway_account.PaymentGatewayAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentGatewayAccountService.UpdatePaymentGatewayAccount(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update payment gateway account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment gateway account updated successfully",
	})
}

func (h *PaymentGatewayAccountHandler) UpdatePaymentGatewayAccountStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment gateway account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *payment_gateway_account.UpdateStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentGatewayAccountService.UpdatePaymentGatewayAccountStatus(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update payment gateway account status", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment gateway account status updated successfully",
	})
}

func (h *PaymentGatewayAccountHandler) DeletePaymentGatewayAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment gateway account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentGatewayAccountService.DeletePaymentGatewayAccount(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete payment gateway account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment gateway account deleted successfully",
	})
}
