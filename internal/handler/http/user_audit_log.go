package http

import (
	"blacking-api/internal/domain/user_audit_log"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type UserAuditLogHandler struct {
	userAuditLogService service.UserAuditLogService
	logger              logger.Logger
}

// NewUserAuditLogHandler creates a new user audit log handler
func NewUserAuditLogHandler(userAuditLogService service.UserAuditLogService, logger logger.Logger) *UserAuditLogHandler {
	return &UserAuditLogHandler{
		userAuditLogService: userAuditLogService,
		logger:              logger,
	}
}

// ListUserAuditLogs handles GET /api/user-audit-logs
func (h *UserAuditLogHandler) ListUserAuditLogs(c *gin.Context) {
	// Parse query parameters
	limitStr := c.<PERSON><PERSON>("limit", "10")
	offsetStr := c.<PERSON><PERSON>("offset", "0")
	username := c.Query("username")
	userRoleIDStr := c.Query("user_role_id")
	action := c.Query("action")

	// Convert limit and offset to integers
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		c.Error(errors.NewValidationError("invalid limit parameter"))
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		c.Error(errors.NewValidationError("invalid offset parameter"))
		return
	}

	// Convert user_role_id to integer if provided
	var userRoleID *int
	if userRoleIDStr != "" {
		roleID, err := strconv.Atoi(userRoleIDStr)
		if err != nil {
			c.Error(errors.NewValidationError("invalid user_role_id parameter"))
			return
		}
		userRoleID = &roleID
	}

	// Get audit logs from service
	auditLogs, err := h.userAuditLogService.ListAuditLogs(c.Request.Context(), limit, offset, username, userRoleID, action)
	if err != nil {
		c.Error(err)
		return
	}

	// Get total count for pagination
	totalCount, err := h.userAuditLogService.GetAuditLogsCount(c.Request.Context(), username, userRoleID, action)
	if err != nil {
		c.Error(err)
		return
	}

	// Calculate pagination info

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    auditLogs,
		"pagination": gin.H{
			"total":  totalCount,
			"limit":  limit,
			"offset": offset,
		},
	})
}

// GetUserAuditLogStats handles GET /api/user-audit-logs/stats
func (h *UserAuditLogHandler) GetUserAuditLogStats(c *gin.Context) {
	// This endpoint could provide statistics about audit logs
	// For now, we'll just return basic counts by action type

	ctx := c.Request.Context()

	// Get counts for each action type
	createCount, _ := h.userAuditLogService.GetAuditLogsCount(ctx, "", nil, "create")
	updateCount, _ := h.userAuditLogService.GetAuditLogsCount(ctx, "", nil, "update")
	deleteCount, _ := h.userAuditLogService.GetAuditLogsCount(ctx, "", nil, "delete")
	activateCount, _ := h.userAuditLogService.GetAuditLogsCount(ctx, "", nil, "activate")
	deactivateCount, _ := h.userAuditLogService.GetAuditLogsCount(ctx, "", nil, "deactivate")
	suspendCount, _ := h.userAuditLogService.GetAuditLogsCount(ctx, "", nil, "suspend")
	passwordCount, _ := h.userAuditLogService.GetAuditLogsCount(ctx, "", nil, "password_change")

	totalCount := createCount + updateCount + deleteCount + activateCount + deactivateCount + suspendCount + passwordCount

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total_logs": totalCount,
			"by_action": gin.H{
				"create":          createCount,
				"update":          updateCount,
				"delete":          deleteCount,
				"activate":        activateCount,
				"deactivate":      deactivateCount,
				"suspend":         suspendCount,
				"password_change": passwordCount,
			},
		},
	})
}

// GetActionTypes handles GET /api/user-audit-logs/action-types
func (h *UserAuditLogHandler) GetActionTypes(c *gin.Context) {
	// Get all action types with Thai translations
	actionTypes := user_audit_log.GetAllActionTypes()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    actionTypes,
		"message": "Action types retrieved successfully",
	})
}
