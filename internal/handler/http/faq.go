package http

import (
	"fmt"
	"net/http"
	"strconv"

	"blacking-api/internal/domain/faq"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type FAQHandler struct {
	userRoleService service.FAQService
	logger          logger.Logger
}

func NewFAQHandler(userRoleService service.FAQService, logger logger.Logger) *FAQHandler {
	return &FAQHandler{
		userRoleService: userRoleService,
		logger:          logger,
	}
}

func (h *FAQHandler) CreateFAQ(c *gin.Context) {
	var req faq.CreateFAQRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	userResp, err := h.userRoleService.CreateFAQ(c.Request.Context(), req)
	if err != nil {
		c.<PERSON>rror(err)
		return
	}

	c.<PERSON>(http.StatusCreated, gin.H{
		"success": true,
		"data":    userResp,
	})
}

func (h *FAQHandler) GetFAQ(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	userResp, err := h.userRoleService.GetFAQByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}

func (h *FAQHandler) UpdateFAQ(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	var req faq.UpdateFAQRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	userResp, err := h.userRoleService.UpdateFAQ(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    userResp,
	})
}

func (h *FAQHandler) DeleteFAQ(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("user role ID is required"))
		return
	}

	if err := h.userRoleService.DeleteFAQ(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "user role deleted successfully",
	})
}

func (h *FAQHandler) ListFAQs(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search") // รับพารามิเตอร์ search

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid limit parameter"))
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid offset parameter"))
		return
	}

	users, err := h.userRoleService.ListFAQs(c.Request.Context(), limit, offset, search)
	if err != nil {
		c.Error(err)
		return
	}

	count, err := h.userRoleService.GetFAQsCount(c.Request.Context(), search)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"users": users,
			"pagination": gin.H{
				"total":  count,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

func (h *FAQHandler) ReorderFAQs(c *gin.Context) {
	var req faq.ReorderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.userRoleService.ReorderFAQs(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("reordered %d user roles successfully", len(req.FAQIDs)),
		"data": gin.H{
			"user_role_ids": req.FAQIDs,
		},
	})
}
