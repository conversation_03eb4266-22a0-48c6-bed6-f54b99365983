package http

import (
	"blacking-api/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UserTransactionTypeHandler struct {
	userTransactionTypeService service.UserTransactionTypeService
}

func NewUserTransactionTypeHandler(userTransactionTypeService service.UserTransactionTypeService) *UserTransactionTypeHandler {
	return &UserTransactionTypeHandler{
		userTransactionTypeService: userTransactionTypeService,
	}
}

func (h *UserTransactionTypeHandler) ListUserTransactionTypes(c *gin.Context) {
	transactionTypes, err := h.userTransactionTypeService.ListUserTransactionTypes(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    transactionTypes,
	})
}
