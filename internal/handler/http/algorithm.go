package http

import (
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

type AlgorithmHandler struct {
	algorithmService service.AlgorithmService
	logger           logger.Logger
}

func NewAlgorithmHandler(algorithmService service.AlgorithmService, logger logger.Logger) *AlgorithmHandler {
	return &AlgorithmHandler{
		algorithmService: algorithmService,
		logger:           logger,
	}
}

func (handler *AlgorithmHandler) ListAlgorithms(c *gin.Context) {
	algorithms, err := handler.algorithmService.ListAlgorithms(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.J<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    algorithms,
	})
}
