package http

import (
	"blacking-api/internal/domain/allowed_ip"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AllowedIPHandler struct {
	allowedIPService service.AllowedIPService
	logger           logger.Logger
}

// NewAllowedIPHandler creates a new allowed IP handler
func NewAllowedIPHandler(allowedIPService service.AllowedIPService, logger logger.Logger) *AllowedIPHandler {
	return &AllowedIPHandler{
		allowedIPService: allowedIPService,
		logger:           logger,
	}
}

// ListAllowedIPs handles GET /api/allowed-ips
func (h *AllowedIPHandler) ListAllowedIPs(c *gin.Context) {
	// Parse query parameters
	limitStr := c.<PERSON>fault<PERSON>("limit", "10")
	offsetStr := c.Default<PERSON>uery("offset", "0")

	// Convert limit and offset to integers
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		c.<PERSON>rror(errors.NewValidationError("invalid limit parameter"))
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		c.Error(errors.NewValidationError("invalid offset parameter"))
		return
	}

	// Get allowed IPs from service
	allowedIPs, err := h.allowedIPService.ListAllowedIPs(c.Request.Context(), limit, offset)
	if err != nil {
		c.Error(err)
		return
	}

	// Get total count for pagination
	totalCount, err := h.allowedIPService.GetAllowedIPsCount(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	// Calculate pagination info
	totalPages := (totalCount + int64(limit) - 1) / int64(limit)
	currentPage := (int64(offset) / int64(limit)) + 1

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    allowedIPs,
		"pagination": gin.H{
			"current_page": currentPage,
			"total_pages":  totalPages,
			"total_count":  totalCount,
			"limit":        limit,
			"offset":       offset,
		},
	})
}

// SyncIPs handles POST /api/allowed-ips/sync
func (h *AllowedIPHandler) SyncIPs(c *gin.Context) {
	var req allowed_ip.SyncIPsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Sync IPs using service
	if err := h.allowedIPService.SyncIPs(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "IP addresses synchronized successfully",
	})
}

// CheckIPAllowed handles GET /api/allowed-ips/check/:ip
func (h *AllowedIPHandler) CheckIPAllowed(c *gin.Context) {
	ipAddress := c.Param("ip")
	if ipAddress == "" {
		c.Error(errors.NewValidationError("IP address is required"))
		return
	}

	// Check if IP is allowed
	isAllowed, err := h.allowedIPService.IsIPAllowed(c.Request.Context(), ipAddress)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"ip_address": ipAddress,
		"is_allowed": isAllowed,
	})
}

// GetAllowedIPsStats handles GET /api/allowed-ips/stats
func (h *AllowedIPHandler) GetAllowedIPsStats(c *gin.Context) {
	// Get total count
	totalCount, err := h.allowedIPService.GetAllowedIPsCount(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	// For now, we'll just return basic stats
	// In the future, we could add more detailed statistics
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total_ips": totalCount,
		},
	})
}
