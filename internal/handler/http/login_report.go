package http

import (
	"net/http"
	"strconv"
	"time"

	"blacking-api/internal/domain/report"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type LoginReportHandler struct {
	loginReportService service.LoginReportService
}

func NewLoginReportHandler(loginReportService service.LoginReportService) *LoginReportHandler {
	return &LoginReportHandler{
		loginReportService: loginReportService,
	}
}

// GetMemberLoginReport handles member login report requests
func (h *LoginReportHandler) GetMemberLoginReport(c *gin.Context) {
	filter, err := h.parseLoginReportFilter(c)
	if err != nil {
		c.Error(err)
		return
	}

	response, err := h.loginReportService.GetMemberLoginReport(c.Request.Context(), filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetAdminLoginReport handles admin login report requests
func (h *LoginReportHandler) GetAdminLoginReport(c *gin.Context) {
	filter, err := h.parseLoginReportFilter(c)
	if err != nil {
		c.Error(err)
		return
	}

	response, err := h.loginReportService.GetAdminLoginReport(c.Request.Context(), filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetDuplicateIPReport handles duplicate IP report requests
func (h *LoginReportHandler) GetDuplicateIPReport(c *gin.Context) {
	filter, err := h.parseLoginReportFilter(c)
	if err != nil {
		c.Error(err)
		return
	}

	// Force duplicate IP filter
	filter.DuplicateIP = true

	reports, err := h.loginReportService.GetDuplicateIPReport(c.Request.Context(), filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"duplicate_ips": reports,
			"total":         len(reports),
		},
	})
}

// DownloadMemberLoginReport handles member login report CSV download
func (h *LoginReportHandler) DownloadMemberLoginReport(c *gin.Context) {
	filter, err := h.parseLoginReportFilter(c)
	if err != nil {
		c.Error(err)
		return
	}

	csvContent, err := h.loginReportService.GenerateMemberLoginCSV(c.Request.Context(), filter)
	if err != nil {
		c.Error(err)
		return
	}

	// Set headers for file download
	filename := "member_login_report_" + time.Now().Format("20060102_150405") + ".csv"
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Length", strconv.Itoa(len(csvContent)))

	c.String(http.StatusOK, csvContent)
}

// DownloadAdminLoginReport handles admin login report CSV download
func (h *LoginReportHandler) DownloadAdminLoginReport(c *gin.Context) {
	filter, err := h.parseLoginReportFilter(c)
	if err != nil {
		c.Error(err)
		return
	}

	csvContent, err := h.loginReportService.GenerateAdminLoginCSV(c.Request.Context(), filter)
	if err != nil {
		c.Error(err)
		return
	}

	// Set headers for file download
	filename := "admin_login_report_" + time.Now().Format("20060102_150405") + ".csv"
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Length", strconv.Itoa(len(csvContent)))

	c.String(http.StatusOK, csvContent)
}

// parseLoginReportFilter parses query parameters into LoginReportFilter
func (h *LoginReportHandler) parseLoginReportFilter(c *gin.Context) (report.LoginReportFilter, error) {
	filter := report.LoginReportFilter{}

	// Parse basic filters
	filter.Username = c.Query("username")
	filter.Phone = c.Query("phone")
	filter.UserID = c.Query("user_id")
	filter.IPAddress = c.Query("ip_address")
	filter.UserAgent = c.Query("user_agent")

	// Parse date filters with proper time handling
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			// Set to start of day (00:00:00) in local timezone
			startOfDay := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, time.Local)
			filter.StartDate = &startOfDay
		} else {
			return filter, errors.NewValidationError("invalid start_date format, use YYYY-MM-DD")
		}
	}

	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			// Set to end of day (23:59:59.999) in local timezone
			endOfDay := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, time.Local)
			filter.EndDate = &endOfDay
		} else {
			return filter, errors.NewValidationError("invalid end_date format, use YYYY-MM-DD")
		}
	}

	// Parse boolean filters
	if onlySuccessStr := c.Query("only_success"); onlySuccessStr != "" {
		if onlySuccess, err := strconv.ParseBool(onlySuccessStr); err == nil {
			filter.OnlySuccess = &onlySuccess
		} else {
			return filter, errors.NewValidationError("invalid only_success value, use true or false")
		}
	}

	if duplicateIPStr := c.Query("duplicate_ip"); duplicateIPStr != "" {
		if duplicateIP, err := strconv.ParseBool(duplicateIPStr); err == nil {
			filter.DuplicateIP = duplicateIP
		} else {
			return filter, errors.NewValidationError("invalid duplicate_ip value, use true or false")
		}
	}

	// Parse pagination
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			filter.Limit = limit
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			filter.Offset = offset
		}
	}

	return filter, nil
}

// GetFilterOptions handles filter options requests for dropdowns
func (h *LoginReportHandler) GetFilterOptions(c *gin.Context) {
	optionType := c.Param("type")

	switch optionType {
	case "user-agents":
		userAgents, err := h.loginReportService.GetDistinctUserAgents(c.Request.Context())
		if err != nil {
			c.Error(err)
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"user_agents": userAgents,
			},
		})

	case "ip-addresses":
		ipAddresses, err := h.loginReportService.GetDistinctIPAddresses(c.Request.Context())
		if err != nil {
			c.Error(err)
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"ip_addresses": ipAddresses,
			},
		})

	case "member-usernames":
		usernames, err := h.loginReportService.GetDistinctMemberUsernames(c.Request.Context())
		if err != nil {
			c.Error(err)
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"usernames": usernames,
			},
		})

	case "admin-usernames":
		usernames, err := h.loginReportService.GetDistinctAdminUsernames(c.Request.Context())
		if err != nil {
			c.Error(err)
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"usernames": usernames,
			},
		})

	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_OPTION_TYPE",
				"message": "Invalid option type. Available types: user-agents, ip-addresses, member-usernames, admin-usernames",
			},
		})
	}
}

// GetAllFilterOptions handles request for all filter options at once
func (h *LoginReportHandler) GetAllFilterOptions(c *gin.Context) {
	ctx := c.Request.Context()

	// Get all options concurrently
	userAgentsChan := make(chan []string, 1)
	ipAddressesChan := make(chan []string, 1)
	memberUsernamesChan := make(chan []string, 1)
	adminUsernamesChan := make(chan []string, 1)
	errorChan := make(chan error, 4)

	// Get user agents
	go func() {
		userAgents, err := h.loginReportService.GetDistinctUserAgents(ctx)
		if err != nil {
			errorChan <- err
			return
		}
		userAgentsChan <- userAgents
	}()

	// Get IP addresses
	go func() {
		ipAddresses, err := h.loginReportService.GetDistinctIPAddresses(ctx)
		if err != nil {
			errorChan <- err
			return
		}
		ipAddressesChan <- ipAddresses
	}()

	// Get member usernames
	go func() {
		usernames, err := h.loginReportService.GetDistinctMemberUsernames(ctx)
		if err != nil {
			errorChan <- err
			return
		}
		memberUsernamesChan <- usernames
	}()

	// Get admin usernames
	go func() {
		usernames, err := h.loginReportService.GetDistinctAdminUsernames(ctx)
		if err != nil {
			errorChan <- err
			return
		}
		adminUsernamesChan <- usernames
	}()

	// Collect results
	var userAgents, ipAddresses, memberUsernames, adminUsernames []string
	for i := 0; i < 4; i++ {
		select {
		case userAgents = <-userAgentsChan:
		case ipAddresses = <-ipAddressesChan:
		case memberUsernames = <-memberUsernamesChan:
		case adminUsernames = <-adminUsernamesChan:
		case err := <-errorChan:
			c.Error(err)
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"user_agents":      userAgents,
			"ip_addresses":     ipAddresses,
			"member_usernames": memberUsernames,
			"admin_usernames":  adminUsernames,
		},
	})
}
