package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/service"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// OTPHandler handles OTP-related HTTP requests
type OTPHandler struct {
	otpService service.OTPService
	logger     logger.Logger
}

// NewOTPHandler creates a new OTP handler
func NewOTPHandler(otpService service.OTPService, logger logger.Logger) *OTPHandler {
	return &OTPHandler{
		otpService: otpService,
		logger:     logger,
	}
}

// ListOTPs handles GET /api/v1/otps
func (h *OTPHandler) ListOTPs(c *gin.Context) {
	// Parse query parameters
	limitStr := c.<PERSON>("limit", "10")
	offsetStr := c.<PERSON>("offset", "0")
	search := c.Query("search")
	username := c.Query("username")
	userID := c.Query("user_id")
	referUserIDs := c.QueryArray("refer_user_ids") // รับเป็น array
	purpose := c.Query("purpose")
	dateFrom := c.Query("date_from")
	dateTo := c.Query("date_to")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	otps, total, err := h.otpService.ListOTPsForAdmin(
		c.Request.Context(),
		limit,
		offset,
		search,
		username,
		userID,
		referUserIDs,
		purpose,
		dateFrom,
		dateTo,
	)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"otps": otps,
			"pagination": gin.H{
				"total":  total,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

// GetOTPOptions handles GET /api/v1/otps/options
func (h *OTPHandler) GetOTPOptions(c *gin.Context) {
	// Get OTP options from service
	response, err := h.otpService.GetOTPOptions(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}
