package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/promotion_web"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type PromotionWebHandler struct {
	promotionWebService service.PromotionWebService
	logger              logger.Logger
}

func NewPromotionWebHandler(promotionWebService service.PromotionWebService, logger logger.Logger) *PromotionWebHandler {
	return &PromotionWebHandler{
		promotionWebService: promotionWebService,
		logger:              logger,
	}
}

// CreatePromotionWeb handles POST /api/v1/promotion-webs
func (h *PromotionWebHandler) CreatePromotionWeb(c *gin.Context) {
	var req promotion_web.PromotionWebCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	id, err := h.promotionWebService.CreatePromotionWeb(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data": gin.H{
			"id": id,
		},
		"message": "Promotion web created successfully",
	})
}

// GetPromotionWebList handles GET /api/v1/promotion-webs
func (h *PromotionWebHandler) GetPromotionWebList(c *gin.Context) {
	var req promotion_web.PromotionWebGetListRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}

	req.Search = c.Query("search")
	req.StartDate = c.Query("start_date")
	req.EndDate = c.Query("end_date")

	if statusId := c.Query("promotion_web_status_id"); statusId != "" {
		if s, err := strconv.ParseInt(statusId, 10, 64); err == nil {
			req.PromotionWebStatusId = &s
		}
	}

	list, total, err := h.promotionWebService.GetPromotionWebList(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items": list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
		"message": "Promotion web list retrieved successfully",
	})
}

// GetPromotionWebById handles GET /api/v1/promotion-webs/:id
func (h *PromotionWebHandler) GetPromotionWebById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	result, err := h.promotionWebService.GetPromotionWebById(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Promotion web retrieved successfully",
	})
}

// UpdatePromotionWeb handles PUT /api/v1/promotion-webs/:id
func (h *PromotionWebHandler) UpdatePromotionWeb(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	var req promotion_web.PromotionWebUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	req.Id = id

	err = h.promotionWebService.UpdatePromotionWeb(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web updated successfully",
	})
}

// DeletePromotionWeb handles DELETE /api/v1/promotion-webs/:id
func (h *PromotionWebHandler) DeletePromotionWeb(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	var req promotion_web.DeletePromotionWebRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	req.Id = id

	err = h.promotionWebService.DeletePromotionWeb(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web deleted successfully",
	})
}

// CancelPromotionWeb handles POST /api/v1/promotion-webs/:id/cancel
func (h *PromotionWebHandler) CancelPromotionWeb(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	var req promotion_web.CancelPromotionWebRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	req.Id = id

	err = h.promotionWebService.CancelPromotionWeb(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web canceled successfully",
	})
}

// GetPromotionWebUserToCancel handles GET /api/v1/promotion-webs/:id/users-to-cancel
func (h *PromotionWebHandler) GetPromotionWebUserToCancel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	result, err := h.promotionWebService.GetPromotionWebUserToCancel(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Promotion web users to cancel retrieved successfully",
	})
}

// GetUserPromotionWebList handles GET /api/v1/promotion-web-users
func (h *PromotionWebHandler) GetUserPromotionWebList(c *gin.Context) {
	var req promotion_web.PromotionWebUserGetListRequest

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}

	req.Search = c.Query("search")
	req.StartDate = c.Query("start_date")
	req.EndDate = c.Query("end_date")

	if promotionWebId := c.Query("promotion_web_id"); promotionWebId != "" {
		if p, err := strconv.ParseInt(promotionWebId, 10, 64); err == nil {
			req.PromotionWebId = &p
		}
	}

	if statusId := c.Query("promotion_web_user_status_id"); statusId != "" {
		if s, err := strconv.ParseInt(statusId, 10, 64); err == nil {
			req.PromotionWebUserStatusId = &s
		}
	}

	list, total, err := h.promotionWebService.GetUserPromotionWebList(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items": list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
		"message": "User promotion web list retrieved successfully",
	})
}

// GetPromotionWebUserById handles GET /api/v1/promotion-web-users/:id
func (h *PromotionWebHandler) GetPromotionWebUserById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web user ID"))
		return
	}

	req := promotion_web.GetPromotionWebUserById{Id: id}
	result, err := h.promotionWebService.GetPromotionWebUserById(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Promotion web user retrieved successfully",
	})
}

// GetUserPromotionWebByUserId handles GET /api/v1/users/:user_id/promotion-web
func (h *PromotionWebHandler) GetUserPromotionWebByUserId(c *gin.Context) {
	userIdStr := c.Param("user_id")
	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid user ID"))
		return
	}

	result, err := h.promotionWebService.GetUserPromotionWebByUserId(c.Request.Context(), userId)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "User promotion web retrieved successfully",
	})
}

// PromotionWebUserGetListByUserId handles GET /api/v1/users/:user_id/promotion-web-users
func (h *PromotionWebHandler) PromotionWebUserGetListByUserId(c *gin.Context) {
	userIdStr := c.Param("user_id")
	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid user ID"))
		return
	}

	var req promotion_web.PromotionWebUserGetListByUserIdRequest
	req.UserId = userId

	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			req.Limit = l
		}
	}

	req.Search = c.Query("search")
	req.OfDate = c.Query("of_date")
	req.DateType = c.Query("date_type")
	req.FromDate = c.Query("from_date")
	req.ToDate = c.Query("to_date")
	req.SortCol = c.Query("sort_col")
	req.SortAsc = c.Query("sort_asc")

	if statusId := c.Query("promotion_web_user_status_id"); statusId != "" {
		if s, err := strconv.ParseInt(statusId, 10, 64); err == nil {
			req.PromotionWebUserStatusId = &s
		}
	}

	list, total, err := h.promotionWebService.PromotionWebUserGetListByUserId(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items": list,
			"total": total,
			"page":  req.Page,
			"limit": req.Limit,
		},
		"message": "Promotion web user list by user ID retrieved successfully",
	})
}

// CancelPromotionWebUserById handles POST /api/v1/promotion-web-users/:id/cancel
func (h *PromotionWebHandler) CancelPromotionWebUserById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web user ID"))
		return
	}

	var req promotion_web.CancelPromotionWebUserById
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	req.Id = id

	err = h.promotionWebService.CancelPromotionWebUserById(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web user canceled successfully",
	})
}

// GetActivePromotionWebSlides handles GET /api/v1/promotion-webs/slides/active
func (h *PromotionWebHandler) GetActivePromotionWebSlides(c *gin.Context) {
	result, err := h.promotionWebService.PromotionWebGetSildeListOnlyActive(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Active promotion web slides retrieved successfully",
	})
}

// UpdatePromotionWebPriorityOrder handles PUT /api/v1/promotion-webs/:id/priority-order
func (h *PromotionWebHandler) UpdatePromotionWebPriorityOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errors.NewValidationError("invalid promotion web ID"))
		return
	}

	err = h.promotionWebService.UpdatePromotionWebPriorityOrderCreate(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web priority order updated successfully",
	})
}

// SortPromotionWebPriorityOrder handles POST /api/v1/promotion-webs/sort-priority
func (h *PromotionWebHandler) SortPromotionWebPriorityOrder(c *gin.Context) {
	var req promotion_web.DragSortRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	err := h.promotionWebService.SortPromotionWebPriorityOrder(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Promotion web priority order sorted successfully",
	})
}

// UploadImageToCloudflare handles POST /api/v1/promotion-webs/upload-image
func (h *PromotionWebHandler) UploadImageToCloudflare(c *gin.Context) {
	pathUpload := c.PostForm("path_upload")
	if pathUpload == "" {
		c.Error(errors.NewValidationError("path_upload is required"))
		return
	}

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.Error(errors.NewValidationError("file is required"))
		return
	}
	defer file.Close()

	result, err := h.promotionWebService.UploadImageToCloudflare(c.Request.Context(), pathUpload, header.Filename, file)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
		"message": "Image uploaded to Cloudflare successfully",
	})
}
