package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/member_group_type"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// MemberGroupTypeHandler handles member group type-related HTTP requests
type MemberGroupTypeHandler struct {
	memberGroupTypeService service.MemberGroupTypeService
	logger                 logger.Logger
}

// NewMemberGroupTypeHandler creates a new member group type handler
func NewMemberGroupTypeHandler(memberGroupTypeService service.MemberGroupTypeService, logger logger.Logger) *MemberGroupTypeHandler {
	return &MemberGroupTypeHandler{
		memberGroupTypeService: memberGroupTypeService,
		logger:                 logger,
	}
}

// Helper function to parse form data to CreateMemberGroupTypeRequest
func (h *MemberGroupTypeHandler) parseCreateFormData(c *gin.Context) (member_group_type.CreateMemberGroupTypeRequest, error) {
	req := member_group_type.CreateMemberGroupTypeRequest{
		Name:                       c.<PERSON>orm("name"),
		ShowInLobby:                c.<PERSON>("show_in_lobby") == "true",
		BadgeBgColor:               c.PostForm("badge_bg_color"),
		BadgeBorderColor:           c.PostForm("badge_border_color"),
		Image1:                     getStringPtr(c.PostForm("image1")),
		Image2:                     getStringPtr(c.PostForm("image2")),
		BgImage:                    getStringPtr(c.PostForm("bg_image")),
		VipEnabled:                 c.PostForm("vip_enabled") == "true",
		VipPersonalCustomerService: c.PostForm("vip_personal_customer_service") == "true",
		VipMaxDailyWithdraw:        c.PostForm("vip_max_daily_withdraw") == "true",
		VipEventParticipation:      c.PostForm("vip_event_participation") == "true",
		BonusEnabled:               c.PostForm("bonus_enabled") == "true",
		BonusBirthday:              c.PostForm("bonus_birthday") == "true",
		BonusLevelMaintenance:      c.PostForm("bonus_level_maintenance") == "true",
		BonusLevelUpgrade:          c.PostForm("bonus_level_upgrade") == "true",
		BonusFestival:              c.PostForm("bonus_festival") == "true",
		CashbackEnabled:            c.PostForm("cashback_enabled") == "true",
		CashbackSports:             c.PostForm("cashback_sports") == "true",
		CashbackCasino:             c.PostForm("cashback_casino") == "true",
		CashbackFishing:            c.PostForm("cashback_fishing") == "true",
		CashbackSlot:               c.PostForm("cashback_slot") == "true",
		CashbackLottery:            c.PostForm("cashback_lottery") == "true",
		CashbackCard:               c.PostForm("cashback_card") == "true",
		CashbackOther:              c.PostForm("cashback_other") == "true",
		UpgradeCalculationType:     member_group_type.CalculationType(c.PostForm("upgrade_calculation_type")),
		UpgradeConditionType:       member_group_type.ConditionType(c.PostForm("upgrade_condition_type")),
		DowngradeCalculationType:   member_group_type.CalculationType(c.PostForm("downgrade_calculation_type")),
		DowngradeConditionType:     member_group_type.ConditionType(c.PostForm("downgrade_condition_type")),
	}

	// Parse numeric fields
	if val := c.PostForm("upgrade_betting_amount"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			req.UpgradeBettingAmount = parsed
		}
	}
	if val := c.PostForm("upgrade_deposit_amount"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			req.UpgradeDepositAmount = parsed
		}
	}
	if val := c.PostForm("upgrade_days"); val != "" {
		if parsed, err := strconv.Atoi(val); err == nil {
			req.UpgradeDays = parsed
		}
	}
	if val := c.PostForm("downgrade_betting_amount"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			req.DowngradeBettingAmount = parsed
		}
	}
	if val := c.PostForm("downgrade_deposit_amount"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			req.DowngradeDepositAmount = parsed
		}
	}
	if val := c.PostForm("downgrade_days"); val != "" {
		if parsed, err := strconv.Atoi(val); err == nil {
			req.DowngradeDays = parsed
		}
	}

	return req, nil
}

// Helper function to parse form data to UpdateMemberGroupTypeRequest
func (h *MemberGroupTypeHandler) parseUpdateFormData(c *gin.Context) (member_group_type.UpdateMemberGroupTypeRequest, error) {
	req := member_group_type.UpdateMemberGroupTypeRequest{}

	// Only set fields that are provided in form data
	if name := c.PostForm("name"); name != "" {
		req.Name = name
	}
	// For boolean fields, check if the form field exists (even if empty)
	if _, exists := c.GetPostForm("show_in_lobby"); exists {
		req.ShowInLobby = c.PostForm("show_in_lobby") == "true"
	}
	if badgeBgColor := c.PostForm("badge_bg_color"); badgeBgColor != "" {
		req.BadgeBgColor = badgeBgColor
	}
	if badgeBorderColor := c.PostForm("badge_border_color"); badgeBorderColor != "" {
		req.BadgeBorderColor = badgeBorderColor
	}
	if image1 := c.PostForm("image1"); image1 != "" {
		req.Image1 = &image1
	}
	if image2 := c.PostForm("image2"); image2 != "" {
		req.Image2 = &image2
	}
	if bgImage := c.PostForm("bg_image"); bgImage != "" {
		req.BgImage = &bgImage
	}

	// Parse delete flags
	if image1Delete := c.PostForm("image1_delete"); image1Delete != "" {
		req.Image1Delete = image1Delete
	}
	if image2Delete := c.PostForm("image2_delete"); image2Delete != "" {
		req.Image2Delete = image2Delete
	}
	if bgImageDelete := c.PostForm("bg_image_delete"); bgImageDelete != "" {
		req.BgImageDelete = bgImageDelete
	}

	if _, exists := c.GetPostForm("vip_enabled"); exists {
		req.VipEnabled = c.PostForm("vip_enabled") == "true"
	}
	if _, exists := c.GetPostForm("vip_personal_customer_service"); exists {
		req.VipPersonalCustomerService = c.PostForm("vip_personal_customer_service") == "true"
	}
	if _, exists := c.GetPostForm("vip_max_daily_withdraw"); exists {
		req.VipMaxDailyWithdraw = c.PostForm("vip_max_daily_withdraw") == "true"
	}
	if _, exists := c.GetPostForm("vip_event_participation"); exists {
		req.VipEventParticipation = c.PostForm("vip_event_participation") == "true"
	}
	if _, exists := c.GetPostForm("bonus_enabled"); exists {
		req.BonusEnabled = c.PostForm("bonus_enabled") == "true"
	}
	if _, exists := c.GetPostForm("bonus_birthday"); exists {
		req.BonusBirthday = c.PostForm("bonus_birthday") == "true"
	}
	if _, exists := c.GetPostForm("bonus_level_maintenance"); exists {
		req.BonusLevelMaintenance = c.PostForm("bonus_level_maintenance") == "true"
	}
	if _, exists := c.GetPostForm("bonus_level_upgrade"); exists {
		req.BonusLevelUpgrade = c.PostForm("bonus_level_upgrade") == "true"
	}
	if _, exists := c.GetPostForm("bonus_festival"); exists {
		req.BonusFestival = c.PostForm("bonus_festival") == "true"
	}
	if _, exists := c.GetPostForm("cashback_enabled"); exists {
		req.CashbackEnabled = c.PostForm("cashback_enabled") == "true"
	}
	if _, exists := c.GetPostForm("cashback_sports"); exists {
		req.CashbackSports = c.PostForm("cashback_sports") == "true"
	}
	if _, exists := c.GetPostForm("cashback_casino"); exists {
		req.CashbackCasino = c.PostForm("cashback_casino") == "true"
	}
	if _, exists := c.GetPostForm("cashback_fishing"); exists {
		req.CashbackFishing = c.PostForm("cashback_fishing") == "true"
	}
	if _, exists := c.GetPostForm("cashback_slot"); exists {
		req.CashbackSlot = c.PostForm("cashback_slot") == "true"
	}
	if _, exists := c.GetPostForm("cashback_lottery"); exists {
		req.CashbackLottery = c.PostForm("cashback_lottery") == "true"
	}
	if _, exists := c.GetPostForm("cashback_card"); exists {
		req.CashbackCard = c.PostForm("cashback_card") == "true"
	}
	if _, exists := c.GetPostForm("cashback_other"); exists {
		req.CashbackOther = c.PostForm("cashback_other") == "true"
	}
	if upgradeCalculationType := c.PostForm("upgrade_calculation_type"); upgradeCalculationType != "" {
		req.UpgradeCalculationType = member_group_type.CalculationType(upgradeCalculationType)
	}
	if upgradeConditionType := c.PostForm("upgrade_condition_type"); upgradeConditionType != "" {
		req.UpgradeConditionType = member_group_type.ConditionType(upgradeConditionType)
	}
	if downgradeCalculationType := c.PostForm("downgrade_calculation_type"); downgradeCalculationType != "" {
		req.DowngradeCalculationType = member_group_type.CalculationType(downgradeCalculationType)
	}
	if downgradeConditionType := c.PostForm("downgrade_condition_type"); downgradeConditionType != "" {
		req.DowngradeConditionType = member_group_type.ConditionType(downgradeConditionType)
	}

	// Parse numeric fields
	if val := c.PostForm("upgrade_betting_amount"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			req.UpgradeBettingAmount = parsed
		}
	}
	if val := c.PostForm("upgrade_deposit_amount"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			req.UpgradeDepositAmount = parsed
		}
	}
	if val := c.PostForm("upgrade_days"); val != "" {
		if parsed, err := strconv.Atoi(val); err == nil {
			req.UpgradeDays = parsed
		}
	}
	if val := c.PostForm("downgrade_betting_amount"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			req.DowngradeBettingAmount = parsed
		}
	}
	if val := c.PostForm("downgrade_deposit_amount"); val != "" {
		if parsed, err := strconv.ParseFloat(val, 64); err == nil {
			req.DowngradeDepositAmount = parsed
		}
	}
	if val := c.PostForm("downgrade_days"); val != "" {
		if parsed, err := strconv.Atoi(val); err == nil {
			req.DowngradeDays = parsed
		}
	}

	return req, nil
}

// CreateMemberGroupType handles POST /api/v1/member-group-types
func (h *MemberGroupTypeHandler) CreateMemberGroupType(c *gin.Context) {
	// Parse multipart form
	if err := c.Request.ParseMultipartForm(10 << 20); err != nil { // 10MB max
		c.Error(errors.NewValidationError("failed to parse form data: " + err.Error()))
		return
	}

	// Parse form data to request struct
	req, err := h.parseCreateFormData(c)
	if err != nil {
		c.Error(errors.NewValidationError("failed to parse form data: " + err.Error()))
		return
	}

	// Handle file uploads for image fields ending with _file
	imageFields := []struct {
		FieldName string
		TargetPtr **string
	}{
		{"image1_file", &req.Image1},
		{"image2_file", &req.Image2},
		{"bg_image_file", &req.BgImage},
	}

	for _, field := range imageFields {
		if _, _, err := c.Request.FormFile(field.FieldName); err == nil {
			// Upload file using service with specific field name
			fileInfo, err := h.memberGroupTypeService.FileUpload(c.Request.Context(), c.Request, field.FieldName)
			if err != nil {
				c.Error(errors.NewValidationError("failed to upload file: " + field.FieldName + ": " + err.Error()))
				return
			}
			*field.TargetPtr = &fileInfo.FileUrl
		}
	}

	memberGroupTypeResp, err := h.memberGroupTypeService.CreateMemberGroupType(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    memberGroupTypeResp,
	})
}

// GetMemberGroupType handles GET /api/v1/member-group-types/:id
func (h *MemberGroupTypeHandler) GetMemberGroupType(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member group type ID"))
		return
	}

	memberGroupTypeResp, err := h.memberGroupTypeService.GetMemberGroupTypeByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberGroupTypeResp,
	})
}

// UpdateMemberGroupType handles PUT /api/v1/member-group-types/:id
func (h *MemberGroupTypeHandler) UpdateMemberGroupType(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member group type ID"))
		return
	}

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(10 << 20); err != nil { // 10MB max
		c.Error(errors.NewValidationError("failed to parse form data: " + err.Error()))
		return
	}

	// Parse form data to request struct
	req, err := h.parseUpdateFormData(c)
	if err != nil {
		c.Error(errors.NewValidationError("failed to parse form data: " + err.Error()))
		return
	}

	// Handle file uploads for image fields ending with _file
	imageFields := []struct {
		FieldName string
		TargetPtr **string
	}{
		{"image1_file", &req.Image1},
		{"image2_file", &req.Image2},
		{"bg_image_file", &req.BgImage},
	}

	for _, field := range imageFields {
		if _, _, err := c.Request.FormFile(field.FieldName); err == nil {
			// Upload file using service with specific field name
			fileInfo, err := h.memberGroupTypeService.FileUpload(c.Request.Context(), c.Request, field.FieldName)
			if err != nil {
				c.Error(errors.NewValidationError("failed to upload file: " + field.FieldName + ": " + err.Error()))
				return
			}
			*field.TargetPtr = &fileInfo.FileUrl
		}
	}

	memberGroupTypeResp, err := h.memberGroupTypeService.UpdateMemberGroupType(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberGroupTypeResp,
	})
}

// DeleteMemberGroupType handles DELETE /api/v1/member-group-types/:id
func (h *MemberGroupTypeHandler) DeleteMemberGroupType(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member group type ID"))
		return
	}

	if err := h.memberGroupTypeService.DeleteMemberGroupType(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "member group type deleted successfully",
	})
}

// ListMemberGroupTypes handles GET /api/v1/member-group-types
func (h *MemberGroupTypeHandler) ListMemberGroupTypes(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search")        // Search by name
	sortBy := c.Query("sort_by")       // Sort column (id, name, position, created_at, updated_at)
	sortOrder := c.Query("sort_order") // Sort order (asc, desc)

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Validate sort parameters
	validSortColumns := map[string]bool{
		"id":         true,
		"name":       true,
		"position":   true,
		"created_at": true,
		"updated_at": true,
	}

	if sortBy != "" && !validSortColumns[sortBy] {
		sortBy = "position" // Default sort column
	}

	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc" // Default sort order
	}

	memberGroupTypes, total, err := h.memberGroupTypeService.ListMemberGroupTypes(c.Request.Context(), limit, offset, search, sortBy, sortOrder)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"member_group_types": memberGroupTypes,
			"pagination": gin.H{
				"total":      total,
				"limit":      limit,
				"offset":     offset,
				"sort_by":    sortBy,
				"sort_order": sortOrder,
			},
		},
	})
}

// ListActiveMemberGroupTypes handles GET /api/v1/member-group-types/active
func (h *MemberGroupTypeHandler) ListActiveMemberGroupTypes(c *gin.Context) {
	memberGroupTypes, err := h.memberGroupTypeService.ListActiveMemberGroupTypes(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"member_group_types": memberGroupTypes,
		},
	})
}

// ReorderMemberGroupType handles PUT /api/v1/member-group-types/:id/reorder
func (h *MemberGroupTypeHandler) ReorderMemberGroupType(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid member group type ID"))
		return
	}

	// Parse request body for direction
	var req struct {
		Direction string `json:"direction" validate:"required,oneof=up down"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	if err := h.memberGroupTypeService.ReorderMemberGroupType(c.Request.Context(), id, req.Direction); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "member group type reordered successfully",
	})
}

// ListMemberGroupTypesForDropdown handles GET /api/v1/member-group-types/dropdown
func (h *MemberGroupTypeHandler) ListMemberGroupTypesForDropdown(c *gin.Context) {
	dropdownItems, err := h.memberGroupTypeService.ListMemberGroupTypesForDropdown(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"member_group_types": dropdownItems,
		},
	})
}

// FileUpload handles POST /api/v1/member-group-types/upload
func (h *MemberGroupTypeHandler) FileUpload(c *gin.Context) {
	// Get field name from query parameter, default to "file"
	fieldName := c.DefaultQuery("field", "file")

	fileInfo, err := h.memberGroupTypeService.FileUpload(c.Request.Context(), c.Request, fieldName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fileInfo,
		"message": "File uploaded successfully",
	})
}

// DeleteFile handles DELETE /api/v1/member-group-types/file
func (h *MemberGroupTypeHandler) DeleteFile(c *gin.Context) {
	var req member_group_type.DeleteFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.memberGroupTypeService.DeleteFile(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "File deleted successfully",
	})
}
