package http

import (
	"blacking-api/internal/domain/payment_method"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"strconv"
)

type PaymentMethodHandler struct {
	paymentMethodService service.PaymentMethodService
	logger               logger.Logger
}

func NewPaymentMethodHandler(paymentMethodService service.PaymentMethodService, logger logger.Logger) *PaymentMethodHandler {
	return &PaymentMethodHandler{
		paymentMethodService: paymentMethodService,
		logger:               logger,
	}
}

func (h *PaymentMethodHandler) CreatePaymentMethod(c *gin.Context) {
	var req *payment_method.PaymentMethodRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentMethodService.CreatePaymentMethod(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create payment method", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.<PERSON>(201, gin.H{
		"success": true,
		"message": "Payment method created successfully",
	})
}

func (h *PaymentMethodHandler) ListPaymentMethods(c *gin.Context) {
	paymentMethods, err := h.paymentMethodService.ListPaymentMethods(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to list payment methods", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    paymentMethods,
	})
}

func (h *PaymentMethodHandler) GetPaymentMethodByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment method ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	paymentMethod, err := h.paymentMethodService.FindPaymentMethodByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to find payment method by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    paymentMethod,
	})
}

func (h *PaymentMethodHandler) UpdatePaymentMethod(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment method ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *payment_method.PaymentMethodRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentMethodService.UpdatePaymentMethod(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update payment method", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment method updated successfully",
	})
}

func (h *PaymentMethodHandler) UpdatePaymentMethodStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment method ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *payment_method.UpdateStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentMethodService.UpdatePaymentMethodStatus(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update payment method status", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment method status updated successfully",
	})
}

func (h *PaymentMethodHandler) DeletePaymentMethod(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("payment method ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.paymentMethodService.DeletePaymentMethod(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete payment method", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Payment method deleted successfully",
	})
}
