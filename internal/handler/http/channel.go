package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/channel"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// ChannelHandler handles channel-related HTTP requests
type ChannelHandler struct {
	channelService service.ChannelService
	logger         logger.Logger
}

// NewChannelHandler creates a new channel handler
func NewChannelHandler(channelService service.ChannelService, logger logger.Logger) *ChannelHandler {
	return &ChannelHandler{
		channelService: channelService,
		logger:         logger,
	}
}

// CreateChannel handles POST /api/v1/channels
func (h *ChannelHandler) CreateChannel(c *gin.Context) {
	var req channel.CreateChannelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Get user ID from JWT (assuming it's set by auth middleware)
	createdBy := "admin" // TODO: Extract from JWT token
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(string); ok {
			createdBy = uid
		}
	}

	channelResp, err := h.channelService.CreateChannel(c.Request.Context(), req, createdBy)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    channelResp,
	})
}

// GetChannel handles GET /api/v1/channels/:id
func (h *ChannelHandler) GetChannel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid channel ID"))
		return
	}

	channelResp, err := h.channelService.GetChannelByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    channelResp,
	})
}

// UpdateChannel handles PUT /api/v1/channels/:id
func (h *ChannelHandler) UpdateChannel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid channel ID"))
		return
	}

	var req channel.UpdateChannelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	channelResp, err := h.channelService.UpdateChannel(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    channelResp,
	})
}

// DeleteChannel handles DELETE /api/v1/channels/:id
func (h *ChannelHandler) DeleteChannel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid channel ID"))
		return
	}

	if err := h.channelService.DeleteChannel(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "channel deleted successfully",
	})
}

// ListChannels handles GET /api/v1/channels
func (h *ChannelHandler) ListChannels(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search") // Search by name

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	channels, total, err := h.channelService.ListChannels(c.Request.Context(), limit, offset, search)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"channels": channels,
			"pagination": gin.H{
				"total":  total,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

// ListActiveChannels handles GET /api/v1/channels/active
func (h *ChannelHandler) ListActiveChannels(c *gin.Context) {
	channels, err := h.channelService.ListActiveChannels(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"channels": channels,
		},
	})
}

// ListChannelsByPlatform handles GET /api/v1/channels/platform/:platformId
func (h *ChannelHandler) ListChannelsByPlatform(c *gin.Context) {
	platformID := c.Param("platformId")
	if platformID == "" {
		c.Error(errors.NewValidationError("platform ID is required"))
		return
	}

	channels, err := h.channelService.ListChannelsByPlatform(c.Request.Context(), platformID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"channels":    channels,
			"platform_id": platformID,
		},
	})
}
