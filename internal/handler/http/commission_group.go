package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/commission_group"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// CommissionGroupHandler handles commission group-related HTTP requests
type CommissionGroupHandler struct {
	commissionGroupService service.CommissionGroupService
	logger                 logger.Logger
}

// NewCommissionGroupHandler creates a new commission group handler
func NewCommissionGroupHandler(commissionGroupService service.CommissionGroupService, logger logger.Logger) *CommissionGroupHandler {
	return &CommissionGroupHandler{
		commissionGroupService: commissionGroupService,
		logger:                 logger,
	}
}

// CreateCommissionGroup handles POST /api/v1/commission-groups
func (h *CommissionGroupHandler) CreateCommissionGroup(c *gin.Context) {
	var req commission_group.CreateCommissionGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.<PERSON>rror(errors.NewValidationError("invalid request body"))
		return
	}

	commissionGroupResp, err := h.commissionGroupService.CreateCommissionGroup(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    commissionGroupResp,
	})
}

// GetCommissionGroup handles GET /api/v1/commission-groups/:id
func (h *CommissionGroupHandler) GetCommissionGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid commission group ID"))
		return
	}

	commissionGroupResp, err := h.commissionGroupService.GetCommissionGroupByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    commissionGroupResp,
	})
}

// GetDefaultCommissionGroup handles GET /api/v1/commission-groups/default
func (h *CommissionGroupHandler) GetDefaultCommissionGroup(c *gin.Context) {
	commissionGroupResp, err := h.commissionGroupService.GetDefaultCommissionGroup(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    commissionGroupResp,
	})
}

// UpdateCommissionGroup handles PUT /api/v1/commission-groups/:id
func (h *CommissionGroupHandler) UpdateCommissionGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid commission group ID"))
		return
	}

	var req commission_group.UpdateCommissionGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	commissionGroupResp, err := h.commissionGroupService.UpdateCommissionGroup(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    commissionGroupResp,
	})
}

// DeleteCommissionGroup handles DELETE /api/v1/commission-groups/:id
func (h *CommissionGroupHandler) DeleteCommissionGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid commission group ID"))
		return
	}

	if err := h.commissionGroupService.DeleteCommissionGroup(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "commission group deleted successfully",
	})
}

// ListCommissionGroups handles GET /api/v1/commission-groups
func (h *CommissionGroupHandler) ListCommissionGroups(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search")        // Search by name
	sortBy := c.Query("sort_by")       // Sort column (id, name, is_default, created_at, updated_at)
	sortOrder := c.Query("sort_order") // Sort order (asc, desc)

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Validate sort parameters
	validSortColumns := map[string]bool{
		"id":         true,
		"name":       true,
		"is_default": true,
		"created_at": true,
		"updated_at": true,
	}

	if sortBy != "" && !validSortColumns[sortBy] {
		sortBy = "is_default" // Default sort column (default first)
	}

	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc" // Default sort order
	}

	commissionGroups, total, err := h.commissionGroupService.ListCommissionGroups(c.Request.Context(), limit, offset, search, sortBy, sortOrder)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"commission_groups": commissionGroups,
			"pagination": gin.H{
				"total":      total,
				"limit":      limit,
				"offset":     offset,
				"sort_by":    sortBy,
				"sort_order": sortOrder,
			},
		},
	})
}

// ListActiveCommissionGroups handles GET /api/v1/commission-groups/active
func (h *CommissionGroupHandler) ListActiveCommissionGroups(c *gin.Context) {
	commissionGroups, err := h.commissionGroupService.ListActiveCommissionGroups(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"commission_groups": commissionGroups,
		},
	})
}

// ListCommissionGroupsForDropdown handles GET /api/v1/commission-groups/dropdown
func (h *CommissionGroupHandler) ListCommissionGroupsForDropdown(c *gin.Context) {
	dropdownItems, err := h.commissionGroupService.ListCommissionGroupsForDropdown(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"commission_groups": dropdownItems,
		},
	})
}

// SetDefaultCommissionGroup handles PUT /api/v1/commission-groups/:id/set-default
func (h *CommissionGroupHandler) SetDefaultCommissionGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid commission group ID"))
		return
	}

	if err := h.commissionGroupService.SetDefaultCommissionGroup(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "commission group set as default successfully",
	})
}
