package http

import (
	"blacking-api/internal/domain/deposit_account"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
	"strconv"
)

type DepositAccountHandler struct {
	depositAccountService service.DepositAccountService
	logger                logger.Logger
}

func NewDepositAccountHandler(depositAccountService service.DepositAccountService, logger logger.Logger) *DepositAccountHandler {
	return &DepositAccountHandler{
		depositAccountService: depositAccountService,
		logger:                logger,
	}
}

func (h *DepositAccountHandler) CreateDepositAccount(c *gin.Context) {
	var req *deposit_account.DepositAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	err := h.depositAccountService.CreateDepositAccount(c.Request.Context(), req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"Message": "Deposit account created successfully",
	})
}

func (h *DepositAccountHandler) GetDepositAccounts(c *gin.Context) {
	var req *deposit_account.DepositAccountSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	depositAccount, err := h.depositAccountService.FindAllDepositAccounts(c.Request.Context(), req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    depositAccount,
	})
}

func (h *DepositAccountHandler) GetDepositAccountByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	depositAccount, err := h.depositAccountService.FindDepositAccountByID(c.Request.Context(), idInt)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    depositAccount,
	})
}

func (h *DepositAccountHandler) GetDepositAccountSettingAlgorithmByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	depositAccount, err := h.depositAccountService.FindDepositAccountSettingAlgorithmByID(c.Request.Context(), idInt)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    depositAccount,
	})
}

func (h *DepositAccountHandler) UpdateDepositAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *deposit_account.DepositAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, errors.NewValidationError("invalid request body"))
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.UpdateDepositAccount(c.Request.Context(), idInt, req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account updated successfully",
	})
}

func (h *DepositAccountHandler) UpdateDepositAccountAutoBot(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	autoBotIDStr := c.Query("bot")
	if autoBotIDStr == "" {
		HandleSingleError(c, errors.NewValidationError("bot ID is required"))
		return
	}

	autoBotID, err := strconv.ParseInt(autoBotIDStr, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.UpdateDepositAccountAutoBot(c.Request.Context(), idInt, autoBotID)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account auto bot updated successfully",
	})
}

func (h *DepositAccountHandler) UpdateDepositAccountAlgorithm(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *deposit_account.DepositAccountSettingAlgorithm
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, errors.NewValidationError("invalid request body"))
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.UpdateDepositAccountAlgorithm(c.Request.Context(), idInt, req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account algorithm updated successfully",
	})
}

func (h *DepositAccountHandler) ActiveDepositAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	statusStr := c.Query("status")
	if statusStr == "" {
		HandleSingleError(c, errors.NewValidationError("status is required"))
		return
	}

	status, err := strconv.ParseBool(statusStr)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.ActiveDepositAccount(c.Request.Context(), idInt, status)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account status updated successfully",
	})
}

func (h *DepositAccountHandler) DeleteDepositAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.depositAccountService.DeleteDepositAccount(c.Request.Context(), idInt)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Deposit account deleted successfully",
	})
}
