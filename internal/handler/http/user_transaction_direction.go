package http

import (
	"blacking-api/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UserTransactionDirectionHandler struct {
	userTransactionDirectionService service.UserTransactionDirectionService
}

func NewUserTransactionDirectionHandler(userTransactionDirectionService service.UserTransactionDirectionService) *UserTransactionDirectionHandler {
	return &UserTransactionDirectionHandler{
		userTransactionDirectionService: userTransactionDirectionService,
	}
}

func (h *UserTransactionDirectionHandler) ListUserTransactionDirections(c *gin.Context) {
	transactionDirections, err := h.userTransactionDirectionService.ListUserTransactionDirections(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    transactionDirections,
	})
}
