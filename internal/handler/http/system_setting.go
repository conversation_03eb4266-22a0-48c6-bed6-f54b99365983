package http

import (
	"blacking-api/internal/domain/system_setting"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type SystemSettingHandler struct {
	systemSettingService service.SystemSettingService
	logger               logger.Logger
}

// NewSystemSettingHandler creates a new system setting handler
func NewSystemSettingHandler(systemSettingService service.SystemSettingService, logger logger.Logger) *SystemSettingHandler {
	return &SystemSettingHandler{
		systemSettingService: systemSettingService,
		logger:               logger,
	}
}

// GetLoginAttemptLimit handles GET /api/system/login-attempt-limit
func (h *SystemSettingHandler) GetLoginAttemptLimit(c *gin.Context) {
	// Get login attempt limit from service
	limit, err := h.systemSettingService.GetLoginAttemptLimit(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    limit,
	})
}

// UpdateLoginAttemptLimit handles PUT /api/system/login-attempt-limit
func (h *SystemSettingHandler) UpdateLoginAttemptLimit(c *gin.Context) {
	var req system_setting.UpdateLoginAttemptLimitRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Update login attempt limit using service
	if err := h.systemSettingService.UpdateLoginAttemptLimit(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Login attempt limit updated successfully",
	})
}

// GetSystemSetting handles GET /api/system/settings/:key
func (h *SystemSettingHandler) GetSystemSetting(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		c.Error(errors.NewValidationError("setting key is required"))
		return
	}

	// Get system setting from service
	setting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), key)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    setting,
	})
}

// UpdateSystemSetting handles PUT /api/system/settings/:key
func (h *SystemSettingHandler) UpdateSystemSetting(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		c.Error(errors.NewValidationError("setting key is required"))
		return
	}

	var req system_setting.UpdateSystemSettingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Update  system setting using service
	if err := h.systemSettingService.UpdateSystemSetting(c.Request.Context(), key, req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "System setting updated successfully",
	})
}

// ListSystemSettings handles GET /api/system/settings
func (h *SystemSettingHandler) ListSystemSettings(c *gin.Context) {
	// Get all system settings from service
	settings, err := h.systemSettingService.ListSystemSettings(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
	})
}

// GetGeneralSettings handles GET /api/system-settings/general
func (h *SystemSettingHandler) GetGeneralSettings(c *gin.Context) {
	// Get general settings using service
	settings, err := h.systemSettingService.GetGeneralSettings(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
		"message": "General settings retrieved successfully",
	})
}

// UpdateGeneralSettings handles PUT /api/system-settings/general
func (h *SystemSettingHandler) UpdateGeneralSettings(c *gin.Context) {
	var req system_setting.UpdateGeneralSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Update  general settings using service
	if err := h.systemSettingService.UpdateGeneralSettings(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "General settings updated successfully",
	})
}

// GetSEOSettings handles GET /api/system-settings/seo
func (h *SystemSettingHandler) GetSEOSettings(c *gin.Context) {
	// Get SEO settings using service
	settings, err := h.systemSettingService.GetSEOSettings(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
		"message": "SEO settings retrieved successfully",
	})
}

// UpdateSEOSettings handles PUT /api/system-settings/seo
func (h *SystemSettingHandler) UpdateSEOSettings(c *gin.Context) {
	// Parse form data
	if err := c.Request.ParseMultipartForm(10 << 20); err != nil { // 10MB max
		c.Error(errors.NewValidationError("failed to parse form data: " + err.Error()))
		return
	}

	// Helper function to handle file deletion
	handleFileDeletion := func(deleteFieldName, settingKey string) error {
		if deleteValue, exists := c.GetPostForm(deleteFieldName); exists && (deleteValue == "true" || deleteValue == "1") {
			// Get current setting to delete file
			if currentSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), settingKey); err == nil && currentSetting.Value != "" {
				deleteReq := &system_setting.DeleteFileRequest{FileUrl: currentSetting.Value}
				if err := h.systemSettingService.DeleteFile(c.Request.Context(), deleteReq); err != nil {
					logger.WithError(err).Errorf("failed to delete file for %s", deleteFieldName)
				}
			}
			// Clear the setting
			updateReq := system_setting.UpdateSystemSettingRequest{Value: ""}
			return h.systemSettingService.UpdateSystemSetting(c.Request.Context(), settingKey, updateReq)
		}
		return nil
	}

	// Process delete flags first
	if err := handleFileDeletion("favicon_delete", system_setting.KeySEOFavicon); err != nil {
		c.Error(err)
		return
	}
	if err := handleFileDeletion("featured_image_delete", system_setting.KeySEOFeaturedImage); err != nil {
		c.Error(err)
		return
	}

	// Get text fields
	faviconValue := c.PostForm("favicon")
	featuredImageValue := c.PostForm("featured_image")

	req := system_setting.UpdateSEOSettingsRequest{
		SiteTitle:       c.PostForm("site_title"),
		Title:           c.PostForm("title"),
		MetaDescription: c.PostForm("meta_description"),
		Favicon:         &faviconValue,       // Keep existing URL if no file uploaded
		FeaturedImage:   &featuredImageValue, // Keep existing URL if no file uploaded
	}

	// Helper function to handle file upload with old file deletion
	handleFileUpload := func(formFieldName, settingKey, errorPrefix string, targetPtr **string) error {
		if _, _, err := c.Request.FormFile(formFieldName); err == nil {
			// Check and delete existing file first
			if currentSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), settingKey); err == nil && currentSetting.Value != "" {
				deleteReq := &system_setting.DeleteFileRequest{FileUrl: currentSetting.Value}
				if err := h.systemSettingService.DeleteFile(c.Request.Context(), deleteReq); err != nil {
					logger.WithError(err).Errorf("failed to delete existing %s file", errorPrefix)
				}
			}

			// Upload new file using service
			fileInfo, err := h.systemSettingService.FileUpload(c.Request.Context(), c.Request, formFieldName)
			if err != nil {
				return errors.NewValidationError("failed to upload " + errorPrefix + ": " + err.Error())
			}
			*targetPtr = &fileInfo.FileUrl
		}
		return nil
	}

	// Handle file uploads
	if err := handleFileUpload("favicon_file", system_setting.KeySEOFavicon, "favicon", &req.Favicon); err != nil {
		c.Error(err)
		return
	}
	if err := handleFileUpload("featured_image_file", system_setting.KeySEOFeaturedImage, "featured image", &req.FeaturedImage); err != nil {
		c.Error(err)
		return
	}

	// Update SEO settings using service
	if err := h.systemSettingService.UpdateSEOSettings(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "SEO settings updated successfully",
	})
}

// GetSiteImageSettings handles GET /api/system-settings/SiteImage
func (h *SystemSettingHandler) GetSiteImageSettings(c *gin.Context) {
	// Get SiteImage settings using service
	settings, err := h.systemSettingService.GetSiteImageSettings(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
		"message": "SiteImage settings retrieved successfully",
	})
}

// UpdateSiteImageSettings handles PUT /api/system-settings/SiteImage
func (h *SystemSettingHandler) UpdateSiteImageSettings(c *gin.Context) {
	// Parse form data
	if err := c.Request.ParseMultipartForm(50 << 20); err != nil { // 50MB max
		c.Error(errors.NewValidationError("failed to parse form data: " + err.Error()))
		return
	}
	req := system_setting.UpdateSiteImageSettingsRequest{}

	enableVideoTutorialValue, exists := c.GetPostForm("enable_video_tutorial")
	if exists {
		req.EnableVideoTutorial = &enableVideoTutorialValue
	}
	networkTutorialTextValue, exists := c.GetPostForm("network_tutorial_text")
	if exists {
		req.NetworkTutorialText = &networkTutorialTextValue
	}

	// Helper function to handle file deletion (reuse from SEO settings)
	handleFileDeletion := func(deleteFieldName, settingKey string) error {
		if deleteValue, exists := c.GetPostForm(deleteFieldName); exists && (deleteValue == "true" || deleteValue == "1") {
			// Get current setting to delete file
			if currentSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), settingKey); err == nil && currentSetting.Value != "" {
				deleteReq := &system_setting.DeleteFileRequest{FileUrl: currentSetting.Value}
				if err := h.systemSettingService.DeleteFile(c.Request.Context(), deleteReq); err != nil {
					logger.WithError(err).Errorf("failed to delete file for %s", deleteFieldName)
				}
			}
			// Clear the setting
			updateReq := system_setting.UpdateSystemSettingRequest{Value: ""}
			return h.systemSettingService.UpdateSystemSetting(c.Request.Context(), settingKey, updateReq)
		}
		return nil
	}

	// Process delete flags first
	deleteFields := map[string]string{
		"logo_before_login_delete":                 system_setting.KeySiteImageLogoBeforeLogin,
		"logo_after_login_delete":                  system_setting.KeySiteImageLogoAfterLogin,
		"game_loading_delete":                      system_setting.KeySiteImageGameLoading,
		"level_icon_delete":                        system_setting.KeySiteImageLevelIcon,
		"notification_text_delete":                 system_setting.KeySiteImageNotificationText,
		"notification_popup_delete":                system_setting.KeySiteImageNotificationPopup,
		"deposit_success_delete":                   system_setting.KeySiteImageDepositSuccess,
		"withdraw_success_delete":                  system_setting.KeySiteImageWithdrawSuccess,
		"deposit_fail_delete":                      system_setting.KeySiteImageDepositFail,
		"withdraw_fail_delete":                     system_setting.KeySiteImageWithdrawFail,
		"video_deposit_delete":                     system_setting.KeySiteImageVideoDeposit,
		"video_deposit_small_delete":               system_setting.KeySiteImageVideoDepositSmall,
		"video_deposit_decimal_delete":             system_setting.KeySiteImageVideoDepositDecimal,
		"video_deposit_decimal_small_delete":       system_setting.KeySiteImageVideoDepositDecimalSmall,
		"video_deposit_qr_delete":                  system_setting.KeySiteImageVideoDepositQR,
		"video_deposit_qr_small_delete":            system_setting.KeySiteImageVideoDepositQRSmall,
		"video_deposit_truewallet_delete":          system_setting.KeySiteImageVideoDepositTruewallet,
		"video_deposit_truewallet_small_delete":    system_setting.KeySiteImageVideoDepositTruewalletSmall,
		"network_tutorial_image_delete":            system_setting.KeySiteNetworkTutorialImage,
		"network_tutorial_make_money_image_delete": system_setting.KeySiteNetworkTutorialMakeMoneyImage,
	}

	for deleteField, settingKey := range deleteFields {
		if err := handleFileDeletion(deleteField, settingKey); err != nil {
			c.Error(err)
			return
		}
	}

	uploadFields := []struct {
		FieldName  string
		TargetPtr  **string
		UploadDir  string
		Type       string
		SettingKey string
	}{
		{"logo_before_login_file", &req.LogoBeforeLogin, "siteimage/", "image", system_setting.KeySiteImageLogoBeforeLogin},
		{"logo_after_login_file", &req.LogoAfterLogin, "siteimage/", "image", system_setting.KeySiteImageLogoAfterLogin},
		{"game_loading_file", &req.GameLoading, "siteimage/", "image", system_setting.KeySiteImageGameLoading},
		{"level_icon_file", &req.LevelIcon, "siteimage/", "image", system_setting.KeySiteImageLevelIcon},
		{"notification_text_file", &req.NotificationText, "siteimage/", "image", system_setting.KeySiteImageNotificationText},
		{"notification_popup_file", &req.NotificationPopup, "siteimage/", "image", system_setting.KeySiteImageNotificationPopup},
		{"deposit_success_file", &req.DepositSuccess, "siteimage/", "image", system_setting.KeySiteImageDepositSuccess},
		{"withdraw_success_file", &req.WithdrawSuccess, "siteimage/", "image", system_setting.KeySiteImageWithdrawSuccess},
		{"deposit_fail_file", &req.DepositFail, "siteimage/", "image", system_setting.KeySiteImageDepositFail},
		{"withdraw_fail_file", &req.WithdrawFail, "siteimage/", "image", system_setting.KeySiteImageWithdrawFail},
		{"video_deposit_file", &req.VideoDeposit, "siteimage/", "video", system_setting.KeySiteImageVideoDeposit},
		{"video_deposit_small_file", &req.VideoDepositSmall, "siteimage/", "video", system_setting.KeySiteImageVideoDepositSmall},
		{"video_deposit_decimal_file", &req.VideoDepositDecimal, "siteimage/", "video", system_setting.KeySiteImageVideoDepositDecimal},
		{"video_deposit_decimal_small_file", &req.VideoDepositDecimalSmall, "siteimage/", "video", system_setting.KeySiteImageVideoDepositDecimalSmall},
		{"video_deposit_qr_file", &req.VideoDepositQR, "siteimage/", "video", system_setting.KeySiteImageVideoDepositQR},
		{"video_deposit_qr_small_file", &req.VideoDepositQRSmall, "siteimage/", "video", system_setting.KeySiteImageVideoDepositQRSmall},
		{"video_deposit_truewallet_file", &req.VideoDepositTruewallet, "siteimage/", "video", system_setting.KeySiteImageVideoDepositTruewallet},
		{"video_deposit_truewallet_small_file", &req.VideoDepositTruewalletSmall, "siteimage/", "video", system_setting.KeySiteImageVideoDepositTruewalletSmall},
		{"network_tutorial_image_file", &req.NetworkTutorialImage, "siteimage/", "image", system_setting.KeySiteNetworkTutorialImage},
		{"network_tutorial_make_money_image_file", &req.NetworkTutorialMakeMoneyImage, "siteimage/", "image", system_setting.KeySiteNetworkTutorialMakeMoneyImage},
	}

	// Handle file uploads
	for _, field := range uploadFields {
		if _, _, err := c.Request.FormFile(field.FieldName); err != nil {
			if err == http.ErrMissingFile {
				continue // ไม่มีไฟล์ข้ามไป
			}
			c.Error(errors.NewValidationError("failed to get file: " + field.FieldName + ": " + err.Error()))
			return
		}

		// Check and delete existing file first
		if currentSetting, err := h.systemSettingService.GetSystemSetting(c.Request.Context(), field.SettingKey); err == nil && currentSetting.Value != "" {
			deleteReq := &system_setting.DeleteFileRequest{FileUrl: currentSetting.Value}
			if err := h.systemSettingService.DeleteFile(c.Request.Context(), deleteReq); err != nil {
				logger.WithError(err).Errorf("failed to delete existing file for %s", field.FieldName)
			}
		}

		// Upload new file
		fileInfo, err := h.systemSettingService.FileUpload(c.Request.Context(), c.Request, field.FieldName)
		if err != nil {
			c.Error(errors.NewValidationError("failed to upload file: " + field.FieldName + ": " + err.Error()))
			return
		}
		*field.TargetPtr = &fileInfo.FileUrl
	}

	// Update SiteImage settings using service
	if err := h.systemSettingService.UpdateSiteImageSettings(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "SiteImage settings updated successfully",
	})
}

// GetCommissionSettings handles GET /api/v1/system-settings/commission
func (h *SystemSettingHandler) GetCommissionSettings(c *gin.Context) {
	settings, err := h.systemSettingService.GetCommissionSettings(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
		"message": "Commission settings retrieved successfully",
	})
}

// UpdateCommissionSettings handles PUT /api/v1/system-settings/commission
func (h *SystemSettingHandler) UpdateCommissionSettings(c *gin.Context) {
	var req system_setting.UpdateCommissionSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	if err := h.systemSettingService.UpdateCommissionSettings(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Commission settings updated successfully",
	})
}

// GetReferralSettings handles GET /api/v1/system/settings/referral
func (h *SystemSettingHandler) GetReferralSettings(c *gin.Context) {
	settings, err := h.systemSettingService.GetReferralSettings(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
		"message": "Referral settings retrieved successfully",
	})
}

// UpdateReferralSettings handles PUT /api/v1/system/settings/referral
func (h *SystemSettingHandler) UpdateReferralSettings(c *gin.Context) {
	var req system_setting.UpdateReferralSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	if err := h.systemSettingService.UpdateReferralSettings(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Referral settings updated successfully",
	})
}

// FileUpload handles POST /api/v1/system-settings/upload
func (h *SystemSettingHandler) FileUpload(c *gin.Context) {
	// Get field name from query parameter, default to "file"
	fieldName := c.DefaultQuery("field", "file")

	fileInfo, err := h.systemSettingService.FileUpload(c.Request.Context(), c.Request, fieldName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fileInfo,
		"message": "File uploaded successfully",
	})
}

// DeleteFile handles DELETE /api/v1/system-settings/file
func (h *SystemSettingHandler) DeleteFile(c *gin.Context) {
	var req system_setting.DeleteFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.systemSettingService.DeleteFile(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "File deleted successfully",
	})
}

// GetMemberLevelSettings handles GET /api/v1/system-settings/member-level
func (h *SystemSettingHandler) GetMemberLevelSettings(c *gin.Context) {
	settings, err := h.systemSettingService.GetMemberLevelSettings(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
		"message": "Member level settings retrieved successfully",
	})
}

// UpdateMemberLevelSettings handles PUT /api/v1/system-settings/member-level
func (h *SystemSettingHandler) UpdateMemberLevelSettings(c *gin.Context) {
	var req system_setting.UpdateMemberLevelSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	if err := h.systemSettingService.UpdateMemberLevelSettings(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Member level settings updated successfully",
	})
}

// GetPlayerAttemptLimitOptions handles GET /api/v1/system/player-attempt-limit-options
func (h *SystemSettingHandler) GetPlayerAttemptLimitOptions(c *gin.Context) {
	// Get player attempt limit options from service
	response, err := h.systemSettingService.GetPlayerAttemptLimitOptions(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetTimeoutOptions handles GET /api/v1/system/timeout-options
func (h *SystemSettingHandler) GetTimeoutOptions(c *gin.Context) {
	// Get timeout options from service
	response, err := h.systemSettingService.GetTimeoutOptions(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}
