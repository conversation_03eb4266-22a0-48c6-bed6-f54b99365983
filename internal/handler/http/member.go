package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/member"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type MemberHandler struct {
	memberService service.MemberService
}

func NewMemberHandler(memberService service.MemberService) *MemberHandler {
	return &MemberHandler{
		memberService: memberService,
	}
}

func (h *MemberHandler) CreateMember(c *gin.Context) {
	var req member.CreateMemberRequest
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	memberResp, err := h.memberService.CreateMember(c.Request.Context(), req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.<PERSON>(http.StatusCreated, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

func (h *MemberHandler) GetMember(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	memberResp, err := h.memberService.GetMemberByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

func (h *MemberHandler) GetMemberByUsername(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		c.Error(errors.NewValidationError("username is required"))
		return
	}

	memberResp, err := h.memberService.GetMemberByUsername(c.Request.Context(), username)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

func (h *MemberHandler) GetMemberByGameUsername(c *gin.Context) {
	gameUsername := c.Param("game_username")
	if gameUsername == "" {
		c.Error(errors.NewValidationError("game username is required"))
		return
	}

	memberResp, err := h.memberService.GetMemberByGameUsername(c.Request.Context(), gameUsername)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

func (h *MemberHandler) UpdateMember(c *gin.Context) {
	id := c.Param("id")
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	if id == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	var req member.UpdateMemberRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	memberResp, err := h.memberService.UpdateMember(c.Request.Context(), id, req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    memberResp,
	})
}

func (h *MemberHandler) DeleteMember(c *gin.Context) {
	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("member ID is required"))
		return
	}

	err := h.memberService.DeleteMember(c.Request.Context(), id, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Member deleted successfully",
	})
}

func (h *MemberHandler) ListMembers(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	members, total, err := h.memberService.ListMembers(c.Request.Context(), limit, offset, search)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"members": members,
			"total":   total,
			"limit":   limit,
			"offset":  offset,
		},
	})
}

func (h *MemberHandler) ListMembersWithFilter(c *gin.Context) {
	// Parse pagination parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Parse filter parameters
	filter := &member.MemberFilter{
		Search:        c.Query("search"),
		Username:      c.Query("username"),
		FirstName:     c.Query("first_name"),
		LastName:      c.Query("last_name"),
		BankNumber:    c.Query("bank_number"),
		ReferUserName: c.Query("refer_user_name"),
		CreatedAt:     getStringPtr(c.Query("created_at")),
	}

	// Parse integer filters
	if memberGroupIDStr := c.Query("member_group_id"); memberGroupIDStr != "" {
		if memberGroupID, err := strconv.Atoi(memberGroupIDStr); err == nil {
			filter.MemberGroupID = &memberGroupID
		}
	}

	if partnerIDStr := c.Query("partner_id"); partnerIDStr != "" {
		if partnerID, err := strconv.Atoi(partnerIDStr); err == nil {
			filter.PartnerID = &partnerID
		}
	}

	if createdByStr := c.Query("created_by"); createdByStr != "" {
		if createdBy, err := strconv.Atoi(createdByStr); err == nil {
			filter.CreatedBy = &createdBy
		}
	}

	members, total, err := h.memberService.ListMembersWithFilter(c.Request.Context(), limit, offset, filter)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"members": members,
			"total":   total,
			"limit":   limit,
			"offset":  offset,
		},
	})
}

func getStringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
