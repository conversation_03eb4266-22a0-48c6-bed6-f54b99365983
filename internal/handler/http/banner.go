package http

import (
	"fmt"
	"net/http"
	"strconv"

	"blacking-api/internal/domain/banner"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type BannerHandler struct {
	bannerService service.BannerService
}

func NewBannerHandler(bannerService service.BannerService) *BannerHandler {
	return &BannerHandler{
		bannerService: bannerService,
	}
}

// CreateBanner handles POST /api/v1/banners
func (h *BannerHandler) CreateBanner(c *gin.Context) {
	LinkURLValue := c.PostForm("link_url")
	req := banner.CreateBannerRequest{
		Name:    c.PostForm("name"),
		Type:    banner.Type(c.PostForm("type")),
		LinkURL: &LinkURLValue, // Keep existing URL if no file uploaded
	}

	if err := c.Request.ParseMultipartForm(10 << 20); err != nil { // 10MB max
		c.Error(errors.NewValidationError("failed to parse form data: " + err.Error()))
		return
	}

	// Handle banner upload
	if _, _, err := c.Request.FormFile("image_file"); err == nil {
		// Upload banner using service
		fileInfo, err := h.bannerService.FileUpload(c.Request.Context(), c.Request, "image_file")
		if err != nil {
			c.Error(errors.NewValidationError("failed to upload banner: " + err.Error()))
			return
		}
		req.ImageURL = &fileInfo.FileUrl
	}

	response, err := h.bannerService.CreateBanner(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "banner created successfully",
		"data":    response,
	})
}

// GetBannerByID handles GET /api/v1/banners/:id
func (h *BannerHandler) GetBannerByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("banner ID is required"))
		return
	}

	response, err := h.bannerService.GetBannerByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// UpdateBanner handles PUT /api/v1/banners/:id
func (h *BannerHandler) UpdateBanner(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("banner ID is required"))
		return
	}
	LinkURLValue := c.PostForm("link_url")

	req := banner.UpdateBannerRequest{
		Name:    c.PostForm("name"),
		Type:    banner.Type(c.PostForm("type")),
		LinkURL: &LinkURLValue, // Keep existing URL if no file uploaded
	}

	if err := c.Request.ParseMultipartForm(10 << 20); err != nil { // 10MB max
		c.Error(errors.NewValidationError("failed to parse form data: " + err.Error()))
		return
	}

	// Handle banner upload
	if _, _, err := c.Request.FormFile("image_file"); err == nil {
		// Upload banner using service
		fileInfo, err := h.bannerService.FileUpload(c.Request.Context(), c.Request, "image_file")
		if err != nil {
			c.Error(errors.NewValidationError("failed to upload banner: " + err.Error()))
			return
		}
		req.ImageURL = &fileInfo.FileUrl
	}

	response, err := h.bannerService.UpdateBanner(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "banner updated successfully",
		"data":    response,
	})
}

// DeleteBanner handles DELETE /api/v1/banners/:id
func (h *BannerHandler) DeleteBanner(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("banner ID is required"))
		return
	}

	err := h.bannerService.DeleteBanner(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "banner deleted successfully",
	})
}

// ListBanners handles GET /api/v1/banners
func (h *BannerHandler) ListBanners(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("name")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Limit maximum results per page
	if limit > 100 {
		limit = 100
	}

	banners, total, err := h.bannerService.ListBanners(c.Request.Context(), limit, offset, search)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"banners": banners,
			"pagination": gin.H{
				"limit":  limit,
				"offset": offset,
				"total":  total,
			},
		},
	})
}

// ReorderBanners handles PUT /api/v1/banners/reorder
func (h *BannerHandler) ReorderBanners(c *gin.Context) {
	var req banner.ReorderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.bannerService.ReorderBanners(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("reordered %d banners successfully", len(req.BannerIDs)),
		"data": gin.H{
			"banner_ids": req.BannerIDs,
		},
	})
}

// FileUpload handles POST /api/v1/banners/upload
func (h *BannerHandler) FileUpload(c *gin.Context) {
	// Get field name from query parameter, default to "file"
	fieldName := c.DefaultQuery("field", "file")

	fileInfo, err := h.bannerService.FileUpload(c.Request.Context(), c.Request, fieldName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fileInfo,
		"message": "File uploaded successfully",
	})
}

// DeleteFile handles DELETE /api/v1/banners/file
func (h *BannerHandler) DeleteFile(c *gin.Context) {
	var req banner.DeleteFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	err := h.bannerService.DeleteFile(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "File deleted successfully",
	})
}
