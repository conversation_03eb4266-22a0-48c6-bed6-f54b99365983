package http

import (
	"blacking-api/internal/domain/sms_provider"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"strconv"
)

type SMSProviderHandler struct {
	smsProviderService service.SMSProviderService
	logger             logger.Logger
}

func NewSMSProviderHandler(smsProviderService service.SMSProviderService, logger logger.Logger) *SMSProviderHandler {
	return &SMSProviderHandler{
		smsProviderService: smsProviderService,
		logger:             logger,
	}
}

func (h *SMSProviderHandler) CreateSMSProvider(c *gin.Context) {
	var req *sms_provider.SMSProviderRequest
	if err := c.ShouldBind<PERSON>SON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.smsProviderService.CreateSMSProvider(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create SMS provider", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(201, gin.H{
		"success": true,
		"message": "SMS provider created successfully",
	})
}

func (h *SMSProviderHandler) GetSMSProviders(c *gin.Context) {
	var req *sms_provider.SMSProviderSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	providers, err := h.smsProviderService.FindAllSMSProviders(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get SMS providers", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    providers,
	})
}

func (h *SMSProviderHandler) GetSMSProviderByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("SMS provider ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	provider, err := h.smsProviderService.FindSMSProviderByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to get SMS provider by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    provider,
	})
}

func (h *SMSProviderHandler) UpdateSMSProvider(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("SMS provider ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *sms_provider.SMSProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.smsProviderService.UpdateSMSProvider(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update SMS provider", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "SMS provider updated successfully",
	})
}

func (h *SMSProviderHandler) UpdateSMSProviderStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("SMS provider ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *sms_provider.UpdateStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.smsProviderService.UpdateSMSProviderStatus(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update SMS provider status", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "SMS provider status updated successfully",
	})
}

func (h *SMSProviderHandler) DeleteSMSProvider(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("SMS provider ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.smsProviderService.DeleteSMSProvider(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete SMS provider", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "SMS provider deleted successfully",
	})
}
