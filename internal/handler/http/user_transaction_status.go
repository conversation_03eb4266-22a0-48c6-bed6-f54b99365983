package http

import (
	"blacking-api/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UserTransactionStatusHandler struct {
	userTransactionStatusService service.UserTransactionStatusService
}

func NewUserTransactionStatusHandler(userTransactionStatusService service.UserTransactionStatusService) *UserTransactionStatusHandler {
	return &UserTransactionStatusHandler{
		userTransactionStatusService: userTransactionStatusService,
	}
}

func (h *UserTransactionStatusHandler) GetUserTransactionStatus(c *gin.Context) {
	transactionStatuses, err := h.userTransactionStatusService.ListUserTransactionStatuses(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    transactionStatuses,
	})
}
