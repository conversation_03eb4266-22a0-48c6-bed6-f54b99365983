package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/member"
	"blacking-api/internal/service"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// PartnerHandler handles partner-related HTTP requests
type PartnerHandler struct {
	partnerService service.PartnerService
	logger         logger.Logger
}

// NewPartnerHandler creates a new partner handler
func NewPartnerHandler(partnerService service.PartnerService, logger logger.Logger) *PartnerHandler {
	return &PartnerHandler{
		partnerService: partnerService,
		logger:         logger,
	}
}

// CreatePartner handles POST /api/v1/partners
func (h *PartnerHandler) CreatePartner(c *gin.Context) {
	var req member.CreatePartnerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Get client IP
	clientIP := c.ClientIP()

	partnerResp, err := h.partnerService.CreatePartner(c.Request.Context(), req, clientIP)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    partnerResp,
	})
}

// ListPartners handles GET /api/v1/partners
func (h *PartnerHandler) ListPartners(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search") // Search by first_name

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	partners, total, err := h.partnerService.ListPartners(c.Request.Context(), limit, offset, search)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"partners": partners,
			"pagination": gin.H{
				"total":  total,
				"limit":  limit,
				"offset": offset,
			},
		},
	})
}

// ListPartnersForDropdown handles GET /api/v1/partners/dropdown
func (h *PartnerHandler) ListPartnersForDropdown(c *gin.Context) {
	partners, err := h.partnerService.ListPartnersForDropdown(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"partners": partners,
		},
	})
}

// UpdatePartner handles PUT /api/v1/partners/:id
func (h *PartnerHandler) UpdatePartner(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("partner ID is required"))
		return
	}

	var req member.UpdatePartnerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	partnerResp, err := h.partnerService.UpdatePartner(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    partnerResp,
	})
}

// ChangePartnerPassword handles PUT /api/v1/partners/:id/password
func (h *PartnerHandler) ChangePartnerPassword(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("partner ID is required"))
		return
	}

	var req member.ChangePartnerPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	err := h.partnerService.ChangePartnerPassword(c.Request.Context(), id, req, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "partner password changed successfully",
	})
}

// SuspendPartner handles PATCH /api/v1/partners/:id/suspend
func (h *PartnerHandler) SuspendPartner(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("partner ID is required"))
		return
	}

	adminId := auth.GetUserIDFromContext(c)
	adminName := auth.GetUsernameFromContext(c)

	err := h.partnerService.SuspendPartner(c.Request.Context(), id, strconv.Itoa(adminId), adminName)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "partner suspended successfully",
	})
}
