package http

import (
	"blacking-api/internal/domain/withdraw_account"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"strconv"
)

type WithdrawAccountHandler struct {
	withdrawAccountService service.WithdrawAccountService
	logger                 logger.Logger
}

func NewWithdrawAccountHandler(withdrawAccountService service.WithdrawAccountService, logger logger.Logger) *WithdrawAccountHandler {
	return &WithdrawAccountHandler{
		withdrawAccountService: withdrawAccountService,
		logger:                 logger,
	}
}

func (h *WithdrawAccountHandler) FileUpload(c *gin.Context) {
	url, err := h.withdrawAccountService.FileUpload(c.Request.Context(), c.Request)
	if err != nil {
		h.logger.Error("Failed to upload file", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    url,
	})
}

func (h *WithdrawAccountHandler) DeleteFile(c *gin.Context) {
	var req *withdraw_account.DeleteFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.withdrawAccountService.DeleteFile(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to delete file upload", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "File deleted successfully",
	})
}

func (h *WithdrawAccountHandler) CreateWithdrawAccount(c *gin.Context) {
	var req *withdraw_account.WithdrawAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.withdrawAccountService.CreateWithdrawAccount(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create withdraw account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(201, gin.H{
		"success": true,
		"message": "Withdraw account created successfully",
	})
}

func (h *WithdrawAccountHandler) GetWithdrawAccounts(c *gin.Context) {
	var req *withdraw_account.WithdrawAccountSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	withdrawAccounts, err := h.withdrawAccountService.FindAllWithdrawAccount(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to retrieve withdraw accounts", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    withdrawAccounts,
	})
}

func (h *WithdrawAccountHandler) GetWithdrawAccountByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	withdrawAccount, err := h.withdrawAccountService.FindWithdrawAccountByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to retrieve withdraw account by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    withdrawAccount,
	})
}

func (h *WithdrawAccountHandler) GetWithdrawAccountSettingAlgorithmByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	withdrawAccountAlgorithm, err := h.withdrawAccountService.FindWithdrawAccountSettingAlgorithmByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to retrieve withdraw account algorithm settings by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    withdrawAccountAlgorithm,
	})
}

func (h *WithdrawAccountHandler) UpdateWithdrawAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *withdraw_account.WithdrawAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.withdrawAccountService.UpdateWithdrawAccount(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update withdraw account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Withdraw account updated successfully",
	})
}

func (h *WithdrawAccountHandler) UpdateWithdrawAccountAutoBot(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	autoBotIDStr := c.Query("bot")
	if autoBotIDStr == "" {
		HandleSingleError(c, errors.NewValidationError("bot ID is required"))
		return
	}

	autoBotID, err := strconv.ParseInt(autoBotIDStr, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.withdrawAccountService.UpdateWithdrawAccountAutoBot(c.Request.Context(), idInt, autoBotID); err != nil {
		h.logger.Error("Failed to update withdraw account auto bot", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Withdraw account auto bot updated successfully",
	})
}

func (h *WithdrawAccountHandler) UpdateWithdrawAccountAlgorithm(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("deposit account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *withdraw_account.WithdrawAccountSettingAlgorithmRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, errors.NewValidationError("invalid request body"))
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.withdrawAccountService.UpdateWithdrawAccountAlgorithm(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update withdraw account algorithm", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Withdraw account algorithm updated successfully",
	})
}

func (h *WithdrawAccountHandler) ActiveWithdrawAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	statusStr := c.Query("status")
	if statusStr == "" {
		HandleSingleError(c, errors.NewValidationError("status is required"))
		return
	}

	status, err := strconv.ParseBool(statusStr)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.withdrawAccountService.ActiveWithdrawAccount(c.Request.Context(), idInt, status); err != nil {
		h.logger.Error("Failed to activate withdraw account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Withdraw account activated successfully",
	})
}

func (h *WithdrawAccountHandler) DeleteWithdrawAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.withdrawAccountService.DeleteWithdrawAccount(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete withdraw account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Withdraw account deleted successfully",
	})
}
