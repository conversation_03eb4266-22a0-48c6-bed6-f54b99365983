package http

import (
	"net/http"

	"blacking-api/internal/domain/member"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"

	"github.com/gin-gonic/gin"
)

type MemberRegistrationHandler struct {
	memberService service.MemberService
}

func NewMemberRegistrationHandler(memberService service.MemberService) *MemberRegistrationHandler {
	return &MemberRegistrationHandler{
		memberService: memberService,
	}
}

// SendRegistrationOTP handles sending OTP for member registration
func (h *MemberRegistrationHandler) SendRegistrationOTP(c *gin.Context) {
	var req member.VerifySendOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	response, err := h.memberService.SendRegistrationOTP(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "OTP sent successfully",
	})
}

// VerifyRegistrationOTP handles OTP verification and member creation
func (h *MemberRegistrationHandler) VerifyRegistrationOTP(c *gin.Context) {
	var req member.VerifyRegistrationOTPRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Get client IP
	clientIP := c.ClientIP()

	_, err := h.memberService.VerifyRegistrationOTP(c.Request.Context(), req, clientIP)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "OTP verified successfully",
	})
}

// CompleteRegistration handles complete member registration with full data
func (h *MemberRegistrationHandler) CompleteRegistration(c *gin.Context) {
	var req member.CompleteRegistrationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	// Get client IP
	clientIP := c.ClientIP()

	memberResp, err := h.memberService.CompleteRegistration(c.Request.Context(), req, clientIP)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    memberResp,
		"message": "Registration completed successfully",
	})
}
