package http

import (
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type LanguageHandler struct {
	languageService service.LanguageService
	logger          logger.Logger
}

// NewLanguageHandler creates a new language handler
func NewLanguageHandler(languageService service.LanguageService, logger logger.Logger) *LanguageHandler {
	return &LanguageHandler{
		languageService: languageService,
		logger:          logger,
	}
}

// GetSupportedLanguages handles GET /api/languages
func (h *LanguageHandler) GetSupportedLanguages(c *gin.Context) {
	// Get supported languages using service
	languages, err := h.languageService.GetSupportedLanguages(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    languages,
		"message": "Supported languages retrieved successfully",
	})
}

// GetActiveLanguages handles GET /api/languages/active
func (h *LanguageHandler) GetActiveLanguages(c *gin.Context) {
	// Get active languages using service
	languages, err := h.languageService.GetActiveLanguages(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    languages,
		"message": "ActiveWithdrawAccount languages retrieved successfully",
	})
}

// GetLanguageByCode handles GET /api/languages/:code
func (h *LanguageHandler) GetLanguageByCode(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		c.Error(errors.NewValidationError("language code is required"))
		return
	}

	// Get language by code using service
	language, err := h.languageService.GetLanguageByCode(c.Request.Context(), code)
	if err != nil {
		c.Error(err)
		return
	}

	if language == nil {
		c.Error(errors.NewNotFoundError("language not found"))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    language,
		"message": "Language retrieved successfully",
	})
}

// ValidateLanguageCode handles POST /api/languages/validate
func (h *LanguageHandler) ValidateLanguageCode(c *gin.Context) {
	var req struct {
		Code string `json:"code" validate:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Validate language code using service
	isValid := h.languageService.ValidateLanguageCode(c.Request.Context(), req.Code)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"code":     req.Code,
			"is_valid": isValid,
		},
		"message": "Language code validation completed",
	})
}
