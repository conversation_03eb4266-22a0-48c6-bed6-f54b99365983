package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/service"

	"github.com/gin-gonic/gin"
)

type MemberAuditLogHandler struct {
	memberAuditLogService service.MemberAuditLogService
}

func NewMemberAuditLogHandler(memberAuditLogService service.MemberAuditLogService) *MemberAuditLogHandler {
	return &MemberAuditLogHandler{
		memberAuditLogService: memberAuditLogService,
	}
}

func (h *MemberAuditLogHandler) ListMemberAuditLogs(c *gin.Context) {
	// Parse query parameters
	limitStr := c.<PERSON>("limit", "10")
	offsetStr := c.<PERSON><PERSON>("offset", "0")
	username := c.Query("username")
	action := c.Query("action")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Get audit logs
	auditLogs, err := h.memberAuditLogService.ListAuditLogs(c.Request.Context(), limit, offset, username, action)
	if err != nil {
		c.Error(err)
		return
	}

	// Get total count
	total, err := h.memberAuditLogService.GetAuditLogsCount(c.Request.Context(), username, action)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"audit_logs": auditLogs,
			"total":      total,
			"limit":      limit,
			"offset":     offset,
		},
	})
}
