package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
)

type PermissionHandler struct {
	permissionService *service.PermissionService
	userRoleService   service.UserRoleService
	logger            logger.Logger
}

func NewPermissionHandler(permissionService *service.PermissionService, userRoleService service.UserRoleService, logger logger.Logger) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
		userRoleService:   userRoleService,
		logger:            logger,
	}
}

// getCurrentUserRoleID extracts current user role ID from JWT context
func (h *PermissionHandler) getCurrentUserRoleID(c *gin.Context) (string, error) {
	claims := jwt.ExtractClaims(c)
	if claims == nil {
		h.logger.Error("JWT claims not found")
		return "", errors.NewUnauthorizedError("user not authenticated")
	}

	// Try to get user_role_id as different types since JWT can store it as number or string
	if userRoleIDFloat, ok := claims["user_role_id"].(float64); ok {
		// JWT numbers are typically float64, convert to int then to string
		userRoleID := strconv.Itoa(int(userRoleIDFloat))
		return userRoleID, nil
	}

	if userRoleIDString, ok := claims["user_role_id"].(string); ok && userRoleIDString != "" {
		return userRoleIDString, nil
	}

	h.logger.Error("user_role_id not found or invalid in JWT claims")
	return "", errors.NewUnauthorizedError("user role information not found")
}

// GetPermissionMatrix godoc
// @Summary Get permission matrix
// @Description Get all permissions grouped by permission groups for UI display, optionally with user role permissions
// @Tags permissions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param user_role_id query int false "User Role ID to include permission status"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/permissions/matrix [get]
func (h *PermissionHandler) GetPermissionMatrix(c *gin.Context) {
	ctx := c.Request.Context()

	// Check if user_role_id parameter is provided
	userRoleIDStr := c.Query("user_role_id")
	
	if userRoleIDStr != "" {
		// Parse user_role_id
		userRoleID, err := strconv.Atoi(userRoleIDStr)
		if err != nil {
			h.logger.WithError(err).WithField("user_role_id", userRoleIDStr).Error("invalid user role ID")
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Invalid user role ID",
				"error":   "user_role_id must be a valid integer",
			})
			return
		}

		// Get permission matrix with user role permissions
		matrix, err := h.permissionService.GetPermissionMatrixWithUserRole(ctx, userRoleID)
		if err != nil {
			h.logger.WithError(err).WithField("user_role_id", userRoleID).Error("failed to get permission matrix with user role")
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to get permission matrix with user role permissions",
				"error":   err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Permission matrix with user role permissions retrieved successfully",
			"data":    matrix,
		})
		return
	}

	// Get basic permission matrix without user role permissions
	matrix, err := h.permissionService.GetPermissionMatrix(ctx)
	if err != nil {
		h.logger.WithError(err).Error("failed to get permission matrix")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get permission matrix",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Permission matrix retrieved successfully",
		"data":    matrix,
	})
}

// GetUserRolePermissions godoc
// @Summary Get user role permissions
// @Description Get permissions matrix with user role permission data
// @Tags permissions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param user_role_id path int true "User Role ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/permissions/user-roles/{user_role_id} [get]
func (h *PermissionHandler) GetUserRolePermissions(c *gin.Context) {
	ctx := c.Request.Context()

	userRoleIDStr := c.Param("user_role_id")
	userRoleID, err := strconv.Atoi(userRoleIDStr)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleIDStr).Error("invalid user role ID")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid user role ID",
			"error":   "user_role_id must be a valid integer",
		})
		return
	}

	// Check if user role exists
	_, err = h.userRoleService.GetUserRoleByID(ctx, userRoleIDStr)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleID).Error("user role not found")
		statusCode := http.StatusNotFound
		if !errors.IsNotFoundError(err) {
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{
			"success": false,
			"message": "User role not found",
			"error":   err.Error(),
		})
		return
	}

	permissions, err := h.permissionService.GetUserRolePermissions(ctx, userRoleID)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleID).Error("failed to get user role permissions")
		statusCode := http.StatusInternalServerError
		if errors.IsNotFoundError(err) {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{
			"success": false,
			"message": "Failed to get user role permissions",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User role permissions retrieved successfully",
		"data":    permissions,
	})
}

// BulkUpdateUserRolePermissions godoc
// @Summary Bulk update user role permissions
// @Description Update  multiple permissions for a user role
// @Tags permissions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param user_role_id path int true "User Role ID"
// @Param request body service.BulkUpdateRequest true "Bulk update request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/permissions/user-roles/{user_role_id}/bulk-update [put]
func (h *PermissionHandler) BulkUpdateUserRolePermissions(c *gin.Context) {
	ctx := c.Request.Context()
	// Get user role ID from JWT claims
	userRoleIDStr := c.Param("user_role_id")
	userRoleID, err := strconv.Atoi(userRoleIDStr)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleIDStr).Error("invalid user role ID")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid user role ID",
			"error":   "user_role_id must be a valid integer",
		})
		return
	}

	// Check if user role exists
	_, err = h.userRoleService.GetUserRoleByID(ctx, userRoleIDStr)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleID).Error("user role not found")
		statusCode := http.StatusNotFound
		if !errors.IsNotFoundError(err) {
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{
			"success": false,
			"message": "User role not found",
			"error":   err.Error(),
		})
		return
	}

	var req service.BulkUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("failed to bind request")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request format",
			"error":   err.Error(),
		})
		return
	}

	// Validate request
	if len(req.Permissions) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Validation error",
			"error":   "permissions array cannot be empty",
		})
		return
	}

	err = h.permissionService.BulkUpdateUserRolePermissions(ctx, userRoleID, req.Permissions)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleID).Error("failed to bulk update user role permissions")
		statusCode := http.StatusInternalServerError
		if errors.IsValidationError(err) {
			statusCode = http.StatusBadRequest
		} else if errors.IsNotFoundError(err) {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{
			"success": false,
			"message": "Failed to update user role permissions",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User role permissions updated successfully",
		"data":    nil,
	})
}

// GetPermissionsByGroup godoc
// @Summary Get permissions by group
// @Description Get all permissions for a specific permission group
// @Tags permissions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param group_id path int true "Permission Group ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/permissions/groups/{group_id}/permissions [get]
func (h *PermissionHandler) GetPermissionsByGroup(c *gin.Context) {
	ctx := c.Request.Context()

	groupIDStr := c.Param("group_id")
	groupID, err := strconv.Atoi(groupIDStr)
	if err != nil {
		h.logger.WithError(err).WithField("group_id", groupIDStr).Error("invalid group ID")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid group ID",
			"error":   "group_id must be a valid integer",
		})
		return
	}

	permissions, err := h.permissionService.GetPermissionsByGroup(ctx, groupID)
	if err != nil {
		h.logger.WithError(err).WithField("group_id", groupID).Error("failed to get permissions by group")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get permissions by group",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Permissions retrieved successfully",
		"data":    permissions,
	})
}

// GetAllPermissions godoc
// @Summary Get all permissions
// @Description Get all permissions with optional search
// @Tags permissions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param search query string false "Search term"
// @Param limit query int false "Limit" default(50)
// @Param offset query int false "Offset" default(0)
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/permissions [get]
func (h *PermissionHandler) GetAllPermissions(c *gin.Context) {
	ctx := c.Request.Context()

	// Parse query parameters
	search := c.Query("search")
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid limit parameter",
			"error":   "limit must be a non-negative integer",
		})
		return
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid offset parameter",
			"error":   "offset must be a non-negative integer",
		})
		return
	}

	permissions, err := h.permissionService.GetAllPermissions(ctx, limit, offset, search)
	if err != nil {
		h.logger.WithError(err).Error("failed to get all permissions")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get permissions",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Permissions retrieved successfully",
		"data":    permissions,
	})
}

// CheckUserPermission godoc
// @Summary Check user permission
// @Description Check what actions the current user's role can perform on a specific permission
// @Tags permissions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param permission_key path string true "Permission Key"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/permissions/check/{permission_key} [get]
func (h *PermissionHandler) CheckUserPermission(c *gin.Context) {
	ctx := c.Request.Context()

	// Get permission key from URL parameter
	permissionKey := c.Param("permission_key")
	if permissionKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Permission key is required",
			"error":   "permission_key parameter cannot be empty",
		})
		return
	}

	// Get user role ID from JWT claims
	userRoleID, error := h.getCurrentUserRoleID(c)
	if error != nil {
		h.logger.Error("user_role_id not found in JWT claims")
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Unauthorized",
			"error":   "user role information not found",
		})
		return
	}

	// Check if user role exists
	_, err := h.userRoleService.GetUserRoleByID(ctx, userRoleID)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleID).Error("user role not found")
		statusCode := http.StatusNotFound
		if !errors.IsNotFoundError(err) {
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{
			"success": false,
			"message": "User role not found",
			"error":   err.Error(),
		})
		return
	}
	// Convert userRoleID to int for permission check
	userRoleIDInt, err := strconv.Atoi(userRoleID)
	if err != nil {
		h.logger.WithError(err).Error("user_role_id is not a valid integer")
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Unauthorized",
			"error":   "invalid user role information",
		})
		return
	}

	// Check user permission
	permission, err := h.permissionService.CheckUserPermission(ctx, userRoleIDInt, permissionKey)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleID).WithField("permission_key", permissionKey).Error("failed to check user permission")
		statusCode := http.StatusInternalServerError
		if errors.IsNotFoundError(err) {
			statusCode = http.StatusNotFound
		}
		c.JSON(statusCode, gin.H{
			"success": false,
			"message": "Failed to check user permission",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User permission checked successfully",
		"data":    permission,
	})
}

// CheckMyPermissions godoc
// @Summary Check my permissions
// @Description Get all permissions for the current user's role
// @Tags permissions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/permissions/my-permissions [get]
func (h *PermissionHandler) CheckMyPermissions(c *gin.Context) {
	ctx := c.Request.Context()

	// Get user role ID from JWT claims
	userRoleID, error := h.getCurrentUserRoleID(c)
	if error != nil {
		h.logger.Error("user_role_id not found in JWT claims")
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Unauthorized",
			"error":   "user role information not found",
		})
		return
	}

	// Check if user role exists
	_, err := h.userRoleService.GetUserRoleByID(ctx, userRoleID)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleID).Error("user role not found")
		statusCode := http.StatusNotFound
		if !errors.IsNotFoundError(err) {
			statusCode = http.StatusInternalServerError
		}
		c.JSON(statusCode, gin.H{
			"success": false,
			"message": "User role not found",
			"error":   err.Error(),
		})
		return
	}

	// Convert userRoleID to int for getting permissions
	userRoleIDInt, err := strconv.Atoi(userRoleID)
	if err != nil {
		h.logger.WithError(err).Error("user_role_id is not a valid integer")
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "Unauthorized",
			"error":   "invalid user role information",
		})
		return
	}

	// Get user role permissions
	permissions, err := h.permissionService.GetUserRolePermissions(ctx, userRoleIDInt)
	if err != nil {
		h.logger.WithError(err).WithField("user_role_id", userRoleID).Error("failed to get my permissions")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to get user permissions",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User permissions retrieved successfully",
		"data":    permissions,
	})
}
