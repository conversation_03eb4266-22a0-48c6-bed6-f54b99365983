package http

import (
	"blacking-api/internal/domain/contact"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"strconv"
)

type ContactHandler struct {
	contactService service.ContactService
	logger         logger.Logger
}

func NewContactHandler(contactService service.ContactService, logger logger.Logger) *ContactHandler {
	return &ContactHandler{
		contactService: contactService,
		logger:         logger,
	}
}

func (h *ContactHandler) CreateContact(c *gin.Context) {
	var req *contact.CreateContactRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.contactService.CreateContact(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create contact", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(201, gin.H{
		"success": true,
		"message": "Contact created successfully",
	})
}

func (h *ContactHandler) GetContacts(c *gin.Context) {
	var req *contact.ContactSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	contacts, err := h.contactService.FindAllContacts(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get contacts", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    contacts,
	})
}

func (h *ContactHandler) GetContactByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("contact ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	res, err := h.contactService.FindContactByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to get contact by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    res,
	})
}

func (h *ContactHandler) UpdateContact(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("contact ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *contact.CreateContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.contactService.UpdateContact(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update contact", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Contact updated successfully",
	})
}

func (h *ContactHandler) UpdateContactStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("contact ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *contact.UpdateStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.contactService.UpdateContactStatus(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update contact status", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Contact status updated successfully",
	})
}

func (h *ContactHandler) DeleteContact(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("contact ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.contactService.DeleteContact(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete contact", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Contact deleted successfully",
	})
}

func (h *ContactHandler) UploadImage(c *gin.Context) {
	fileUrl, err := h.contactService.UploadImage(c.Request.Context(), c.Request)
	if err != nil {
		h.logger.Error("Failed to upload image", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Image uploaded successfully",
		"data":    fileUrl,
	})
}
