package http

import (
	"blacking-api/internal/domain/banking"
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type BankingHandler struct {
	bankingService service.BankingService
	logger         logger.Logger
}

func NewBankingHandler(bankingService service.BankingService, logger logger.Logger) *BankingHandler {
	return &BankingHandler{
		bankingService: bankingService,
		logger:         logger,
	}
}

func (h *BankingHandler) ListBankings(c *gin.Context) {
	var req banking.BankingSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	bankings, err := h.bankingService.ListBankings(c.Request.Context(), &req)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    bankings,
	})
}
