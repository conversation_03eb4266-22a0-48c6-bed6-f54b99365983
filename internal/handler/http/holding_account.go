package http

import (
	"blacking-api/internal/domain/holding_account"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
	"strconv"
)

type HoldingAccountHandler struct {
	holdingAccountService service.HodlingAccountService
	logger                logger.Logger
}

func NewHoldingAccountHandler(holdingAccountService service.HodlingAccountService, logger logger.Logger) *HoldingAccountHandler {
	return &HoldingAccountHandler{
		holdingAccountService: holdingAccountService,
		logger:                logger,
	}
}

func (h *HoldingAccountHandler) CreateHoldingAccount(c *gin.Context) {
	var req *holding_account.HoldingAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.holdingAccountService.CreateHoldingAccount(c.Request.Context(), req); err != nil {
		h.logger.Error("Failed to create holding account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(201, gin.H{
		"success": true,
		"message": "Holding account created successfully",
	})
}

func (h *HoldingAccountHandler) GetHoldingAccounts(c *gin.Context) {
	var req *holding_account.HoldingAccountSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	holdingAccounts, err := h.holdingAccountService.FindAllHoldingAccount(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get holding accounts", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success":          true,
		"holding_accounts": holdingAccounts,
	})
}

func (h *HoldingAccountHandler) GetHoldingAccountByID(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("holding account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	holdingAccount, err := h.holdingAccountService.FindHoldingAccountByID(c.Request.Context(), idInt)
	if err != nil {
		h.logger.Error("Failed to get holding account by ID", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success":         true,
		"holding_account": holdingAccount,
	})
}

func (h *HoldingAccountHandler) UpdateHoldingAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	var req *holding_account.HoldingAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := validator.New().Struct(req); err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.holdingAccountService.UpdateHoldingAccount(c.Request.Context(), idInt, req); err != nil {
		h.logger.Error("Failed to update holding account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Holding account updated successfully",
	})
}

func (h *HoldingAccountHandler) ActiveHoldingAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("withdraw account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	statusStr := c.Query("status")
	if statusStr == "" {
		HandleSingleError(c, errors.NewValidationError("status is required"))
		return
	}

	status, err := strconv.ParseBool(statusStr)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	err = h.holdingAccountService.ActiveHoldingAccount(c.Request.Context(), idInt, status)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"Message": "Holding account status updated successfully",
	})
}

func (h *HoldingAccountHandler) DeleteHoldingAccount(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		HandleSingleError(c, errors.NewValidationError("holding account ID is required"))
		return
	}

	idInt, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	if err := h.holdingAccountService.DeleteHoldingAccount(c.Request.Context(), idInt); err != nil {
		h.logger.Error("Failed to delete holding account", "error", err)
		HandleSingleError(c, err)
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "Holding account deleted successfully",
	})
}
