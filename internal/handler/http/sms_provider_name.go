package http

import (
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

type SMSProviderNameHandler struct {
	smsProviderNameService service.SMSProviderNameService
	logger                 logger.Logger
}

func NewSMSProviderNameHandler(smsProviderNameService service.SMSProviderNameService, logger logger.Logger) *SMSProviderNameHandler {
	return &SMSProviderNameHandler{
		smsProviderNameService: smsProviderNameService,
		logger:                 logger,
	}
}

func (handler *SMSProviderNameHandler) ListSMSProviderNames(c *gin.Context) {
	smsProviderNames, err := handler.smsProviderNameService.ListSMSProviderNames(c.Request.Context())
	if err != nil {
		HandleSingleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    smsProviderNames,
	})
}
