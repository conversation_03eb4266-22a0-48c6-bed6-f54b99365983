package http

import (
	"blacking-api/internal/domain/user_2fa"
	"blacking-api/internal/middleware"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"
	"reflect"
	"strconv"

	jwt "github.com/appleboy/gin-jwt/v2"
	"github.com/gin-gonic/gin"
	jwtLib "github.com/golang-jwt/jwt/v4"
)

type User2FAHandler struct {
	user2FAService service.User2FAService
	userService    service.UserService
	authMiddleware *jwt.GinJWTMiddleware
	logger         logger.Logger
}

// NewUser2FAHandler creates a new user 2FA handler
func NewUser2FAHandler(user2FAService service.User2FAService, userService service.UserService, authMiddleware *jwt.GinJWTMiddleware, logger logger.Logger) *User2FAHandler {
	return &User2FAHandler{
		user2FAService: user2FAService,
		userService:    userService,
		authMiddleware: authMiddleware,
		logger:         logger,
	}
}

// getCurrentUserID extracts current user ID from JWT context
func (h *User2FAHandler) getCurrentUserID(c *gin.Context) (int, error) {
	if authUser, exists := c.Get("auth-user"); exists {
		h.logger.WithField("auth_user_type", reflect.TypeOf(authUser)).Info("found auth-user in context")
		h.logger.Error("failed to extract ID field from auth-user struct")
		claims := jwt.ExtractClaims(c)

		// Try to get ID as different types since JWT can store it as number or string
		if idFloat, ok := claims["id"].(float64); ok {
			// JWT numbers are typically float64, convert to int
			id := int(idFloat)
			return id, nil
		}

		if idString, ok := claims["id"].(string); ok && idString != "" {
			// Convert string to int
			id, err := strconv.Atoi(idString)
			if err != nil {
				h.logger.WithError(err).Error("failed to convert string ID to int")
				return 0, errors.NewUnauthorizedError("invalid user ID format")
			}
			return id, nil
		}

		h.logger.Error("ID field is empty or invalid type in JWT claims")
		return 0, errors.NewUnauthorizedError("admin not authenticated")
	} else {
		h.logger.Error("auth-user not found in context")
	}
	return 0, errors.NewUnauthorizedError("user not authenticated")
}

// Verify2FA handles POST /api/auth/2fa/verify
func (h *User2FAHandler) Verify2FA(c *gin.Context) {

	var reqBody struct {
		Token string `json:"token" validate:"required"`
		Code  string `json:"code" validate:"required"`
	}
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Extract user ID from setup token
	userID, err := h.extractUserIDFromToken(reqBody.Token)
	if err != nil {
		c.Error(errors.NewUnauthorizedError("invalid setup token"))
		return
	}

	req := user_2fa.Verify2FARequest{
		UserID: userID,
		Code:   reqBody.Code,
	}

	// Verify 2FA code using service
	if err := h.user2FAService.Verify2FA(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "2FA code verified successfully",
	})
}

// Enable2FA handles POST /api/auth/2fa/enable
func (h *User2FAHandler) Enable2FA(c *gin.Context) {
	var reqBody struct {
		Token string `json:"token" validate:"required"`
		Code  string `json:"code" validate:"required"`
	}
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Extract user ID from setup token
	userID, err := h.extractUserIDFromToken(reqBody.Token)
	if err != nil {
		c.Error(errors.NewUnauthorizedError("invalid setup token"))
		return
	}

	req := user_2fa.Enable2FARequest{
		UserID: userID,
		Code:   reqBody.Code,
	}

	// Enable 2FA using service
	if err := h.user2FAService.Enable2FA(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "2FA enabled successfully",
	})
}

// Disable2FA handles POST /api/auth/2fa/disable
func (h *User2FAHandler) Disable2FA(c *gin.Context) {
	var req user_2fa.Disable2FARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Disable 2FA using service
	if err := h.user2FAService.Disable2FA(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "2FA disabled successfully",
	})
}

// Get2FAStatus handles GET /api/auth/2fa/status/:user_id
func (h *User2FAHandler) Get2FAStatus(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.Error(errors.NewValidationError("user_id is required"))
		return
	}

	// Get 2FA status using service
	status, err := h.user2FAService.Get2FAStatus(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// Login2FA handles POST /api/auth/login-2fa
func (h *User2FAHandler) Login2FA(c *gin.Context) {
	var req user_2fa.Login2FARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Validate the request
	if err := user_2fa.ValidateLogin2FARequest(req); err != nil {
		c.Error(err)
		return
	}

	// Extract user ID from temp token
	h.logger.WithField("token", req.Token).Info("extracting user ID from temp token")
	userID, err := h.extractUserIDFromToken(req.Token)
	if err != nil {
		h.logger.WithError(err).WithField("token", req.Token).Error("failed to extract user ID from temp token")
		c.Error(errors.NewUnauthorizedError("invalid temp token"))
		return
	}
	h.logger.WithField("user_id", userID).Info("extracted user ID from temp token")

	// Verify 2FA code
	err = h.user2FAService.Verify2FA(c.Request.Context(), user_2fa.Verify2FARequest{
		UserID: userID,
		Code:   req.Code,
	})
	if err != nil {
		c.Error(errors.NewUnauthorizedError("invalid 2FA code"))
		return
	}

	// 2FA verification successful - get user info and create JWT token
	user, err := h.userService.GetUserByID(c.Request.Context(), strconv.Itoa(userID))
	if err != nil {
		c.Error(errors.NewInternalError("failed to get user information"))
		return
	}

	// Create login claims for JWT
	claims := &middleware.LoginClaims{
		ID:            user.ID,
		Username:      user.Username,
		AdminRoleName: user.UserRoleName,
		AdminRoleID:   user.UserRoleID,
		IsEnable:      user.IsEnable,
	}

	h.logger.WithField("claims", claims).Info("created login claims for 2FA")
	h.logger.WithField("user", user).Info("user data for 2FA")

	token, expire, err := h.authMiddleware.TokenGenerator(claims)
	h.logger.WithField("token", token).WithField("expire", expire).Info("generated JWT token")

	if err != nil {
		c.Error(errors.NewInternalError("failed to generate JWT token"))
		return
	}
	h.authMiddleware.SetCookie(c, token)

	// Use LoginResponse to handle cookie and response consistently
	h.authMiddleware.LoginResponse(c, http.StatusOK, token, expire)
}

// GenerateBackupCodes handles POST /api/auth/2fa/backup-codes/:user_id
func (h *User2FAHandler) GenerateBackupCodes(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.Error(errors.NewValidationError("user_id is required"))
		return
	}

	// Generate backup codes using service
	backupCodes, err := h.user2FAService.GenerateBackupCodes(c.Request.Context(), userID)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    gin.H{"backup_codes": backupCodes},
		"message": "Backup codes generated successfully",
		"warning": "Please save these backup codes in a secure location. They will not be shown again.",
	})
}

// ValidateBackupCode handles POST /api/auth/2fa/validate-backup
func (h *User2FAHandler) ValidateBackupCode(c *gin.Context) {
	var req struct {
		UserID string `json:"user_id" validate:"required"`
		Code   string `json:"code" validate:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Validate backup code using service
	isValid, err := h.user2FAService.ValidateBackupCode(c.Request.Context(), req.UserID, req.Code)
	if err != nil {
		c.Error(err)
		return
	}

	if !isValid {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid backup code",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Backup code validated successfully",
		"warning": "This backup code has been used and is no longer valid",
	})
}

// GetMyStatus handles GET /api/auth/2fa/my-status (for current logged-in user)
func (h *User2FAHandler) GetMyStatus(c *gin.Context) {
	// Get current user ID from JWT context
	userID, err := h.getCurrentUserID(c)
	if err != nil {
		c.Error(err)
		return
	}

	// Get 2FA status using service
	status, err := h.user2FAService.Get2FAStatus(c.Request.Context(), strconv.Itoa(userID))
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// SetupMy2FA handles POST /api/auth/2fa/my-setup (for current logged-in user)
func (h *User2FAHandler) SetupMy2FA(c *gin.Context) {
	// Get current user ID from JWT context
	userID, err := h.getCurrentUserID(c)
	if err != nil {
		c.Error(err)
		return
	}

	// Create setup request with current user ID
	req := user_2fa.Setup2FARequest{
		UserID: userID,
	}

	// Setup 2FA using service
	response, err := h.user2FAService.Setup2FA(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "2FA setup initiated successfully",
	})
}

// EnableMy2FA handles POST /api/auth/2fa/my-enable (for current logged-in user)
func (h *User2FAHandler) EnableMy2FA(c *gin.Context) {
	// Get current user ID from JWT context
	userID, err := h.getCurrentUserID(c)
	if err != nil {
		c.Error(err)
		return
	}

	var reqBody struct {
		Code string `json:"code" validate:"required,len=6"`
	}
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Create enable request with current user ID
	req := user_2fa.Enable2FARequest{
		UserID: userID,
		Code:   reqBody.Code,
	}

	// Enable 2FA using service
	if err := h.user2FAService.Enable2FA(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "2FA enabled successfully",
	})
}

// DisableMy2FA handles POST /api/auth/2fa/my-disable (for current logged-in user)
func (h *User2FAHandler) DisableMy2FA(c *gin.Context) {
	// TODO: Get user ID from JWT token in context
	// For now, we'll return an error indicating this needs to be implemented
	c.Error(errors.NewInternalError("disable my 2FA not yet implemented - requires JWT user context"))
}

// Reset2FA handles POST /api/auth/2fa/admin/reset (admin only)
func (h *User2FAHandler) Reset2FA(c *gin.Context) {
	h.logger.Info("Reset2FA API called")

	// Get admin ID from JWT context
	adminID, err := h.getCurrentUserID(c)
	if err != nil {
		h.logger.WithError(err).Error("failed to get admin ID from JWT context")
		c.Error(err)
		return
	}
	h.logger.WithField("admin_id", adminID).Info("extracted admin ID from JWT")

	var reqBody struct {
		UserID int `json:"user_id" validate:"required"`
	}
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		h.logger.WithError(err).Error("failed to bind request body")
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}
	h.logger.WithFields(map[string]interface{}{
		"target_user_id": reqBody.UserID,
	}).Info("parsed request body")

	// Create reset request with admin ID from JWT
	req := user_2fa.Reset2FARequest{
		UserID:  reqBody.UserID,
		AdminID: strconv.Itoa(adminID),
	}
	h.logger.WithField("reset_request", req).Info("created reset request")

	// Reset 2FA using service
	if err := h.user2FAService.Reset2FA(c.Request.Context(), req); err != nil {
		h.logger.WithError(err).Error("failed to reset 2FA via service")
		c.Error(err)
		return
	}

	h.logger.Info("2FA reset completed successfully")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "2FA reset successfully",
	})
}

// SetupWithToken handles POST /api/auth/2fa-setup/setup (using token)
func (h *User2FAHandler) SetupWithToken(c *gin.Context) {
	var reqBody struct {
		Token string `json:"token" validate:"required"`
	}
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Extract user ID from setup token
	userID, err := h.extractUserIDFromToken(reqBody.Token)
	if err != nil {
		c.Error(errors.NewUnauthorizedError("invalid setup token"))
		return
	}

	// Create setup request
	req := user_2fa.Setup2FARequest{
		UserID: userID,
	}

	// Setup 2FA using service
	response, err := h.user2FAService.Setup2FA(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "2FA setup initiated successfully",
	})
}

// EnableWithToken handles POST /api/auth/2fa-setup/enable (using token)
func (h *User2FAHandler) EnableWithToken(c *gin.Context) {
	var reqBody struct {
		Token string `json:"token" validate:"required"`
		Code  string `json:"code" validate:"required,len=6"`
	}
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		c.Error(errors.NewValidationError("invalid request body: " + err.Error()))
		return
	}

	// Extract user ID from setup token
	userID, err := h.extractUserIDFromToken(reqBody.Token)
	if err != nil {
		c.Error(errors.NewUnauthorizedError("invalid setup token"))
		return
	}

	// Create enable request
	req := user_2fa.Enable2FARequest{
		UserID: userID,
		Code:   reqBody.Code,
	}

	// Enable 2FA using service
	if err := h.user2FAService.Enable2FA(c.Request.Context(), req); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "2FA enabled successfully. You can now login with 2FA.",
	})
}

// extractUserIDFromToken extracts user ID from JWT token
func (h *User2FAHandler) extractUserIDFromToken(tokenString string) (int, error) {
	// Parse token manually using golang-jwt with the same key used by the middleware
	token, err := jwtLib.Parse(tokenString, func(token *jwtLib.Token) (interface{}, error) {
		// Return the same key used by the middleware (from auth.go line 59)
		return []byte("icqHh7zR3.yLZdKUimV7"), nil
	})

	if err != nil {
		return 0, errors.NewValidationError("invalid JWT token")
	}

	// Extract claims as MapClaims
	if claims, ok := token.Claims.(jwtLib.MapClaims); ok && token.Valid {
		if userID, exists := claims["id"]; exists {
			// Try int (direct int type)
			if userIDInt, ok := userID.(int); ok {
				return userIDInt, nil
			}

			if userIDFloat, ok := userID.(float64); ok {
				return int(userIDFloat), nil
			}
		}
	}

	return 0, errors.NewValidationError("user ID not found in token")
}

// parseUUID validates UUID format
func parseUUID(s string) (string, error) {
	// Simple UUID validation - check format
	if len(s) != 36 {
		return "", errors.NewValidationError("invalid UUID length")
	}

	// Check UUID pattern: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
	if s[8] != '-' || s[13] != '-' || s[18] != '-' || s[23] != '-' {
		return "", errors.NewValidationError("invalid UUID format")
	}

	return s, nil
}
