package http

import (
	"net/http"
	"strconv"

	"blacking-api/internal/domain/referral_group"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// ReferralGroupHandler handles referral group-related HTTP requests
type ReferralGroupHandler struct {
	referralGroupService service.ReferralGroupService
	logger               logger.Logger
}

// NewReferralGroupHandler creates a new referral group handler
func NewReferralGroupHandler(referralGroupService service.ReferralGroupService, logger logger.Logger) *ReferralGroupHandler {
	return &ReferralGroupHandler{
		referralGroupService: referralGroupService,
		logger:               logger,
	}
}

// CreateReferralGroup handles POST /api/v1/referral-groups
func (h *ReferralGroupHandler) CreateReferralGroup(c *gin.Context) {
	var req referral_group.CreateReferralGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.<PERSON>r(errors.NewValidationError("invalid request body"))
		return
	}

	referralGroupResp, err := h.referralGroupService.CreateReferralGroup(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    referralGroupResp,
	})
}

// GetReferralGroup handles GET /api/v1/referral-groups/:id
func (h *ReferralGroupHandler) GetReferralGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid referral group ID"))
		return
	}

	referralGroupResp, err := h.referralGroupService.GetReferralGroupByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    referralGroupResp,
	})
}

// GetDefaultReferralGroup handles GET /api/v1/referral-groups/default
func (h *ReferralGroupHandler) GetDefaultReferralGroup(c *gin.Context) {
	referralGroupResp, err := h.referralGroupService.GetDefaultReferralGroup(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    referralGroupResp,
	})
}

// UpdateReferralGroup handles PUT /api/v1/referral-groups/:id
func (h *ReferralGroupHandler) UpdateReferralGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid referral group ID"))
		return
	}

	var req referral_group.UpdateReferralGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errors.NewValidationError("invalid request body"))
		return
	}

	referralGroupResp, err := h.referralGroupService.UpdateReferralGroup(c.Request.Context(), id, req)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    referralGroupResp,
	})
}

// DeleteReferralGroup handles DELETE /api/v1/referral-groups/:id
func (h *ReferralGroupHandler) DeleteReferralGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid referral group ID"))
		return
	}

	if err := h.referralGroupService.DeleteReferralGroup(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "referral group deleted successfully",
	})
}

// ListReferralGroups handles GET /api/v1/referral-groups
func (h *ReferralGroupHandler) ListReferralGroups(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")
	search := c.Query("search")        // Search by code or name
	sortBy := c.Query("sort_by")       // Sort column (id, code, name, is_default, created_at, updated_at)
	sortOrder := c.Query("sort_order") // Sort order (asc, desc)

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100 // Max limit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// Validate sort parameters
	validSortColumns := map[string]bool{
		"id":         true,
		"code":       true,
		"name":       true,
		"is_default": true,
		"created_at": true,
		"updated_at": true,
	}

	if sortBy != "" && !validSortColumns[sortBy] {
		sortBy = "is_default" // Default sort column (default first)
	}

	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc" // Default sort order
	}

	referralGroups, total, err := h.referralGroupService.ListReferralGroups(c.Request.Context(), limit, offset, search, sortBy, sortOrder)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"referral_groups": referralGroups,
			"pagination": gin.H{
				"total":      total,
				"limit":      limit,
				"offset":     offset,
				"sort_by":    sortBy,
				"sort_order": sortOrder,
			},
		},
	})
}

// ListActiveReferralGroups handles GET /api/v1/referral-groups/active
func (h *ReferralGroupHandler) ListActiveReferralGroups(c *gin.Context) {
	referralGroups, err := h.referralGroupService.ListActiveReferralGroups(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"referral_groups": referralGroups,
		},
	})
}

// ListReferralGroupsForDropdown handles GET /api/v1/referral-groups/dropdown
func (h *ReferralGroupHandler) ListReferralGroupsForDropdown(c *gin.Context) {
	names, err := h.referralGroupService.ListReferralGroupsForDropdown(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"names": names,
		},
	})
}

// SetDefaultReferralGroup handles PUT /api/v1/referral-groups/:id/set-default
func (h *ReferralGroupHandler) SetDefaultReferralGroup(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Error(errors.NewValidationError("invalid referral group ID"))
		return
	}

	if err := h.referralGroupService.SetDefaultReferralGroup(c.Request.Context(), id); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "referral group set as default successfully",
	})
}
