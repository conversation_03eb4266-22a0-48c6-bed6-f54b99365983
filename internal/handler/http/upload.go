package http

import (
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type UploadHandler struct {
	uploadService service.UploadService
	logger        logger.Logger
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(uploadService service.UploadService, logger logger.Logger) *UploadHandler {
	return &UploadHandler{
		uploadService: uploadService,
		logger:        logger,
	}
}

// UploadImage handles POST /api/upload/image
func (h *UploadHandler) UploadImage(c *gin.Context) {
	// Get folder from query parameter (optional)
	folder := c.DefaultQuery("folder", "images")

	// Get file from form
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.Error(errors.NewValidationError("file is required"))
		return
	}
	defer file.Close()

	// Upload image using service
	fileInfo, err := h.uploadService.UploadImage(c.Request.Context(), file, header.Filename, folder)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fileInfo,
		"message": "Image uploaded successfully",
	})
}

// UploadFile handles POST /api/upload/file
func (h *UploadHandler) UploadFile(c *gin.Context) {
	// Get folder from query parameter (optional)
	folder := c.DefaultQuery("folder", "files")

	// Get allowed extensions from query parameter (optional)
	allowedExtsParam := c.Query("allowed_exts")
	var allowedExts []string
	if allowedExtsParam != "" {
		allowedExts = strings.Split(allowedExtsParam, ",")
		// Trim spaces and ensure dots
		for i, ext := range allowedExts {
			ext = strings.TrimSpace(ext)
			if !strings.HasPrefix(ext, ".") {
				ext = "." + ext
			}
			allowedExts[i] = ext
		}
	}

	// Get file from form
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.Error(errors.NewValidationError("file is required"))
		return
	}
	defer file.Close()

	// Upload file using service
	fileInfo, err := h.uploadService.UploadFile(c.Request.Context(), file, header.Filename, folder, allowedExts)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fileInfo,
		"message": "File uploaded successfully",
	})
}

// DeleteFile handles DELETE /api/upload/file/:path
func (h *UploadHandler) DeleteFile(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		c.Error(errors.NewValidationError("file path is required"))
		return
	}

	// Delete file using service
	if err := h.uploadService.DeleteFile(c.Request.Context(), path); err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "File deleted successfully",
	})
}

// GetFileInfo handles GET /api/upload/file/:path/info
func (h *UploadHandler) GetFileInfo(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		c.Error(errors.NewValidationError("file path is required"))
		return
	}

	// Get file info using service
	fileInfo, err := h.uploadService.GetFileInfo(c.Request.Context(), path)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    fileInfo,
		"message": "File info retrieved successfully",
	})
}

// ListFiles handles GET /api/upload/files
func (h *UploadHandler) ListFiles(c *gin.Context) {
	// Get folder from query parameter (optional)
	folder := c.DefaultQuery("folder", "")

	// List files using service
	files, err := h.uploadService.ListFiles(c.Request.Context(), folder)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"files":  files,
			"count":  len(files),
			"folder": folder,
		},
		"message": "Files listed successfully",
	})
}

// GetFileURL handles GET /api/upload/file/:path/url
func (h *UploadHandler) GetFileURL(c *gin.Context) {
	path := c.Param("path")
	if path == "" {
		c.Error(errors.NewValidationError("file path is required"))
		return
	}

	// Get file URL using service
	url, err := h.uploadService.GetFileURL(c.Request.Context(), path)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"url":  url,
			"path": path,
		},
		"message": "File URL retrieved successfully",
	})
}
