package http

import (
	"net/http"

	"blacking-api/internal/domain/platform"
	"blacking-api/internal/service"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

// PlatformHandler handles platform-related HTTP requests
type PlatformHandler struct {
	platformService service.PlatformService
	logger          logger.Logger
}

// NewPlatformHandler creates a new platform handler
func NewPlatformHandler(platformService service.PlatformService, logger logger.Logger) *PlatformHandler {
	return &PlatformHandler{
		platformService: platformService,
		logger:          logger,
	}
}

// GetPlatformTypes handles GET /api/v1/platforms/types
func (h *PlatformHandler) GetPlatformTypes(c *gin.Context) {
	types := h.platformService.GetPlatformTypes(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"types": types,
		},
	})
}

// GetPlatform handles GET /api/v1/platforms/:id
func (h *PlatformHandler) GetPlatform(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.Error(errors.NewValidationError("platform ID is required"))
		return
	}

	platformResp, err := h.platformService.GetPlatformByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    platformResp,
	})
}

// GetPlatformsByType handles GET /api/v1/platforms/type/:type
func (h *PlatformHandler) GetPlatformsByType(c *gin.Context) {
	typeStr := c.Param("type")
	platformType := platform.PlatformType(typeStr)

	platforms, err := h.platformService.GetPlatformsByType(c.Request.Context(), platformType)
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"platforms": platforms,
			"type":      platformType,
		},
	})
}

// ListPlatforms handles GET /api/v1/platforms
func (h *PlatformHandler) ListPlatforms(c *gin.Context) {
	platforms, err := h.platformService.ListPlatforms(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"platforms": platforms,
		},
	})
}
