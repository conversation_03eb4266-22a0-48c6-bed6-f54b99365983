package http

import (
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

type AutoBotHandler struct {
	autoBotService service.AutoBotService
	logger         logger.Logger
}

func NewAutoBotHandler(autoBotService service.AutoBotService, logger logger.Logger) *AutoBotHandler {
	return &AutoBotHandler{
		autoBotService: autoBotService,
		logger:         logger,
	}
}

func (h *AutoBotHandler) ListAutoBots(c *gin.Context) {
	autoBots, err := h.autoBotService.ListAutoBots(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    autoBots,
	})
}
