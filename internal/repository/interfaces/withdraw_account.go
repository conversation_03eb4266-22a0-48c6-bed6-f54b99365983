package interfaces

import (
	"blacking-api/internal/domain/withdraw_account"
	"context"
)

type WithdrawAccountRepository interface {
	Create(ctx context.Context, req *withdraw_account.WithdrawAccountRequest) error
	FindAll(ctx context.Context, limit int, offset int, where<PERSON><PERSON><PERSON> string, args []interface{}) ([]*withdraw_account.WithdrawAccount, int64, error)
	FindByID(ctx context.Context, id int64) (*withdraw_account.WithdrawAccount, error)
	FindByAccountNumberDuplicate(ctx context.Context, accountNumber string) (bool, error)
	FindByAccountNumberDuplicateAndIdNot(ctx context.Context, accountNumber string, id int64) (bool, error)
	Update(ctx context.Context, id int64, req *withdraw_account.WithdrawAccountRequest) error
	UpdateAutoBot(ctx context.Context, id int64, autoBotID int64) error
	UpdateAlgorithm(ctx context.Context, id int64, req *withdraw_account.WithdrawAccountSettingAlgorithmRequest) error
	Active(ctx context.Context, id int64, status bool) error
	Delete(ctx context.Context, id int64) error
}
