package interfaces

import (
	"blacking-api/internal/domain/member_group_type"
	"context"
)

// MemberGroupTypeRepository defines the interface for member group type repository operations
type MemberGroupTypeRepository interface {
	// Create creates a new member group type
	Create(ctx context.Context, memberGroupType *member_group_type.MemberGroupType) error

	// GetByID retrieves member group type by ID
	GetByID(ctx context.Context, id int) (*member_group_type.MemberGroupType, error)

	// GetByName retrieves member group type by name
	GetByName(ctx context.Context, name string) (*member_group_type.MemberGroupType, error)

	// Update updates an existing member group type
	Update(ctx context.Context, memberGroupType *member_group_type.MemberGroupType) error

	// Delete soft deletes a member group type (sets status to inactive)
	Delete(ctx context.Context, id int) error

	// List retrieves member group types with pagination, search, and sorting
	List(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*member_group_type.MemberGroupType, error)

	// Count returns total count of member group types with search filter
	Count(ctx context.Context, search string) (int64, error)

	// ListActive retrieves only active member group types
	ListActive(ctx context.Context) ([]*member_group_type.MemberGroupType, error)

	// ListForDropdown retrieves member group types for dropdown filter
	ListForDropdown(ctx context.Context) ([]*member_group_type.MemberGroupTypeDropdownResponse, error)

	// ReorderUp moves a member group type up in position
	ReorderUp(ctx context.Context, id int) error

	// ReorderDown moves a member group type down in position
	ReorderDown(ctx context.Context, id int) error

	// GetNextPosition gets the next available position for new member group type
	GetNextPosition(ctx context.Context) (int, error)
}
