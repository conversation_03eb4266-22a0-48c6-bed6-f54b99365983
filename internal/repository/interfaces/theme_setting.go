package interfaces

import (
	"blacking-api/internal/domain/theme_setting"
	"context"
)

// ThemeSettingRepository defines the interface for theme setting repository operations
type ThemeSettingRepository interface {
	// Get retrieves the current theme setting
	Get(ctx context.Context) (*theme_setting.ThemeSetting, error)
	
	// Create creates a new theme setting
	Create(ctx context.Context, setting *theme_setting.ThemeSetting) error
	
	// Update updates the theme setting
	Update(ctx context.Context, setting *theme_setting.ThemeSetting) error
	
	// Upsert creates or updates the theme setting (since there should only be one record)
	Upsert(ctx context.Context, setting *theme_setting.ThemeSetting) error
}