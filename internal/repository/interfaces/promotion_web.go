package interfaces

import (
	"blacking-api/internal/domain/promotion_web"
	"context"
	"io"
)

type PromotionWebRepository interface {
	//promotion web
	CreatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebCreateRequest) (int64, error)
	GetPromotionWebList(ctx context.Context, req promotion_web.PromotionWebGetListRequest) ([]promotion_web.PromotionWebGetListResponse, int64, error)
	GetPromotionWebById(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error)
	UpdatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebUpdateRequest) error
	GetPromotionWebUserToCancel(ctx context.Context, PromotionWebId int64) ([]promotion_web.GetPromotionWebIdToCancel, error)
	CancelPromotionWeb(ctx context.Context, req promotion_web.CancelPromotionWebRequest) error
	DeletePromotionWeb(ctx context.Context, req promotion_web.DeletePromotionWebRequest) error
	GetExpiredPromotionWeb(ctx context.Context, today string) ([]promotion_web.PromotionWebExpired, error)
	PromotionConfirmUpdatePromotionWebUser(ctx context.Context, confirmId int64, promotionWebUserId int64) error
	CancelPromotionWebUserById(ctx context.Context, req promotion_web.CancelPromotionWebUserById) error
	ExpiredPromotionWebUserByIds(ctx context.Context, req promotion_web.CancelPromotionWebUserByPromotionWebId) error
	GetUserPromotionWebByUserId(ctx context.Context, userId int64) (*promotion_web.PromotionWebUserByUserIdResponse, error)
	GetUserPromotionWebList(ctx context.Context, req promotion_web.PromotionWebUserGetListRequest) ([]promotion_web.PromotionWebUserGetListResponse, int64, error)
	GetPromotionWebUserById(ctx context.Context, req promotion_web.GetPromotionWebUserById) (*promotion_web.GetPromotionWebUserByIdResponse, error)
	PromotionWebUserGetListByUserId(ctx context.Context, req promotion_web.PromotionWebUserGetListByUserIdRequest) ([]promotion_web.PromotionWebUserGetListByUserIdResponse, int64, error)
	PromotionWebGetSildeListOnlyActive(ctx context.Context) ([]promotion_web.PromotionWebGetSildeListOnlyActive, error)
	UploadImageToCloudflare(ctx context.Context, pathUpload string, filename string, fileReader io.Reader) (*promotion_web.CloudFlareUploadCreateBody, error)
	UpdatePromotionWebPriorityOrderCreate(ctx context.Context, id int64) error
	SortPromotionWebPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest) error
}
