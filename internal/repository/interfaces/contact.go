package interfaces

import (
	"blacking-api/internal/domain/contact"
	"context"
)

type ContactRepository interface {
	Create(ctx context.Context, req *contact.CreateContactRequest) error
	FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*contact.Contact, int64, error)
	FindByID(ctx context.Context, id int64) (*contact.Contact, error)
	FindByNameDuplicate(ctx context.Context, name string) (bool, error)
	FindByNameDuplicateAndIdNot(ctx context.Context, name string, id int64) (bool, error)
	Update(ctx context.Context, id int64, req *contact.CreateContactRequest) error
	UpdateStatus(ctx context.Context, id int64, name string, status bool) error
	Delete(ctx context.Context, id int64) error
}
