package interfaces

import (
	"blacking-api/internal/domain/login_attempt"
	"context"
	"time"
)

// LoginAttemptRepository defines the interface for login attempt repository operations
type LoginAttemptRepository interface {
	// Create creates a new login attempt record
	Create(ctx context.Context, attempt *login_attempt.LoginAttempt) error

	// GetFailedAttemptsCount gets the count of failed login attempts for a user within a time window (only non-cleared)
	GetFailedAttemptsCount(ctx context.Context, username string, since time.Time) (int, error)

	// GetLastFailedAttempt gets the last failed login attempt for a user (only non-cleared)
	GetLastFailedAttempt(ctx context.Context, username string) (*login_attempt.LoginAttempt, error)

	// ClearFailedAttempts marks failed login attempts as cleared for a user (called after successful login)
	ClearFailedAttempts(ctx context.Context, username string) error

	// GetAttemptsSummary gets login attempt summary for a user
	GetAttemptsSummary(ctx context.Context, username string, maxAttempts int) (*login_attempt.LoginAttemptSummary, error)
}
