package interfaces

import (
	"blacking-api/internal/domain/user_audit_log"
	"context"
)

// UserAuditLogRepository defines the interface for user audit log repository operations
type UserAuditLogRepository interface {
	// Create creates a new user audit log entry
	Create(ctx context.Context, auditLog *user_audit_log.UserAuditLog) error
	
	// List retrieves all audit logs with pagination and optional filters
	List(ctx context.Context, limit, offset int, username string, userRoleID *int, action string) ([]*user_audit_log.UserAuditLog, error)
	
	// Count returns the total count of audit logs with optional filters
	Count(ctx context.Context, username string, userRoleID *int, action string) (int64, error)
}
