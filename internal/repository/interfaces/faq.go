package interfaces

import (
	"blacking-api/internal/domain/faq"
	"context"
)

type FAQRepository interface {
	Create(ctx context.Context, faq *faq.FAQ) error
	GetByID(ctx context.Context, id string) (*faq.FAQ, error)
	Update(ctx context.Context, faq *faq.FAQ) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, limit, offset int, search string) ([]*faq.FAQ, error)
	Count(ctx context.Context, search string) (int64, error)
	Reorder(ctx context.Context, userRoleIDs []int) error
}
