package interfaces

import (
	"blacking-api/internal/domain/payment_gateway_account"
	"context"
)

type PaymentGatewayAccountRepository interface {
	Create(ctx context.Context, req *payment_gateway_account.PaymentGatewayAccountRequest) error
	FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*payment_gateway_account.PaymentGatewayAccount, int64, error)
	FindByID(ctx context.Context, id int64) (*payment_gateway_account.PaymentGatewayAccount, error)
	FindByAccountNameDuplicate(ctx context.Context, accountName string) (bool, error)
	FindByAccountNameDuplicateAndIdNot(ctx context.Context, accountName string, id int64) (bool, error)
	Update(ctx context.Context, id int64, req *payment_gateway_account.PaymentGatewayAccountRequest) error
	UpdateStatus(ctx context.Context, id int64, name string, status bool) error
	Delete(ctx context.Context, id int64) error
}
