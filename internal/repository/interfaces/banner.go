package interfaces

import (
	"blacking-api/internal/domain/banner"
	"context"
)

type BannerRepository interface {
	Create(ctx context.Context, banner *banner.Banner) error
	GetByID(ctx context.Context, id string) (*banner.Banner, error)
	Update(ctx context.Context, banner *banner.Banner) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, limit, offset int, search string) ([]*banner.Banner, error)
	Count(ctx context.Context, search string) (int64, error)
	Reorder(ctx context.Context, bannerIDs []int) error
}
