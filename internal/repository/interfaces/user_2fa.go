package interfaces

import (
	"blacking-api/internal/domain/user_2fa"
	"context"
)

// User2FARepository defines the interface for user 2FA repository operations
type User2FARepository interface {
	// Create creates a new 2FA configuration for a user
	Create(ctx context.Context, user2FA *user_2fa.User2FA) error
	
	// GetByUserID retrieves 2FA configuration by user ID
	GetByUserID(ctx context.Context, userID string) (*user_2fa.User2FA, error)
	
	// Update updates a user's 2FA configuration
	Update(ctx context.Context, user2FA *user_2fa.User2FA) error
	
	// Delete deletes a user's 2FA configuration
	Delete(ctx context.Context, userID string) error
	
	// IsEnabled checks if 2FA is enabled for a user
	IsEnabled(ctx context.Context, userID string) (bool, error)
	
	// HasSetup checks if a user has setup 2FA (has secret key)
	HasSetup(ctx context.Context, userID string) (bool, error)
	
	// Upsert creates or updates a user's 2FA configuration
	Upsert(ctx context.Context, user2FA *user_2fa.User2FA) error
}
