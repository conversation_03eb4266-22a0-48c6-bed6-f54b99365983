package interfaces

import (
	"blacking-api/internal/domain/allowed_ip"
	"context"
)

// AllowedIPRepository defines the interface for allowed IP repository operations
type AllowedIPRepository interface {
	// Create creates a new allowed IP entry
	Create(ctx context.Context, allowedIP *allowed_ip.AllowedIP) error
	
	// GetByIPAddress retrieves an allowed IP by IP address
	GetByIPAddress(ctx context.Context, ipAddress string) (*allowed_ip.AllowedIP, error)
	
	// List retrieves all allowed IPs with pagination
	List(ctx context.Context, limit, offset int) ([]*allowed_ip.AllowedIP, error)
	
	// Count returns the total count of allowed IPs
	Count(ctx context.Context) (int64, error)
	
	// Update updates an allowed IP entry
	Update(ctx context.Context, allowedIP *allowed_ip.AllowedIP) error
	
	// Delete deletes an allowed IP entry by ID
	Delete(ctx context.Context, id string) error
	
	// IsIPAllowed checks if an IP address is allowed and active
	IsIPAllowed(ctx context.Context, ipAddress string) (bool, error)
}
