package interfaces

import (
	"blacking-api/internal/domain/channel"
	"context"
)

// ChannelRepository defines the interface for channel repository operations
type ChannelRepository interface {
	// Create creates a new channel
	Create(ctx context.Context, channel *channel.Channel) error

	// GetByID retrieves channel by ID
	GetByID(ctx context.Context, id int) (*channel.Channel, error)

	// GetByName retrieves channel by name
	GetByName(ctx context.Context, name string) (*channel.Channel, error)

	// Update updates an existing channel
	Update(ctx context.Context, channel *channel.Channel) error

	// Delete soft deletes a channel (sets status to inactive)
	Delete(ctx context.Context, id int) error

	// List retrieves channels with pagination and search by name
	List(ctx context.Context, limit, offset int, search string) ([]*channel.Channel, error)

	// Count returns total count of channels with search filter
	Count(ctx context.Context, search string) (int64, error)

	// ListActive retrieves only active channels
	ListActive(ctx context.Context) ([]*channel.Channel, error)

	// ListByPlatform retrieves channels by platform ID
	ListByPlatform(ctx context.Context, platformID string) ([]*channel.Channel, error)
}
