package interfaces

import (
	"blacking-api/internal/domain/member_group"
	"context"
)

// MemberGroupRepository defines the interface for member group repository operations
type MemberGroupRepository interface {
	// Create creates a new member group with related data
	Create(ctx context.Context, memberGroup *member_group.MemberGroup, withdrawalApprovals []member_group.CreateWithdrawalApprovalRequest, depositAccountIDs []int) error

	// GetByID retrieves member group by ID with related data
	GetByID(ctx context.Context, id int) (*member_group.MemberGroup, error)

	// GetByCode retrieves member group by code
	GetByCode(ctx context.Context, code string) (*member_group.MemberGroup, error)

	// GetDefault retrieves the default member group
	GetDefault(ctx context.Context) (*member_group.MemberGroup, error)

	// Update updates an existing member group with related data
	Update(ctx context.Context, memberGroup *member_group.MemberGroup, withdrawalApprovals []member_group.CreateWithdrawalApprovalRequest, depositAccountIDs []int) error

	// Delete soft deletes a member group (sets status to inactive)
	Delete(ctx context.Context, id int) error

	// List retrieves member groups with pagination, search, and sorting
	List(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*member_group.MemberGroup, error)

	// Count returns total count of member groups with search filter
	Count(ctx context.Context, search string) (int64, error)

	// ListActive retrieves only active member groups
	ListActive(ctx context.Context) ([]*member_group.MemberGroup, error)

	// ListForDropdown retrieves unique member group names for dropdown filter
	ListForDropdown(ctx context.Context) ([]string, error)

	// SetDefault sets a member group as default (and unsets others)
	SetDefault(ctx context.Context, id int) error

	// UnsetAllDefaults unsets all member groups as default
	UnsetAllDefaults(ctx context.Context) error

	// GetWithdrawalApprovals retrieves withdrawal approvals for a member group
	GetWithdrawalApprovals(ctx context.Context, memberGroupID int) ([]member_group.MemberGroupWithdrawalApproval, error)

	// GetDepositAccounts retrieves deposit accounts for a member group
	GetDepositAccounts(ctx context.Context, memberGroupID int) ([]member_group.MemberGroupDepositAccount, error)

	// CreateWithdrawalApprovals creates withdrawal approvals for a member group
	CreateWithdrawalApprovals(ctx context.Context, memberGroupID int, approvals []member_group.CreateWithdrawalApprovalRequest) error

	// DeleteWithdrawalApprovals deletes all withdrawal approvals for a member group
	DeleteWithdrawalApprovals(ctx context.Context, memberGroupID int) error

	// CreateDepositAccounts creates deposit account assignments for a member group
	CreateDepositAccounts(ctx context.Context, memberGroupID int, accountIDs []int) error

	// DeleteDepositAccounts deletes all deposit account assignments for a member group
	DeleteDepositAccounts(ctx context.Context, memberGroupID int) error
}
