package interfaces

import (
	"blacking-api/internal/domain/holding_account"
	"context"
)

type HoldingAccountRepository interface {
	Create(ctx context.Context, req *holding_account.HoldingAccountRequest) error
	FindAll(ctx context.Context, limit int, offset int, where<PERSON>lause string, args []interface{}) ([]*holding_account.HoldingAccount, int64, error)
	FindByID(ctx context.Context, id int64) (*holding_account.HoldingAccount, error)
	FindByAccountNumberDuplicate(ctx context.Context, accountNumber string) (bool, error)
	FindByAccountNumberDuplicateAndIdNot(ctx context.Context, accountNumber string, id int64) (bool, error)
	Update(ctx context.Context, id int64, req *holding_account.HoldingAccountRequest) error
	Active(ctx context.Context, id int64, status bool) error
	Delete(ctx context.Context, id int64) error
}
