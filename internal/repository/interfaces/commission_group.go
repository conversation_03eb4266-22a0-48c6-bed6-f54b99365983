package interfaces

import (
	"blacking-api/internal/domain/commission_group"
	"context"
)

// CommissionGroupRepository defines the interface for commission group repository operations
type CommissionGroupRepository interface {
	// Create creates a new commission group
	Create(ctx context.Context, commissionGroup *commission_group.CommissionGroup) error

	// GetByID retrieves commission group by ID
	GetByID(ctx context.Context, id int) (*commission_group.CommissionGroup, error)

	// GetByName retrieves commission group by name
	GetByName(ctx context.Context, name string) (*commission_group.CommissionGroup, error)

	// GetDefault retrieves the default commission group
	GetDefault(ctx context.Context) (*commission_group.CommissionGroup, error)

	// Update updates an existing commission group
	Update(ctx context.Context, commissionGroup *commission_group.CommissionGroup) error

	// Delete soft deletes a commission group (sets status to inactive)
	Delete(ctx context.Context, id int) error

	// List retrieves commission groups with pagination, search, and sorting
	List(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*commission_group.CommissionGroup, error)

	// Count returns total count of commission groups with search filter
	Count(ctx context.Context, search string) (int64, error)

	// ListActive retrieves only active commission groups
	ListActive(ctx context.Context) ([]*commission_group.CommissionGroup, error)

	// ListForDropdown retrieves commission groups for dropdown filter
	ListForDropdown(ctx context.Context) ([]*commission_group.CommissionGroupDropdownResponse, error)

	// SetDefault sets a commission group as default (and unsets others)
	SetDefault(ctx context.Context, id int) error

	// UnsetAllDefaults unsets all commission groups as default
	UnsetAllDefaults(ctx context.Context) error
}
