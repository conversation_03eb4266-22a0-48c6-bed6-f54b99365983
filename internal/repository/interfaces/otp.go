package interfaces

import (
	"blacking-api/internal/domain/otp"
	"context"
)

// OTPRepository defines the interface for OTP repository operations
type OTPRepository interface {
	// Create creates a new OTP record
	Create(ctx context.Context, otpRecord *otp.OTP) error

	// GetByRecipientAndPurpose retrieves the latest valid OTP for recipient and purpose
	GetByRecipientAndPurpose(ctx context.Context, recipient string, purpose otp.OTPPurpose) (*otp.OTP, error)

	// GetByRecipientAndPurpose retrieves the latest valid OTP for recipient and purpose
	GetByRecipientAndPurposeIsUsed(ctx context.Context, recipient string, purpose otp.OTPPurpose) (*otp.OTP, error)

	// Update updates an existing OTP record
	Update(ctx context.Context, otpRecord *otp.OTP) error

	// DeleteExpired deletes all expired OTP records
	DeleteExpired(ctx context.Context) error

	// GetByID retrieves OTP by ID
	GetByID(ctx context.Context, id string) (*otp.OTP, error)

	// ListForAdmin retrieves OTPs with pagination and filters for admin
	ListForAdmin(ctx context.Context, limit, offset int, search, purpose, dateFrom, dateTo string) ([]*otp.OTP, error)

	// CountForAdmin returns total count of OTPs with filters for admin
	CountForAdmin(ctx context.Context, search, purpose, dateFrom, dateTo string) (int64, error)
}
