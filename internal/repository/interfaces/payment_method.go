package interfaces

import (
	"blacking-api/internal/domain/payment_method"
	"context"
)

type PaymentMethodRepository interface {
	Create(ctx context.Context, req *payment_method.PaymentMethodRequest) error
	List(ctx context.Context) ([]*payment_method.PaymentMethod, error)
	FindIdExists(ctx context.Context, id int64) (bool, error)
	FindByID(ctx context.Context, id int64) (*payment_method.PaymentMethod, error)
	FindByNameDuplicate(ctx context.Context, name string) (bool, error)
	FindByNameDuplicateAndIdNot(ctx context.Context, name string, id int64) (bool, error)
	Update(ctx context.Context, id int64, req *payment_method.PaymentMethodRequest) error
	UpdateStatus(ctx context.Context, id int64, name string, status bool) error
	Delete(ctx context.Context, id int64) error
}
