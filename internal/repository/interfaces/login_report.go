package interfaces

import (
	"blacking-api/internal/domain/report"
	"context"
)

// LoginReportRepository defines the interface for login report repository operations
type LoginReportRepository interface {
	// GetMemberLoginReport retrieves member login report data
	GetMemberLoginReport(ctx context.Context, filter report.LoginReportFilter) ([]*report.MemberLoginReport, error)

	// CountMemberLoginReport returns the total count of member login records
	CountMemberLoginReport(ctx context.Context, filter report.LoginReportFilter) (int64, error)

	// GetAdminLoginReport retrieves admin/user login report data
	GetAdminLoginReport(ctx context.Context, filter report.LoginReportFilter) ([]*report.AdminLoginReport, error)

	// CountAdminLoginReport returns the total count of admin login records
	CountAdminLoginReport(ctx context.Context, filter report.LoginReportFilter) (int64, error)

	// GetDuplicateIPReport retrieves IPs with multiple login attempts
	GetDuplicateIPReport(ctx context.Context, filter report.LoginReportFilter) ([]*report.DuplicateIPReport, error)

	// GetDuplicateIPLoginDetails retrieves detailed login attempts for a specific duplicate IP
	GetDuplicateIPLoginDetails(ctx context.Context, ipAddress string, filter report.LoginReportFilter) ([]*report.DuplicateIPLoginDetail, error)

	// GetFilterOptions retrieves all available filter options for dropdowns
	GetDistinctUserAgents(ctx context.Context) ([]string, error)
	GetDistinctIPAddresses(ctx context.Context) ([]string, error)
	GetDistinctMemberUsernames(ctx context.Context) ([]string, error)
	GetDistinctAdminUsernames(ctx context.Context) ([]string, error)
}
