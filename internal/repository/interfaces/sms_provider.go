package interfaces

import (
	"blacking-api/internal/domain/sms_provider"
	"context"
)

type SMSProviderRepository interface {
	Create(ctx context.Context, req *sms_provider.SMSProviderRequest) error
	FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*sms_provider.SMSProvider, int64, error)
	FindByID(ctx context.Context, id int64) (*sms_provider.SMSProvider, error)
	FindByNameDuplicate(ctx context.Context, name string) (bool, error)
	FindByNameDuplicateAndIdNot(ctx context.Context, name string, id int64) (bool, error)
	Update(ctx context.Context, id int64, req *sms_provider.SMSProviderRequest) error
	UpdateStatus(ctx context.Context, id int64, name string, status bool) error
	Delete(ctx context.Context, id int64) error
}
