package interfaces

import (
	userrole "blacking-api/internal/domain/user_role"
	"context"
)

type UserRoleRepository interface {
	Create(ctx context.Context, userrole *userrole.UserRole) error
	GetByID(ctx context.Context, id string) (*userrole.UserRole, error)
	Update(ctx context.Context, userrole *userrole.UserRole) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, limit, offset int, search string) ([]*userrole.UserRole, error)
	Count(ctx context.Context, search string) (int64, error)
	BulkToggleLockIP(ctx context.Context, toggles []userrole.LockIPToggle) error
	BulkToggle2FA(ctx context.Context, toggles []userrole.Toggle2FA) error
	BulkListLockIP(ctx context.Context) ([]*userrole.UserRole, error)
	BulkList2FA(ctx context.Context) ([]*userrole.UserRole, error)
	Reorder(ctx context.Context, userRoleIDs []int) error
}
