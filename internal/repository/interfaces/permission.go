package interfaces

import (
	"blacking-api/internal/domain/permission"
	"context"
)

type PermissionRepository interface {
	Create(ctx context.Context, perm *permission.Permission) error
	GetByID(ctx context.Context, id string) (*permission.Permission, error)
	GetByKey(ctx context.Context, key string) (*permission.Permission, error)
	Update(ctx context.Context, perm *permission.Permission) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, limit, offset int, search string) ([]*permission.Permission, error)
	Count(ctx context.Context, search string) (int64, error)
	ListWithSubPermissions(ctx context.Context) ([]*permission.Permission, error)
	Reorder(ctx context.Context, permissionIDs []int) error
}

type UserRolePermissionRepository interface {
	Create(ctx context.Context, userRolePerm *permission.UserRolePermission) error
	GetByUserRoleAndPermission(ctx context.Context, userRoleID int, permissionKey string) (*permission.UserRolePermission, error)
	Update(ctx context.Context, userRolePerm *permission.UserRolePermission) error
	Delete(ctx context.Context, userRoleID int, permissionKey string) error
	ListByUserRoleID(ctx context.Context, userRoleID int) ([]*permission.UserRolePermission, error)
	BulkUpsert(ctx context.Context, userRoleID int, permissions []permission.UserRolePermissionUpdate) error
	DeleteByUserRoleID(ctx context.Context, userRoleID int) error
}
