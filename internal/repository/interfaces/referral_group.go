package interfaces

import (
	"blacking-api/internal/domain/referral_group"
	"context"
)

// ReferralGroupRepository defines the interface for referral group repository operations
type ReferralGroupRepository interface {
	// Create creates a new referral group
	Create(ctx context.Context, referralGroup *referral_group.ReferralGroup) error

	// GetByID retrieves referral group by ID
	GetByID(ctx context.Context, id int) (*referral_group.ReferralGroup, error)

	// GetDefault retrieves the default referral group
	GetDefault(ctx context.Context) (*referral_group.ReferralGroup, error)

	// Update updates an existing referral group
	Update(ctx context.Context, referralGroup *referral_group.ReferralGroup) error

	// Delete soft deletes a referral group (sets status to inactive)
	Delete(ctx context.Context, id int) error

	// List retrieves referral groups with pagination, search, and sorting
	List(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*referral_group.ReferralGroup, error)

	// Count returns total count of referral groups with search filter
	Count(ctx context.Context, search string) (int64, error)

	// ListActive retrieves only active referral groups
	ListActive(ctx context.Context) ([]*referral_group.ReferralGroup, error)

	// ListForDropdown retrieves unique referral group names for dropdown filter
	ListForDropdown(ctx context.Context) ([]string, error)

	// SetDefault sets a referral group as default (and unsets others)
	SetDefault(ctx context.Context, id int) error

	// UnsetAllDefaults unsets all referral groups as default
	UnsetAllDefaults(ctx context.Context) error
}
