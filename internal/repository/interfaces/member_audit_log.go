package interfaces

import (
	"blacking-api/internal/domain/member_audit_log"
	"context"
)

// MemberAuditLogRepository defines the interface for member audit log repository operations
type MemberAuditLogRepository interface {
	// Create creates a new member audit log entry
	Create(ctx context.Context, auditLog *member_audit_log.MemberAuditLog) error
	
	// List retrieves all audit logs with pagination and optional filters
	List(ctx context.Context, limit, offset int, username string, action string) ([]*member_audit_log.MemberAuditLog, error)
	
	// Count returns the total count of audit logs with optional filters
	Count(ctx context.Context, username string, action string) (int64, error)
}
