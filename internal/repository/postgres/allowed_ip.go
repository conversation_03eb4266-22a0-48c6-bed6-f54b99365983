package postgres

import (
	"blacking-api/internal/domain/allowed_ip"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type AllowedIPRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewAllowedIPRepository creates a new allowed IP repository
func NewAllowedIPRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.AllowedIPRepository {
	return &AllowedIPRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new allowed IP entry
func (r *AllowedIPRepository) Create(ctx context.Context, allowedIP *allowed_ip.AllowedIP) error {
	query := `
		INSERT INTO allowed_ips (id, ip_address, description, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		allowedIP.ID,
		allowedIP.IPAddress,
		allowedIP.Description,
		allowedIP.Status,
		allowedIP.CreatedAt,
		allowedIP.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("ip_address", allowedIP.IPAddress).Error("failed to create allowed IP")
		return errors.NewDatabaseError("failed to create allowed IP")
	}

	r.logger.WithField("ip_address", allowedIP.IPAddress).Info("allowed IP created successfully")
	return nil
}

// GetByIPAddress retrieves an allowed IP by IP address
func (r *AllowedIPRepository) GetByIPAddress(ctx context.Context, ipAddress string) (*allowed_ip.AllowedIP, error) {
	query := `
		SELECT id, ip_address, description, status, created_at, updated_at
		FROM allowed_ips
		WHERE ip_address = $1
	`

	allowedIP := &allowed_ip.AllowedIP{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, ipAddress)

	err := row.Scan(
		&allowedIP.ID,
		&allowedIP.IPAddress,
		&allowedIP.Description,
		&allowedIP.Status,
		&allowedIP.CreatedAt,
		&allowedIP.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("allowed IP not found")
		}
		r.logger.WithError(err).WithField("ip_address", ipAddress).Error("failed to get allowed IP by IP address")
		return nil, errors.NewDatabaseError("failed to get allowed IP")
	}

	return allowedIP, nil
}

// List retrieves all allowed IPs with pagination
func (r *AllowedIPRepository) List(ctx context.Context, limit, offset int) ([]*allowed_ip.AllowedIP, error) {
	query := `
		SELECT id, ip_address, description, status, created_at, updated_at
		FROM allowed_ips
		ORDER BY created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, limit, offset)
	if err != nil {
		r.logger.WithError(err).Error("failed to list allowed IPs")
		return nil, errors.NewDatabaseError("failed to list allowed IPs")
	}
	defer rows.Close()

	var allowedIPs []*allowed_ip.AllowedIP
	for rows.Next() {
		allowedIP := &allowed_ip.AllowedIP{}
		err := rows.Scan(
			&allowedIP.ID,
			&allowedIP.IPAddress,
			&allowedIP.Description,
			&allowedIP.Status,
			&allowedIP.CreatedAt,
			&allowedIP.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan allowed IP")
			return nil, errors.NewDatabaseError("failed to scan allowed IP")
		}
		allowedIPs = append(allowedIPs, allowedIP)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating allowed IP rows")
		return nil, errors.NewDatabaseError("error iterating allowed IP rows")
	}

	return allowedIPs, nil
}

// Count returns the total count of allowed IPs
func (r *AllowedIPRepository) Count(ctx context.Context) (int64, error) {
	query := `SELECT COUNT(*) FROM allowed_ips`

	var count int64
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query)
	err := row.Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count allowed IPs")
		return 0, errors.NewDatabaseError("failed to count allowed IPs")
	}

	return count, nil
}

// Update updates an allowed IP entry
func (r *AllowedIPRepository) Update(ctx context.Context, allowedIP *allowed_ip.AllowedIP) error {
	query := `
		UPDATE allowed_ips
		SET description = $2, status = $3, updated_at = $4
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		allowedIP.ID,
		allowedIP.Description,
		allowedIP.Status,
		allowedIP.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("ip_id", allowedIP.ID).Error("failed to update allowed IP")
		return errors.NewDatabaseError("failed to update allowed IP")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("allowed IP not found")
	}

	r.logger.WithField("ip_id", allowedIP.ID).Info("allowed IP updated successfully")
	return nil
}

// Delete deletes an allowed IP entry by ID
func (r *AllowedIPRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM allowed_ips WHERE id = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("ip_id", id).Error("failed to delete allowed IP")
		return errors.NewDatabaseError("failed to delete allowed IP")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("allowed IP not found")
	}

	r.logger.WithField("ip_id", id).Info("allowed IP deleted successfully")
	return nil
}

// IsIPAllowed checks if an IP address is allowed and active
func (r *AllowedIPRepository) IsIPAllowed(ctx context.Context, ipAddress string) (bool, error) {
	query := `
		SELECT COUNT(*)
		FROM allowed_ips
		WHERE ip_address = $1 AND status = 'active'
	`

	var count int64
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, ipAddress)
	err := row.Scan(&count)
	if err != nil {
		r.logger.WithError(err).WithField("ip_address", ipAddress).Error("failed to check if IP is allowed")
		return false, errors.NewDatabaseError("failed to check if IP is allowed")
	}

	return count > 0, nil
}
