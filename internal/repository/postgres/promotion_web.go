package postgres

import (
	"blacking-api/internal/domain/promotion_web"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"bytes"
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"mime/multipart"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type PromotionWebRepository struct {
	pool     *pgxpool.Pool
	logger   logger.Logger
	datetime interfaces.DateTimeRepository
}

func NewPromotionWebRepository(pool *pgxpool.Pool, logger logger.Logger, datetime interfaces.DateTimeRepository) interfaces.PromotionWebRepository {
	return &PromotionWebRepository{
		pool:     pool,
		logger:   logger,
		datetime: datetime,
	}
}

// generateRandomString generates a random string for unique file naming
func (r *PromotionWebRepository) generateRandomString() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 16

	result := make([]byte, length)
	for i := range result {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		result[i] = charset[num.Int64()]
	}
	return string(result)
}

func (r *PromotionWebRepository) CreatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebCreateRequest) (int64, error) {
	query := `
		INSERT INTO promotion_web (
			promotion_web_type_id, promotion_web_status_id, name, short_description, description,
			condition_detail, image_url, start_date, end_date, free_bonus_amount, privilege_per_day,
			able_withdraw_morethan, promotion_web_bonus_condition_id, bonus_condition_amount,
			promotion_web_bonus_type_id, bonus_type_amount, able_withdraw_pertime,
			promotion_web_turnover_type_id, turnover_amount, monday, tuesday, wednesday, thursday,
			friday, saturday, sunday, time_start, time_end, promotion_web_date_type_id,
			bonus_type_amount_max, hidden_url_link, created_by_admin_id, updated_by_admin_id,
			created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19,
			$20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36
		) RETURNING id
	`

	now := time.Now().UTC()
	var id int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		req.PromotionWebTypeId, req.PromotionWebStatusId, req.Name, req.ShortDescription, req.Description,
		req.ConditionDetail, req.ImageUrl, req.StartDate, req.EndDate, req.FreeBonusAmount, req.PrivilegePerDay,
		req.AbleWithdrawMorethan, req.PromotionWebBonusConditionId, req.BonusConditionAmount,
		req.PromotionWebBonusTypeId, req.BonusTypeAmount, req.AbleWithdrawPertime,
		req.PromotionWebTurnoverTypeId, req.TurnoverAmount, req.Monday, req.Tuesday, req.Wednesday, req.Thursday,
		req.Friday, req.Saturday, req.Sunday, req.TimeStart, req.TimeEnd, req.PromotionWebDateTypeId,
		req.BonusTypeAmountMax, req.HiddenUrlLink, req.CreatedByAdminId, req.CreatedByAdminId,
		now, now,
	).Scan(&id)

	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505": // unique_violation
				return 0, errors.NewValidationError("promotion web already exists")
			}
		}
		r.logger.WithError(err).Error("failed to create promotion web")
		return 0, errors.NewDatabaseError("failed to create promotion web")
	}

	return id, nil
}

func (r *PromotionWebRepository) GetPromotionWebList(ctx context.Context, req promotion_web.PromotionWebGetListRequest) ([]promotion_web.PromotionWebGetListResponse, int64, error) {
	var list []promotion_web.PromotionWebGetListResponse
	var total int64

	// Build WHERE conditions
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Base WHERE condition
	whereConditions = append(whereConditions, "promotion_web.deleted_at IS NULL")

	// Date filters
	if req.StartDate != "" {
		startDateAtBkk, err := r.datetime.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid start date format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web.created_at >= $%d", argIndex))
		args = append(args, *startDateAtBkk)
		argIndex++
	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.datetime.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid end date format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web.created_at <= $%d", argIndex))
		args = append(args, *endDateAtBkk)
		argIndex++
	}

	// Search filter
	if req.Search != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web.name ILIKE $%d", argIndex))
		args = append(args, "%"+req.Search+"%")
		argIndex++
	}

	// Status filter
	if req.PromotionWebStatusId != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web.promotion_web_status_id = $%d", argIndex))
		args = append(args, *req.PromotionWebStatusId)
		argIndex++
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Get total count
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM promotion_web
		LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id
		LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id
		LEFT JOIN admin AS admincreate ON promotion_web.created_by_admin_id = admincreate.id
		LEFT JOIN admin AS adminupdate ON promotion_web.updated_by_admin_id = adminupdate.id
		LEFT JOIN admin AS admincancel ON promotion_web.canceled_by_admin_id = admincancel.id
		LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id
		WHERE %s
	`, whereClause)

	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count promotion web list")
		return nil, 0, errors.NewDatabaseError("failed to count promotion web list")
	}

	if total > 0 {
		// Build the main query with pagination
		selectedFields := `
			promotion_web.id AS id,
			promotion_web.promotion_web_type_id AS promotion_web_type_id,
			promotion_web_type.label_th AS promotion_web_type_th,
			promotion_web.promotion_web_status_id AS promotion_web_status_id,
			promotion_web_status.label_th AS promotion_web_status_th,
			promotion_web.name AS name,
			promotion_web.start_date AS start_date,
			promotion_web.end_date AS end_date,
			promotion_web.created_by_admin_id AS created_by_admin_id,
			CASE WHEN promotion_web.created_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincreate.username END AS created_by_admin_name,
			promotion_web.updated_by_admin_id AS updated_by_admin_id,
			CASE WHEN promotion_web.updated_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE adminupdate.username END AS updated_by_admin_name,
			promotion_web.canceled_by_admin_id AS canceled_by_admin_id,
			CASE WHEN promotion_web.canceled_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admincancel.username END AS canceled_by_admin_name,
			promotion_web.updated_at AS updated_at,
			promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id,
			promotion_web_date_type.label_th AS promotion_web_date_type_th,
			promotion_web.time_start AS time_start,
			promotion_web.time_end AS time_end,
			promotion_web.hidden_url_link AS hidden_url_link
		`

		// Add pagination parameters
		paginationArgs := append(args, req.Limit, req.Page*req.Limit)

		dataQuery := fmt.Sprintf(`
			SELECT %s
			FROM promotion_web
			LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id
			LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id
			LEFT JOIN admin AS admincreate ON promotion_web.created_by_admin_id = admincreate.id
			LEFT JOIN admin AS adminupdate ON promotion_web.updated_by_admin_id = adminupdate.id
			LEFT JOIN admin AS admincancel ON promotion_web.canceled_by_admin_id = admincancel.id
			LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id
			WHERE %s
			ORDER BY promotion_web.priority_order ASC
			LIMIT $%d OFFSET $%d
		`, selectedFields, whereClause, len(args)+1, len(args)+2)

		rows, err := dbutil.QueryWithSchema(ctx, r.pool, dataQuery, paginationArgs...)
		if err != nil {
			r.logger.WithError(err).Error("failed to get promotion web list")
			return nil, 0, errors.NewDatabaseError("failed to get promotion web list")
		}
		defer rows.Close()

		for rows.Next() {
			var item promotion_web.PromotionWebGetListResponse
			err := rows.Scan(
				&item.Id, &item.PromotionWebTypeId, &item.PromotionWebTypeTh,
				&item.PromotionWebStatusId, &item.PromotionWebStatusTh,
				&item.Name, &item.StartDate, &item.EndDate,
				&item.CreatedByAdminId, &item.CreatedByAdminName,
				&item.UpdatedByAdminId, &item.UpdatedByAdminName,
				&item.CanceledByAdminId, &item.CanceledByAdminName,
				&item.UpdatedAt, &item.PromotionWebDateTypeId,
				&item.TimeStart, &item.TimeEnd, &item.HiddenUrlLink,
			)
			if err != nil {
				r.logger.WithError(err).Error("failed to scan promotion web row")
				return nil, 0, errors.NewDatabaseError("failed to scan promotion web row")
			}
			list = append(list, item)
		}

		if err = rows.Err(); err != nil {
			r.logger.WithError(err).Error("error iterating promotion web rows")
			return nil, 0, errors.NewDatabaseError("error iterating promotion web rows")
		}
	}

	return list, total, nil
}

func (r *PromotionWebRepository) GetPromotionWebById(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error) {
	query := `
		SELECT
			promotion_web.id AS id,
			promotion_web.promotion_web_type_id AS promotion_web_type_id,
			promotion_web_type.label_th AS promotion_web_type_th,
			promotion_web.promotion_web_status_id AS promotion_web_status_id,
			promotion_web_status.label_th AS promotion_web_status_th,
			promotion_web.condition_detail AS condition_detail,
			promotion_web.image_url AS image_url,
			promotion_web.name AS name,
			promotion_web.short_description AS short_description,
			promotion_web.description AS description,
			promotion_web.start_date AS start_date,
			promotion_web.end_date AS end_date,
			promotion_web.free_bonus_amount AS free_bonus_amount,
			promotion_web.privilege_per_day AS privilege_per_day,
			promotion_web.able_withdraw_morethan AS able_withdraw_morethan,
			promotion_web.promotion_web_bonus_condition_id AS promotion_web_bonus_condition_id,
			promotion_web_bonus_condition.label_th AS promotion_web_bonus_condition_th,
			promotion_web_bonus_condition.syntax AS promotion_web_bonus_condition_syntax,
			promotion_web.bonus_condition_amount AS bonus_condition_amount,
			promotion_web.promotion_web_bonus_type_id AS promotion_web_bonus_type_id,
			promotion_web_bonus_type.label_th AS promotion_web_bonus_type_th,
			promotion_web.bonus_type_amount AS bonus_type_amount,
			promotion_web.able_withdraw_pertime AS able_withdraw_pertime,
			promotion_web.promotion_web_turnover_type_id AS promotion_web_turnover_type_id,
			promotion_web_turnover_type.label_th AS promotion_web_turnover_type_th,
			promotion_web.turnover_amount AS turnover_amount,
			promotion_web.monday AS monday,
			promotion_web.tuesday AS tuesday,
			promotion_web.wednesday AS wednesday,
			promotion_web.thursday AS thursday,
			promotion_web.friday AS friday,
			promotion_web.saturday AS saturday,
			promotion_web.sunday AS sunday,
			promotion_web.time_start AS time_start,
			promotion_web.time_end AS time_end,
			promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id,
			promotion_web_date_type.label_th AS promotion_web_date_type_th,
			promotion_web.bonus_type_amount_max AS bonus_type_amount_max
		FROM promotion_web
		LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id
		LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id
		LEFT JOIN promotion_web_bonus_condition ON promotion_web.promotion_web_bonus_condition_id = promotion_web_bonus_condition.id
		LEFT JOIN promotion_web_bonus_type ON promotion_web.promotion_web_bonus_type_id = promotion_web_bonus_type.id
		LEFT JOIN promotion_web_turnover_type ON promotion_web.promotion_web_turnover_type_id = promotion_web_turnover_type.id
		LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id
		WHERE promotion_web.id = $1 AND promotion_web.deleted_at IS NULL
	`

	var result promotion_web.PromotionWebGetByIdResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&result.Id, &result.PromotionWebTypeId, &result.PromotionWebTypeTh,
		&result.PromotionWebStatusId, &result.PromotionWebStatusTh,
		&result.ConditionDetail, &result.ImageUrl, &result.Name,
		&result.ShortDescription, &result.Description, &result.StartDate, &result.EndDate,
		&result.FreeBonusAmount, &result.PrivilegePerDay, &result.AbleWithdrawMorethan,
		&result.PromotionWebBonusConditionId, &result.PromotionWebBonusConditionTh, &result.PromotionWebBonusConditionSyntax,
		&result.BonusConditionAmount, &result.PromotionWebBonusTypeId, &result.PromotionWebBonusTypeTh,
		&result.BonusTypeAmount, &result.AbleWithdrawPertime, &result.PromotionWebTurnoverTypeId,
		&result.PromotionWebTurnoverTypeTh, &result.TurnoverAmount,
		&result.Monday, &result.Tuesday, &result.Wednesday, &result.Thursday,
		&result.Friday, &result.Saturday, &result.Sunday,
		&result.TimeStart, &result.TimeEnd, &result.PromotionWebDateTypeId,
		&result.BonusTypeAmountMax,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("promotion web not found")
		}
		r.logger.WithError(err).Error("failed to get promotion web by ID")
		return nil, errors.NewDatabaseError("failed to get promotion web")
	}

	return &result, nil
}

func (r *PromotionWebRepository) UpdatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebUpdateRequest) error {
	query := `
		UPDATE promotion_web SET
			promotion_web_type_id = $2,
			promotion_web_status_id = $3,
			name = $4,
			short_description = $5,
			description = $6,
			condition_detail = $7,
			image_url = $8,
			start_date = $9,
			end_date = $10,
			free_bonus_amount = $11,
			privilege_per_day = $12,
			able_withdraw_morethan = $13,
			promotion_web_bonus_condition_id = $14,
			bonus_condition_amount = $15,
			promotion_web_bonus_type_id = $16,
			bonus_type_amount = $17,
			able_withdraw_pertime = $18,
			promotion_web_turnover_type_id = $19,
			turnover_amount = $20,
			monday = $21,
			tuesday = $22,
			wednesday = $23,
			thursday = $24,
			friday = $25,
			saturday = $26,
			sunday = $27,
			time_start = $28,
			time_end = $29,
			promotion_web_date_type_id = $30,
			bonus_type_amount_max = $31,
			hidden_url_link = $32,
			updated_by_admin_id = $33,
			updated_at = $34
		WHERE id = $1 AND deleted_at IS NULL
	`

	now := time.Now().UTC()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Id, req.PromotionWebTypeId, req.PromotionWebStatusId, req.Name,
		req.ShortDescription, req.Description, req.ConditionDetail, req.ImageUrl,
		req.StartDate, req.EndDate, req.FreeBonusAmount, req.PrivilegePerDay,
		req.AbleWithdrawMorethan, req.PromotionWebBonusConditionId, req.BonusConditionAmount,
		req.PromotionWebBonusTypeId, req.BonusTypeAmount, req.AbleWithdrawPertime,
		req.PromotionWebTurnoverTypeId, req.TurnoverAmount,
		req.Monday, req.Tuesday, req.Wednesday, req.Thursday,
		req.Friday, req.Saturday, req.Sunday,
		req.TimeStart, req.TimeEnd, req.PromotionWebDateTypeId,
		req.BonusTypeAmountMax, req.HiddenUrlLink, req.UpdatedByAdminId, now,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update promotion web")
		return errors.NewDatabaseError("failed to update promotion web")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web not found")
	}

	return nil
}

func (r *PromotionWebRepository) GetPromotionWebUserToCancel(ctx context.Context, PromotionWebId int64) ([]promotion_web.GetPromotionWebIdToCancel, error) {
	query := `
		SELECT
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id
		FROM promotion_web_user
		WHERE promotion_web_id = $1
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, PromotionWebId)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion web users to cancel")
		return nil, errors.NewDatabaseError("failed to get promotion web users to cancel")
	}
	defer rows.Close()

	var list []promotion_web.GetPromotionWebIdToCancel
	for rows.Next() {
		var item promotion_web.GetPromotionWebIdToCancel
		err := rows.Scan(&item.Id, &item.PromotionWebUserStatusId)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion web user to cancel row")
			return nil, errors.NewDatabaseError("failed to scan promotion web user to cancel row")
		}
		list = append(list, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion web users to cancel rows")
		return nil, errors.NewDatabaseError("error iterating promotion web users to cancel rows")
	}

	return list, nil
}

func (r *PromotionWebRepository) CancelPromotionWeb(ctx context.Context, req promotion_web.CancelPromotionWebRequest) error {
	query := `
		UPDATE promotion_web SET
			promotion_web_status_id = $2,
			canceled_by_admin_id = $3,
			updated_at = $4
		WHERE id = $1 AND deleted_at IS NULL
	`

	now := time.Now().UTC()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Id, req.PromotionWebStatusId, req.CanceledByAdminId, now,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to cancel promotion web")
		return errors.NewDatabaseError("failed to cancel promotion web")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web not found")
	}

	return nil
}

func (r *PromotionWebRepository) DeletePromotionWeb(ctx context.Context, req promotion_web.DeletePromotionWebRequest) error {
	query := `
		UPDATE promotion_web SET
			deleted_by_admin_id = $2,
			deleted_at = $3
		WHERE id = $1 AND deleted_at IS NULL
	`

	now := time.Now().UTC()
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Id, req.DeletedByAdminId, now,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to delete promotion web")
		return errors.NewDatabaseError("failed to delete promotion web")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web not found")
	}

	return nil
}

func (r *PromotionWebRepository) GetExpiredPromotionWeb(ctx context.Context, StartCleanUpDate string) ([]promotion_web.PromotionWebExpired, error) {
	query := `
		SELECT promotion_web.id AS id
		FROM promotion_web
		WHERE promotion_web.end_date < $1
		AND promotion_web.promotion_web_date_type_id = $2
		AND promotion_web.deleted_at IS NULL
	`

	// Use the constant from promotion_web domain
	const PROMOTION_WEB_DATE_TYPE_FIXED_DATE = 1

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, StartCleanUpDate, PROMOTION_WEB_DATE_TYPE_FIXED_DATE)
	if err != nil {
		r.logger.WithError(err).Error("failed to get expired promotion web")
		return nil, errors.NewDatabaseError("failed to get expired promotion web")
	}
	defer rows.Close()

	var list []promotion_web.PromotionWebExpired
	for rows.Next() {
		var item promotion_web.PromotionWebExpired
		err := rows.Scan(&item.Id)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan expired promotion web row")
			return nil, errors.NewDatabaseError("failed to scan expired promotion web row")
		}
		list = append(list, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating expired promotion web rows")
		return nil, errors.NewDatabaseError("error iterating expired promotion web rows")
	}

	return list, nil
}

func (r *PromotionWebRepository) PromotionConfirmUpdatePromotionWebUser(ctx context.Context, confirmId int64, promotionWebUserId int64) error {
	query := `
		UPDATE promotion_web_user_confirm
		SET promotion_web_user_id = $2
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, confirmId, promotionWebUserId)
	if err != nil {
		r.logger.WithError(err).Error("failed to update promotion web user confirm")
		return errors.NewDatabaseError("failed to update promotion web user confirm")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web user confirm not found")
	}

	return nil
}

func (r *PromotionWebRepository) CancelPromotionWebUserById(ctx context.Context, req promotion_web.CancelPromotionWebUserById) error {
	query := `
		UPDATE promotion_web_user SET
			promotion_web_user_status_id = $2,
			canceled_by_admin_id = $3,
			canceled_at = $4
		WHERE id = $1
		AND promotion_web_user_status_id = $5
	`

	// Use constants from promotion_web domain
	const PROMOTION_WEB_USER_STATUS_ON_PROCESS = 1
	statusCanceled := promotion_web.PROMOTION_WEB_USER_STATUS_CANCELED
	now := time.Now().UTC()

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Id, statusCanceled, req.CanceledByAdminId, now, PROMOTION_WEB_USER_STATUS_ON_PROCESS,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to cancel promotion web user by id")
		return errors.NewDatabaseError("failed to cancel promotion web user by id")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web user not found or not in process status")
	}

	return nil
}

func (r *PromotionWebRepository) ExpiredPromotionWebUserByIds(ctx context.Context, req promotion_web.CancelPromotionWebUserByPromotionWebId) error {
	query := `
		UPDATE promotion_web_user SET
			promotion_web_user_status_id = $2,
			canceled_by_admin_id = $3,
			canceled_at = $4
		WHERE promotion_web_id = $1
		AND promotion_web_user_status_id = $5
	`

	// Use constants from promotion_web domain
	const PROMOTION_WEB_USER_STATUS_ON_PROCESS = 1
	statusCanceled := promotion_web.PROMOTION_WEB_USER_STATUS_CANCELED

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.PromotionWebId, statusCanceled, req.CanceledByAdminId, req.CanceledAt, PROMOTION_WEB_USER_STATUS_ON_PROCESS,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to expire promotion web users by promotion web id")
		return errors.NewDatabaseError("failed to expire promotion web users by promotion web id")
	}

	return nil
}

func (r *PromotionWebRepository) GetUserPromotionWebByUserId(ctx context.Context, userId int64) (*promotion_web.PromotionWebUserByUserIdResponse, error) {
	query := `
		SELECT
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_id AS promotion_web_id,
			promotion_web.name AS promotion_name,
			promotion_web_user.user_id AS user_id,
			user.member_code AS member_code,
			user.fullname AS full_name,
			user.phone AS phone,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id,
			promotion_web_user_status.label_th AS promotion_web_user_status_th,
			promotion_web_user.total_amount AS total_amount,
			promotion_web_user.created_at AS created_at
		FROM promotion_web_user
		LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
		LEFT JOIN user ON promotion_web_user.user_id = user.id
		LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
		WHERE promotion_web_user.user_id = $1
		AND promotion_web_user.deleted_at IS NULL
		LIMIT 1
	`

	var result promotion_web.PromotionWebUserByUserIdResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userId).Scan(
		&result.Id, &result.PromotionWebId, &result.PromotionName,
		&result.UserId, &result.MemberCode, &result.FullName, &result.Phone,
		&result.PromotionWebUserStatusId, &result.PromotionWebUserStatusTh,
		&result.TotalAmount, &result.CreatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("promotion web user not found")
		}
		r.logger.WithError(err).Error("failed to get user promotion web by user ID")
		return nil, errors.NewDatabaseError("failed to get user promotion web")
	}

	return &result, nil
}

// UploadImageToCloudflare uploads an image to Cloudflare Images
func (r *PromotionWebRepository) UploadImageToCloudflare(ctx context.Context, pathUpload string, filename string, fileReader io.Reader) (*promotion_web.CloudFlareUploadCreateBody, error) {
	// Generate random filename for unique identification
	randomFilename := r.generateRandomString()

	// Set imageCloudFlarePathName
	imageCloudFlarePathName := fmt.Sprintf("%s%s", pathUpload, randomFilename)

	// Create multipart form body
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Set custom ID for the image
	err := writer.WriteField("id", imageCloudFlarePathName)
	if err != nil {
		r.logger.WithError(err).Error("failed to write id field to multipart form")
		return nil, errors.NewInternalError("failed to prepare upload form")
	}

	// Create form file part
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		r.logger.WithError(err).Error("failed to create form file part")
		return nil, errors.NewInternalError("failed to prepare file upload")
	}

	// Copy file content to form
	_, err = io.Copy(part, fileReader)
	if err != nil {
		r.logger.WithError(err).Error("failed to copy file content")
		return nil, errors.NewInternalError("failed to copy file content")
	}

	err = writer.Close()
	if err != nil {
		r.logger.WithError(err).Error("failed to close multipart writer")
		return nil, errors.NewInternalError("failed to finalize upload form")
	}

	// Get Cloudflare configuration from environment
	accountId := os.Getenv("CLOUDFLARE_ACCOUNT_ID")
	token := os.Getenv("CLOUDFLARE_API_TOKEN")
	baseURL := os.Getenv("CLOUDFLARE_UPLOAD_URL")

	if accountId == "" || token == "" || baseURL == "" {
		r.logger.Error("missing Cloudflare configuration environment variables")
		return nil, errors.NewInternalError("Cloudflare configuration not found")
	}

	// Build Cloudflare API URL
	url := fmt.Sprintf("%s/accounts/%s/images/v1", baseURL, accountId)

	// Create HTTP request with context
	request, err := http.NewRequestWithContext(ctx, "POST", url, body)
	if err != nil {
		r.logger.WithError(err).Error("failed to create HTTP request")
		return nil, errors.NewInternalError("failed to create upload request")
	}

	// Set headers
	request.Header.Set("Authorization", "Bearer "+token)
	request.Header.Set("Content-Type", writer.FormDataContentType())

	// Execute request
	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		r.logger.WithError(err).Error("failed to execute Cloudflare upload request")
		return nil, errors.NewInternalError("failed to upload to Cloudflare")
	}
	defer resp.Body.Close()

	// Read response
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		r.logger.WithError(err).Error("failed to read Cloudflare response")
		return nil, errors.NewInternalError("failed to read upload response")
	}

	// Check response status
	if resp.StatusCode != 200 {
		r.logger.WithField("status_code", resp.StatusCode).WithField("response", string(responseData)).Error("Cloudflare upload failed")
		return nil, errors.NewInternalError(fmt.Sprintf("Cloudflare upload failed with status %d", resp.StatusCode))
	}

	// Parse response
	var response promotion_web.CloudFlareUploadResponse
	if err := json.Unmarshal(responseData, &response); err != nil {
		r.logger.WithError(err).Error("failed to unmarshal Cloudflare response")
		return nil, errors.NewInternalError("failed to parse upload response")
	}

	// Validate response structure
	if len(response.Result.Variants) == 0 {
		r.logger.Error("Cloudflare response missing variants")
		return nil, errors.NewInternalError("invalid Cloudflare response format")
	}

	// Build result
	saveData := promotion_web.CloudFlareUploadCreateBody{
		ImageId:           response.Result.Id,
		Filename:          response.Result.Filename,
		Uploaded:          response.Result.Uploaded,
		RequireSignedURLs: response.Result.RequireSignedURLs,
		FileUrl:           response.Result.Variants[0],
	}

	r.logger.WithField("image_id", saveData.ImageId).WithField("filename", saveData.Filename).Info("successfully uploaded image to Cloudflare")
	return &saveData, nil
}

func (r *PromotionWebRepository) GetUserPromotionWebList(ctx context.Context, req promotion_web.PromotionWebUserGetListRequest) ([]promotion_web.PromotionWebUserGetListResponse, int64, error) {
	var list []promotion_web.PromotionWebUserGetListResponse
	var total int64

	// Build WHERE conditions
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Base WHERE condition
	whereConditions = append(whereConditions, "promotion_web_user.deleted_at IS NULL")

	// Promotion web filter
	if req.PromotionWebId != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.promotion_web_id = $%d", argIndex))
		args = append(args, *req.PromotionWebId)
		argIndex++
	}

	// Status filter
	if req.PromotionWebUserStatusId != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.promotion_web_user_status_id = $%d", argIndex))
		args = append(args, *req.PromotionWebUserStatusId)
		argIndex++
	}

	// Date filters
	if req.StartDate != "" {
		startDateAtBkk, err := r.datetime.ParseBodBkk(req.StartDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid start date format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at >= $%d", argIndex))
		args = append(args, *startDateAtBkk)
		argIndex++
	}

	if req.EndDate != "" {
		endDateAtBkk, err := r.datetime.ParseEodBkk(req.EndDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid end date format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at <= $%d", argIndex))
		args = append(args, *endDateAtBkk)
		argIndex++
	}

	// Search filter
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		whereConditions = append(whereConditions, fmt.Sprintf("(promotion_web.name ILIKE $%d OR user.member_code ILIKE $%d OR user.fullname ILIKE $%d OR user.phone ILIKE $%d)", argIndex, argIndex+1, argIndex+2, argIndex+3))
		args = append(args, searchPattern, searchPattern, searchPattern, searchPattern)
		argIndex += 4
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Get total count
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM promotion_web_user
		LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
		LEFT JOIN user ON promotion_web_user.user_id = user.id
		LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
		LEFT JOIN admin AS admincancel ON promotion_web_user.canceled_by_admin_id = admincancel.id
		LEFT JOIN admin AS adminapprove ON promotion_web_user.approve_credit_by_admin_id = adminapprove.id
		WHERE %s
	`, whereClause)

	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count promotion web user list")
		return nil, 0, errors.NewDatabaseError("failed to count promotion web user list")
	}

	if total > 0 {
		// Build the main query with pagination
		selectedFields := `
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_id AS promotion_web_id,
			promotion_web.name AS promotion_name,
			promotion_web_user.user_id AS user_id,
			user.member_code AS member_code,
			user.fullname AS full_name,
			user.phone AS phone,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id,
			promotion_web_user_status.label_th AS promotion_web_user_status_th,
			promotion_web_user.total_amount AS total_amount,
			promotion_web_user.created_at AS created_at,
			promotion_web_user.canceled_by_admin_id AS canceled_by_admin_id,
			promotion_web_user.canceled_at AS canceled_at,
			CASE WHEN admincancel.id IS NULL THEN 'อัตโนมัติ' ELSE admincancel.fullname END AS canceled_by_admin_name,
			promotion_web_lock_credit.is_locked AS is_locked,
			promotion_web_user.approve_credit_by_admin_id AS approve_credit_by_admin_id,
			adminapprove.fullname AS approve_credit_by_admin_name,
			promotion_web_user.approve_credit_at AS approve_credit_at,
			promotion_web.able_withdraw_morethan AS able_withdraw_morethan,
			promotion_web.able_withdraw_pertime AS able_withdraw_pertime
		`

		// Add pagination parameters
		paginationArgs := append(args, req.Limit, req.Page*req.Limit)

		dataQuery := fmt.Sprintf(`
			SELECT %s
			FROM promotion_web_user
			LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
			LEFT JOIN user ON promotion_web_user.user_id = user.id
			LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
			LEFT JOIN admin AS admincancel ON promotion_web_user.canceled_by_admin_id = admincancel.id
			LEFT JOIN promotion_web_lock_credit ON promotion_web_user.user_id = promotion_web_lock_credit.user_id AND promotion_web_user.promotion_web_id = promotion_web_lock_credit.promotion_id
			LEFT JOIN admin AS adminapprove ON promotion_web_user.approve_credit_by_admin_id = adminapprove.id
			WHERE %s
			ORDER BY promotion_web_user.created_at DESC
			LIMIT $%d OFFSET $%d
		`, selectedFields, whereClause, len(args)+1, len(args)+2)

		rows, err := dbutil.QueryWithSchema(ctx, r.pool, dataQuery, paginationArgs...)
		if err != nil {
			r.logger.WithError(err).Error("failed to get promotion web user list")
			return nil, 0, errors.NewDatabaseError("failed to get promotion web user list")
		}
		defer rows.Close()

		for rows.Next() {
			var item promotion_web.PromotionWebUserGetListResponse
			err := rows.Scan(
				&item.Id, &item.PromotionWebId, &item.PromotionName,
				&item.UserId, &item.MemberCode, &item.FullName, &item.Phone,
				&item.PromotionWebUserStatusId, &item.PromotionWebUserStatusTh,
				&item.TotalAmount, &item.CreatedAt,
				&item.CanceledByAdminId, &item.CanceledAt, &item.CanceledByAdminName,
				&item.IsLocked, &item.ApproveCreditByAdminId, &item.ApproveCreditByAdminName,
				&item.ApproveCreditAt, &item.AbleWithdrawMorethan, &item.AbleWithdrawPertime,
			)
			if err != nil {
				r.logger.WithError(err).Error("failed to scan promotion web user row")
				return nil, 0, errors.NewDatabaseError("failed to scan promotion web user row")
			}
			list = append(list, item)
		}

		if err = rows.Err(); err != nil {
			r.logger.WithError(err).Error("error iterating promotion web user rows")
			return nil, 0, errors.NewDatabaseError("error iterating promotion web user rows")
		}
	}

	return list, total, nil
}

func (r *PromotionWebRepository) GetPromotionWebUserById(ctx context.Context, req promotion_web.GetPromotionWebUserById) (*promotion_web.GetPromotionWebUserByIdResponse, error) {
	query := `
		SELECT
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_id AS promotion_web_id,
			promotion_web.name AS promotion_name,
			promotion_web_user.user_id AS user_id,
			user.member_code AS member_code,
			user.fullname AS full_name,
			user.phone AS phone,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id,
			promotion_web_user_status.label_th AS promotion_web_user_status_th,
			promotion_web_user.total_amount AS total_amount,
			promotion_web_user.created_at AS created_at
		FROM promotion_web_user
		LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
		LEFT JOIN user ON promotion_web_user.user_id = user.id
		LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
		WHERE promotion_web_user.id = $1
		AND promotion_web_user.deleted_at IS NULL
	`

	var result promotion_web.GetPromotionWebUserByIdResponse
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, req.Id).Scan(
		&result.Id, &result.PromotionWebId, &result.PromotionName,
		&result.UserId, &result.MemberCode, &result.FullName, &result.Phone,
		&result.PromotionWebUserStatusId, &result.PromotionWebUserStatusTh,
		&result.TotalAmount, &result.CreatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("promotion web user not found")
		}
		r.logger.WithError(err).Error("failed to get promotion web user by ID")
		return nil, errors.NewDatabaseError("failed to get promotion web user")
	}

	return &result, nil
}

func (r *PromotionWebRepository) PromotionWebUserGetListByUserId(ctx context.Context, req promotion_web.PromotionWebUserGetListByUserIdRequest) ([]promotion_web.PromotionWebUserGetListByUserIdResponse, int64, error) {
	var list []promotion_web.PromotionWebUserGetListByUserIdResponse
	var total int64

	// Build WHERE conditions
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Base WHERE conditions
	whereConditions = append(whereConditions, "promotion_web_user.deleted_at IS NULL")
	whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.user_id = $%d", argIndex))
	args = append(args, req.UserId)
	argIndex++

	// Status filter
	if req.PromotionWebUserStatusId != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.promotion_web_user_status_id = $%d", argIndex))
		args = append(args, *req.PromotionWebUserStatusId)
		argIndex++
	}

	// Date filtering logic
	if req.OfDate != "" {
		// OfDate is Primary - use specific date
		startDateAtBkk, err := r.datetime.ParseBodBkk(req.OfDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid OfDate format")
		}
		endDateAtBkk, err := r.datetime.ParseEodBkk(req.OfDate)
		if err != nil {
			return nil, 0, errors.NewValidationError("invalid OfDate format")
		}
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at >= $%d", argIndex))
		args = append(args, *startDateAtBkk)
		argIndex++
		whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at <= $%d", argIndex))
		args = append(args, *endDateAtBkk)
		argIndex++
	} else {
		// Handle DateType logic
		action := time.Now().UTC()
		bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
		filterTime := action.In(bkkLoc)

		var fromDate, toDate string

		switch req.DateType {
		case "today":
			fromDate = filterTime.Format("2006-01-02")
			toDate = filterTime.Format("2006-01-02")
		case "yesterday":
			fromDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
			toDate = filterTime.AddDate(0, 0, -1).Format("2006-01-02")
		case "this_month":
			fromDate = filterTime.Format("2006-01") + "-01"
			toDate = filterTime.AddDate(0, 1, -1).Format("2006-01-02")
		default:
			// Use FromDate and ToDate from request
			fromDate = req.FromDate
			toDate = req.ToDate
		}

		// Apply date filters
		if fromDate != "" {
			startDateAtBkk, err := r.datetime.ParseBodBkk(fromDate)
			if err != nil {
				return nil, 0, errors.NewValidationError("invalid FromDate format")
			}
			whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at >= $%d", argIndex))
			args = append(args, *startDateAtBkk)
			argIndex++
		}

		if toDate != "" {
			endDateAtBkk, err := r.datetime.ParseEodBkk(toDate)
			if err != nil {
				return nil, 0, errors.NewValidationError("invalid ToDate format")
			}
			whereConditions = append(whereConditions, fmt.Sprintf("promotion_web_user.created_at <= $%d", argIndex))
			args = append(args, *endDateAtBkk)
			argIndex++
		}
	}

	// Search filter
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		whereConditions = append(whereConditions, fmt.Sprintf("(promotion_web.name ILIKE $%d OR user.member_code ILIKE $%d OR user.fullname ILIKE $%d OR user.phone ILIKE $%d)", argIndex, argIndex+1, argIndex+2, argIndex+3))
		args = append(args, searchPattern, searchPattern, searchPattern, searchPattern)
		argIndex += 4
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Get total count
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM promotion_web_user
		LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
		LEFT JOIN user ON promotion_web_user.user_id = user.id
		LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
		LEFT JOIN admin ON promotion_web_user.canceled_by_admin_id = admin.id
		WHERE %s
	`, whereClause)

	err := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count promotion web user list by user id")
		return nil, 0, errors.NewDatabaseError("failed to count promotion web user list by user id")
	}

	if total > 0 {
		// Build the main query with pagination and sorting
		selectedFields := `
			promotion_web_user.id AS id,
			promotion_web_user.promotion_web_id AS promotion_web_id,
			promotion_web.name AS promotion_name,
			promotion_web_user.user_id AS user_id,
			user.member_code AS member_code,
			user.fullname AS full_name,
			user.phone AS phone,
			promotion_web_user.promotion_web_user_status_id AS promotion_web_user_status_id,
			promotion_web_user_status.label_th AS promotion_web_user_status_th,
			promotion_web_user.total_amount AS total_amount,
			promotion_web_user.created_at AS created_at,
			promotion_web_user.canceled_by_admin_id AS canceled_by_admin_id,
			CASE WHEN promotion_web_user.canceled_by_admin_id = 0 THEN 'อัตโนมัติ' ELSE admin.fullname END AS canceled_by_admin_name
		`

		// Build ORDER BY clause
		orderBy := "ORDER BY promotion_web_user.created_at DESC" // default
		if req.SortCol != "" {
			sortCol := strings.TrimSpace(req.SortCol)
			sortAsc := "ASC"
			if strings.ToLower(strings.TrimSpace(req.SortAsc)) == "desc" {
				sortAsc = "DESC"
			}
			// Validate sort column to prevent SQL injection
			validSortCols := map[string]bool{
				"promotion_web_user.id":           true,
				"promotion_web_user.created_at":   true,
				"promotion_web.name":              true,
				"user.member_code":                true,
				"user.fullname":                   true,
				"promotion_web_user.total_amount": true,
			}
			if validSortCols[sortCol] {
				orderBy = fmt.Sprintf("ORDER BY %s %s", sortCol, sortAsc)
			}
		}

		// Add pagination parameters
		paginationArgs := append(args, req.Limit, req.Page*req.Limit)

		dataQuery := fmt.Sprintf(`
			SELECT %s
			FROM promotion_web_user
			LEFT JOIN promotion_web ON promotion_web_user.promotion_web_id = promotion_web.id
			LEFT JOIN user ON promotion_web_user.user_id = user.id
			LEFT JOIN promotion_web_user_status ON promotion_web_user.promotion_web_user_status_id = promotion_web_user_status.id
			LEFT JOIN admin ON promotion_web_user.canceled_by_admin_id = admin.id
			WHERE %s
			%s
			LIMIT $%d OFFSET $%d
		`, selectedFields, whereClause, orderBy, len(args)+1, len(args)+2)

		rows, err := dbutil.QueryWithSchema(ctx, r.pool, dataQuery, paginationArgs...)
		if err != nil {
			r.logger.WithError(err).Error("failed to get promotion web user list by user id")
			return nil, 0, errors.NewDatabaseError("failed to get promotion web user list by user id")
		}
		defer rows.Close()

		for rows.Next() {
			var item promotion_web.PromotionWebUserGetListByUserIdResponse
			err := rows.Scan(
				&item.Id, &item.PromotionWebId, &item.PromotionName,
				&item.UserId, &item.MemberCode, &item.FullName, &item.Phone,
				&item.PromotionWebUserStatusId, &item.PromotionWebUserStatusTh,
				&item.TotalAmount, &item.CreatedAt,
				&item.CanceledByAdminId, &item.CanceledByAdminName,
			)
			if err != nil {
				r.logger.WithError(err).Error("failed to scan promotion web user by user id row")
				return nil, 0, errors.NewDatabaseError("failed to scan promotion web user by user id row")
			}
			list = append(list, item)
		}

		if err = rows.Err(); err != nil {
			r.logger.WithError(err).Error("error iterating promotion web user by user id rows")
			return nil, 0, errors.NewDatabaseError("error iterating promotion web user by user id rows")
		}
	}

	return list, total, nil
}

func (r *PromotionWebRepository) PromotionWebGetSildeListOnlyActive(ctx context.Context) ([]promotion_web.PromotionWebGetSildeListOnlyActive, error) {
	query := `
		SELECT
			promotion_web.id AS id,
			promotion_web.promotion_web_type_id AS promotion_web_type_id,
			promotion_web_type.label_th AS promotion_web_type_th,
			promotion_web.promotion_web_status_id AS promotion_web_status_id,
			promotion_web_status.label_th AS promotion_web_status_th,
			promotion_web.name AS name,
			promotion_web.promotion_web_date_type_id AS promotion_web_date_type_id,
			promotion_web.start_date AS start_date,
			promotion_web.end_date AS end_date,
			promotion_web.time_start AS time_start,
			promotion_web.time_end AS time_end
		FROM promotion_web
		LEFT JOIN promotion_web_type ON promotion_web.promotion_web_type_id = promotion_web_type.id
		LEFT JOIN promotion_web_status ON promotion_web.promotion_web_status_id = promotion_web_status.id
		LEFT JOIN promotion_web_date_type ON promotion_web.promotion_web_date_type_id = promotion_web_date_type.id
		WHERE promotion_web.promotion_web_status_id IN ($1, $2, $3)
		AND promotion_web.deleted_at IS NULL
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query,
		promotion_web.PROMOTION_WEB_STATUS_ACTIVE,
		promotion_web.PROMOTION_WEB_STATUS_DISABLE_WEB,
		promotion_web.PROMOTION_WEB_STATUS_ONLY_URL,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to get active promotion web slide list")
		return nil, errors.NewDatabaseError("failed to get active promotion web slide list")
	}
	defer rows.Close()

	var list []promotion_web.PromotionWebGetSildeListOnlyActive
	for rows.Next() {
		var item promotion_web.PromotionWebGetSildeListOnlyActive
		err := rows.Scan(
			&item.Id, &item.PromotionWebTypeId, &item.PromotionWebTypeTh,
			&item.PromotionWebStatusId, &item.PromotionWebStatusTh,
			&item.Name, &item.PromotionWebDateTypeId,
			&item.StartDate, &item.EndDate, &item.TimeStart, &item.TimeEnd,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan promotion web slide row")
			return nil, errors.NewDatabaseError("failed to scan promotion web slide row")
		}
		list = append(list, item)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating promotion web slide rows")
		return nil, errors.NewDatabaseError("error iterating promotion web slide rows")
	}

	return list, nil
}

func (r *PromotionWebRepository) UpdatePromotionWebPriorityOrderCreate(ctx context.Context, id int64) error {
	query := `
		UPDATE promotion_web
		SET priority_order = $2
		WHERE id = $1 AND deleted_at IS NULL
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to update promotion web priority order")
		return errors.NewDatabaseError("failed to update promotion web priority order")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("promotion web not found")
	}

	return nil
}

func (r *PromotionWebRepository) SortPromotionWebPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest) error {
	// Get the priority orders for both items
	query := `
		SELECT id, priority_order
		FROM promotion_web
		WHERE id IN ($1, $2) AND deleted_at IS NULL
		LIMIT 2
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, req.FromItemId, req.ToItemId)
	if err != nil {
		r.logger.WithError(err).Error("failed to get promotion web items for sorting")
		return errors.NewDatabaseError("failed to get promotion web items for sorting")
	}
	defer rows.Close()

	var results []promotion_web.PrioritySortResponse
	for rows.Next() {
		var result promotion_web.PrioritySortResponse
		err := rows.Scan(&result.Id, &result.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan priority sort row")
			return errors.NewDatabaseError("failed to scan priority sort row")
		}
		results = append(results, result)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating priority sort rows")
		return errors.NewDatabaseError("error iterating priority sort rows")
	}

	if len(results) != 2 {
		return errors.NewValidationError("both items must exist for sorting")
	}

	var fromItem, toItem *promotion_web.PrioritySortResponse
	for _, result := range results {
		if result.Id == req.FromItemId {
			fromItem = &result
		} else if result.Id == req.ToItemId {
			toItem = &result
		}
	}

	if fromItem == nil || toItem == nil {
		return errors.NewValidationError("invalid item IDs for sorting")
	}

	// Perform the sorting operations
	if fromItem.PriorityOrder < toItem.PriorityOrder {
		// Drag down - shift items up
		shiftQuery := `
			UPDATE promotion_web
			SET priority_order = priority_order - 1
			WHERE priority_order > $1 AND priority_order <= $2 AND deleted_at IS NULL
		`
		_, err = dbutil.ExecWithSchema(ctx, r.pool, shiftQuery, fromItem.PriorityOrder, toItem.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to shift items down")
			return errors.NewDatabaseError("failed to shift items down")
		}

		// Move the from item to the to position
		moveQuery := `UPDATE promotion_web SET priority_order = $2 WHERE id = $1`
		_, err = dbutil.ExecWithSchema(ctx, r.pool, moveQuery, fromItem.Id, toItem.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to move item to new position")
			return errors.NewDatabaseError("failed to move item to new position")
		}
	} else if fromItem.PriorityOrder > toItem.PriorityOrder {
		// Drag up - shift items down
		shiftQuery := `
			UPDATE promotion_web
			SET priority_order = priority_order + 1
			WHERE priority_order < $1 AND priority_order >= $2 AND deleted_at IS NULL
		`
		_, err = dbutil.ExecWithSchema(ctx, r.pool, shiftQuery, fromItem.PriorityOrder, toItem.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to shift items up")
			return errors.NewDatabaseError("failed to shift items up")
		}

		// Move the from item to the to position
		moveQuery := `UPDATE promotion_web SET priority_order = $2 WHERE id = $1`
		_, err = dbutil.ExecWithSchema(ctx, r.pool, moveQuery, fromItem.Id, toItem.PriorityOrder)
		if err != nil {
			r.logger.WithError(err).Error("failed to move item to new position")
			return errors.NewDatabaseError("failed to move item to new position")
		}
	}

	return nil
}
