package postgres

import (
	"blacking-api/internal/domain/sms_provider_name"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
)

type SMSOProviderName struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewSMSProviderNameRepository(pool *pgxpool.Pool, logger logger.Logger) *SMSOProviderName {
	return &SMSOProviderName{
		pool:   pool,
		logger: logger,
	}
}

func (r *SMSOProviderName) List(ctx context.Context) ([]*sms_provider_name.SMSProviderName, error) {
	var smsp []*sms_provider_name.SMSProviderName
	query := `SELECT id, name FROM sms_provider_name ORDER BY id ASC`
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list sms provider name settings")
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		sms := &sms_provider_name.SMSProviderName{}
		if err := rows.Scan(&sms.ID, &sms.Name); err != nil {
			r.logger.WithError(err).Error("failed to scan sms provider name setting")
			return nil, err
		}
		smsp = append(smsp, sms)
	}

	return smsp, nil
}

func (r *SMSOProviderName) FindIdExists(ctx context.Context, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM sms_provider_name WHERE id = $1)`
	var exists bool

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check if sms provider name ID exists")
		return false, err
	}

	return exists, nil
}
