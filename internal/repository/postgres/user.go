package postgres

import (
	"context"
	"fmt"
	"strings"

	"blacking-api/internal/domain/user"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type UserRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewUserRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.UserRepository {
	return &UserRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *UserRepository) Create(ctx context.Context, u *user.User) error {
	query := `
		INSERT INTO users (username, password, first_name, last_name, status, created_at, updated_at, user_role_id, user_role_name, is_enable)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING id
	`
	var lastInsertID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		u.Username, u.Password, u.FirstName, u.LastName, u.Status, u.CreatedAt, u.UpdatedAt, u.UserRoleID, u.UserRoleName, u.IsEnable,
	)
	err := row.Scan(&lastInsertID)
	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505":
				if pgErr.ConstraintName == "users_username_key" {
					return errors.NewConflictError("username already exists")
				}
			}
		}
		r.logger.WithError(err).Error("failed to create user")
		return errors.NewDatabaseError("failed to create user")
	}

	u.ID = lastInsertID
	r.logger.WithField("user_id", u.ID).Info("user created successfully")
	return nil
}

func (r *UserRepository) GetByID(ctx context.Context, id string) (*user.User, error) {
	query := `
		SELECT id, username, password, first_name, last_name, status, created_at, updated_at, user_role_id, user_role_name, is_enable
		FROM users
		WHERE id = $1 AND status = 'active'
	`

	u := &user.User{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(
		&u.ID, &u.Username, &u.Password, &u.FirstName, &u.LastName, &u.Status, &u.CreatedAt, &u.UpdatedAt, &u.UserRoleID, &u.UserRoleName, &u.IsEnable,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("user not found")
		}
		r.logger.WithError(err).WithField("user_id", id).Error("failed to get user by ID")
		return nil, errors.NewDatabaseError("failed to get user")
	}

	// Debug: Log scanned data
	r.logger.WithFields(map[string]interface{}{
		"scanned_id":           u.ID,
		"scanned_username":     u.Username,
		"scanned_user_role_id": u.UserRoleID,
		"queried_id":           id,
	}).Info("User data scanned from database")

	return u, nil
}

func (r *UserRepository) GetByUsername(ctx context.Context, username string) (*user.User, error) {
	query := `
		SELECT id, username, password, first_name, last_name, status, created_at, updated_at, user_role_id, user_role_name, is_enable
		FROM users
		WHERE username = $1
	`

	u := &user.User{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, username)
	err := row.Scan(
		&u.ID, &u.Username, &u.Password, &u.FirstName, &u.LastName, &u.Status, &u.CreatedAt, &u.UpdatedAt, &u.UserRoleID, &u.UserRoleName, &u.IsEnable,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("user not found")
		}
		r.logger.WithError(err).WithField("username", username).Error("failed to get user by username")
		return nil, errors.NewDatabaseError("failed to get user")
	}

	return u, nil
}

func (r *UserRepository) Update(ctx context.Context, u *user.User) error {
	query := `
		UPDATE users
		SET username = $2, first_name = $3, last_name = $4, status = $5, password = $6,
		    user_role_id = $7, user_role_name = $8, updated_at = $9, is_enable = $10
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		u.ID, u.Username, u.FirstName, u.LastName, u.Status, u.Password,
		u.UserRoleID, u.UserRoleName, u.UpdatedAt, u.IsEnable,
	)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", u.ID).Error("failed to update user")
		return errors.NewDatabaseError("failed to update user")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("user not found")
	}

	r.logger.WithField("user_id", u.ID).Info("user updated successfully")
	return nil
}

func (r *UserRepository) Delete(ctx context.Context, id string) error {
	query := `UPDATE users SET status = 'inactive', updated_at = NOW() WHERE id = $1 AND status != 'inactive'`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", id).Error("failed to delete user")
		return errors.NewDatabaseError("failed to delete user")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("user not found")
	}

	r.logger.WithField("user_id", id).Info("user deleted successfully")
	return nil
}

func (r *UserRepository) List(ctx context.Context, limit, offset int, search string, userRoleID *int) ([]*user.User, error) {
	var query string
	var args []interface{}
	var whereConditions []string
	var paramIndex int = 1

	// Base query
	baseQuery := `
		SELECT u.id, u.username, u.password, u.first_name, u.last_name, u.status, u.created_at, u.updated_at,
			   u.user_role_id, COALESCE(ur.name, '') as user_role_name, u.is_enable,
			   CASE WHEN u2fa.user_id IS NOT NULL THEN true ELSE false END as is_2fa_enabled
		FROM users u
		LEFT JOIN user_roles ur ON u.user_role_id = ur.id AND ur.status = 'active'
		LEFT JOIN user_2fa u2fa ON u.id = u2fa.user_id
		WHERE u.status = 'active'
	`

	// Add search condition
	if search != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("u.username ILIKE '%%' || $%d || '%%'", paramIndex))
		args = append(args, search)
		paramIndex++
	}

	// Add user_role_id filter
	if userRoleID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("u.user_role_id = $%d", paramIndex))
		args = append(args, *userRoleID)
		paramIndex++
	}

	// Build final query
	if len(whereConditions) > 0 {
		query = baseQuery + " AND " + strings.Join(whereConditions, " AND ")
	} else {
		query = baseQuery
	}

	query += fmt.Sprintf(" ORDER BY u.username ASC NULLS LAST, u.created_at DESC LIMIT $%d OFFSET $%d", paramIndex, paramIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list users")
		return nil, errors.NewDatabaseError("failed to list users")
	}
	defer rows.Close()

	var users []*user.User
	for rows.Next() {
		u := &user.User{}
		var is2FAEnabled bool
		err := rows.Scan(
			&u.ID, &u.Username, &u.Password, &u.FirstName, &u.LastName, &u.Status, &u.CreatedAt, &u.UpdatedAt,
			&u.UserRoleID, &u.UserRoleName, &u.IsEnable, &is2FAEnabled,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user row")
			return nil, errors.NewDatabaseError("failed to scan user")
		}

		// Set 2FA status
		u.Is2FAEnabled = is2FAEnabled

		users = append(users, u)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user rows")
		return nil, errors.NewDatabaseError("failed to list users")
	}

	return users, nil
}

func (r *UserRepository) Count(ctx context.Context, search string, userRoleID *int) (int64, error) {
	var query string
	var args []interface{}
	var whereConditions []string
	var paramIndex int = 1

	// Base query
	baseQuery := `SELECT COUNT(*) FROM users WHERE status = 'active'`

	// Add search condition
	if search != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("username ILIKE '%%' || $%d || '%%'", paramIndex))
		args = append(args, search)
		paramIndex++
	}

	// Add user_role_id filter
	if userRoleID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("user_role_id = $%d", paramIndex))
		args = append(args, *userRoleID)
		paramIndex++
	}

	// Build final query
	if len(whereConditions) > 0 {
		query = baseQuery + " AND " + strings.Join(whereConditions, " AND ")
	} else {
		query = baseQuery
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count users")
		return 0, errors.NewDatabaseError("failed to count users")
	}

	return count, nil
}
