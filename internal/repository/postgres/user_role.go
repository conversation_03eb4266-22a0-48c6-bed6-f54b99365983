package postgres

import (
	"context"
	"fmt"
	"strings"

	userrole "blacking-api/internal/domain/user_role"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type UserRoleRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewUserRoleRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.UserRoleRepository {
	return &UserRoleRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *UserRoleRepository) Create(ctx context.Context, u *userrole.UserRole) error {
	query := `
		INSERT INTO user_roles (name, status, is_enable, created_at, updated_at, is_2fa, is_lock_ip)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id
	`
	var lastInsertID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		u.Name, u.Status, u.IsEnable, u.CreatedAt, u.UpdatedAt, u.Is2FA, u.IsLockIP,
	)
	err := row.Scan(&lastInsertID)

	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505": // unique_violation
			}
		}

		r.logger.WithError(err).Error("failed to create user_role")
		return errors.NewDatabaseError("failed to create user_role")
	}

	updatePositionQuery := `UPDATE user_roles SET position = id WHERE id = $1`
	_, err = dbutil.ExecWithSchema(ctx, r.pool, updatePositionQuery, lastInsertID)
	if err != nil {
		r.logger.WithError(err).Error("failed to set position user_role")
	}

	u.ID = lastInsertID
	u.Position = &lastInsertID

	r.logger.WithField("user_role_id", u.ID).Info("user_role created successfully")
	return nil
}

func (r *UserRoleRepository) GetByID(ctx context.Context, id string) (*userrole.UserRole, error) {
	query := `
		SELECT id, position, name, is_enable, is_2fa, is_lock_ip, status, created_at, updated_at
		FROM user_roles
		WHERE id = $1 AND status = 'active'
	`

	u := &userrole.UserRole{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&u.ID, &u.Position, &u.Name, &u.IsEnable, &u.Is2FA, &u.IsLockIP, &u.Status, &u.CreatedAt, &u.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("user_role not found")
		}
		r.logger.WithError(err).WithField("user_role_id", id).Error("failed to get user_role by ID")
		return nil, errors.NewDatabaseError("failed to get user_role")
	}

	return u, nil
}

func (r *UserRoleRepository) Update(ctx context.Context, u *userrole.UserRole) error {
	query := `
		UPDATE user_roles
		SET name = $2, status = $3, is_enable = $4, is_2fa = $5, is_lock_ip = $6, updated_at = $7
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		u.ID, u.Name, u.Status, u.IsEnable, u.Is2FA, u.IsLockIP, u.UpdatedAt,
	)
	if err != nil {
		r.logger.WithError(err).WithField("user_role_id", u.ID).Error("failed to update user_role")
		return errors.NewDatabaseError("failed to update user_role")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("user_role not found")
	}

	r.logger.WithField("user_role_id", u.ID).Info("user_role updated successfully")
	return nil
}

func (r *UserRoleRepository) Delete(ctx context.Context, id string) error {
	query := `UPDATE user_roles SET status = 'inactive', updated_at = NOW() WHERE id = $1 AND status != 'inactive'`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("user_role_id", id).Error("failed to soft delete user_role")
		return errors.NewDatabaseError("failed to delete user_role")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("user_role not found or already deleted")
	}

	r.logger.WithField("user_role_id", id).Info("user_role soft deleted successfully")
	return nil
}

func (r *UserRoleRepository) List(ctx context.Context, limit, offset int, search string) ([]*userrole.UserRole, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT id, position, name, is_enable, is_2fa, is_lock_ip, status, created_at, updated_at
			FROM user_roles
			WHERE name ILIKE '%' || $1 || '%' AND status = 'active'
			ORDER BY position ASC NULLS LAST, created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{search, limit, offset}
	} else {
		query = `
			SELECT id, position, name, is_enable, is_2fa, is_lock_ip, status, created_at, updated_at
			FROM user_roles
			WHERE status = 'active'
			ORDER BY position ASC NULLS LAST, created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list user_role")
		return nil, errors.NewDatabaseError("failed to list user_role")
	}
	defer rows.Close()

	var userRoles []*userrole.UserRole
	for rows.Next() {
		u := &userrole.UserRole{}
		err := rows.Scan(
			&u.ID, &u.Position, &u.Name, &u.IsEnable, &u.Is2FA, &u.IsLockIP, &u.Status, &u.CreatedAt, &u.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user_role row")
			return nil, errors.NewDatabaseError("failed to scan user_role")
		}
		userRoles = append(userRoles, u)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user_role rows")
		return nil, errors.NewDatabaseError("failed to list user_role")
	}

	return userRoles, nil
}

func (r *UserRoleRepository) Count(ctx context.Context, search string) (int64, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `SELECT COUNT(*) FROM user_roles WHERE name ILIKE '%' || $1 || '%' AND status = 'active'`
		args = []interface{}{search}
	} else {
		query = `SELECT COUNT(*) FROM user_roles WHERE status = 'active'`
		args = []interface{}{}
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count user_role")
		return 0, errors.NewDatabaseError("failed to count user_role")
	}

	return count, nil
}

func (r *UserRoleRepository) BulkToggleLockIP(ctx context.Context, toggles []userrole.LockIPToggle) error {
	if len(toggles) == 0 {
		return errors.NewValidationError("toggles cannot be empty")
	}

	// สร้าง CASE statement สำหรับ bulk update
	var caseStatements []string
	var ids []string
	var args []interface{}
	argIndex := 1

	for _, toggle := range toggles {
		caseStatements = append(caseStatements, fmt.Sprintf("WHEN $%d THEN $%d", argIndex, argIndex+1))
		ids = append(ids, fmt.Sprintf("$%d", argIndex))
		args = append(args, toggle.ID, toggle.IsLockIP)
		argIndex += 2
	}

	query := fmt.Sprintf(`
		UPDATE user_roles
		SET is_lock_ip = (CASE id %s ELSE is_lock_ip END), updated_at = NOW()
		WHERE id IN (%s) AND status = 'active'
	`, strings.Join(caseStatements, " "), strings.Join(ids, ","))

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).WithField("toggles", toggles).Error("failed to bulk toggle lock IP")
		return errors.NewDatabaseError("failed to update user roles")
	}

	r.logger.WithFields(map[string]interface{}{
		"toggles":       toggles,
		"rows_affected": result.RowsAffected(),
	}).Info("bulk toggle lock IP completed")

	return nil
}

func (r *UserRoleRepository) BulkToggle2FA(ctx context.Context, toggles []userrole.Toggle2FA) error {
	if len(toggles) == 0 {
		return errors.NewValidationError("toggles cannot be empty")
	}

	// สร้าง CASE statement สำหรับ bulk update
	var caseStatements []string
	var ids []string
	var args []interface{}
	argIndex := 1

	for _, toggle := range toggles {
		caseStatements = append(caseStatements, fmt.Sprintf("WHEN $%d THEN $%d", argIndex, argIndex+1))
		ids = append(ids, fmt.Sprintf("$%d", argIndex))
		args = append(args, toggle.ID, toggle.Is2FA)
		argIndex += 2
	}

	query := fmt.Sprintf(`
		UPDATE user_roles
		SET is_2fa = (CASE id %s ELSE is_2fa END), updated_at = NOW()
		WHERE id IN (%s) AND status = 'active'
	`, strings.Join(caseStatements, " "), strings.Join(ids, ","))

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).WithField("toggles", toggles).Error("failed to bulk toggle 2FA")
		return errors.NewDatabaseError("failed to update user roles")
	}

	r.logger.WithFields(map[string]interface{}{
		"toggles":       toggles,
		"rows_affected": result.RowsAffected(),
	}).Info("bulk toggle 2FA completed")

	return nil
}

func (r *UserRoleRepository) BulkListLockIP(ctx context.Context) ([]*userrole.UserRole, error) {
	query := `
		SELECT id, position, name, is_2fa, is_lock_ip
		FROM user_roles
		WHERE status = 'active'
		ORDER BY position ASC NULLS LAST, name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to bulk list lock IP")
		return nil, errors.NewDatabaseError("failed to list user roles")
	}
	defer rows.Close()

	var userRoles []*userrole.UserRole
	for rows.Next() {
		u := &userrole.UserRole{}
		err := rows.Scan(&u.ID, &u.Position, &u.Name, &u.Is2FA, &u.IsLockIP)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan bulk lock IP row")
			return nil, errors.NewDatabaseError("failed to scan user role")
		}
		userRoles = append(userRoles, u)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating bulk lock IP rows")
		return nil, errors.NewDatabaseError("failed to list user roles")
	}

	return userRoles, nil
}

func (r *UserRoleRepository) BulkList2FA(ctx context.Context) ([]*userrole.UserRole, error) {
	query := `
		SELECT id, position, name, status, is_2fa, is_lock_ip
		FROM user_roles
		WHERE status = 'active'
		ORDER BY position ASC NULLS LAST, name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to bulk list 2FA")
		return nil, errors.NewDatabaseError("failed to list user roles")
	}
	defer rows.Close()

	var userRoles []*userrole.UserRole
	for rows.Next() {
		u := &userrole.UserRole{}
		err := rows.Scan(&u.ID, &u.Position, &u.Name, &u.Status, &u.Is2FA, &u.IsLockIP)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan bulk 2FA row")
			return nil, errors.NewDatabaseError("failed to scan user role")
		}
		userRoles = append(userRoles, u)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating bulk 2FA rows")
		return nil, errors.NewDatabaseError("failed to list user roles")
	}

	return userRoles, nil
}

func (r *UserRoleRepository) Reorder(ctx context.Context, userRoleIDs []int) error {
	if len(userRoleIDs) == 0 {
		return errors.NewValidationError("user_role_ids cannot be empty")
	}

	// สร้าง CASE statement สำหรับ update position
	var caseStatements []string
	var ids []string
	var args []interface{}
	argIndex := 1

	for position, userRoleID := range userRoleIDs {
		caseStatements = append(caseStatements, fmt.Sprintf("WHEN $%d THEN $%d", argIndex, argIndex+1))
		ids = append(ids, fmt.Sprintf("$%d", argIndex))
		args = append(args, userRoleID, position+1) // position เริ่มจาก 1
		argIndex += 2
	}

	query := fmt.Sprintf(`
		UPDATE user_roles
		SET position = (CASE id %s ELSE position END), updated_at = NOW()
		WHERE id IN (%s) AND status = 'active'
	`, strings.Join(caseStatements, " "), strings.Join(ids, ","))

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).WithField("user_role_ids", userRoleIDs).Error("failed to reorder user roles")
		return errors.NewDatabaseError("failed to reorder user roles")
	}

	r.logger.WithFields(map[string]interface{}{
		"user_role_ids": userRoleIDs,
		"rows_affected": result.RowsAffected(),
	}).Info("user roles reordered successfully")

	return nil
}
