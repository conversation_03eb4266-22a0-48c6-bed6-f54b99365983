package postgres

import (
	"blacking-api/internal/domain/contact"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ContactRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewContactRepository(pool *pgxpool.Pool, logger logger.Logger) *ContactRepository {
	return &ContactRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *ContactRepository) Create(ctx context.Context, req *contact.CreateContactRequest) error {
	query := `INSERT INTO contact (name, image_url, link, account, first_color, second_color, active, inactive) 
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`

	var link, account *string
	if req.Link != "" {
		link = &req.Link
	}
	if req.Account != "" {
		account = &req.Account
	}

	_, err := dbutil.ExecWithSchema(
		ctx, r.pool, query,
		req.Name,
		req.ImageURL,
		link,
		account,
		req.FirstColor,
		req.SecondColor,
		true,  // active
		false, // inactive
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to create contact")
		return errors.NewDatabaseError("failed to create contact")
	}

	return nil
}

func (r *ContactRepository) FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*contact.Contact, int64, error) {
	var contacts []*contact.Contact

	query := `
		SELECT id, name, image_url, link, account, main_contract, brithday, 
		       first_color, second_color, active, inactive, created_at, updated_at
		FROM contact
		WHERE inactive = false
	`

	if whereClause != "" {
		query += " AND " + whereClause[6:]
	}

	query += " ORDER BY created_at DESC"

	if limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", len(args)+1, len(args)+2)
		args = append(args, limit, offset)
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to find all contacts")
		return nil, 0, errors.NewDatabaseError("failed to find contacts")
	}
	defer rows.Close()

	for rows.Next() {
		c := &contact.Contact{}
		err := rows.Scan(
			&c.ID, &c.Name, &c.ImageURL, &c.Link, &c.Account, &c.MainContract, &c.Birthday,
			&c.FirstColor, &c.SecondColor, &c.Active, &c.Inactive, &c.CreatedAt, &c.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan contact")
			return nil, 0, errors.NewDatabaseError("failed to scan contact")
		}
		contacts = append(contacts, c)
	}

	countQuery := `
		SELECT COUNT(*)
		FROM contact
		WHERE inactive = false
	`
	if whereClause != "" {
		countQuery += " AND " + whereClause[6:]
	}

	var total int64
	countArgs := args
	if limit > 0 {
		countArgs = args[:len(args)-2]
	}

	row := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, countArgs...)
	err = row.Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count contacts")
		return nil, 0, errors.NewDatabaseError("failed to count contacts")
	}

	return contacts, total, nil
}

func (r *ContactRepository) FindByID(ctx context.Context, id int64) (*contact.Contact, error) {
	query := `
		SELECT id, name, image_url, link, account, main_contract, brithday, 
		       first_color, second_color, active, inactive, created_at, updated_at
		FROM contact
		WHERE id = $1 AND inactive = false
	`

	c := &contact.Contact{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(
		&c.ID, &c.Name, &c.ImageURL, &c.Link, &c.Account, &c.MainContract, &c.Birthday,
		&c.FirstColor, &c.SecondColor, &c.Active, &c.Inactive, &c.CreatedAt, &c.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("contact not found")
		}
		r.logger.WithError(err).Error("failed to find contact by ID")
		return nil, errors.NewDatabaseError("failed to find contact")
	}

	return c, nil
}

func (r *ContactRepository) FindByNameDuplicate(ctx context.Context, name string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM contact WHERE UPPER(name) = UPPER($1) AND inactive = false)`
	var exists bool

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, name)
	err := row.Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check contact name duplicate")
		return false, errors.NewDatabaseError("failed to check contact name")
	}

	return exists, nil
}

func (r *ContactRepository) FindByNameDuplicateAndIdNot(ctx context.Context, name string, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM contact WHERE UPPER(name) = UPPER($1) AND id != $2 AND inactive = false)`
	var exists bool

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, name, id)
	err := row.Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check contact name duplicate")
		return false, errors.NewDatabaseError("failed to check contact name")
	}

	return exists, nil
}

func (r *ContactRepository) Update(ctx context.Context, id int64, req *contact.CreateContactRequest) error {
	var link, account *string
	if req.Link != "" {
		link = &req.Link
	}
	if req.Account != "" {
		account = &req.Account
	}

	query := `UPDATE contact 
			SET name = $1, image_url = $2, link = $3, account = $4, 
			    first_color = $5, second_color = $6, updated_at = CURRENT_TIMESTAMP
			WHERE id = $7 AND inactive = false`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Name, req.ImageURL, link, account, req.FirstColor, req.SecondColor, id,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to update contact")
		return errors.NewDatabaseError("failed to update contact")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("contact not found")
	}

	return nil
}

func (r *ContactRepository) UpdateStatus(ctx context.Context, id int64, name string, status bool) error {
	query := fmt.Sprintf(`
        UPDATE contact 
        SET %s = $1
        WHERE id = $2 AND inactive = false
    `, name)

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, status, id)
	if err != nil {
		r.logger.Error("Failed to update contact status", "error", err, "column", name)
		return err
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating contact status")
		return errors.NewValidationError("contact not found")
	}

	return nil
}

func (r *ContactRepository) Delete(ctx context.Context, id int64) error {
	query := `UPDATE contact SET inactive = true, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND inactive = false`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete contact")
		return errors.NewDatabaseError("failed to delete contact")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("contact not found")
	}

	return nil
}
