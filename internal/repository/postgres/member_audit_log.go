package postgres

import (
	"blacking-api/internal/domain/member_audit_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5/pgxpool"
)

type MemberAuditLogRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewMemberAuditLogRepository creates a new member audit log repository
func NewMemberAuditLogRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.MemberAuditLogRepository {
	return &MemberAuditLogRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new member audit log entry
func (r *MemberAuditLogRepository) Create(ctx context.Context, auditLog *member_audit_log.MemberAuditLog) error {
	query := `
		INSERT INTO member_audit_logs (member_id, username, action, old_values, new_values, changed_by, changed_by_name, changed_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		auditLog.MemberID,
		auditLog.Username,
		auditLog.Action,
		auditLog.OldValues,
		auditLog.NewValues,
		auditLog.ChangedBy,
		auditLog.ChangedByName,
		auditLog.ChangedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("audit_log_id", auditLog.ID).Error("failed to create member audit log")
		return errors.NewDatabaseError("failed to create member audit log")
	}

	r.logger.WithField("audit_log_id", auditLog.ID).Info("member audit log created successfully")
	return nil
}

// List retrieves all audit logs with pagination and optional filters
func (r *MemberAuditLogRepository) List(ctx context.Context, limit, offset int, username string, action string) ([]*member_audit_log.MemberAuditLog, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT id, member_id, username, action, old_values, new_values, changed_by, changed_by_name, changed_at
		FROM member_audit_logs
	`

	if username != "" {
		conditions = append(conditions, fmt.Sprintf("username ILIKE $%d", argIndex))
		args = append(args, "%"+username+"%")
		argIndex++
	}

	if action != "" {
		conditions = append(conditions, fmt.Sprintf("action = $%d", argIndex))
		args = append(args, action)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	query += fmt.Sprintf(" ORDER BY changed_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list member audit logs")
		return nil, errors.NewDatabaseError("failed to list member audit logs")
	}
	defer rows.Close()

	var auditLogs []*member_audit_log.MemberAuditLog
	for rows.Next() {
		auditLog := &member_audit_log.MemberAuditLog{}
		err := rows.Scan(
			&auditLog.ID,
			&auditLog.MemberID,
			&auditLog.Username,
			&auditLog.Action,
			&auditLog.OldValues,
			&auditLog.NewValues,
			&auditLog.ChangedBy,
			&auditLog.ChangedByName,
			&auditLog.ChangedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member audit log")
			return nil, errors.NewDatabaseError("failed to scan member audit log")
		}
		auditLogs = append(auditLogs, auditLog)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member audit log rows")
		return nil, errors.NewDatabaseError("error iterating member audit log rows")
	}

	return auditLogs, nil
}

// Count returns the total count of audit logs with optional filters
func (r *MemberAuditLogRepository) Count(ctx context.Context, username string, action string) (int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT COUNT(*)
		FROM member_audit_logs
	`

	if username != "" {
		conditions = append(conditions, fmt.Sprintf("username ILIKE $%d", argIndex))
		args = append(args, "%"+username+"%")
		argIndex++
	}

	if action != "" {
		conditions = append(conditions, fmt.Sprintf("action = $%d", argIndex))
		args = append(args, action)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count member audit logs")
		return 0, errors.NewDatabaseError("failed to count member audit logs")
	}

	return count, nil
}
