package postgres

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"blacking-api/internal/domain/member"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type MemberRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewMemberRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.MemberRepository {
	return &MemberRepository{
		pool:   pool,
		logger: logger,
	}
}

type MemberFilter struct {
	Search        string  `json:"search"`          // firstname, lastname, phone contain
	Username      string  `json:"username"`        // username contain
	FirstName     string  `json:"first_name"`      // first name contain
	LastName      string  `json:"last_name"`       // last name contain
	BankN<PERSON>ber    string  `json:"bank_number"`     // bank number contain
	Member<PERSON>roupID *int    `json:"member_group_id"` // exact match
	PartnerID     *int    `json:"partner_id"`      // refer_user_id exact match
	ReferUserName string  `json:"refer_user_name"` // refer user name contain
	CreatedBy     *int    `json:"created_by"`      // created_by exact match
	CreatedAt     *string `json:"created_at"`      // date YYYY-MM-DD
	BirthDate     *string `json:"birth_date"`      // date YYYY-MM-DD
}

func (r *MemberRepository) Create(ctx context.Context, m *member.Member, clientIP string) error {
	// Set register IP from parameter
	m.RegisterIP = &clientIP

	// Create temporary username using phone
	tempUsername := "_temp_" + *m.Phone

	// Convert refer_user_id to string if not nil
	var referUserIDStr *string
	if m.ReferUserID != nil {
		str := strconv.Itoa(*m.ReferUserID)
		referUserIDStr = &str
	}

	// First insert with temporary username to get ID
	query := `
		INSERT INTO members (
			username, password, phone, gender, bank_code, bank_number, remark, created_by,
			login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			is_enable, register_ip, member_group_id, referral_group_id,
			status, delete_by, created_at, updated_at
		)
		VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14,
			$15, $16, $17, $18, $19, $20, $21, $22, $23
		)
		RETURNING id
	`
	var lastInsertID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		tempUsername, m.Password, m.Phone, m.Gender, m.BankCode, m.BankNumber, m.Remark, m.CreatedBy,
		m.LoginStatus, m.OperateStatus, m.RegisterStatus, m.Balance, referUserIDStr, m.ReferCode, m.RegisterReferCode,
		m.IsEnable, m.RegisterIP, m.MemberGroupID, m.ReferralGroupID,
		m.Status, m.DeleteBy, m.CreatedAt, m.UpdatedAt,
	)

	if err := row.Scan(&lastInsertID); err != nil {
		return err
	}

	m.ID = lastInsertID
	m.Username = tempUsername // Set temp username in struct
	return nil
}

func (r *MemberRepository) GetByID(ctx context.Context, id string) (*member.Member, error) {
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE id = $1 AND status = 'active'
	`

	m := &member.Member{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member not found")
		}
		r.logger.WithError(err).WithField("member_id", id).Error("failed to get member by ID")
		return nil, errors.NewDatabaseError("failed to get member")
	}

	return m, nil
}
func (r *MemberRepository) GetByPhone(ctx context.Context, phone string) (*member.Member, error) {
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE phone = $1 AND status = 'active'
	`

	m := &member.Member{}
	var referUserIDStr *string
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, phone).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &referUserIDStr, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)

	// Convert string back to int if not nil
	if referUserIDStr != nil {
		if referUserID, err := strconv.Atoi(*referUserIDStr); err == nil {
			m.ReferUserID = &referUserID
		}
	}

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member not found")
		}
		r.logger.WithError(err).WithField("phone", phone).Error("failed to get member by phone")
		return nil, errors.NewDatabaseError("failed to get member")
	}

	return m, nil
}

func (r *MemberRepository) GetByUsername(ctx context.Context, username string) (*member.Member, error) {
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE username = $1 AND status = 'active'
	`

	m := &member.Member{}
	var referUserIDStr *string
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, username).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &referUserIDStr, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)

	// Convert string back to int if not nil
	if referUserIDStr != nil {
		if referUserID, err := strconv.Atoi(*referUserIDStr); err == nil {
			m.ReferUserID = &referUserID
		}
	}

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member not found")
		}
		r.logger.WithError(err).WithField("username", username).Error("failed to get member by username")
		return nil, errors.NewDatabaseError("failed to get member")
	}

	return m, nil
}

func (r *MemberRepository) GetByGameUsername(ctx context.Context, gameUsername string) (*member.Member, error) {
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE game_username = $1 AND status = 'active'
	`

	m := &member.Member{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, gameUsername).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("member not found")
		}
		r.logger.WithError(err).WithField("game_username", gameUsername).Error("failed to get member by game username")
		return nil, errors.NewDatabaseError("failed to get member")
	}

	return m, nil
}

// GetByReferCode retrieves a member by refer code
func (r *MemberRepository) GetByReferCode(ctx context.Context, referCode string) (*member.Member, error) {
	var m member.Member
	query := `
		SELECT id, username, password, game_username, game_password, first_name, last_name,
			   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
			   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
			   last_online, is_enable, register_ip, last_login_ip, session_id,
			   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
			   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
			   status, delete_by, created_at, updated_at
		FROM members
		WHERE refer_code = $1 AND status = 'active'
	`

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, referCode).Scan(
		&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
		&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
		&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
		&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
		&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
		&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
		&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	return &m, nil
}

func (r *MemberRepository) Update(ctx context.Context, m *member.Member) error {
	// Convert refer_user_id to string if not nil
	var referUserIDStr *string
	if m.ReferUserID != nil {
		str := strconv.Itoa(*m.ReferUserID)
		referUserIDStr = &str
	}

	query := `
		UPDATE members
		SET username = $2, password = $3, game_username = $4, game_password = $5,
			first_name = $6, last_name = $7, phone = $8, tw_username = $9, line_id = $10,
			bank_code = $11, bank_number = $12, avatar = $13, login_status = $14,
			operate_status = $15, register_status = $16, balance = $17, refer_user_id = $18,
			last_online = $19, is_enable = $20, last_login_ip = $21, session_id = $22,
			twofa_secret = $23, twofa_status = $24, twofa_verify_count = $25,
			status = $26, updated_at = $27
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		m.ID, m.Username, m.Password, m.GameUsername, m.GamePassword,
		m.FirstName, m.LastName, m.Phone, m.TwUsername, m.LineID,
		m.BankCode, m.BankNumber, m.Avatar, m.LoginStatus,
		m.OperateStatus, m.RegisterStatus, m.Balance, referUserIDStr,
		m.LastOnline, m.IsEnable, m.LastLoginIP, m.SessionID,
		m.TwofaSecret, m.TwofaStatus, m.TwofaVerifyCount,
		m.Status, m.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("member_id", m.ID).Error("failed to update member")
		return errors.NewDatabaseError("failed to update member")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member not found")
	}

	return nil
}

func (r *MemberRepository) Delete(ctx context.Context, id string) error {
	query := `
		UPDATE members
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("member_id", id).Error("failed to delete member")
		return errors.NewDatabaseError("failed to delete member")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("member not found")
	}

	r.logger.WithField("member_id", id).Info("member deleted successfully")
	return nil
}

func (r *MemberRepository) List(ctx context.Context, limit, offset int, search string) ([]*member.Member, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT id, username, password, game_username, game_password, first_name, last_name,
				   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
				   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
				   last_online, is_enable, register_ip, last_login_ip, session_id,
				   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
				   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
				   status, delete_by, created_at, updated_at
			FROM members
			WHERE (username ILIKE '%' || $1 || '%' OR game_username ILIKE '%' || $1 || '%'
				   OR first_name ILIKE '%' || $1 || '%' OR last_name ILIKE '%' || $1 || '%')
				   AND status = 'active'
			ORDER BY created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{search, limit, offset}
	} else {
		query = `
			SELECT id, username, password, game_username, game_password, first_name, last_name,
				   phone, gender, tw_username, line_id, bank_code, bank_number, avatar,
				   login_status, operate_status, register_status, balance, refer_user_id, refer_code, register_refer_code,
				   last_online, is_enable, register_ip, last_login_ip, session_id,
				   twofa_secret, twofa_status, twofa_verify_count, is_partner, member_group_id,
				   referral_group_id, show_partner_info, channel_id, platform_id, partner_remark,
				   status, delete_by, created_at, updated_at
			FROM members
			WHERE status = 'active'
			ORDER BY created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list members")
		return nil, errors.NewDatabaseError("failed to list members")
	}
	defer rows.Close()

	var members []*member.Member
	for rows.Next() {
		m := &member.Member{}
		var referUserIDStr *string
		err := rows.Scan(
			&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
			&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
			&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &referUserIDStr, &m.ReferCode, &m.RegisterReferCode,
			&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
			&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
			&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
			&m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member row")
			return nil, errors.NewDatabaseError("failed to scan member")
		}

		// Convert string back to int if not nil
		if referUserIDStr != nil {
			if referUserID, err := strconv.Atoi(*referUserIDStr); err == nil {
				m.ReferUserID = &referUserID
			}
		}

		members = append(members, m)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member rows")
		return nil, errors.NewDatabaseError("failed to list members")
	}

	return members, nil
}

func (r *MemberRepository) Count(ctx context.Context, search string) (int64, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT COUNT(*)
			FROM members
			WHERE (username ILIKE '%' || $1 || '%' OR game_username ILIKE '%' || $1 || '%'
				   OR first_name ILIKE '%' || $1 || '%' OR last_name ILIKE '%' || $1 || '%')
				   AND status = 'active'
		`
		args = []interface{}{search}
	} else {
		query = `
			SELECT COUNT(*)
			FROM members
			WHERE status = 'active'
		`
		args = []interface{}{}
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count members")
		return 0, errors.NewDatabaseError("failed to count members")
	}

	return count, nil
}

// ListPartners retrieves partners (members with is_partner = true) with pagination and search by first_name
func (r *MemberRepository) ListPartners(ctx context.Context, limit, offset int, search string) ([]*member.Member, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT m.id, m.username, m.password, m.game_username, m.game_password, m.first_name, m.last_name, m.phone, m.gender,
				   m.tw_username, m.line_id, m.bank_code, m.bank_number, m.avatar, m.login_status, m.operate_status,
				   m.register_status, m.balance, m.refer_user_id, m.refer_code, m.register_refer_code, m.last_online, m.is_enable, m.register_ip, m.last_login_ip,
				   m.session_id, m.twofa_secret, m.twofa_status, m.twofa_verify_count, m.is_partner, m.member_group_id,
				   m.referral_group_id, m.show_partner_info, m.channel_id, m.platform_id, m.partner_remark, m.status, m.delete_by, m.created_at, m.updated_at,
				   COALESCE(c.name, '') as channel_name
			FROM members m
			LEFT JOIN channels c ON m.channel_id = c.id AND c.status = 'active'
			WHERE m.is_partner = true AND m.status = 'active' AND m.first_name ILIKE $1
			ORDER BY m.created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{"%" + search + "%", limit, offset}
	} else {
		query = `
			SELECT m.id, m.username, m.password, m.game_username, m.game_password, m.first_name, m.last_name, m.phone, m.gender,
				   m.tw_username, m.line_id, m.bank_code, m.bank_number, m.avatar, m.login_status, m.operate_status,
				   m.register_status, m.balance, m.refer_user_id, m.refer_code, m.register_refer_code, m.last_online, m.is_enable, m.register_ip, m.last_login_ip,
				   m.session_id, m.twofa_secret, m.twofa_status, m.twofa_verify_count, m.is_partner, m.member_group_id,
				   m.referral_group_id, m.show_partner_info, m.channel_id, m.platform_id, m.partner_remark, m.status, m.delete_by, m.created_at, m.updated_at,
				   COALESCE(c.name, '') as channel_name
			FROM members m
			LEFT JOIN channels c ON m.channel_id = c.id AND c.status = 'active'
			WHERE m.is_partner = true AND m.status = 'active'
			ORDER BY m.created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list partners")
		return nil, errors.NewDatabaseError("failed to list partners")
	}
	defer rows.Close()

	var partners []*member.Member
	for rows.Next() {
		m := &member.Member{}
		var channelName string
		err := rows.Scan(
			&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword,
			&m.FirstName, &m.LastName, &m.Phone, &m.Gender, &m.TwUsername,
			&m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar, &m.LoginStatus,
			&m.OperateStatus, &m.RegisterStatus, &m.Balance, &m.ReferUserID, &m.ReferCode, &m.RegisterReferCode,
			&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP,
			&m.SessionID, &m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount,
			&m.IsPartner, &m.MemberGroupID, &m.ReferralGroupID, &m.ShowPartnerInfo,
			&m.ChannelID, &m.PlatformID, &m.PartnerRemark, &m.Status, &m.DeleteBy, &m.CreatedAt, &m.UpdatedAt,
			&channelName, // Add channel_name from JOIN
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan partner")
			return nil, errors.NewDatabaseError("failed to scan partner")
		}

		// Set channel name if available
		if channelName != "" {
			m.ChannelName = &channelName
		}

		partners = append(partners, m)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating partner rows")
		return nil, errors.NewDatabaseError("error iterating partner rows")
	}

	return partners, nil
}

// CountPartners returns total count of partners with search filter
func (r *MemberRepository) CountPartners(ctx context.Context, search string) (int64, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT COUNT(*)
			FROM members
			WHERE is_partner = true AND status = 'active' AND first_name ILIKE $1
		`
		args = []interface{}{"%" + search + "%"}
	} else {
		query = `
			SELECT COUNT(*)
			FROM members
			WHERE is_partner = true AND status = 'active'
		`
		args = []interface{}{}
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count partners")
		return 0, errors.NewDatabaseError("failed to count partners")
	}

	return count, nil
}

func (r *MemberRepository) ListWithFilter(ctx context.Context, limit, offset int, filter *member.MemberFilter) ([]*member.Member, error) {
	baseQuery := `
		SELECT m.id, m.username, m.password, m.game_username, m.game_password, m.first_name, m.last_name,
			   m.phone, m.gender, m.tw_username, m.line_id, m.bank_code, m.bank_number, m.avatar,
			   m.login_status, m.operate_status, m.register_status, m.balance, m.refer_user_id::text, m.refer_code, m.register_refer_code,
			   m.last_online, m.is_enable, m.register_ip, m.last_login_ip, m.session_id,
			   m.twofa_secret, m.twofa_status, m.twofa_verify_count, m.is_partner, m.member_group_id,
			   m.referral_group_id, m.show_partner_info, m.channel_id, m.platform_id, m.partner_remark,
			   m.status, m.delete_by, m.created_at, m.updated_at, m.birth_date,
			   COALESCE(mg.name, '') as member_group_name,
			   COALESCE(cg.name, '') as commission_group_name,
			   COALESCE(rg.name, '') as referral_group_name
		FROM members m
		LEFT JOIN member_groups mg ON m.member_group_id = mg.id AND mg.status = 'active'
		LEFT JOIN commission_groups cg ON mg.commission_group_id = cg.id AND cg.status = 'active'
		LEFT JOIN referral_groups rg ON m.referral_group_id = rg.id AND rg.status = 'active'
		WHERE m.status = 'active'
	`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// 1. Search in firstname, lastname, phone
	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(m.first_name ILIKE $%d OR m.last_name ILIKE $%d OR m.phone ILIKE $%d)", argIndex, argIndex, argIndex))
		args = append(args, "%"+filter.Search+"%")
		argIndex++
	}

	// 2. Username contain
	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("m.username ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Username+"%")
		argIndex++
	}

	// 3. First name contain
	if filter.FirstName != "" {
		conditions = append(conditions, fmt.Sprintf("m.first_name ILIKE $%d", argIndex))
		args = append(args, "%"+filter.FirstName+"%")
		argIndex++
	}

	// 3. Last name contain
	if filter.LastName != "" {
		conditions = append(conditions, fmt.Sprintf("m.last_name ILIKE $%d", argIndex))
		args = append(args, "%"+filter.LastName+"%")
		argIndex++
	}

	// 4. Bank number contain
	if filter.BankNumber != "" {
		conditions = append(conditions, fmt.Sprintf("m.bank_number ILIKE $%d", argIndex))
		args = append(args, "%"+filter.BankNumber+"%")
		argIndex++
	}

	// 5. Member group ID exact match
	if filter.MemberGroupID != nil {
		conditions = append(conditions, fmt.Sprintf("m.member_group_id = $%d", argIndex))
		args = append(args, *filter.MemberGroupID)
		argIndex++
	}

	// 6. Partner ID (refer_user_id) exact match
	if filter.PartnerID != nil {
		conditions = append(conditions, fmt.Sprintf("m.refer_user_id::int = $%d", argIndex))
		args = append(args, *filter.PartnerID)
		argIndex++
	}

	// 7. Refer user name contain
	if filter.ReferUserName != "" {
		conditions = append(conditions, fmt.Sprintf("(refer_user.first_name ILIKE $%d OR refer_user.last_name ILIKE $%d OR refer_user.username ILIKE $%d)", argIndex, argIndex, argIndex))
		args = append(args, "%"+filter.ReferUserName+"%")
		argIndex++
	}

	// 8. Created by exact match
	if filter.CreatedBy != nil {
		conditions = append(conditions, fmt.Sprintf("m.created_by = $%d", argIndex))
		args = append(args, *filter.CreatedBy)
		argIndex++
	}

	// 9. Created at date match
	if filter.CreatedAt != nil {
		conditions = append(conditions, fmt.Sprintf("DATE(m.created_at) = $%d", argIndex))
		args = append(args, *filter.CreatedAt)
		argIndex++
	}

	// 10. Birth date match
	if filter.BirthDate != nil {
		conditions = append(conditions, fmt.Sprintf("DATE(m.birth_date) = $%d", argIndex))
		args = append(args, *filter.BirthDate)
		argIndex++
	}

	// Build final query
	query := baseQuery
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}
	query += " ORDER BY m.created_at DESC"
	query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list members with filter")
		return nil, errors.NewDatabaseError("failed to list members")
	}
	defer rows.Close()

	var members []*member.Member
	for rows.Next() {
		m := &member.Member{}
		var referUserIDStr *string
		var deleteBy *int
		var memberGroupName, commissionGroupName, referralGroupName string
		err := rows.Scan(
			&m.ID, &m.Username, &m.Password, &m.GameUsername, &m.GamePassword, &m.FirstName, &m.LastName,
			&m.Phone, &m.Gender, &m.TwUsername, &m.LineID, &m.BankCode, &m.BankNumber, &m.Avatar,
			&m.LoginStatus, &m.OperateStatus, &m.RegisterStatus, &m.Balance, &referUserIDStr, &m.ReferCode, &m.RegisterReferCode,
			&m.LastOnline, &m.IsEnable, &m.RegisterIP, &m.LastLoginIP, &m.SessionID,
			&m.TwofaSecret, &m.TwofaStatus, &m.TwofaVerifyCount, &m.IsPartner, &m.MemberGroupID,
			&m.ReferralGroupID, &m.ShowPartnerInfo, &m.ChannelID, &m.PlatformID, &m.PartnerRemark,
			&m.Status, &deleteBy, &m.CreatedAt, &m.UpdatedAt, &m.BirthDate,
			&memberGroupName, &commissionGroupName, &referralGroupName,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member row")
			return nil, errors.NewDatabaseError("failed to scan member")
		}

		// Convert string back to int if not nil
		if referUserIDStr != nil {
			if referUserID, err := strconv.Atoi(*referUserIDStr); err == nil {
				m.ReferUserID = &referUserID
			}
		}

		// Set delete_by if not nil
		if deleteBy != nil {
			m.DeleteBy = deleteBy
		}

		// Set group names
		if memberGroupName != "" {
			m.MemberGroupName = &memberGroupName
		}
		if commissionGroupName != "" {
			m.CommissionGroupName = &commissionGroupName
		}
		if referralGroupName != "" {
			m.ReferralGroupName = &referralGroupName
		}

		members = append(members, m)
	}

	return members, nil
}

func (r *MemberRepository) CountWithFilter(ctx context.Context, filter *member.MemberFilter) (int64, error) {
	baseQuery := `SELECT COUNT(*) FROM members m`

	var conditions []string
	var args []interface{}
	argIndex := 1

	// Always filter by active status
	conditions = append(conditions, "m.status = 'active'")

	// Apply filters same as ListWithFilter
	if filter.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(m.first_name ILIKE $%d OR m.last_name ILIKE $%d OR m.phone ILIKE $%d)", argIndex, argIndex, argIndex))
		args = append(args, "%"+filter.Search+"%")
		argIndex++
	}

	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("m.username ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Username+"%")
		argIndex++
	}

	if filter.FirstName != "" {
		conditions = append(conditions, fmt.Sprintf("m.first_name ILIKE $%d", argIndex))
		args = append(args, "%"+filter.FirstName+"%")
		argIndex++
	}

	if filter.LastName != "" {
		conditions = append(conditions, fmt.Sprintf("m.last_name ILIKE $%d", argIndex))
		args = append(args, "%"+filter.LastName+"%")
		argIndex++
	}

	if filter.BankNumber != "" {
		conditions = append(conditions, fmt.Sprintf("m.bank_number ILIKE $%d", argIndex))
		args = append(args, "%"+filter.BankNumber+"%")
		argIndex++
	}

	if filter.MemberGroupID != nil {
		conditions = append(conditions, fmt.Sprintf("m.member_group_id = $%d", argIndex))
		args = append(args, *filter.MemberGroupID)
		argIndex++
	}

	if filter.PartnerID != nil {
		conditions = append(conditions, fmt.Sprintf("m.refer_user_id = $%d", argIndex))
		args = append(args, *filter.PartnerID)
		argIndex++
	}

	if filter.CreatedBy != nil {
		conditions = append(conditions, fmt.Sprintf("m.created_by = $%d", argIndex))
		args = append(args, *filter.CreatedBy)
		argIndex++
	}

	if filter.CreatedAt != nil {
		conditions = append(conditions, fmt.Sprintf("DATE(m.created_at) = $%d", argIndex))
		args = append(args, *filter.CreatedAt)
		argIndex++
	}

	query := baseQuery + " WHERE " + strings.Join(conditions, " AND ")

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count members with filter")
		return 0, errors.NewDatabaseError("failed to count members with filter")
	}

	return count, nil
}
