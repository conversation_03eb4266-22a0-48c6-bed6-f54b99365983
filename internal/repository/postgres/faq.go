package postgres

import (
	"context"
	"fmt"
	"strings"

	"blacking-api/internal/domain/faq"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type FAQRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewFAQRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.FAQRepository {
	return &FAQRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *FAQRepository) Create(ctx context.Context, u *faq.FAQ) error {
	query := `
		INSERT INTO faqs (title, answer, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id
	`
	var lastInsertID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		u.Title, u.Answer, u.Status, u.CreatedAt, u.UpdatedAt,
	)
	err := row.Scan(&lastInsertID)

	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505": // unique_violation
			}
		}
		r.logger.WithError(err).Error("failed to create faq")
		return errors.NewDatabaseError("failed to create faq")
	}

	updatePositionQuery := `UPDATE faqs SET position = id WHERE id = $1`
	_, err = dbutil.ExecWithSchema(ctx, r.pool, updatePositionQuery, lastInsertID)
	if err != nil {
		r.logger.WithError(err).Error("failed to set position faq")
	}

	u.ID = lastInsertID
	u.Position = &lastInsertID

	r.logger.WithField("faq_id", u.ID).Info("faq created successfully")
	return nil
}

func (r *FAQRepository) GetByID(ctx context.Context, id string) (*faq.FAQ, error) {
	query := `
		SELECT id, position, title, answer, status, created_at, updated_at
		FROM faqs
		WHERE id = $1 AND status = 'active'
	`

	u := &faq.FAQ{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(
		&u.ID, &u.Position, &u.Title, &u.Answer, &u.Status, &u.CreatedAt, &u.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("faq not found")
		}
		r.logger.WithError(err).WithField("faq_id", id).Error("failed to get faq by ID")
		return nil, errors.NewDatabaseError("failed to get faq")
	}

	return u, nil
}

func (r *FAQRepository) Update(ctx context.Context, u *faq.FAQ) error {
	query := `
		UPDATE faqs
		SET updated_at = $2, status = $3, title = $4, answer = $5
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		u.ID, u.UpdatedAt, u.Status, u.Title, u.Answer,
	)
	if err != nil {
		r.logger.WithError(err).WithField("faq_id", u.ID).Error("failed to update faq")
		return errors.NewDatabaseError("failed to update faq")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("faq not found")
	}

	r.logger.WithField("faq_id", u.ID).Info("faq updated successfully")
	return nil
}

func (r *FAQRepository) Delete(ctx context.Context, id string) error {
	query := `UPDATE faqs SET status = 'inactive', updated_at = NOW() WHERE id = $1 AND status != 'inactive'`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("faq_id", id).Error("failed to soft delete faq")
		return errors.NewDatabaseError("failed to delete faq")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("faq not found or already deleted")
	}

	r.logger.WithField("faq_id", id).Info("faq soft deleted successfully")
	return nil
}

func (r *FAQRepository) List(ctx context.Context, limit, offset int, search string) ([]*faq.FAQ, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT id, position, title, answer, status, created_at, updated_at
			FROM faqs
			WHERE name ILIKE '%' || $1 || '%' AND status = 'active'
			ORDER BY position ASC NULLS LAST, created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{search, limit, offset}
	} else {
		query = `
			SELECT id, position, title, answer, status, created_at, updated_at
			FROM faqs
			WHERE status = 'active'
			ORDER BY position ASC NULLS LAST, created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list faq")
		return nil, errors.NewDatabaseError("failed to list faq")
	}
	defer rows.Close()

	var userRoles []*faq.FAQ
	for rows.Next() {
		u := &faq.FAQ{}
		err := rows.Scan(
			&u.ID, &u.Position, &u.Title, &u.Answer, &u.Status, &u.CreatedAt, &u.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan faq row")
			return nil, errors.NewDatabaseError("failed to scan faq")
		}
		userRoles = append(userRoles, u)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating faq rows")
		return nil, errors.NewDatabaseError("failed to list faq")
	}

	return userRoles, nil
}

func (r *FAQRepository) Count(ctx context.Context, search string) (int64, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `SELECT COUNT(*) FROM faqs WHERE name ILIKE '%' || $1 || '%' AND status = 'active'`
		args = []interface{}{search}
	} else {
		query = `SELECT COUNT(*) FROM faqs WHERE status = 'active'`
		args = []interface{}{}
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count faq")
		return 0, errors.NewDatabaseError("failed to count faq")
	}

	return count, nil
}

func (r *FAQRepository) Reorder(ctx context.Context, userRoleIDs []int) error {
	if len(userRoleIDs) == 0 {
		return errors.NewValidationError("faq_ids cannot be empty")
	}

	// สร้าง CASE statement สำหรับ update position
	var caseStatements []string
	var ids []string
	var args []interface{}
	argIndex := 1

	for position, userRoleID := range userRoleIDs {
		caseStatements = append(caseStatements, fmt.Sprintf("WHEN $%d THEN $%d", argIndex, argIndex+1))
		ids = append(ids, fmt.Sprintf("$%d", argIndex))
		args = append(args, userRoleID, position+1) // position เริ่มจาก 1
		argIndex += 2
	}

	query := fmt.Sprintf(`
		UPDATE faqs
		SET position = (CASE id %s ELSE position END), updated_at = NOW()
		WHERE id IN (%s) AND status = 'active'
	`, strings.Join(caseStatements, " "), strings.Join(ids, ","))

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).WithField("faq_ids", userRoleIDs).Error("failed to reorder faq")
		return errors.NewDatabaseError("failed to reorder faq")
	}

	r.logger.WithFields(map[string]interface{}{
		"faq_ids":       userRoleIDs,
		"rows_affected": result.RowsAffected(),
	}).Info("faq reordered successfully")

	return nil
}
