package postgres

import (
	"blacking-api/internal/domain/user_transaction_direction"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
)

type UserTransactionDirectionRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewUserTransactionDirectionRepository creates a new user transaction direction repository
func NewUserTransactionDirectionRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.UserTransactionDirectionRepository {
	return &UserTransactionDirectionRepository{
		pool:   pool,
		logger: logger,
	}
}

// List retrieves all user transaction directions
func (r *UserTransactionDirectionRepository) List(ctx context.Context) ([]*user_transaction_direction.UserTransactionDirection, error) {
	query := `
		SELECT id, name, detail, created_at
		FROM user_transaction_direction
		ORDER BY id ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to query user transaction directions")
		return nil, errors.NewDatabaseError("failed to retrieve user transaction directions")
	}
	defer rows.Close()

	var transactionDirections []*user_transaction_direction.UserTransactionDirection
	for rows.Next() {
		var transactionDirection user_transaction_direction.UserTransactionDirection
		err := rows.Scan(
			&transactionDirection.ID,
			&transactionDirection.Name,
			&transactionDirection.Detail,
			&transactionDirection.CreatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user transaction direction")
			return nil, errors.NewDatabaseError("failed to scan user transaction direction")
		}
		transactionDirections = append(transactionDirections, &transactionDirection)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user transaction direction rows")
		return nil, errors.NewDatabaseError("failed to retrieve user transaction directions")
	}

	r.logger.WithField("count", len(transactionDirections)).Info("user transaction directions retrieved successfully")
	return transactionDirections, nil
}
