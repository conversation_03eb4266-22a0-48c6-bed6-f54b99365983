package postgres

import (
	"blacking-api/internal/domain/algorithm"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
)

type Algorithm struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewAlgorithmRepository(pool *pgxpool.Pool, logger logger.Logger) *Algorithm {
	return &Algorithm{
		pool:   pool,
		logger: logger,
	}
}

func (r *Algorithm) List(ctx context.Context) ([]*algorithm.Algorithm, error) {
	var algorithmsSetting []*algorithm.Algorithm

	query := `SELECT id, name FROM algorithm ORDER BY id ASC`
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list algorithm settings")
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		alg := &algorithm.Algorithm{}
		if err := rows.Scan(&alg.ID, &alg.Name); err != nil {
			r.logger.WithError(err).Error("failed to scan algorithm setting")
			return nil, err
		}
		algorithmsSetting = append(algorithmsSetting, alg)
	}

	return algorithmsSetting, nil
}

func (r *Algorithm) FindIdExists(ctx context.Context, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM algorithm WHERE id = $1)`
	var exists bool

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check if algorithm ID exists")
		return false, err
	}

	return exists, nil
}
