package postgres

import (
	"blacking-api/internal/domain/user_audit_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5/pgxpool"
)

type UserAuditLogRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewUserAuditLogRepository creates a new user audit log repository
func NewUserAuditLogRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.UserAuditLogRepository {
	return &UserAuditLogRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new user audit log entry
func (r *UserAuditLogRepository) Create(ctx context.Context, auditLog *user_audit_log.UserAuditLog) error {
	query := `
		INSERT INTO user_audit_logs (user_id, username, action, old_values, new_values, changed_by, changed_by_name, changed_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		auditLog.UserID,
		auditLog.Username,
		auditLog.Action,
		auditLog.OldValues,
		auditLog.NewValues,
		auditLog.ChangedBy,
		auditLog.ChangedByName,
		auditLog.ChangedAt,
	)

	if err != nil {
		fmt.Println(err.Error())
		r.logger.WithError(err).WithField("audit_log_id", auditLog.ID).Error("failed to create user audit log")
		return errors.NewDatabaseError("failed to create user audit log")
	}

	r.logger.WithField("audit_log_id", auditLog.ID).Info("user audit log created successfully")
	return nil
}

// List retrieves all audit logs with pagination and optional filters
func (r *UserAuditLogRepository) List(ctx context.Context, limit, offset int, username string, userRoleID *int, action string) ([]*user_audit_log.UserAuditLog, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT ual.id, ual.user_id, ual.username, ual.action, ual.old_values, ual.new_values, ual.changed_by, ual.changed_by_name, ual.changed_at
		FROM user_audit_logs ual
		LEFT JOIN users u ON u.username = ual.username
	`

	if username != "" {
		conditions = append(conditions, fmt.Sprintf("ual.username ILIKE $%d", argIndex))
		args = append(args, "%"+username+"%")
		argIndex++
	}

	if userRoleID != nil {
		conditions = append(conditions, fmt.Sprintf("u.user_role_id = $%d", argIndex))
		args = append(args, *userRoleID)
		argIndex++
	}

	if action != "" {
		conditions = append(conditions, fmt.Sprintf("ual.action = $%d", argIndex))
		args = append(args, action)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	query += fmt.Sprintf(" ORDER BY changed_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list user audit logs")
		return nil, errors.NewDatabaseError("failed to list user audit logs")
	}
	defer rows.Close()

	var auditLogs []*user_audit_log.UserAuditLog
	for rows.Next() {
		auditLog := &user_audit_log.UserAuditLog{}
		err := rows.Scan(
			&auditLog.ID,
			&auditLog.UserID,
			&auditLog.Username,
			&auditLog.Action,
			&auditLog.OldValues,
			&auditLog.NewValues,
			&auditLog.ChangedBy,
			&auditLog.ChangedByName,
			&auditLog.ChangedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user audit log")
			return nil, errors.NewDatabaseError("failed to scan user audit log")
		}
		auditLogs = append(auditLogs, auditLog)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user audit log rows")
		return nil, errors.NewDatabaseError("error iterating user audit log rows")
	}

	return auditLogs, nil
}

// Count returns the total count of audit logs with optional filters
func (r *UserAuditLogRepository) Count(ctx context.Context, username string, userRoleID *int, action string) (int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT COUNT(*)
		FROM user_audit_logs ual
		LEFT JOIN users u ON ual.username = u.username

	`

	if username != "" {
		conditions = append(conditions, fmt.Sprintf("ual.username ILIKE $%d", argIndex))
		args = append(args, "%"+username+"%")
		argIndex++
	}

	if userRoleID != nil {
		conditions = append(conditions, fmt.Sprintf("u.user_role_id = $%d", argIndex))
		args = append(args, *userRoleID)
		argIndex++
	}

	if action != "" {
		conditions = append(conditions, fmt.Sprintf("ual.action = $%d", argIndex))
		args = append(args, action)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count user audit logs")
		return 0, errors.NewDatabaseError("failed to count user audit logs")
	}

	return count, nil
}
