package postgres

import (
	"context"
	"fmt"
	"strings"

	"blacking-api/internal/domain/banner"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type BannerRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewBannerRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.BannerRepository {
	return &BannerRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *BannerRepository) Create(ctx context.Context, b *banner.Banner) error {
	query := `
		INSERT INTO banners (name, type, link_url, image_url, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id
	`
	var lastInsertID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		b.Name, b.Type, b.LinkURL, b.ImageURL, b.Status, b.CreatedAt, b.UpdatedAt,
	)
	err := row.Scan(&lastInsertID)

	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505": // unique_violation
				return errors.NewValidationError("banner already exists")
			}
		}
		r.logger.WithError(err).Error("failed to create banner")
		return errors.NewDatabaseError("failed to create banner")
	}

	updatePositionQuery := `UPDATE banners SET position = id WHERE id = $1`
	_, err = dbutil.ExecWithSchema(ctx, r.pool, updatePositionQuery, lastInsertID)
	if err != nil {
		r.logger.WithError(err).Error("failed to set position user_role")
	}

	b.ID = lastInsertID
	b.Position = &lastInsertID

	r.logger.WithField("banner_id", lastInsertID).Info("banner created successfully")
	return nil
}

func (r *BannerRepository) GetByID(ctx context.Context, id string) (*banner.Banner, error) {
	query := `
		SELECT id, position, name, type, link_url, image_url, status, 
		       deleted_by_username, deleted_at, created_at, updated_at
		FROM banners
		WHERE id = $1 AND status = 'active'
	`

	b := &banner.Banner{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(
		&b.ID, &b.Position, &b.Name, &b.Type, &b.LinkURL, &b.ImageURL,
		&b.Status, &b.DeletedByUsername, &b.DeletedAt, &b.CreatedAt, &b.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("banner not found")
		}
		r.logger.WithError(err).WithField("banner_id", id).Error("failed to get banner by ID")
		return nil, errors.NewDatabaseError("failed to get banner")
	}

	return b, nil
}

func (r *BannerRepository) Update(ctx context.Context, b *banner.Banner) error {
	query := `
		UPDATE banners
		SET name = $2, type = $3, link_url = $4, image_url = $5, status = $6, 
		    deleted_by_username = $7, deleted_at = $8, updated_at = $9
		WHERE id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		b.ID, b.Name, b.Type, b.LinkURL, b.ImageURL, b.Status,
		b.DeletedByUsername, b.DeletedAt, b.UpdatedAt,
	)
	if err != nil {
		r.logger.WithError(err).WithField("banner_id", b.ID).Error("failed to update banner")
		return errors.NewDatabaseError("failed to update banner")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("banner not found")
	}

	r.logger.WithField("banner_id", b.ID).Info("banner updated successfully")
	return nil
}

func (r *BannerRepository) Delete(ctx context.Context, id string) error {
	query := `
		UPDATE banners
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("banner_id", id).Error("failed to delete banner")
		return errors.NewDatabaseError("failed to delete banner")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("banner not found")
	}

	r.logger.WithField("banner_id", id).Info("banner deleted successfully")
	return nil
}

func (r *BannerRepository) List(ctx context.Context, limit, offset int, search string) ([]*banner.Banner, error) {
	var args []interface{}
	var conditions []string
	argIndex := 1

	// Base query
	query := `
		SELECT id, position, name, type, link_url, image_url, status,
		       deleted_by_username, deleted_at, created_at, updated_at
		FROM banners
	`

	// Add status condition (only active banners)
	conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
	args = append(args, "active")
	argIndex++

	// Add search condition if provided
	if search != "" {
		conditions = append(conditions, fmt.Sprintf("name ILIKE $%d", argIndex))
		args = append(args, "%"+search+"%")
		argIndex++
	}

	// Add WHERE clause if there are conditions
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	// Add ORDER BY and pagination
	query += fmt.Sprintf(" ORDER BY COALESCE(position, 999999), created_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list banners")
		return nil, errors.NewDatabaseError("failed to list banners")
	}
	defer rows.Close()

	var banners []*banner.Banner
	for rows.Next() {
		b := &banner.Banner{}
		err := rows.Scan(
			&b.ID, &b.Position, &b.Name, &b.Type, &b.LinkURL, &b.ImageURL,
			&b.Status, &b.DeletedByUsername, &b.DeletedAt, &b.CreatedAt, &b.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan banner row")
			return nil, errors.NewDatabaseError("failed to scan banner")
		}
		banners = append(banners, b)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating banner rows")
		return nil, errors.NewDatabaseError("failed to iterate banners")
	}

	return banners, nil
}

func (r *BannerRepository) Count(ctx context.Context, search string) (int64, error) {
	var args []interface{}
	var conditions []string
	argIndex := 1

	query := "SELECT COUNT(*) FROM banners"

	// Add status condition (only active banners)
	conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
	args = append(args, "active")
	argIndex++

	// Add search condition if provided
	if search != "" {
		conditions = append(conditions, fmt.Sprintf("name ILIKE $%d", argIndex))
		args = append(args, "%"+search+"%")
		argIndex++
	}

	// Add WHERE clause if there are conditions
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count banners")
		return 0, errors.NewDatabaseError("failed to count banners")
	}

	return count, nil
}

func (r *BannerRepository) Reorder(ctx context.Context, bannerIDs []int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction for reorder")
		return errors.NewDatabaseError("failed to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Update positions based on array order
	for i, bannerID := range bannerIDs {
		position := i + 1
		query := `
			UPDATE banners 
			SET position = $1, updated_at = CURRENT_TIMESTAMP 
			WHERE id = $2 AND status = 'active'
		`
		result, err := tx.Exec(ctx, query, position, bannerID)
		if err != nil {
			r.logger.WithError(err).WithField("banner_id", bannerID).Error("failed to update banner position")
			return errors.NewDatabaseError("failed to update banner position")
		}

		if result.RowsAffected() == 0 {
			return errors.NewNotFoundError(fmt.Sprintf("banner with ID %d not found", bannerID))
		}
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit reorder transaction")
		return errors.NewDatabaseError("failed to commit reorder")
	}

	r.logger.WithField("banner_count", len(bannerIDs)).Info("banners reordered successfully")
	return nil
}
