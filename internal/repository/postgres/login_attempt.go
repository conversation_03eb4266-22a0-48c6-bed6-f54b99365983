package postgres

import (
	"blacking-api/internal/domain/login_attempt"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type LoginAttemptRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewLoginAttemptRepository creates a new login attempt repository
func NewLoginAttemptRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.LoginAttemptRepository {
	return &LoginAttemptRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new login attempt record
func (r *LoginAttemptRepository) Create(ctx context.Context, attempt *login_attempt.LoginAttempt) error {
	query := `
		INSERT INTO login_attempts (
			username, ip_address, success, user_agent,
			device_type, browser, browser_version, os, os_version, platform,
			is_mobile, is_tablet, is_desktop, is_admin, is_member, is_cleared, created_at
		)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
		RETURNING id
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		attempt.Username,
		attempt.IPAddress,
		attempt.Success,
		attempt.UserAgent,
		attempt.DeviceType,
		attempt.Browser,
		attempt.BrowserVersion,
		attempt.OS,
		attempt.OSVersion,
		attempt.Platform,
		attempt.IsMobile,
		attempt.IsTablet,
		attempt.IsDesktop,
		attempt.IsAdmin,
		attempt.IsMember,
		attempt.IsCleared,
		attempt.CreatedAt,
	)
	err := row.Scan(&attempt.ID)

	if err != nil {
		r.logger.WithError(err).WithField("username", attempt.Username).Error("failed to create login attempt")
		return errors.NewDatabaseError("failed to create login attempt")
	}

	return nil
}

// GetFailedAttemptsCount gets the count of failed login attempts for a user within a time window
func (r *LoginAttemptRepository) GetFailedAttemptsCount(ctx context.Context, username string, since time.Time) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM login_attempts
		WHERE username = $1 AND success = false AND created_at >= $2 AND is_cleared = false
	`

	var count int
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, username, since).Scan(&count)
	if err != nil {
		r.logger.WithError(err).WithField("username", username).Error("failed to get failed attempts count")
		return 0, errors.NewDatabaseError("failed to get failed attempts count")
	}

	return count, nil
}

// GetLastFailedAttempt gets the last failed login attempt for a user
func (r *LoginAttemptRepository) GetLastFailedAttempt(ctx context.Context, username string) (*login_attempt.LoginAttempt, error) {
	query := `
		SELECT id, username, ip_address, success, user_agent, is_admin, is_member, is_cleared, created_at
		FROM login_attempts
		WHERE username = $1 AND success = false AND is_cleared = false
		ORDER BY created_at DESC
		LIMIT 1
	`

	attempt := &login_attempt.LoginAttempt{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, username).Scan(
		&attempt.ID,
		&attempt.Username,
		&attempt.IPAddress,
		&attempt.Success,
		&attempt.UserAgent,
		&attempt.IsAdmin,
		&attempt.IsMember,
		&attempt.IsCleared,
		&attempt.CreatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("no failed login attempts found")
		}
		r.logger.WithError(err).WithField("username", username).Error("failed to get last failed attempt")
		return nil, errors.NewDatabaseError("failed to get last failed attempt")
	}

	return attempt, nil
}

// ClearFailedAttempts marks failed login attempts as cleared for a user (called after successful login)
func (r *LoginAttemptRepository) ClearFailedAttempts(ctx context.Context, username string) error {
	query := `
		UPDATE login_attempts
		SET is_cleared = true
		WHERE username = $1 AND success = false AND is_cleared = false
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, username)
	if err != nil {
		r.logger.WithError(err).WithField("username", username).Error("failed to clear failed attempts")
		return errors.NewDatabaseError("failed to clear failed attempts")
	}

	r.logger.WithField("username", username).WithField("rows_affected", result.RowsAffected()).Info("failed login attempts cleared")
	return nil
}

// GetAttemptsSummary gets login attempt summary for a user
func (r *LoginAttemptRepository) GetAttemptsSummary(ctx context.Context, username string, maxAttempts int) (*login_attempt.LoginAttemptSummary, error) {
	// Get failed attempts count in the last 24 hours
	since := time.Now().Add(-24 * time.Hour)
	failedCount, err := r.GetFailedAttemptsCount(ctx, username, since)
	if err != nil {
		return nil, err
	}

	// Get last failed attempt
	lastFailed, err := r.GetLastFailedAttempt(ctx, username)
	var lastFailedTime *time.Time
	if err == nil && lastFailed != nil {
		lastFailedTime = &lastFailed.CreatedAt
	}

	summary := &login_attempt.LoginAttemptSummary{
		Username:           username,
		FailedAttempts:     failedCount,
		LastFailedAttempt:  lastFailedTime,
		IsLocked:           maxAttempts > 0 && failedCount >= maxAttempts,
		MaxAttemptsAllowed: maxAttempts,
	}

	return summary, nil
}
