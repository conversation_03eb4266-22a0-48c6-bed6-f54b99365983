package postgres

import (
	"blacking-api/internal/domain/payment_method"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type PaymentMethod struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewPaymentMethodRepository(pool *pgxpool.Pool, logger logger.Logger) *PaymentMethod {
	return &PaymentMethod{
		pool:   pool,
		logger: logger,
	}
}

func (r *PaymentMethod) Create(ctx context.Context, req *payment_method.PaymentMethodRequest) error {
	query := `INSERT INTO payment_method (name, minimum_deposit, maximum_deposit, fee, is_fee, active, is_lobby)
			VALUES ($1, $2, $3, $4, $5, $6, $7)`
	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Name,
		req.MiniminDeposit,
		req.MaximinDeposit,
		req.<PERSON>,
		req.<PERSON>,
		req.Active,
		req.Is<PERSON>,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to create payment method")
		return err
	}

	return nil
}

func (r *PaymentMethod) List(ctx context.Context) ([]*payment_method.PaymentMethod, error) {
	var paymentMethods []*payment_method.PaymentMethod

	query := `
		SELECT
		    pm.id,
		    pm.name,
		    pm.minimum_deposit,
		    pm.maximum_deposit,
		    pm.fee,
		    pm.is_fee,
		    pm.active,
		    pm.is_lobby,
		    pm.inactive,
		    pm.created_at,
		    pm.updated_at
		FROM payment_method pm
		WHERE pm.inactive = false
		ORDER BY id ASC
	`
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list payment methods")
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		pm := &payment_method.PaymentMethod{}
		if err := rows.Scan(
			&pm.ID,
			&pm.Name,
			&pm.MiniminDeposit,
			&pm.MaximinDeposit,
			&pm.Fee,
			&pm.IsFee,
			&pm.Active,
			&pm.IsLobby,
			&pm.Inactive,
			&pm.CreatedAt,
			&pm.UpdatedAt,
		); err != nil {
			r.logger.WithError(err).Error("failed to scan payment method")
			return nil, err
		}
		paymentMethods = append(paymentMethods, pm)
	}

	return paymentMethods, nil
}

func (r *PaymentMethod) FindIdExists(ctx context.Context, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM payment_method WHERE id = $1 AND inactive = false)`
	var exists bool

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check if payment method ID exists")
		return false, err
	}

	return exists, nil
}

func (r *PaymentMethod) FindByID(ctx context.Context, id int64) (*payment_method.PaymentMethod, error) {
	pm := &payment_method.PaymentMethod{}
	query := `
		SELECT
		    pm.id,
		    pm.name,
		    pm.minimum_deposit,
		    pm.maximum_deposit,
		    pm.fee,
		    pm.is_fee,
		    pm.active,
		    pm.is_lobby,
		    pm.inactive,
		    pm.created_at,
		    pm.updated_at
		FROM payment_method pm
		WHERE pm.id = $1 AND pm.inactive = false
	`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	if err := row.Scan(
		&pm.ID,
		&pm.Name,
		&pm.MiniminDeposit,
		&pm.MaximinDeposit,
		&pm.Fee,
		&pm.IsFee,
		&pm.Active,
		&pm.IsLobby,
		&pm.Inactive,
		&pm.CreatedAt,
		&pm.UpdatedAt,
	); err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("payment method not found")
		}
		r.logger.WithError(err).Error("failed to find payment method by ID")
		return nil, errors.NewDatabaseError("failed to find payment method by ID")
	}

	return pm, nil
}

func (r *PaymentMethod) FindByNameDuplicate(ctx context.Context, name string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM payment_method WHERE name = $1)`
	var exists bool

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, name).Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check duplicate payment method name")
		return false, errors.NewDatabaseError("failed to check duplicate payment method name")
	}

	return exists, nil
}

func (r *PaymentMethod) FindByNameDuplicateAndIdNot(ctx context.Context, name string, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM payment_method WHERE name = $1 AND id != $2)`
	var exists bool

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, name, id).Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check duplicate payment method name excluding ID")
		return false, errors.NewDatabaseError("failed to check duplicate payment method name excluding ID")
	}

	return exists, nil
}

func (r *PaymentMethod) Update(ctx context.Context, id int64, req *payment_method.PaymentMethodRequest) error {
	query := `
		UPDATE payment_method
		SET name = $1, minimum_deposit = $2, maximum_deposit = $3, fee = $4, is_fee = $5, active = $6, is_lobby = $7
		WHERE id = $8
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Name,
		req.MiniminDeposit,
		req.MaximinDeposit,
		req.Fee,
		req.IsFee,
		req.Active,
		req.IsLobby,
		id,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to update payment method")
		return errors.NewDatabaseError("failed to update payment method")
	}

	if result.RowsAffected() == 0 {
		r.logger.Error("no rows affected when updating payment method")
		return errors.NewNotFoundError("payment method not found or no changes made")
	}

	return nil
}

func (r *PaymentMethod) UpdateStatus(ctx context.Context, id int64, name string, status bool) error {
	query := fmt.Sprintf(`
        UPDATE payment_method 
        SET %s = $1
        WHERE id = $2 AND inactive = false
    `, name)

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, status, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to update payment method status")
		return errors.NewDatabaseError("failed to update payment method status")
	}

	if result.RowsAffected() == 0 {
		r.logger.Error("no rows affected when updating payment method status")
		return errors.NewNotFoundError("payment method not found or no changes made")
	}

	return nil
}

func (r *PaymentMethod) Delete(ctx context.Context, id int64) error {

	query := `
		UPDATE payment_method
		SET inactive = true
		WHERE id = $1 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete payment method")
		return errors.NewDatabaseError("failed to delete payment method")
	}

	if result.RowsAffected() == 0 {
		r.logger.Error("no rows affected when deleting payment method")
		return errors.NewNotFoundError("payment method not found or already deleted")
	}

	return nil
}
