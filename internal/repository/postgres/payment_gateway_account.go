package postgres

import (
	"blacking-api/internal/domain/payment_gateway_account"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type PaymentGatewayAccount struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewPaymentGatewayAccountRepository(pool *pgxpool.Pool, logger logger.Logger) *PaymentGatewayAccount {
	return &PaymentGatewayAccount{
		pool:   pool,
		logger: logger,
	}
}

func (r *PaymentGatewayAccount) Create(ctx context.Context, req *payment_gateway_account.PaymentGatewayAccountRequest) error {
	query := `INSERT INTO payment_gateway_account (account_name, code, provider, merchant_code, secret_key, secret_key_two, first_username, second_username, first_password, second_password, minimum_withdraw, maximum_withdraw, withdraw_splitting, maximum_withdraw_per_transaction, maximum_split_withdraw_per_transaction, is_deposit, is_withdraw, is_transfer) 
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
	`
	_, err := dbutil.ExecWithSchema(
		ctx, r.pool, query,
		req.AccountName,
		req.Code,
		req.Provider,
		req.MerchantCode,
		req.SecretKey,
		req.SecretKeyTwo,
		req.FirstUsername,
		req.SecondUsername,
		req.FirstPassword,
		req.SecondPassword,
		req.MinimumWithdraw,
		req.MaximumWithdraw,
		req.WithdrawSplit,
		req.MaximumWithdrawPerTransaction,
		req.MaximumSplitWithdrawPerTransaction,
		req.IsDeposit,
		req.IsWithdraw,
		req.IsTransfer,
	)
	if err != nil {
		r.logger.Error("Failed to create payment gateway account", "error", err)
		return err
	}

	return nil
}

func (r *PaymentGatewayAccount) FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*payment_gateway_account.PaymentGatewayAccount, int64, error) {
	var paymentGatewayAccounts []*payment_gateway_account.PaymentGatewayAccount
	var total int64
	var err error

	argLast := len(args)
	cteQuery := fmt.Sprintf(`
		WITH counted_data AS (
			SELECT 
				pma.id, 
				pma.account_name, 
				pma.code, 
				pma.provider, 
				pma.merchant_code, 
				pma.secret_key,
				pma.secret_key_two,
				pma.first_username, 
				pma.second_username, 
				pma.first_password, 
				pma.second_password, 
				pma.minimum_withdraw, 
				pma.maximum_withdraw,
				pma.withdraw_splitting,
				pma.maximum_withdraw_per_transaction,
				pma.maximum_split_withdraw_per_transaction,
				pma.is_deposit,
				pma.is_withdraw,
				pma.is_transfer,
				pma.active,
				pma.inactive,
				pma.created_at,
 				pma.updated_at,
				COUNT(*) OVER() as total_count
			FROM payment_gateway_account pma
			%s
			WHERE inactive = false
			LIMIT $%v OFFSET $%v
		)
		SELECT 
			id,
			account_name, 
			code, 
			provider, 
			merchant_code, 
			secret_key,
			secret_key_two,
			first_username, 
			second_username, 
			first_password, 
			second_password, 
			minimum_withdraw, 
			maximum_withdraw,
			withdraw_splitting,
			maximum_withdraw_per_transaction,
			maximum_split_withdraw_per_transaction,
			is_deposit,
			is_withdraw,
			is_transfer,
			active,
			inactive,
			created_at,
			updated_at,
			total_count
		FROM counted_data
	`, whereClause, argLast+1, argLast+2)
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, cteQuery, append(args, limit, offset)...)
	if err != nil {
		r.logger.WithError(err).Error("failed to find all payment gateway accounts")
		return nil, 0, err
	}
	defer rows.Close()

	for rows.Next() {
		var paymentGatewayAccount payment_gateway_account.PaymentGatewayAccount
		if err := rows.Scan(
			&paymentGatewayAccount.ID,
			&paymentGatewayAccount.AccountName,
			&paymentGatewayAccount.Code,
			&paymentGatewayAccount.Provider,
			&paymentGatewayAccount.MerchantCode,
			&paymentGatewayAccount.SecretKey,
			&paymentGatewayAccount.SecretKeyTwo,
			&paymentGatewayAccount.FirstUsername,
			&paymentGatewayAccount.SecondUsername,
			&paymentGatewayAccount.FirstPassword,
			&paymentGatewayAccount.SecondPassword,
			&paymentGatewayAccount.MinimumWithdraw,
			&paymentGatewayAccount.MaximumWithdraw,
			&paymentGatewayAccount.WithdrawSplit,
			&paymentGatewayAccount.MaximumWithdrawPerTransaction,
			&paymentGatewayAccount.MaximumSplitWithdrawPerTransaction,
			&paymentGatewayAccount.IsDeposit,
			&paymentGatewayAccount.IsWithdraw,
			&paymentGatewayAccount.IsTransfer,
			&paymentGatewayAccount.Active,
			&paymentGatewayAccount.Inactive,
			&paymentGatewayAccount.CreatedAt,
			&paymentGatewayAccount.UpdatedAt,
			&total,
		); err != nil {
			r.logger.WithError(err).Error("failed to scan payment gateway account")
			return nil, 0, err
		}
		paymentGatewayAccounts = append(paymentGatewayAccounts, &paymentGatewayAccount)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating payment gateway account rows")
		return nil, 0, err
	}

	return paymentGatewayAccounts, total, nil
}

func (r *PaymentGatewayAccount) FindByID(ctx context.Context, id int64) (*payment_gateway_account.PaymentGatewayAccount, error) {
	paymentGatewayAccount := payment_gateway_account.PaymentGatewayAccount{}
	query := `
		SELECT 
			pma.id, 
			pma.account_name, 
			pma.code, 
			pma.provider, 
			pma.merchant_code, 
			pma.secret_key,
			pma.secret_key_two,
			pma.first_username, 
			pma.second_username, 
			pma.first_password, 
			pma.second_password, 
			pma.minimum_withdraw, 
			pma.maximum_withdraw,
			pma.withdraw_splitting,
			pma.maximum_withdraw_per_transaction,
			pma.maximum_split_withdraw_per_transaction,
			pma.is_deposit,
			pma.is_withdraw,
			pma.is_transfer,
			pma.active,
			pma.inactive,
			pma.created_at,
			pma.updated_at
		FROM payment_gateway_account pma
		WHERE pma.id = $1 AND pma.inactive = false
	`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	if err := row.Scan(
		&paymentGatewayAccount.ID,
		&paymentGatewayAccount.AccountName,
		&paymentGatewayAccount.Code,
		&paymentGatewayAccount.Provider,
		&paymentGatewayAccount.MerchantCode,
		&paymentGatewayAccount.SecretKey,
		&paymentGatewayAccount.SecretKeyTwo,
		&paymentGatewayAccount.FirstUsername,
		&paymentGatewayAccount.SecondUsername,
		&paymentGatewayAccount.FirstPassword,
		&paymentGatewayAccount.SecondPassword,
		&paymentGatewayAccount.MinimumWithdraw,
		&paymentGatewayAccount.MaximumWithdraw,
		&paymentGatewayAccount.WithdrawSplit,
		&paymentGatewayAccount.MaximumWithdrawPerTransaction,
		&paymentGatewayAccount.MaximumSplitWithdrawPerTransaction,
		&paymentGatewayAccount.IsDeposit,
		&paymentGatewayAccount.IsWithdraw,
		&paymentGatewayAccount.IsTransfer,
		&paymentGatewayAccount.Active,
		&paymentGatewayAccount.Inactive,
		&paymentGatewayAccount.CreatedAt,
		&paymentGatewayAccount.UpdatedAt,
	); err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("payment gateway account not found")
		}
		log.Printf("error iterating payment gateway account rows: %v", err)
		r.logger.WithError(err).Error("failed to find payment gateway account by ID")
		return nil, errors.NewDatabaseError("failed to find payment gateway account by ID")
	}

	return &paymentGatewayAccount, nil
}

func (r *PaymentGatewayAccount) FindByAccountNameDuplicate(ctx context.Context, accountName string) (bool, error) {
	query := `SELECT COUNT(*) FROM payment_gateway_account WHERE account_name = $1 AND inactive = false`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, accountName)

	var count int
	if err := row.Scan(&count); err != nil {
		r.logger.WithError(err).Error("failed to check duplicate account name")
		return false, err
	}

	return count > 0, nil
}

func (r *PaymentGatewayAccount) FindByAccountNameDuplicateAndIdNot(ctx context.Context, accountName string, id int64) (bool, error) {
	query := `SELECT COUNT(*) FROM payment_gateway_account WHERE account_name = $1 AND id != $2 AND inactive = false`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, accountName, id)

	var count int
	if err := row.Scan(&count); err != nil {
		r.logger.WithError(err).Error("failed to check duplicate account name excluding specific ID")
		return false, err
	}

	return count > 0, nil
}

func (r *PaymentGatewayAccount) Update(ctx context.Context, id int64, req *payment_gateway_account.PaymentGatewayAccountRequest) error {
	query := `UPDATE payment_gateway_account 
			  SET account_name = $1, code = $2, provider = $3, merchant_code = $4, secret_key = $5,
			      secret_key_two = $6, first_username = $7, second_username = $8, first_password = $9,
			      second_password = $10, minimum_withdraw = $11, maximum_withdraw = $12, withdraw_splitting = $13,
			      maximum_withdraw_per_transaction = $14, maximum_split_withdraw_per_transaction = $15, is_deposit = $16,
			      is_withdraw = $17, is_transfer = $18
			  WHERE id = $19 AND inactive = false`

	result, err := dbutil.ExecWithSchema(
		ctx, r.pool, query,
		req.AccountName,
		req.Code,
		req.Provider,
		req.MerchantCode,
		req.SecretKey,
		req.SecretKeyTwo,
		req.FirstUsername,
		req.SecondUsername,
		req.FirstPassword,
		req.SecondPassword,
		req.MinimumWithdraw,
		req.MaximumWithdraw,
		req.WithdrawSplit,
		req.MaximumWithdrawPerTransaction,
		req.MaximumSplitWithdrawPerTransaction,
		req.IsDeposit,
		req.IsWithdraw,
		req.IsTransfer,
		id,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to update payment gateway account")
		return errors.NewDatabaseError("failed to update payment gateway account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating payment gateway account")
		return errors.NewNotFoundError("payment gateway account not found")
	}

	return nil
}

func (r *PaymentGatewayAccount) UpdateStatus(ctx context.Context, id int64, name string, status bool) error {
	query := fmt.Sprintf(`
        UPDATE payment_gateway_account 
        SET %s = $1
        WHERE id = $2 AND inactive = false
    `, name)

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, status, id)
	if err != nil {
		r.logger.Error("Failed to update payment gateway account status", "error", err, "column", name)
		return err
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating payment gateway account status")
		return errors.NewValidationError("payment gateway account not found")
	}

	return nil
}

func (r *PaymentGatewayAccount) Delete(ctx context.Context, id int64) error {
	query := `
		UPDATE payment_gateway_account
		SET inactive = true
		WHERE id = $1 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete payment gateway account")
		return errors.NewDatabaseError("failed to delete payment gateway account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when deleting payment gateway account")
		return errors.NewNotFoundError("payment gateway account not found")
	}

	return nil
}
