package postgres

import (
	"blacking-api/internal/domain/report"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5/pgxpool"
)

type LoginReportRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewLoginReportRepository creates a new login report repository
func NewLoginReportRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.LoginReportRepository {
	return &LoginReportRepository{
		pool:   pool,
		logger: logger,
	}
}

// GetMemberLoginReport retrieves member login report data
func (r *LoginReportRepository) GetMemberLoginReport(ctx context.Context, filter report.LoginReportFilter) ([]*report.MemberLoginReport, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT
			m.id as user_id,
			m.username,
			COALESCE(m.first_name, '') as first_name,
			COALESCE(m.last_name, '') as last_name,
			m.phone,
			la.created_at as login_date,
			COALESCE(la.ip_address, '') as ip_address,
			COALESCE(la.user_agent, '') as user_agent,
			la.device_type,
			la.browser,
			la.browser_version,
			la.os,
			la.os_version,
			la.platform,
			COALESCE(la.is_mobile, false) as is_mobile,
			COALESCE(la.is_tablet, false) as is_tablet,
			COALESCE(la.is_desktop, false) as is_desktop,
			la.success
		FROM login_attempts la
		JOIN members m ON la.username = m.username
		WHERE la.is_member = true
	`

	// Add date range filter (always applied with defaults)
	if filter.StartDate != nil {
		conditions = append(conditions, fmt.Sprintf("la.created_at >= $%d", argIndex))
		args = append(args, *filter.StartDate)
		argIndex++
	}
	if filter.EndDate != nil {
		conditions = append(conditions, fmt.Sprintf("la.created_at <= $%d", argIndex))
		args = append(args, *filter.EndDate)
		argIndex++
	}

	// Add other filters
	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("m.username ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Username+"%")
		argIndex++
	}
	if filter.Phone != "" {
		conditions = append(conditions, fmt.Sprintf("m.phone ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Phone+"%")
		argIndex++
	}
	if filter.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("m.id = $%d", argIndex))
		args = append(args, filter.UserID)
		argIndex++
	}
	if filter.IPAddress != "" {
		conditions = append(conditions, fmt.Sprintf("la.ip_address ILIKE $%d", argIndex))
		args = append(args, "%"+filter.IPAddress+"%")
		argIndex++
	}
	if filter.UserAgent != "" {
		conditions = append(conditions, fmt.Sprintf("la.user_agent ILIKE $%d", argIndex))
		args = append(args, "%"+filter.UserAgent+"%")
		argIndex++
	}
	if filter.OnlySuccess != nil {
		conditions = append(conditions, fmt.Sprintf("la.success = $%d", argIndex))
		args = append(args, *filter.OnlySuccess)
		argIndex++
	}

	// Handle duplicate IP filter
	if filter.DuplicateIP {
		duplicateIPSubquery := `
			la.ip_address IN (
				SELECT ip_address 
				FROM login_attempts 
				WHERE is_member = true 
				AND created_at >= $1 AND created_at <= $2
				GROUP BY ip_address 
				HAVING COUNT(DISTINCT username) > 1
			)
		`
		conditions = append(conditions, duplicateIPSubquery)
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}

	query += fmt.Sprintf(" ORDER BY la.created_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, filter.Limit, filter.Offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get member login report")
		return nil, errors.NewDatabaseError("failed to get member login report")
	}
	defer rows.Close()

	var reports []*report.MemberLoginReport
	for rows.Next() {
		rep := &report.MemberLoginReport{}
		err := rows.Scan(
			&rep.UserID,
			&rep.Username,
			&rep.FirstName,
			&rep.LastName,
			&rep.Phone,
			&rep.LoginDate,
			&rep.IPAddress,
			&rep.UserAgent,
			&rep.DeviceType,
			&rep.Browser,
			&rep.BrowserVersion,
			&rep.OS,
			&rep.OSVersion,
			&rep.Platform,
			&rep.IsMobile,
			&rep.IsTablet,
			&rep.IsDesktop,
			&rep.Success,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan member login report")
			return nil, errors.NewDatabaseError("failed to scan member login report")
		}

		reports = append(reports, rep)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member login report rows")
		return nil, errors.NewDatabaseError("error iterating member login report rows")
	}

	return reports, nil
}

// CountMemberLoginReport returns the total count of member login records
func (r *LoginReportRepository) CountMemberLoginReport(ctx context.Context, filter report.LoginReportFilter) (int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT COUNT(*)
		FROM login_attempts la
		JOIN members m ON la.username = m.username
		WHERE la.is_member = true
	`

	// Add date range filter
	if filter.StartDate != nil {
		conditions = append(conditions, fmt.Sprintf("la.created_at >= $%d", argIndex))
		args = append(args, *filter.StartDate)
		argIndex++
	}
	if filter.EndDate != nil {
		conditions = append(conditions, fmt.Sprintf("la.created_at <= $%d", argIndex))
		args = append(args, *filter.EndDate)
		argIndex++
	}

	// Add other filters (same as GetMemberLoginReport)
	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("m.username ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Username+"%")
		argIndex++
	}
	if filter.Phone != "" {
		conditions = append(conditions, fmt.Sprintf("m.phone ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Phone+"%")
		argIndex++
	}
	if filter.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("m.id = $%d", argIndex))
		args = append(args, filter.UserID)
		argIndex++
	}
	if filter.IPAddress != "" {
		conditions = append(conditions, fmt.Sprintf("la.ip_address ILIKE $%d", argIndex))
		args = append(args, "%"+filter.IPAddress+"%")
		argIndex++
	}
	if filter.UserAgent != "" {
		conditions = append(conditions, fmt.Sprintf("la.user_agent ILIKE $%d", argIndex))
		args = append(args, "%"+filter.UserAgent+"%")
		argIndex++
	}
	if filter.OnlySuccess != nil {
		conditions = append(conditions, fmt.Sprintf("la.success = $%d", argIndex))
		args = append(args, *filter.OnlySuccess)
		argIndex++
	}

	if filter.DuplicateIP {
		duplicateIPSubquery := `
			la.ip_address IN (
				SELECT ip_address 
				FROM login_attempts 
				WHERE is_member = true 
				AND created_at >= $1 AND created_at <= $2
				GROUP BY ip_address 
				HAVING COUNT(DISTINCT username) > 1
			)
		`
		conditions = append(conditions, duplicateIPSubquery)
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count member login report")
		return 0, errors.NewDatabaseError("failed to count member login report")
	}

	return count, nil
}

// GetAdminLoginReport retrieves admin/user login report data
func (r *LoginReportRepository) GetAdminLoginReport(ctx context.Context, filter report.LoginReportFilter) ([]*report.AdminLoginReport, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT
			u.id as user_id,
			u.username,
			COALESCE(u.first_name, '') as first_name,
			COALESCE(u.last_name, '') as last_name,
			la.created_at as login_date,
			COALESCE(la.ip_address, '') as ip_address,
			COALESCE(la.user_agent, '') as user_agent,
			la.device_type,
			la.browser,
			la.browser_version,
			la.os,
			la.os_version,
			la.platform,
			COALESCE(la.is_mobile, false) as is_mobile,
			COALESCE(la.is_tablet, false) as is_tablet,
			COALESCE(la.is_desktop, false) as is_desktop,
			la.success
		FROM login_attempts la
		JOIN users u ON la.username = u.username
		WHERE la.is_admin = true
	`

	// Add date range filter
	if filter.StartDate != nil {
		conditions = append(conditions, fmt.Sprintf("la.created_at >= $%d", argIndex))
		args = append(args, *filter.StartDate)
		argIndex++
	}
	if filter.EndDate != nil {
		conditions = append(conditions, fmt.Sprintf("la.created_at <= $%d", argIndex))
		args = append(args, *filter.EndDate)
		argIndex++
	}

	// Add other filters
	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("u.username ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Username+"%")
		argIndex++
	}
	if filter.IPAddress != "" {
		conditions = append(conditions, fmt.Sprintf("la.ip_address ILIKE $%d", argIndex))
		args = append(args, "%"+filter.IPAddress+"%")
		argIndex++
	}
	if filter.UserAgent != "" {
		conditions = append(conditions, fmt.Sprintf("la.user_agent ILIKE $%d", argIndex))
		args = append(args, "%"+filter.UserAgent+"%")
		argIndex++
	}
	if filter.OnlySuccess != nil {
		conditions = append(conditions, fmt.Sprintf("la.success = $%d", argIndex))
		args = append(args, *filter.OnlySuccess)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}

	query += fmt.Sprintf(" ORDER BY la.created_at DESC LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, filter.Limit, filter.Offset)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get admin login report")
		return nil, errors.NewDatabaseError("failed to get admin login report")
	}
	defer rows.Close()

	var reports []*report.AdminLoginReport
	for rows.Next() {
		rep := &report.AdminLoginReport{}
		err := rows.Scan(
			&rep.UserID,
			&rep.Username,
			&rep.FirstName,
			&rep.LastName,
			&rep.LoginDate,
			&rep.IPAddress,
			&rep.UserAgent,
			&rep.DeviceType,
			&rep.Browser,
			&rep.BrowserVersion,
			&rep.OS,
			&rep.OSVersion,
			&rep.Platform,
			&rep.IsMobile,
			&rep.IsTablet,
			&rep.IsDesktop,
			&rep.Success,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan admin login report")
			return nil, errors.NewDatabaseError("failed to scan admin login report")
		}

		reports = append(reports, rep)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating admin login report rows")
		return nil, errors.NewDatabaseError("error iterating admin login report rows")
	}

	return reports, nil
}

// CountAdminLoginReport returns the total count of admin login records
func (r *LoginReportRepository) CountAdminLoginReport(ctx context.Context, filter report.LoginReportFilter) (int64, error) {
	var conditions []string
	var args []interface{}
	argIndex := 1

	baseQuery := `
		SELECT COUNT(*)
		FROM login_attempts la
		JOIN users u ON la.username = u.username
		WHERE la.is_admin = true
	`

	// Add date range filter
	if filter.StartDate != nil {
		conditions = append(conditions, fmt.Sprintf("la.created_at >= $%d", argIndex))
		args = append(args, *filter.StartDate)
		argIndex++
	}
	if filter.EndDate != nil {
		conditions = append(conditions, fmt.Sprintf("la.created_at <= $%d", argIndex))
		args = append(args, *filter.EndDate)
		argIndex++
	}

	// Add other filters
	if filter.Username != "" {
		conditions = append(conditions, fmt.Sprintf("u.username ILIKE $%d", argIndex))
		args = append(args, "%"+filter.Username+"%")
		argIndex++
	}
	if filter.IPAddress != "" {
		conditions = append(conditions, fmt.Sprintf("la.ip_address ILIKE $%d", argIndex))
		args = append(args, "%"+filter.IPAddress+"%")
		argIndex++
	}
	if filter.UserAgent != "" {
		conditions = append(conditions, fmt.Sprintf("la.user_agent ILIKE $%d", argIndex))
		args = append(args, "%"+filter.UserAgent+"%")
		argIndex++
	}
	if filter.OnlySuccess != nil {
		conditions = append(conditions, fmt.Sprintf("la.success = $%d", argIndex))
		args = append(args, *filter.OnlySuccess)
		argIndex++
	}

	query := baseQuery
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count admin login report")
		return 0, errors.NewDatabaseError("failed to count admin login report")
	}

	return count, nil
}

// GetDuplicateIPReport retrieves IPs with multiple login attempts
func (r *LoginReportRepository) GetDuplicateIPReport(ctx context.Context, filter report.LoginReportFilter) ([]*report.DuplicateIPReport, error) {
	var args []interface{}
	argIndex := 1

	// Base query for duplicate IPs
	query := `
		SELECT 
			la.ip_address,
			COUNT(DISTINCT la.username) as user_count,
			COUNT(*) as login_count,
			MAX(la.created_at) as last_login,
			ARRAY_AGG(DISTINCT la.username) as usernames
		FROM login_attempts la
		WHERE la.is_member = true
	`

	// Add date range filter
	if filter.StartDate != nil {
		query += fmt.Sprintf(" AND la.created_at >= $%d", argIndex)
		args = append(args, *filter.StartDate)
		argIndex++
	}
	if filter.EndDate != nil {
		query += fmt.Sprintf(" AND la.created_at <= $%d", argIndex)
		args = append(args, *filter.EndDate)
		argIndex++
	}

	query += `
		GROUP BY la.ip_address
		HAVING COUNT(DISTINCT la.username) > 1
		ORDER BY user_count DESC, login_count DESC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get duplicate IP report")
		return nil, errors.NewDatabaseError("failed to get duplicate IP report")
	}
	defer rows.Close()

	var reports []*report.DuplicateIPReport
	for rows.Next() {
		rep := &report.DuplicateIPReport{}
		var usernames interface{}
		err := rows.Scan(
			&rep.IPAddress,
			&rep.UserCount,
			&rep.LoginCount,
			&rep.LastLogin,
			&usernames,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan duplicate IP report")
			return nil, errors.NewDatabaseError("failed to scan duplicate IP report")
		}
		// Convert interface{} to []string
		if usernameSlice, ok := usernames.([]interface{}); ok {
			rep.Usernames = make([]string, len(usernameSlice))
			for i, v := range usernameSlice {
				if str, ok := v.(string); ok {
					rep.Usernames[i] = str
				}
			}
		}
		reports = append(reports, rep)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating duplicate IP report rows")
		return nil, errors.NewDatabaseError("error iterating duplicate IP report rows")
	}

	return reports, nil
}

// GetDuplicateIPLoginDetails retrieves detailed login attempts for a specific duplicate IP
func (r *LoginReportRepository) GetDuplicateIPLoginDetails(ctx context.Context, ipAddress string, filter report.LoginReportFilter) ([]*report.DuplicateIPLoginDetail, error) {
	var args []interface{}
	argIndex := 1

	query := `
		SELECT
			m.id as user_id,
			m.username,
			m.phone,
			la.created_at as login_date,
			la.user_agent,
			la.success
		FROM login_attempts la
		JOIN members m ON la.username = m.username
		WHERE la.is_member = true
		AND la.ip_address ILIKE $1
	`
	args = append(args, "%"+ipAddress+"%")
	argIndex++

	// Add date range filter
	if filter.StartDate != nil {
		query += fmt.Sprintf(" AND la.created_at >= $%d", argIndex)
		args = append(args, *filter.StartDate)
		argIndex++
	}
	if filter.EndDate != nil {
		query += fmt.Sprintf(" AND la.created_at <= $%d", argIndex)
		args = append(args, *filter.EndDate)
		argIndex++
	}

	query += " ORDER BY la.created_at DESC"

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to get duplicate IP login details")
		return nil, errors.NewDatabaseError("failed to get duplicate IP login details")
	}
	defer rows.Close()

	var details []*report.DuplicateIPLoginDetail
	for rows.Next() {
		detail := &report.DuplicateIPLoginDetail{}
		err := rows.Scan(
			&detail.UserID,
			&detail.Username,
			&detail.Phone,
			&detail.LoginDate,
			&detail.UserAgent,
			&detail.Success,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan duplicate IP login detail")
			return nil, errors.NewDatabaseError("failed to scan duplicate IP login detail")
		}
		details = append(details, detail)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating duplicate IP login detail rows")
		return nil, errors.NewDatabaseError("error iterating duplicate IP login detail rows")
	}

	return details, nil
}

// GetDistinctUserAgents retrieves all distinct user agents from login attempts
func (r *LoginReportRepository) GetDistinctUserAgents(ctx context.Context) ([]string, error) {
	query := `
		SELECT DISTINCT user_agent
		FROM login_attempts
		WHERE user_agent IS NOT NULL
		AND user_agent != ''
		AND created_at >= NOW() - INTERVAL '30 days'
		ORDER BY user_agent
		LIMIT 100
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get distinct user agents")
		return nil, errors.NewDatabaseError("failed to get distinct user agents")
	}
	defer rows.Close()

	var userAgents []string
	for rows.Next() {
		var userAgent string
		if err := rows.Scan(&userAgent); err != nil {
			r.logger.WithError(err).Error("failed to scan user agent")
			return nil, errors.NewDatabaseError("failed to scan user agent")
		}
		userAgents = append(userAgents, userAgent)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user agent rows")
		return nil, errors.NewDatabaseError("error iterating user agent rows")
	}

	return userAgents, nil
}

// GetDistinctIPAddresses retrieves all distinct IP addresses from login attempts
func (r *LoginReportRepository) GetDistinctIPAddresses(ctx context.Context) ([]string, error) {
	query := `
		SELECT DISTINCT ip_address
		FROM login_attempts
		WHERE ip_address IS NOT NULL
		AND ip_address != ''
		AND created_at >= NOW() - INTERVAL '30 days'
		ORDER BY ip_address
		LIMIT 200
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get distinct IP addresses")
		return nil, errors.NewDatabaseError("failed to get distinct IP addresses")
	}
	defer rows.Close()

	var ipAddresses []string
	for rows.Next() {
		var ipAddr string
		if err := rows.Scan(&ipAddr); err != nil {
			r.logger.WithError(err).Error("failed to scan IP address")
			return nil, errors.NewDatabaseError("failed to scan IP address")
		}
		ipAddresses = append(ipAddresses, ipAddr)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating IP address rows")
		return nil, errors.NewDatabaseError("error iterating IP address rows")
	}

	return ipAddresses, nil
}

// GetDistinctMemberUsernames retrieves all distinct member usernames from login attempts
func (r *LoginReportRepository) GetDistinctMemberUsernames(ctx context.Context) ([]string, error) {
	query := `
		SELECT DISTINCT la.username
		FROM login_attempts la
		JOIN members m ON la.username = m.username
		WHERE la.is_member = true
		AND la.created_at >= NOW() - INTERVAL '30 days'
		ORDER BY la.username
		LIMIT 500
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get distinct member usernames")
		return nil, errors.NewDatabaseError("failed to get distinct member usernames")
	}
	defer rows.Close()

	var usernames []string
	for rows.Next() {
		var username string
		if err := rows.Scan(&username); err != nil {
			r.logger.WithError(err).Error("failed to scan member username")
			return nil, errors.NewDatabaseError("failed to scan member username")
		}
		usernames = append(usernames, username)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating member username rows")
		return nil, errors.NewDatabaseError("error iterating member username rows")
	}

	return usernames, nil
}

// GetDistinctAdminUsernames retrieves all distinct admin usernames from login attempts
func (r *LoginReportRepository) GetDistinctAdminUsernames(ctx context.Context) ([]string, error) {
	query := `
		SELECT DISTINCT la.username
		FROM login_attempts la
		JOIN users u ON la.username = u.username
		WHERE la.is_admin = true
		AND la.created_at >= NOW() - INTERVAL '30 days'
		ORDER BY la.username
		LIMIT 100
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to get distinct admin usernames")
		return nil, errors.NewDatabaseError("failed to get distinct admin usernames")
	}
	defer rows.Close()

	var usernames []string
	for rows.Next() {
		var username string
		if err := rows.Scan(&username); err != nil {
			r.logger.WithError(err).Error("failed to scan admin username")
			return nil, errors.NewDatabaseError("failed to scan admin username")
		}
		usernames = append(usernames, username)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating admin username rows")
		return nil, errors.NewDatabaseError("error iterating admin username rows")
	}

	return usernames, nil
}
