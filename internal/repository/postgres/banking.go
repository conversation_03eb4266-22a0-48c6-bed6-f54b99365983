package postgres

import (
	"blacking-api/internal/domain/banking"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/logger"
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgxpool"
)

type Banking struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewBankingRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.BankingRepository {
	return &Banking{
		pool:   pool,
		logger: logger,
	}
}

func (r *Banking) List(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*banking.Banking, int64, error) {
	var bankings []*banking.Banking
	var total int64
	var err error

	argLast := len(args)
	cteQuery := fmt.Sprintf(`
        WITH counted_data AS (
            SELECT id, name, short_name, code, image_url, created_at, updated_at,
                   COUNT(*) OVER() as total_count
            FROM banking %s
            LIMIT $%v OFFSET $%v
        )
        SELECT id, name, short_name, code, image_url, created_at, updated_at, total_count 
        FROM counted_data
    `, whereClause, argLast+1, argLast+2)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, cteQuery, append(args, limit, offset)...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list bankings")
		return nil, 0, err
	}
	defer rows.Close()

	for rows.Next() {
		b := &banking.Banking{}
		if err := rows.Scan(
			&b.ID, &b.Name, &b.ShortName, &b.Code, &b.ImageURL,
			&b.CreatedAt, &b.UpdatedAt, &total,
		); err != nil {
			r.logger.WithError(err).Error("failed to scan banking row")
			return nil, 0, err
		}
		bankings = append(bankings, b)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating banking rows")
		return nil, 0, err
	}

	// If no rows found, get total count separately
	if len(bankings) == 0 {
		queryTotal := fmt.Sprintf(`SELECT COUNT(*) FROM banking %s`, whereClause)
		row := dbutil.QueryRowWithSchema(ctx, r.pool, queryTotal, args...)
		if err = row.Scan(&total); err != nil {
			r.logger.WithError(err).Error("failed to count bankings")
			return nil, 0, err
		}
	}

	return bankings, total, nil
}

func (r *Banking) FindIdExists(ctx context.Context, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM banking WHERE id = $1)`
	var exists bool

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check if banking ID exists")
		return false, err
	}

	return exists, nil
}
