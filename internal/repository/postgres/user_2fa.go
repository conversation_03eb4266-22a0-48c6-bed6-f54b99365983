package postgres

import (
	"blacking-api/internal/domain/user_2fa"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"database/sql"
	"strconv"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/lib/pq"
)

type User2FARepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewUser2FARepository creates a new user 2FA repository
func NewUser2FARepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.User2FARepository {
	return &User2FARepository{
		pool:   pool,
		logger: logger,
	}
}

// <PERSON>reate creates a new 2FA configuration for a user
func (r *User2FARepository) Create(ctx context.Context, user2FA *user_2fa.User2FA) error {
	query := `
		INSERT INTO user_2fa (user_id, secret_key, is_enabled, backup_codes, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		user2FA.UserID,
		user2FA.SecretKey,
		user2FA.IsEnabled,
		user2FA.BackupCodes,
		user2FA.CreatedAt,
		user2FA.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("user_id", user2FA.UserID).Error("failed to create user 2FA")
		return errors.NewDatabaseError("failed to create user 2FA")
	}

	r.logger.WithField("user_id", user2FA.UserID).Info("user 2FA created successfully")
	return nil
}

// GetByUserID retrieves 2FA configuration by user ID
func (r *User2FARepository) GetByUserID(ctx context.Context, userID string) (*user_2fa.User2FA, error) {
	query := `
		SELECT user_id, secret_key, is_enabled, backup_codes, created_at, updated_at
		FROM user_2fa
		WHERE user_id = $1
	`

	user2FA := &user_2fa.User2FA{}
	var backupCodesStr sql.NullString
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID).Scan(
		&user2FA.UserID,
		&user2FA.SecretKey,
		&user2FA.IsEnabled,
		&backupCodesStr,
		&user2FA.CreatedAt,
		&user2FA.UpdatedAt,
	)

	// Parse backup codes from PostgreSQL array format
	user2FA.BackupCodes = []string{}
	if backupCodesStr.Valid && backupCodesStr.String != "" {
		// Remove curly braces and split by comma
		arrayStr := strings.Trim(backupCodesStr.String, "{}")
		if arrayStr != "" {
			codes := strings.Split(arrayStr, ",")
			for _, code := range codes {
				// Clean up each code (remove quotes if any)
				cleanCode := strings.Trim(code, `"`)
				if cleanCode != "" {
					user2FA.BackupCodes = append(user2FA.BackupCodes, cleanCode)
				}
			}
		}
	}

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("user 2FA configuration not found")
		}
		r.logger.WithError(err).WithField("user_id", userID).Error("failed to get user 2FA by user ID")
		return nil, errors.NewDatabaseError("failed to get user 2FA")
	}

	return user2FA, nil
}

// Update updates a user's 2FA configuration
func (r *User2FARepository) Update(ctx context.Context, user2FA *user_2fa.User2FA) error {
	query := `
		UPDATE user_2fa 
		SET secret_key = $2, is_enabled = $3, backup_codes = $4, updated_at = $5
		WHERE user_id = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		strconv.Itoa(user2FA.UserID),
		user2FA.SecretKey,
		user2FA.IsEnabled,
		pq.Array(user2FA.BackupCodes),
		user2FA.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("user_id", user2FA.UserID).Error("failed to update user 2FA")
		return errors.NewDatabaseError("failed to update user 2FA")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("user 2FA configuration not found")
	}

	r.logger.WithField("user_id", user2FA.UserID).Info("user 2FA updated successfully")
	return nil
}

// Delete deletes a user's 2FA configuration
func (r *User2FARepository) Delete(ctx context.Context, userID string) error {
	query := `DELETE FROM user_2fa WHERE user_id = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, userID)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("failed to delete user 2FA")
		return errors.NewDatabaseError("failed to delete user 2FA")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("user 2FA configuration not found")
	}

	r.logger.WithField("user_id", userID).Info("user 2FA deleted successfully")
	return nil
}

// IsEnabled checks if 2FA is enabled for a user
func (r *User2FARepository) IsEnabled(ctx context.Context, userID string) (bool, error) {
	query := `
		SELECT is_enabled 
		FROM user_2fa 
		WHERE user_id = $1
	`

	var isEnabled bool
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID).Scan(&isEnabled)
	if err != nil {
		if err == pgx.ErrNoRows {
			return false, nil // User doesn't have 2FA setup
		}
		r.logger.WithError(err).WithField("user_id", userID).Error("failed to check if 2FA is enabled")
		return false, errors.NewDatabaseError("failed to check if 2FA is enabled")
	}

	return isEnabled, nil
}

// HasSetup checks if a user has setup 2FA (has secret key)
func (r *User2FARepository) HasSetup(ctx context.Context, userID string) (bool, error) {
	query := `
		SELECT COUNT(*) 
		FROM user_2fa 
		WHERE user_id = $1 AND secret_key IS NOT NULL AND secret_key != ''
	`

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userID).Scan(&count)
	if err != nil {
		r.logger.WithError(err).WithField("user_id", userID).Error("failed to check if user has 2FA setup")
		return false, errors.NewDatabaseError("failed to check if user has 2FA setup")
	}

	return count > 0, nil
}

// Upsert creates or updates a user's 2FA configuration
func (r *User2FARepository) Upsert(ctx context.Context, user2FA *user_2fa.User2FA) error {
	query := `
		INSERT INTO user_2fa (user_id, secret_key, is_enabled, backup_codes, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		ON CONFLICT (user_id) 
		DO UPDATE SET 
			secret_key = EXCLUDED.secret_key,
			is_enabled = EXCLUDED.is_enabled,
			backup_codes = EXCLUDED.backup_codes,
			updated_at = EXCLUDED.updated_at
	`

	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		user2FA.UserID,
		user2FA.SecretKey,
		user2FA.IsEnabled,
		pq.Array(user2FA.BackupCodes),
		user2FA.CreatedAt,
		user2FA.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("user_id", user2FA.UserID).Error("failed to upsert user 2FA")
		return errors.NewDatabaseError("failed to upsert user 2FA")
	}

	r.logger.WithField("user_id", user2FA.UserID).Info("user 2FA upserted successfully")
	return nil
}
