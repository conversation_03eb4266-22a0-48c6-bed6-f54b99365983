package postgres

import (
	"context"
	"fmt"
	"strings"

	"blacking-api/internal/domain/permission"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
)

type PermissionRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewPermissionRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.PermissionRepository {
	return &PermissionRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *PermissionRepository) Create(ctx context.Context, perm *permission.Permission) error {
	query := `
		INSERT INTO permissions (parent_id, name, key, description, position, level,
		                        supports_create, supports_view, supports_edit, supports_delete,
		                        is_tab, is_button, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
		RETURNING id
	`
	var lastInsertID int
	var isTab, isButton int16
	if perm.IsTab {
		isTab = 1
	}
	if perm.IsButton {
		isButton = 1
	}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		perm.ParentID, perm.Name, perm.Key, perm.Description, perm.Position, perm.Level,
		perm.SupportsCreate, perm.SupportsView, perm.SupportsEdit, perm.SupportsDelete,
		isTab, isButton, perm.Status, perm.CreatedAt, perm.UpdatedAt,
	)
	err := row.Scan(&lastInsertID)

	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505": // unique_violation
				return errors.NewValidationError("permission key already exists")
			}
		}
		r.logger.WithError(err).Error("failed to create permission")
		return errors.NewDatabaseError("failed to create permission")
	}

	perm.ID = lastInsertID
	r.logger.WithField("permission_id", lastInsertID).Info("permission created successfully")
	return nil
}

func (r *PermissionRepository) GetByID(ctx context.Context, id string) (*permission.Permission, error) {
	query := `
		SELECT id, parent_id, name, key, description, position, level,
		       supports_create, supports_view, supports_edit, supports_delete,
		       is_tab, is_button, status, created_at, updated_at
		FROM permissions
		WHERE id = $1 AND status = 'active'
	`

	perm := &permission.Permission{}
	var isTab, isButton int16
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&perm.ID, &perm.ParentID, &perm.Name, &perm.Key, &perm.Description, &perm.Position, &perm.Level,
		&perm.SupportsCreate, &perm.SupportsView, &perm.SupportsEdit, &perm.SupportsDelete,
		&isTab, &isButton, &perm.Status, &perm.CreatedAt, &perm.UpdatedAt,
	)
	if err == nil {
		perm.IsTab = isTab == 1
		perm.IsButton = isButton == 1
	}
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("permission not found")
		}
		r.logger.WithError(err).WithField("permission_id", id).Error("failed to get permission by ID")
		return nil, errors.NewDatabaseError("failed to get permission")
	}

	return perm, nil
}

func (r *PermissionRepository) GetByKey(ctx context.Context, key string) (*permission.Permission, error) {
	query := `
		SELECT id, parent_id, name, key, description, position, level,
		       supports_create, supports_view, supports_edit, supports_delete,
		       is_tab, is_button, status, created_at, updated_at
		FROM permissions
		WHERE key = $1 AND status = 'active'
	`

	perm := &permission.Permission{}
	var isTab, isButton int16
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, key).Scan(
		&perm.ID, &perm.ParentID, &perm.Name, &perm.Key, &perm.Description, &perm.Position, &perm.Level,
		&perm.SupportsCreate, &perm.SupportsView, &perm.SupportsEdit, &perm.SupportsDelete,
		&isTab, &isButton, &perm.Status, &perm.CreatedAt, &perm.UpdatedAt,
	)
	if err == nil {
		perm.IsTab = isTab == 1
		perm.IsButton = isButton == 1
	}
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("permission not found")
		}
		r.logger.WithError(err).WithField("permission_key", key).Error("failed to get permission by key")
		return nil, errors.NewDatabaseError("failed to get permission")
	}

	return perm, nil
}

func (r *PermissionRepository) Update(ctx context.Context, perm *permission.Permission) error {
	query := `
		UPDATE permissions
		SET parent_id = $2, name = $3, key = $4, description = $5, position = $6, level = $7,
		    supports_create = $8, supports_view = $9, supports_edit = $10, supports_delete = $11,
		    is_tab = $12, is_button = $13, status = $14, updated_at = $15
		WHERE id = $1
	`

	var isTab, isButton int16
	if perm.IsTab {
		isTab = 1
	}
	if perm.IsButton {
		isButton = 1
	}
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		perm.ID, perm.ParentID, perm.Name, perm.Key, perm.Description, perm.Position, perm.Level,
		perm.SupportsCreate, perm.SupportsView, perm.SupportsEdit, perm.SupportsDelete,
		isTab, isButton, perm.Status, perm.UpdatedAt,
	)
	if err != nil {
		if pgErr, ok := err.(*pgconn.PgError); ok {
			switch pgErr.Code {
			case "23505": // unique_violation
				return errors.NewValidationError("permission key already exists")
			}
		}
		r.logger.WithError(err).WithField("permission_id", perm.ID).Error("failed to update permission")
		return errors.NewDatabaseError("failed to update permission")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("permission not found")
	}

	r.logger.WithField("permission_id", perm.ID).Info("permission updated successfully")
	return nil
}

func (r *PermissionRepository) Delete(ctx context.Context, id string) error {
	query := `
		UPDATE permissions
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).WithField("permission_id", id).Error("failed to delete permission")
		return errors.NewDatabaseError("failed to delete permission")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("permission not found")
	}

	r.logger.WithField("permission_id", id).Info("permission deleted successfully")
	return nil
}

func (r *PermissionRepository) List(ctx context.Context, limit, offset int, search string) ([]*permission.Permission, error) {
	var args []interface{}
	var conditions []string
	argIndex := 1

	query := `
		SELECT id, parent_id, name, key, description, position, level,
		       supports_create, supports_view, supports_edit, supports_delete,
		       is_tab, is_button, status, created_at, updated_at
		FROM permissions
	`

	// Add status condition (only active permissions)
	conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
	args = append(args, "active")
	argIndex++

	// Add search condition if provided
	if search != "" {
		conditions = append(conditions, fmt.Sprintf("name ILIKE $%d", argIndex))
		args = append(args, "%"+search+"%")
		argIndex++
	}

	// Add WHERE clause if there are conditions
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	// Add ORDER BY
	query += " ORDER BY COALESCE(position, 999999), created_at ASC"

	// Add pagination only if limit > 0
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
		args = append(args, limit, offset)
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list permissions")
		return nil, errors.NewDatabaseError("failed to list permissions")
	}
	defer rows.Close()

	var permissions []*permission.Permission
	for rows.Next() {
		perm := &permission.Permission{}
		var isTab, isButton int16
		err := rows.Scan(
			&perm.ID, &perm.ParentID, &perm.Name, &perm.Key, &perm.Description, &perm.Position, &perm.Level,
			&perm.SupportsCreate, &perm.SupportsView, &perm.SupportsEdit, &perm.SupportsDelete,
			&isTab, &isButton, &perm.Status, &perm.CreatedAt, &perm.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan permission row")
			return nil, errors.NewDatabaseError("failed to scan permission")
		}
		perm.IsTab = isTab == 1
		perm.IsButton = isButton == 1
		permissions = append(permissions, perm)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating permission rows")
		return nil, errors.NewDatabaseError("failed to iterate permissions")
	}

	return permissions, nil
}

func (r *PermissionRepository) Count(ctx context.Context, search string) (int64, error) {
	var args []interface{}
	var conditions []string
	argIndex := 1

	query := "SELECT COUNT(*) FROM permissions"

	// Add status condition (only active permissions)
	conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
	args = append(args, "active")
	argIndex++

	// Add search condition if provided
	if search != "" {
		conditions = append(conditions, fmt.Sprintf("name ILIKE $%d", argIndex))
		args = append(args, "%"+search+"%")
		argIndex++
	}

	// Add WHERE clause if there are conditions
	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count permissions")
		return 0, errors.NewDatabaseError("failed to count permissions")
	}

	return count, nil
}

// ListByCategory function removed as category column no longer exists in the database schema

func (r *PermissionRepository) ListWithSubPermissions(ctx context.Context) ([]*permission.Permission, error) {
	query := `
		SELECT id, parent_id, name, key, description, position, level,
		       supports_create, supports_view, supports_edit, supports_delete,
		       is_tab, is_button, status, created_at, updated_at
		FROM permissions
		WHERE status = 'active'
		ORDER BY COALESCE(position, 999999), level, created_at ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list permissions with sub-permissions")
		return nil, errors.NewDatabaseError("failed to list permissions with sub-permissions")
	}
	defer rows.Close()

	permissionMap := make(map[int]*permission.Permission)
	var mainPermissions []*permission.Permission

	for rows.Next() {
		perm := &permission.Permission{}
		var isTab, isButton int16
		err := rows.Scan(
			&perm.ID, &perm.ParentID, &perm.Name, &perm.Key, &perm.Description, &perm.Position, &perm.Level,
			&perm.SupportsCreate, &perm.SupportsView, &perm.SupportsEdit, &perm.SupportsDelete,
			&isTab, &isButton, &perm.Status, &perm.CreatedAt, &perm.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan permission row")
			return nil, errors.NewDatabaseError("failed to scan permission")
		}

		perm.IsTab = isTab == 1
		perm.IsButton = isButton == 1
		permissionMap[perm.ID] = perm

		if perm.ParentID == nil {
			// Main permission
			mainPermissions = append(mainPermissions, perm)
		} else {
			// Sub-permission
			if parent, exists := permissionMap[*perm.ParentID]; exists {
				parent.SubPermissions = append(parent.SubPermissions, *perm)
			}
		}
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating permission rows")
		return nil, errors.NewDatabaseError("failed to iterate permissions")
	}

	return mainPermissions, nil
}

func (r *PermissionRepository) Reorder(ctx context.Context, permissionIDs []int) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction for reorder")
		return errors.NewDatabaseError("failed to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Update positions based on array order
	for i, permissionID := range permissionIDs {
		position := i + 1
		query := `
			UPDATE permissions
			SET position = $1, updated_at = CURRENT_TIMESTAMP
			WHERE id = $2 AND status = 'active'
		`
		result, err := tx.Exec(ctx, query, position, permissionID)
		if err != nil {
			r.logger.WithError(err).WithField("permission_id", permissionID).Error("failed to update permission position")
			return errors.NewDatabaseError("failed to update permission position")
		}

		if result.RowsAffected() == 0 {
			return errors.NewNotFoundError(fmt.Sprintf("permission with ID %d not found", permissionID))
		}
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit reorder transaction")
		return errors.NewDatabaseError("failed to commit reorder")
	}

	r.logger.WithField("permission_count", len(permissionIDs)).Info("permissions reordered successfully")
	return nil
}
