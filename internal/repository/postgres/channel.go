package postgres

import (
	"context"

	"blacking-api/internal/domain/channel"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ChannelRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewChannelRepository creates a new channel repository
func NewChannelRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.ChannelRepository {
	return &ChannelRepository{
		pool:   pool,
		logger: logger,
	}
}

// Create creates a new channel
func (r *ChannelRepository) Create(ctx context.Context, c *channel.Channel) error {
	query := `
		INSERT INTO channels (name, platform_id, description, status, created_by, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id
	`

	var lastInsertID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		c.Name, c.PlatformID, c.Description, c.Status, c.CreatedBy, c.CreatedAt, c.UpdatedAt,
	)
	err := row.Scan(&lastInsertID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create channel")
		return errors.NewDatabaseError("failed to create channel")
	}

	c.ID = lastInsertID
	r.logger.WithField("channel_id", c.ID).Info("channel created successfully")
	return nil
}

// GetByID retrieves channel by ID
func (r *ChannelRepository) GetByID(ctx context.Context, id int) (*channel.Channel, error) {
	query := `
		SELECT id, name, platform_id, description, status, created_by, created_at, updated_at
		FROM channels
		WHERE id = $1 AND status = 'active'
	`

	c := &channel.Channel{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(
		&c.ID, &c.Name, &c.PlatformID, &c.Description, &c.Status,
		&c.CreatedBy, &c.CreatedAt, &c.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("channel not found")
		}
		r.logger.WithError(err).Error("failed to get channel by ID")
		return nil, errors.NewDatabaseError("failed to get channel")
	}

	return c, nil
}

// GetByName retrieves channel by name
func (r *ChannelRepository) GetByName(ctx context.Context, name string) (*channel.Channel, error) {
	query := `
		SELECT id, name, platform_id, description, status, created_by, created_at, updated_at
		FROM channels
		WHERE name = $1 AND status = 'active'
	`

	c := &channel.Channel{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, name)
	err := row.Scan(
		&c.ID, &c.Name, &c.PlatformID, &c.Description, &c.Status,
		&c.CreatedBy, &c.CreatedAt, &c.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("channel not found")
		}
		r.logger.WithError(err).Error("failed to get channel by name")
		return nil, errors.NewDatabaseError("failed to get channel")
	}

	return c, nil
}

// Update updates an existing channel
func (r *ChannelRepository) Update(ctx context.Context, c *channel.Channel) error {
	query := `
		UPDATE channels
		SET name = $2, platform_id = $3, description = $4, updated_at = $5
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		c.ID, c.Name, c.PlatformID, c.Description, c.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update channel")
		return errors.NewDatabaseError("failed to update channel")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("channel not found")
	}

	r.logger.WithField("channel_id", c.ID).Info("channel updated successfully")
	return nil
}

// Delete soft deletes a channel (sets status to inactive)
func (r *ChannelRepository) Delete(ctx context.Context, id int) error {
	query := `
		UPDATE channels
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete channel")
		return errors.NewDatabaseError("failed to delete channel")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("channel not found")
	}

	r.logger.WithField("channel_id", id).Info("channel deleted successfully")
	return nil
}

// List retrieves channels with pagination and search by name
func (r *ChannelRepository) List(ctx context.Context, limit, offset int, search string) ([]*channel.Channel, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT id, name, platform_id, description, status, created_by, created_at, updated_at
			FROM channels
			WHERE status = 'active' AND name ILIKE $1
			ORDER BY created_at DESC
			LIMIT $2 OFFSET $3
		`
		args = []interface{}{"%" + search + "%", limit, offset}
	} else {
		query = `
			SELECT id, name, platform_id, description, status, created_by, created_at, updated_at
			FROM channels
			WHERE status = 'active'
			ORDER BY created_at DESC
			LIMIT $1 OFFSET $2
		`
		args = []interface{}{limit, offset}
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list channels")
		return nil, errors.NewDatabaseError("failed to list channels")
	}
	defer rows.Close()

	var channels []*channel.Channel
	for rows.Next() {
		c := &channel.Channel{}
		err := rows.Scan(
			&c.ID, &c.Name, &c.PlatformID, &c.Description, &c.Status,
			&c.CreatedBy, &c.CreatedAt, &c.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan channel")
			return nil, errors.NewDatabaseError("failed to scan channel")
		}
		channels = append(channels, c)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating channel rows")
		return nil, errors.NewDatabaseError("error iterating channel rows")
	}

	return channels, nil
}

// Count returns total count of channels with search filter
func (r *ChannelRepository) Count(ctx context.Context, search string) (int64, error) {
	var query string
	var args []interface{}

	if search != "" {
		query = `
			SELECT COUNT(*)
			FROM channels
			WHERE status = 'active' AND name ILIKE $1
		`
		args = []interface{}{"%" + search + "%"}
	} else {
		query = `
			SELECT COUNT(*)
			FROM channels
			WHERE status = 'active'
		`
		args = []interface{}{}
	}

	var count int64
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...)
	err := row.Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count channels")
		return 0, errors.NewDatabaseError("failed to count channels")
	}

	return count, nil
}

// ListActive retrieves only active channels
func (r *ChannelRepository) ListActive(ctx context.Context) ([]*channel.Channel, error) {
	query := `
		SELECT id, name, platform_id, description, status, created_by, created_at, updated_at
		FROM channels
		WHERE status = 'active'
		ORDER BY name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list active channels")
		return nil, errors.NewDatabaseError("failed to list active channels")
	}
	defer rows.Close()

	var channels []*channel.Channel
	for rows.Next() {
		c := &channel.Channel{}
		err := rows.Scan(
			&c.ID, &c.Name, &c.PlatformID, &c.Description, &c.Status,
			&c.CreatedBy, &c.CreatedAt, &c.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan active channel")
			return nil, errors.NewDatabaseError("failed to scan active channel")
		}
		channels = append(channels, c)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating active channel rows")
		return nil, errors.NewDatabaseError("error iterating active channel rows")
	}

	return channels, nil
}

// ListByPlatform retrieves channels by platform ID
func (r *ChannelRepository) ListByPlatform(ctx context.Context, platformID string) ([]*channel.Channel, error) {
	query := `
		SELECT id, name, platform_id, description, status, created_by, created_at, updated_at
		FROM channels
		WHERE platform_id = $1 AND status = 'active'
		ORDER BY name ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, platformID)
	if err != nil {
		r.logger.WithError(err).Error("failed to list channels by platform")
		return nil, errors.NewDatabaseError("failed to list channels by platform")
	}
	defer rows.Close()

	var channels []*channel.Channel
	for rows.Next() {
		c := &channel.Channel{}
		err := rows.Scan(
			&c.ID, &c.Name, &c.PlatformID, &c.Description, &c.Status,
			&c.CreatedBy, &c.CreatedAt, &c.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan channel by platform")
			return nil, errors.NewDatabaseError("failed to scan channel by platform")
		}
		channels = append(channels, c)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating channel by platform rows")
		return nil, errors.NewDatabaseError("error iterating channel by platform rows")
	}

	return channels, nil
}
