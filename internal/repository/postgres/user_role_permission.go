package postgres

import (
	"context"

	"blacking-api/internal/domain/permission"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type UserRolePermissionRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewUserRolePermissionRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.UserRolePermissionRepository {
	return &UserRolePermissionRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *UserRolePermissionRepository) Create(ctx context.Context, userRolePerm *permission.UserRolePermission) error {
	query := `
		INSERT INTO user_role_permissions (user_role_id, permission_key, can_create, can_view, can_edit, can_delete, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id
	`
	var lastInsertID int
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		userRolePerm.UserRoleID, userRolePerm.PermissionKey, userRolePerm.CanCreate, userRolePerm.CanView,
		userRolePerm.CanEdit, userRolePerm.CanDelete, userRolePerm.Status, userRolePerm.CreatedAt, userRolePerm.UpdatedAt,
	)
	err := row.Scan(&lastInsertID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create user role permission")
		return errors.NewDatabaseError("failed to create user role permission")
	}

	userRolePerm.ID = lastInsertID
	r.logger.WithField("user_role_permission_id", lastInsertID).Info("user role permission created successfully")
	return nil
}

func (r *UserRolePermissionRepository) GetByUserRoleAndPermission(ctx context.Context, userRoleID int, permissionKey string) (*permission.UserRolePermission, error) {
	query := `
		SELECT id, user_role_id, permission_key, can_create, can_view, can_edit, can_delete, status, created_at, updated_at
		FROM user_role_permissions
		WHERE user_role_id = $1 AND permission_key = $2 AND status = 'active'
	`

	userRolePerm := &permission.UserRolePermission{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, userRoleID, permissionKey).Scan(
		&userRolePerm.ID, &userRolePerm.UserRoleID, &userRolePerm.PermissionKey,
		&userRolePerm.CanCreate, &userRolePerm.CanView, &userRolePerm.CanEdit, &userRolePerm.CanDelete,
		&userRolePerm.Status, &userRolePerm.CreatedAt, &userRolePerm.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("user role permission not found")
		}
		r.logger.WithError(err).WithField("user_role_id", userRoleID).WithField("permission_key", permissionKey).Error("failed to get user role permission")
		return nil, errors.NewDatabaseError("failed to get user role permission")
	}

	return userRolePerm, nil
}

func (r *UserRolePermissionRepository) Update(ctx context.Context, userRolePerm *permission.UserRolePermission) error {
	query := `
		UPDATE user_role_permissions
		SET can_create = $3, can_view = $4, can_edit = $5, can_delete = $6, status = $7, updated_at = $8
		WHERE user_role_id = $1 AND permission_key = $2
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		userRolePerm.UserRoleID, userRolePerm.PermissionKey, userRolePerm.CanCreate, userRolePerm.CanView,
		userRolePerm.CanEdit, userRolePerm.CanDelete, userRolePerm.Status, userRolePerm.UpdatedAt,
	)
	if err != nil {
		r.logger.WithError(err).WithField("user_role_id", userRolePerm.UserRoleID).WithField("permission_key", userRolePerm.PermissionKey).Error("failed to update user role permission")
		return errors.NewDatabaseError("failed to update user role permission")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("user role permission not found")
	}

	r.logger.WithField("user_role_id", userRolePerm.UserRoleID).WithField("permission_key", userRolePerm.PermissionKey).Info("user role permission updated successfully")
	return nil
}

func (r *UserRolePermissionRepository) Delete(ctx context.Context, userRoleID int, permissionKey string) error {
	query := `
		UPDATE user_role_permissions
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE user_role_id = $1 AND permission_key = $2 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, userRoleID, permissionKey)
	if err != nil {
		r.logger.WithError(err).WithField("user_role_id", userRoleID).WithField("permission_key", permissionKey).Error("failed to delete user role permission")
		return errors.NewDatabaseError("failed to delete user role permission")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("user role permission not found")
	}

	r.logger.WithField("user_role_id", userRoleID).WithField("permission_key", permissionKey).Info("user role permission deleted successfully")
	return nil
}

func (r *UserRolePermissionRepository) ListByUserRoleID(ctx context.Context, userRoleID int) ([]*permission.UserRolePermission, error) {
	query := `
		SELECT id, user_role_id, permission_key, can_create, can_view, can_edit, can_delete, status, created_at, updated_at
		FROM user_role_permissions
		WHERE user_role_id = $1 AND status = 'active'
		ORDER BY permission_key
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, userRoleID)
	if err != nil {
		r.logger.WithError(err).Error("failed to list user role permissions")
		return nil, errors.NewDatabaseError("failed to list user role permissions")
	}
	defer rows.Close()

	var userRolePermissions []*permission.UserRolePermission
	for rows.Next() {
		userRolePerm := &permission.UserRolePermission{}
		err := rows.Scan(
			&userRolePerm.ID, &userRolePerm.UserRoleID, &userRolePerm.PermissionKey,
			&userRolePerm.CanCreate, &userRolePerm.CanView, &userRolePerm.CanEdit, &userRolePerm.CanDelete,
			&userRolePerm.Status, &userRolePerm.CreatedAt, &userRolePerm.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user role permission row")
			return nil, errors.NewDatabaseError("failed to scan user role permission")
		}
		userRolePermissions = append(userRolePermissions, userRolePerm)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user role permission rows")
		return nil, errors.NewDatabaseError("failed to iterate user role permissions")
	}

	return userRolePermissions, nil
}

func (r *UserRolePermissionRepository) BulkUpsert(ctx context.Context, userRoleID int, permissions []permission.UserRolePermissionUpdate) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		r.logger.WithError(err).Error("failed to begin transaction for bulk upsert")
		return errors.NewDatabaseError("failed to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Upsert each permission
	for _, perm := range permissions {
		query := `
			INSERT INTO user_role_permissions (user_role_id, permission_key, can_create, can_view, can_edit, can_delete, status, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
			ON CONFLICT (user_role_id, permission_key)
			DO UPDATE SET
				can_create = EXCLUDED.can_create,
				can_view = EXCLUDED.can_view,
				can_edit = EXCLUDED.can_edit,
				can_delete = EXCLUDED.can_delete,
				status = 'active',
				updated_at = CURRENT_TIMESTAMP
		`
		_, err := tx.Exec(ctx, query,
			userRoleID, perm.PermissionKey, perm.CanCreate, perm.CanView, perm.CanEdit, perm.CanDelete,
		)
		if err != nil {
			r.logger.WithError(err).WithField("user_role_id", userRoleID).WithField("permission_key", perm.PermissionKey).Error("failed to upsert user role permission")
			return errors.NewDatabaseError("failed to upsert user role permission")
		}
	}

	if err := tx.Commit(ctx); err != nil {
		r.logger.WithError(err).Error("failed to commit bulk upsert transaction")
		return errors.NewDatabaseError("failed to commit bulk upsert")
	}

	r.logger.WithField("user_role_id", userRoleID).WithField("permission_count", len(permissions)).Info("user role permissions bulk upserted successfully")
	return nil
}

func (r *UserRolePermissionRepository) DeleteByUserRoleID(ctx context.Context, userRoleID int) error {
	query := `
		UPDATE user_role_permissions
		SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
		WHERE user_role_id = $1 AND status = 'active'
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, userRoleID)
	if err != nil {
		r.logger.WithError(err).WithField("user_role_id", userRoleID).Error("failed to delete user role permissions by user role ID")
		return errors.NewDatabaseError("failed to delete user role permissions by user role ID")
	}

	r.logger.WithField("user_role_id", userRoleID).WithField("rows_affected", result.RowsAffected()).Info("user role permissions deleted by user role ID successfully")
	return nil
}
