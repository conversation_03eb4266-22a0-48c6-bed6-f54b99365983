package postgres

import (
	"blacking-api/internal/domain/user_transaction_status"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
)

type UserTransactionStatusRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewUserTransactionStatusRepository creates a new user transaction status repository
func NewUserTransactionStatusRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.UserTransactionStatusRepository {
	return &UserTransactionStatusRepository{
		pool:   pool,
		logger: logger,
	}
}

// List retrieves all user transaction statuses
func (r *UserTransactionStatusRepository) List(ctx context.Context) ([]*user_transaction_status.UserTransactionStatus, error) {
	query := `
		SELECT id, name, detail, created_at
		FROM user_transaction_status
		ORDER BY created_at ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to query user transaction statuses")
		return nil, errors.NewDatabaseError("failed to retrieve user transaction statuses")
	}
	defer rows.Close()

	var transactionStatuses []*user_transaction_status.UserTransactionStatus
	for rows.Next() {
		var transactionStatus user_transaction_status.UserTransactionStatus
		err := rows.Scan(
			&transactionStatus.ID,
			&transactionStatus.Name,
			&transactionStatus.Detail,
			&transactionStatus.CreatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user transaction status")
			return nil, errors.NewDatabaseError("failed to scan user transaction status")
		}
		transactionStatuses = append(transactionStatuses, &transactionStatus)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user transaction status rows")
		return nil, errors.NewDatabaseError("failed to retrieve user transaction statuses")
	}

	r.logger.WithField("count", len(transactionStatuses)).Info("user transaction statuses retrieved successfully")
	return transactionStatuses, nil
}
