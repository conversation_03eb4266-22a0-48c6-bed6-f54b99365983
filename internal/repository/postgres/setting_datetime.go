package postgres

import (
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

type DateTimeRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewDateTimeRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.DateTimeRepository {
	return &DateTimeRepository{
		pool:   pool,
		logger: logger,
	}
}

// ParseBomUTC parses a date string and returns the beginning of month in UTC
func (r *DateTimeRepository) ParseBomUTC(input string) (*time.Time, error) {
	if input == "" || len(input) < 7 {
		return nil, errors.NewValidationError("INVALID_DATE_STRING_INPUT")
	}

	// Parse YYYY-MM format and set to first day of month
	var startDate time.Time
	var err error

	if len(input) == 7 { // YYYY-MM format
		startDate, err = time.Parse("2006-01", input)
	} else if len(input) >= 10 { // YYYY-MM-DD format, use first day of month
		parsedDate, parseErr := time.Parse("2006-01-02", input[:10])
		if parseErr != nil {
			return nil, errors.NewValidationError("INVALID_DATE_FORMAT")
		}
		startDate = time.Date(parsedDate.Year(), parsedDate.Month(), 1, 0, 0, 0, 0, time.UTC)
	} else {
		return nil, errors.NewValidationError("INVALID_DATE_FORMAT")
	}

	if err != nil {
		return nil, errors.NewValidationError("INVALID_DATE_FORMAT")
	}

	result := startDate.UTC()
	return &result, nil
}

// ParseEomUTC parses a date string and returns the end of month in UTC
func (r *DateTimeRepository) ParseEomUTC(input string) (*time.Time, error) {
	if input == "" || len(input) < 7 {
		return nil, errors.NewValidationError("INVALID_DATE_STRING_INPUT")
	}

	// Parse YYYY-MM format and set to last day of month
	var startDate time.Time
	var err error

	if len(input) == 7 { // YYYY-MM format
		startDate, err = time.Parse("2006-01", input)
	} else if len(input) >= 10 { // YYYY-MM-DD format, use the month from the date
		parsedDate, parseErr := time.Parse("2006-01-02", input[:10])
		if parseErr != nil {
			return nil, errors.NewValidationError("INVALID_DATE_FORMAT")
		}
		startDate = time.Date(parsedDate.Year(), parsedDate.Month(), 1, 0, 0, 0, 0, time.UTC)
	} else {
		return nil, errors.NewValidationError("INVALID_DATE_FORMAT")
	}

	if err != nil {
		return nil, errors.NewValidationError("INVALID_DATE_FORMAT")
	}

	// Get the last day of the month
	nextMonth := startDate.AddDate(0, 1, 0)
	lastDay := nextMonth.Add(-time.Nanosecond)

	result := lastDay.UTC()
	return &result, nil
}

// ParseBodBkk parses a date string and returns the beginning of day in Bangkok timezone, converted to UTC
func (r *DateTimeRepository) ParseBodBkk(input string) (*time.Time, error) {
	if input == "" || len(input) != 10 {
		return nil, errors.NewValidationError("INVALID_DATE_STRING_INPUT")
	}

	startDate, err := time.Parse("2006-01-02", input)
	if err != nil {
		return nil, errors.NewValidationError("INVALID_DATE_FORMAT")
	}

	// Set the time to beginning of day in Bangkok timezone (UTC+7)
	bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	result := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, bkkLoc)

	// Convert to UTC
	utcResult := result.UTC()
	return &utcResult, nil
}

// ParseEodBkk parses a date string and returns the end of day in Bangkok timezone, converted to UTC
func (r *DateTimeRepository) ParseEodBkk(input string) (*time.Time, error) {
	if input == "" || len(input) != 10 {
		return nil, errors.NewValidationError("INVALID_DATE_STRING_INPUT")
	}

	startDate, err := time.Parse("2006-01-02", input)
	if err != nil {
		return nil, errors.NewValidationError("INVALID_DATE_FORMAT")
	}

	// Set the time to end of day in Bangkok timezone (UTC+7)
	bkkLoc := time.FixedZone("Asia/Bangkok", 7*60*60)
	result := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 23, 59, 59, 999999999, bkkLoc)

	// Convert to UTC
	utcResult := result.UTC()
	return &utcResult, nil
}
