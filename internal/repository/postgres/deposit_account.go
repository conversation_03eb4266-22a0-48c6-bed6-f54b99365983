package postgres

import (
	"blacking-api/internal/domain/deposit_account"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type DepositAccount struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewDepositAccountRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.DepositAccountRepository {
	return &DepositAccount{
		pool:   pool,
		logger: logger,
	}
}

func (r *DepositAccount) Create(ctx context.Context, req *deposit_account.DepositAccountRequest) error {
	query := `INSERT INTO deposit_account (banking_id, payment_method_id, auto_bot_id, account_name, account_name_display, account_number, phone_number, auto_transfer)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`
	_, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.BankingID, req.PaymentMethodID, 1, req.AccountName, req.AccountNameDisplay, req.AccountNumber, req.PhoneNumber, req.AutoTransfer,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to insert deposit account")
		return errors.NewDatabaseError("failed to insert deposit account")
	}

	return nil
}

func (r *DepositAccount) FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*deposit_account.DepositAccount, int64, error) {
	var depositAccounts []*deposit_account.DepositAccount
	var total int64
	var err error

	argLast := len(args)
	cteQuery := fmt.Sprintf(`
       WITH counted_data AS (
          SELECT 
             da.id, 
             da.banking_id, 
             da.payment_method_id, 
             da.auto_bot_id, 
             da.account_name, 
             da.account_name_display, 
             da.account_number, 
             da.phone_number, 
             da.auto_transfer,
             da.push_bullet_nickname,
             da.push_bullet_token,
             da.active,
             da.inactive,
             da.created_at,
             da.updated_at,
             b.name as banking_name,
             b.image_url as banking_image_url,
             a.id as algorithm_id,
             a.name as algorithm_name,
             COUNT(*) OVER() as total_count
          FROM deposit_account da
          LEFT JOIN banking b ON da.banking_id = b.id
          LEFT JOIN algorithm a ON da.algorithm_id = a.id
          %s
          WHERE da.inactive = false
          LIMIT $%v OFFSET $%v
       )
       SELECT 
          id, 
          banking_id, 
          payment_method_id, 
          auto_bot_id, 
          account_name, 
          account_name_display, 
          account_number, 
          phone_number, 
          auto_transfer,
          push_bullet_nickname,
          push_bullet_token,
          active,
          inactive,
          created_at,
          updated_at,
          banking_name,
          banking_image_url,
          algorithm_id,
          algorithm_name,
          total_count
       FROM counted_data
    `, whereClause, argLast+1, argLast+2)

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, cteQuery, append(args, limit, offset)...)
	if err != nil {
		r.logger.WithError(err).Error("failed to find all deposit accounts")
		return nil, 0, err
	}
	defer rows.Close()

	for rows.Next() {
		depositAccount := &deposit_account.DepositAccount{}
		if err := rows.Scan(
			&depositAccount.ID,
			&depositAccount.BankingID,
			&depositAccount.PaymentMethodID,
			&depositAccount.AutoBotID,
			&depositAccount.AccountName,
			&depositAccount.AccountNameDisplay,
			&depositAccount.AccountNumber,
			&depositAccount.PhoneNumber,
			&depositAccount.AutoTransfer,
			&depositAccount.PushBulletNickname,
			&depositAccount.PushBulletToken,
			&depositAccount.Active,
			&depositAccount.Inactive,
			&depositAccount.CreatedAt,
			&depositAccount.UpdatedAt,
			&depositAccount.BankingName,
			&depositAccount.BankingImageUrl,
			&depositAccount.AlgorithmID,
			&depositAccount.AlgorithmName,
			&total,
		); err != nil {
			r.logger.WithError(err).Error("failed to scan deposit account row")
			return nil, 0, err
		}
		depositAccounts = append(depositAccounts, depositAccount)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating deposit account rows")
		return nil, 0, err
	}

	return depositAccounts, total, nil

	//var depositAccounts []*deposit_account.DepositAccount
	//var total int64
	//var err error
	//
	//argLast := len(args)
	//cteQuery := fmt.Sprintf(`
	//	WITH counted_data AS (
	//		SELECT id, banking_id, payment_method_id, auto_bot_id, account_name, account_name_display, account_number, phone_number, auto_transfer,
	//			COUNT(*) OVER() as total_count
	//		FROM deposit_account %s
	//		WHERE inactive = false
	//		LIMIT $%v OFFSET $%v
	//	)
	//	SELECT id, banking_id, payment_method_id, auto_bot_id, account_name, account_name_display, account_number, phone_number, auto_transfer, total_count
	//	FROM counted_data
	//`, whereClause, argLast+1, argLast+2)
	//rows, err := r.db.QueryContext(ctx, cteQuery, append(args, limit, offset)...)
	//if err != nil {
	//	r.logger.WithError(err).Error("failed to find all deposit accounts")
	//	return nil, 0, err
	//}
	//defer rows.Close()
	//
	//for rows.Next() {
	//	depositAccount := &deposit_account.DepositAccount{}
	//	if err := rows.Scan(
	//		&depositAccount.ID,
	//		&depositAccount.BankingID,
	//		&depositAccount.BankingName,
	//		&depositAccount.BankingImageUrl,
	//		&depositAccount.PaymentMethodID,
	//		&depositAccount.AlgorithmID,
	//		&depositAccount.AlgorithmName,
	//		&depositAccount.AutoBotID,
	//		&depositAccount.AccountName,
	//		&depositAccount.AccountNameDisplay,
	//		&depositAccount.AccountNumber,
	//		&depositAccount.PhoneNumber,
	//		&depositAccount.AutoTransfer,
	//		&depositAccount.PushBulletNickname,
	//		&depositAccount.PushBulletToken,
	//		&depositAccount.Active,
	//		&depositAccount.Inactive,
	//		&depositAccount.CreatedAt,
	//		&depositAccount.UpdatedAt,
	//		&total,
	//	); err != nil {
	//		r.logger.WithError(err).Error("failed to scan deposit account row")
	//		return nil, 0, err
	//	}
	//	depositAccounts = append(depositAccounts, depositAccount)
	//}
	//
	//if err := rows.Err(); err != nil {
	//	r.logger.WithError(err).Error("error iterating deposit account rows")
	//	return nil, 0, err
	//}
	//
	//return depositAccounts, total, nil
}

func (r *DepositAccount) FindByID(ctx context.Context, id int64) (*deposit_account.DepositAccount, error) {
	depositAccount := deposit_account.DepositAccount{}
	query := `
		SELECT 
			da.id,
			da.banking_id,
			b.name as banking_name,
			b.image_url as banking_image_url,
			da.payment_method_id,
			da.algorithm_id,
			a.name as algorithm_name,
			da.auto_bot_id,
			da.account_name,
			da.account_name_display,
			da.account_number,
			da.phone_number,
			da.auto_transfer,
			da.push_bullet_nickname,
			da.push_bullet_token,
			da.active,
			da.inactive,
			da.created_at,
			da.updated_at
		FROM deposit_account da
		LEFT JOIN banking b ON da.banking_id = b.id
		LEFT JOIN algorithm a ON da.algorithm_id = a.id
		WHERE da.id = $1 AND da.inactive = false
	`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(
		&depositAccount.ID,
		&depositAccount.BankingID,
		&depositAccount.BankingName,
		&depositAccount.BankingImageUrl,
		&depositAccount.PaymentMethodID,
		&depositAccount.AlgorithmID,
		&depositAccount.AlgorithmName,
		&depositAccount.AutoBotID,
		&depositAccount.AccountName,
		&depositAccount.AccountNameDisplay,
		&depositAccount.AccountNumber,
		&depositAccount.PhoneNumber,
		&depositAccount.AutoTransfer,
		&depositAccount.PushBulletNickname,
		&depositAccount.PushBulletToken,
		&depositAccount.Active,
		&depositAccount.Inactive,
		&depositAccount.CreatedAt,
		&depositAccount.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("deposit account not found")
		}
		r.logger.WithError(err).Error("failed to find deposit account by ID")
		return nil, errors.NewDatabaseError("failed to find deposit account by ID")
	}

	return &depositAccount, nil
}

func (r *DepositAccount) FindDepositAccountSettingAlgorithmByID(ctx context.Context, id int64) (*deposit_account.DepositAccount, error) {
	depositAccount := deposit_account.DepositAccount{}
	query := `SELECT * FROM deposit_account WHERE id = $1 AND inactive = false`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(
		&depositAccount.ID,
		&depositAccount.BankingID,
		&depositAccount.PaymentMethodID,
		&depositAccount.AlgorithmID,
		&depositAccount.AutoBotID,
		&depositAccount.AccountName,
		&depositAccount.AccountNameDisplay,
		&depositAccount.AccountNumber,
		&depositAccount.PhoneNumber,
		&depositAccount.AutoTransfer,
		&depositAccount.PushBulletNickname,
		&depositAccount.PushBulletToken,
		&depositAccount.Active,
		&depositAccount.Inactive,
		&depositAccount.CreatedAt,
		&depositAccount.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("deposit account not found")
		}
		r.logger.WithError(err).Error("failed to find deposit account by ID")
		return nil, errors.NewDatabaseError("failed to find deposit account by ID")
	}

	return &depositAccount, nil
}

func (r *DepositAccount) FindByAccountNumberDuplicate(ctx context.Context, accountNumber string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM deposit_account WHERE account_number = $1 AND inactive = false)`
	var exists bool

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, accountNumber).Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check for duplicate account number")
		return false, errors.NewDatabaseError("failed to check for duplicate account number")
	}

	return exists, nil
}

func (r *DepositAccount) FindByAccountNumberDuplicateAndIdNot(ctx context.Context, accountNumber string, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM deposit_account WHERE account_number = $1 AND id != $2 AND inactive = false)`
	var exists bool

	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, accountNumber, id).Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check for duplicate account number excluding specific ID")
		return false, errors.NewDatabaseError("failed to check for duplicate account number excluding specific ID")
	}

	return exists, nil
}

func (r *DepositAccount) Update(ctx context.Context, id int64, req *deposit_account.DepositAccountRequest) error {
	query := `
		UPDATE deposit_account 
		SET banking_id = $2, payment_method_id = $3, account_name = $4, account_name_display = $5, account_number = $6, phone_number = $7, auto_transfer = $8
		WHERE id = $1
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		id, req.BankingID, req.PaymentMethodID, req.AccountName, req.AccountNameDisplay, req.AccountNumber, req.PhoneNumber, req.AutoTransfer)
	if err != nil {
		r.logger.WithError(err).Error("failed to update deposit account")
		return errors.NewDatabaseError("failed to update deposit account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating deposit account")
		return errors.NewNotFoundError("deposit account not found")
	}

	return nil
}

func (r *DepositAccount) UpdateAutoBot(ctx context.Context, id int64, autoBotID int64) error {
	query := `
		UPDATE deposit_account
		SET auto_bot_id = $2
		WHERE id = $1
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id, autoBotID)
	if err != nil {
		r.logger.WithError(err).Error("failed to update auto bot ID for deposit account")
		return errors.NewDatabaseError("failed to update auto bot ID for deposit account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating auto bot ID for deposit account")
		return errors.NewNotFoundError("deposit account not found")
	}

	return nil
}

func (r *DepositAccount) UpdateAlgorithm(ctx context.Context, id int64, req *deposit_account.DepositAccountSettingAlgorithm) error {
	query := `
		UPDATE deposit_account
		SET algorithm_id = $2, push_bullet_nickname = $3, push_bullet_token = $4
		WHERE id = $1
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id, req.AlgorithmID, req.PushBulletNickname, req.PushBulletToken)
	if err != nil {
		r.logger.WithError(err).Error("failed to update deposit account algorithm settings")
		return errors.NewDatabaseError("failed to update deposit account algorithm settings")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating deposit account algorithm settings")
		return errors.NewNotFoundError("deposit account not found")
	}

	return nil
}

func (r *DepositAccount) Active(ctx context.Context, id int64, status bool) error {
	query := `
		UPDATE deposit_account
		SET active = $2
		WHERE id = $1
		
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id, status)
	if err != nil {
		r.logger.WithError(err).Error("failed to update deposit account active status")
		return errors.NewDatabaseError("failed to update deposit account active status")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating deposit account active status")
		return errors.NewNotFoundError("deposit account not found")
	}

	return nil
}

func (r *DepositAccount) Delete(ctx context.Context, id int64) error {
	query := `
		UPDATE deposit_account
		SET inactive = true
		WHERE id = $1 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete deposit account")
		return errors.NewDatabaseError("failed to delete deposit account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when deleting deposit account")
		return errors.NewNotFoundError("deposit account not found")
	}

	return nil
}
