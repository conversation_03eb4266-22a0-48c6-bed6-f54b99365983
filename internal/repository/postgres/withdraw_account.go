package postgres

import (
	"blacking-api/internal/domain/withdraw_account"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type WithdrawAccount struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewWithdrawAccountRepository(pool *pgxpool.Pool, logger logger.Logger) *WithdrawAccount {
	return &WithdrawAccount{
		pool:   pool,
		logger: logger,
	}
}

func (r *WithdrawAccount) Create(ctx context.Context, req *withdraw_account.WithdrawAccountRequest) error {
	query := `INSERT INTO withdraw_account (auto_bot_id, banking_id, account_name, account_name_display, account_number, phone_number, minimum_withdraw, maximum_withdraw, withdraw_splitting, maximum_withdraw_per_transaction, maximum_split_withdraw_per_transaction, limit_auto_transfer)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
	`
	_, err := dbutil.ExecWithSchema(
		ctx, r.pool, query,
		1,
		req.BankingID,
		req.AccountName,
		req.AccountNameDisplay,
		req.AccountNumber,
		req.PhoneNumber,
		req.MinimumWithdraw,
		req.MaximumWithdraw,
		req.WithdrawSplitting,
		req.MaximumWithdrawPerTransaction,
		req.MaximumSplitWithdrawPerTransaction,
		req.LimitAutoTransfer,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to insert withdraw account")
		return err
	}

	return nil
}

func (r *WithdrawAccount) FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*withdraw_account.WithdrawAccount, int64, error) {
	var withdrawAccounts []*withdraw_account.WithdrawAccount
	var total int64
	var err error

	argLast := len(args)
	cteQuery := fmt.Sprintf(`
		WITH counted_data AS (
			SELECT 
				wa.id, 
				wa.banking_id, 
				wa.account_name, 
				wa.account_name_display, 
				wa.account_number, 
				wa.phone_number,
				wa.auto_bot_id,
				wa.minimum_withdraw, 
				wa.maximum_withdraw, 
				wa.withdraw_splitting, 
				wa.maximum_withdraw_per_transaction, 
				wa.maximum_split_withdraw_per_transaction, 
				wa.limit_auto_transfer,
				wa.push_bullet_nickname,
				wa.push_bullet_token,
				wa.identification_number,
				wa.laser_id,
				wa.pin,
				wa.encryption_key,
				wa.device_id,
				wa.file_json_url,
				wa.birthday,
 				wa.active,
				wa.inactive,
				wa.created_at,
				wa.updated_at,
				b.name as banking_name,
				b.image_url as banking_image_url,
				a.id as algorithm_id,
				a.name as algorithm_name,
				COUNT(*) OVER() as total_count
			FROM withdraw_account wa
			LEFT JOIN banking b ON wa.banking_id = b.id
			LEFT JOIN algorithm a ON wa.algorithm_id = a.id
			%s
			WHERE inactive = false
			LIMIT $%v OFFSET $%v
		)
		SELECT 
			id, banking_id, 
			account_name, 
			account_name_display, 
			account_number, 
			phone_number,
			auto_bot_id,
			minimum_withdraw, 
			maximum_withdraw, 
			withdraw_splitting, 
			maximum_withdraw_per_transaction, 
			maximum_split_withdraw_per_transaction, 
			limit_auto_transfer,
			push_bullet_nickname,
			push_bullet_token,
			identification_number,
			laser_id,
			pin,
			encryption_key,
			device_id,
			file_json_url,
			birthday,
			active,
			inactive,
			created_at,
			updated_at,
			banking_name,
			banking_image_url,
			algorithm_id,
			algorithm_name,
			total_count
		FROM counted_data
	`, whereClause, argLast+1, argLast+2)
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, cteQuery, append(args, limit, offset)...)
	if err != nil {
		r.logger.WithError(err).Error("failed to find all withdraw accounts")
		return nil, 0, err
	}
	defer rows.Close()

	for rows.Next() {
		var withdrawAccount withdraw_account.WithdrawAccount
		if err := rows.Scan(
			&withdrawAccount.ID,
			&withdrawAccount.BankingID,
			&withdrawAccount.AccountName,
			&withdrawAccount.AccountNameDisplay,
			&withdrawAccount.AccountNumber,
			&withdrawAccount.PhoneNumber,
			&withdrawAccount.AutoBotID,
			&withdrawAccount.MinimumWithdraw,
			&withdrawAccount.MaximumWithdraw,
			&withdrawAccount.WithdrawSplitting,
			&withdrawAccount.MaximumWithdrawPerTransaction,
			&withdrawAccount.MaximumSplitWithdrawPerTransaction,
			&withdrawAccount.LimitAutoTransfer,
			&withdrawAccount.PushBulletNickname,
			&withdrawAccount.PushBulletToken,
			&withdrawAccount.IdentificationNumber,
			&withdrawAccount.LaserId,
			&withdrawAccount.Pin,
			&withdrawAccount.EncryptionKey,
			&withdrawAccount.DeviceId,
			&withdrawAccount.FileJsonUrl,
			&withdrawAccount.BirthDay,
			&withdrawAccount.Active,
			&withdrawAccount.Inactive,
			&withdrawAccount.CreatedAt,
			&withdrawAccount.UpdatedAt,
			&withdrawAccount.BankingName,
			&withdrawAccount.BankingImageUrl,
			&withdrawAccount.AlgorithmID,
			&withdrawAccount.AlgorithmName,
			&total,
		); err != nil {
			r.logger.WithError(err).Error("failed to scan withdraw account")
			return nil, 0, err
		}
		withdrawAccounts = append(withdrawAccounts, &withdrawAccount)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating withdraw account rows")
		return nil, 0, err
	}

	return withdrawAccounts, total, nil
}

func (r *WithdrawAccount) FindByID(ctx context.Context, id int64) (*withdraw_account.WithdrawAccount, error) {
	withdrawAccount := withdraw_account.WithdrawAccount{}
	query := `
		SELECT 
			wa.id, 
			wa.banking_id, 
			wa.account_name, 
			wa.account_name_display, 
			wa.account_number, 
			wa.phone_number,
			wa.auto_bot_id,
			wa.minimum_withdraw, 
			wa.maximum_withdraw, 
			wa.withdraw_splitting, 
			wa.maximum_withdraw_per_transaction, 
			wa.maximum_split_withdraw_per_transaction, 
			wa.limit_auto_transfer,
			wa.push_bullet_nickname,
			wa.push_bullet_token,
			wa.identification_number,
			wa.laser_id,
			wa.pin,
			wa.encryption_key,
			wa.device_id,
			wa.file_json_url,
			wa.birthday,
			wa.active,
			wa.inactive,
			wa.created_at,
			wa.updated_at,
			b.name as banking_name,
			b.image_url as banking_image_url,
			a.id as algorithm_id,
			a.name as algorithm_name
		FROM withdraw_account wa
		LEFT JOIN banking b ON wa.banking_id = b.id
		LEFT JOIN algorithm a ON wa.algorithm_id = a.id
		WHERE wa.id = $1 AND wa.inactive = false
	`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	if err := row.Scan(
		&withdrawAccount.ID,
		&withdrawAccount.BankingID,
		&withdrawAccount.AccountName,
		&withdrawAccount.AccountNameDisplay,
		&withdrawAccount.AccountNumber,
		&withdrawAccount.PhoneNumber,
		&withdrawAccount.AutoBotID,
		&withdrawAccount.MinimumWithdraw,
		&withdrawAccount.MaximumWithdraw,
		&withdrawAccount.WithdrawSplitting,
		&withdrawAccount.MaximumWithdrawPerTransaction,
		&withdrawAccount.MaximumSplitWithdrawPerTransaction,
		&withdrawAccount.LimitAutoTransfer,
		&withdrawAccount.PushBulletNickname,
		&withdrawAccount.PushBulletToken,
		&withdrawAccount.IdentificationNumber,
		&withdrawAccount.LaserId,
		&withdrawAccount.Pin,
		&withdrawAccount.EncryptionKey,
		&withdrawAccount.DeviceId,
		&withdrawAccount.FileJsonUrl,
		&withdrawAccount.BirthDay,
		&withdrawAccount.Active,
		&withdrawAccount.Inactive,
		&withdrawAccount.CreatedAt,
		&withdrawAccount.UpdatedAt,
		&withdrawAccount.BankingName,
		&withdrawAccount.BankingImageUrl,
		&withdrawAccount.AlgorithmID,
		&withdrawAccount.AlgorithmName,
	); err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("withdraw account not found")
		}
		log.Printf("error iterating withdraw account rows: %v", err)
		r.logger.WithError(err).Error("failed to find withdraw account by ID")
		return nil, errors.NewDatabaseError("failed to find withdraw account by ID")
	}

	return &withdrawAccount, nil
}

func (r *WithdrawAccount) FindByAccountNumberDuplicate(ctx context.Context, accountNumber string) (bool, error) {
	query := `SELECT COUNT(*) FROM withdraw_account WHERE account_number = $1 AND inactive = false`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, accountNumber)

	var count int
	if err := row.Scan(&count); err != nil {
		r.logger.WithError(err).Error("failed to check duplicate account number")
		return false, err
	}

	return count > 0, nil
}

func (r *WithdrawAccount) FindByAccountNumberDuplicateAndIdNot(ctx context.Context, accountNumber string, id int64) (bool, error) {
	query := `SELECT COUNT(*) FROM withdraw_account WHERE account_number = $1 AND id != $2 AND inactive = false`
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, accountNumber, id)

	var count int
	if err := row.Scan(&count); err != nil {
		r.logger.WithError(err).Error("failed to check duplicate account number excluding specific ID")
		return false, err
	}

	return count > 0, nil
}

func (r *WithdrawAccount) Update(ctx context.Context, id int64, req *withdraw_account.WithdrawAccountRequest) error {
	query := `
		UPDATE withdraw_account
		SET banking_id = $1, account_name = $2, account_name_display = $3, account_number = $4,
			phone_number = $5, minimum_withdraw = $6, maximum_withdraw = $7, withdraw_splitting = $8,
			maximum_withdraw_per_transaction = $9, maximum_split_withdraw_per_transaction = $10, limit_auto_transfer = $11
		WHERE id = $12 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.BankingID,
		req.AccountName,
		req.AccountNameDisplay,
		req.AccountNumber,
		req.PhoneNumber,
		req.MinimumWithdraw,
		req.MaximumWithdraw,
		req.WithdrawSplitting,
		req.MaximumWithdrawPerTransaction,
		req.MaximumSplitWithdrawPerTransaction,
		req.LimitAutoTransfer,
		id,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to update withdraw account")
		return errors.NewDatabaseError("failed to update withdraw account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating withdraw account")
		return errors.NewNotFoundError("withdraw account not found")
	}

	return nil
}

func (r *WithdrawAccount) UpdateAutoBot(ctx context.Context, id int64, autoBotID int64) error {
	query := `
		UPDATE withdraw_account
		SET auto_bot_id = $1
		WHERE id = $2 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, autoBotID, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to update withdraw account auto bot")
		return errors.NewDatabaseError("failed to update withdraw account auto bot")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating withdraw account auto bot")
		return errors.NewNotFoundError("withdraw account not found")
	}

	return nil
}

func (r *WithdrawAccount) UpdateAlgorithm(ctx context.Context, id int64, req *withdraw_account.WithdrawAccountSettingAlgorithmRequest) error {
	query := `
		UPDATE withdraw_account
		SET algorithm_id = $1, identification_number = $2, laser_id = $3, birthday = $4,
			pin = $5, encryption_key = $6, device_id = $7, file_json_url = $8,
			push_bullet_token = $9, push_bullet_nickname = $10
		WHERE id = $11 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		int64(req.AlgorithmID),
		req.IdentificationNumber,
		req.LaserId,
		req.BirthDay,
		req.Pin,
		req.EncryptionKey,
		req.DeviceId,
		req.FileJsonUrl,
		req.PushBulletToken,
		req.PushBulletNickname,
		id,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to update withdraw account algorithm settings")
		return errors.NewDatabaseError("failed to update withdraw account algorithm settings")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating withdraw account algorithm settings")
		return errors.NewNotFoundError("withdraw account not found")
	}

	return nil
}

func (r *WithdrawAccount) Active(ctx context.Context, id int64, status bool) error {
	query := `
		UPDATE withdraw_account
		SET active = $1, inactive = NOT $1
		WHERE id = $2 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, status, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to update withdraw account active status")
		return errors.NewDatabaseError("failed to update withdraw account active status")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating withdraw account active status")
		return errors.NewNotFoundError("withdraw account not found")
	}

	return nil
}

func (r *WithdrawAccount) Delete(ctx context.Context, id int64) error {
	query := `
		UPDATE withdraw_account
		SET inactive = true
		WHERE id = $1 AND inactive = false
	`
	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete withdraw account")
		return errors.NewDatabaseError("failed to delete withdraw account")
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when deleting withdraw account")
		return errors.NewNotFoundError("withdraw account not found")
	}

	return nil
}
