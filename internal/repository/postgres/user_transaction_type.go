package postgres

import (
	"blacking-api/internal/domain/user_transaction_type"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
)

type UserTransactionTypeRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewUserTransactionTypeRepository creates a new user transaction type repository
func NewUserTransactionTypeRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.UserTransactionTypeRepository {
	return &UserTransactionTypeRepository{
		pool:   pool,
		logger: logger,
	}
}

// List retrieves all user transaction types
func (r *UserTransactionTypeRepository) List(ctx context.Context) ([]*user_transaction_type.UserTransactionType, error) {
	query := `
		SELECT id, name, detail, created_at
		FROM user_transaction_type
		ORDER BY id ASC
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to query user transaction types")
		return nil, errors.NewDatabaseError("failed to retrieve user transaction types")
	}
	defer rows.Close()

	var transactionTypes []*user_transaction_type.UserTransactionType
	for rows.Next() {
		var transactionType user_transaction_type.UserTransactionType
		err := rows.Scan(
			&transactionType.ID,
			&transactionType.Name,
			&transactionType.Detail,
			&transactionType.CreatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan user transaction type")
			return nil, errors.NewDatabaseError("failed to scan user transaction type")
		}
		transactionTypes = append(transactionTypes, &transactionType)
	}

	if err := rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating user transaction type rows")
		return nil, errors.NewDatabaseError("failed to retrieve user transaction types")
	}

	r.logger.WithField("count", len(transactionTypes)).Info("user transaction types retrieved successfully")
	return transactionTypes, nil
}
