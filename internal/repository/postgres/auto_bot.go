package postgres

import (
	"blacking-api/internal/domain/auto_bot"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
)

type AutoBot struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewAutoBotRepository(pool *pgxpool.Pool, logger logger.Logger) *AutoBot {
	return &AutoBot{
		pool:   pool,
		logger: logger,
	}
}

func (r *AutoBot) List(ctx context.Context) ([]*auto_bot.AutoBot, error) {
	var autoBots []*auto_bot.AutoBot

	query := `SELECT id, name FROM auto_bot ORDER BY id ASC`
	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to query auto bot settings")
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		autoBot := &auto_bot.AutoBot{}
		if err := rows.Scan(&autoBot.ID, &autoBot.Name); err != nil {
			r.logger.WithError(err).Error("failed to scan auto bot setting")
			return nil, err
		}
		autoBots = append(autoBots, autoBot)
	}

	return autoBots, nil
}

func (r *AutoBot) FindIdExists(ctx context.Context, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM auto_bot WHERE id = $1)`
	var exists bool

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check if auto bot ID exists")
		return false, err
	}

	return exists, nil
}
