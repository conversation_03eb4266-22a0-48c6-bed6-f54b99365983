package postgres

import (
	"blacking-api/internal/domain/otp"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type OTPRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewOTPRepository creates a new OTP repository
func NewOTPRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.OTPRepository {
	return &OTPRepository{
		pool:   pool,
		logger: logger,
	}
}

// <PERSON><PERSON> creates a new OTP record
func (r *OTPRepository) Create(ctx context.Context, otpRecord *otp.OTP) error {
	query := `
		INSERT INTO otps (type, purpose, recipient, code, reference, is_used, expires_at, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		otpRecord.Type,
		otpRecord.Purpose,
		otpRecord.Recipient,
		otpRecord.Code,
		otpRecord.Reference,
		otpRecord.IsUsed,
		otpRecord.ExpiresAt,
		otpRecord.CreatedAt,
	)
	err := row.Scan(&otpRecord.ID)

	if err != nil {
		r.logger.WithError(err).Error("failed to create OTP record")
		return errors.NewDatabaseError("failed to create OTP record")
	}

	r.logger.WithField("otp_id", otpRecord.ID).WithField("recipient", otpRecord.Recipient).Info("OTP record created")
	return nil
}

// GetByRecipientAndPurpose retrieves the latest valid OTP for recipient and purpose
func (r *OTPRepository) GetByRecipientAndPurpose(ctx context.Context, recipient string, purpose otp.OTPPurpose) (*otp.OTP, error) {
	query := `
		SELECT id, type, purpose, recipient, code, reference, is_used, expires_at, created_at, used_at
		FROM otps
		WHERE recipient = $1 AND purpose = $2 AND is_used = false AND expires_at > NOW()
		ORDER BY created_at DESC
		LIMIT 1
	`

	otpRecord := &otp.OTP{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, recipient, purpose).Scan(
		&otpRecord.ID,
		&otpRecord.Type,
		&otpRecord.Purpose,
		&otpRecord.Recipient,
		&otpRecord.Code,
		&otpRecord.Reference,
		&otpRecord.IsUsed,
		&otpRecord.ExpiresAt,
		&otpRecord.CreatedAt,
		&otpRecord.UsedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("no valid OTP found")
		}
		r.logger.WithError(err).Error("failed to get OTP by recipient and purpose")
		return nil, errors.NewDatabaseError("failed to get OTP")
	}

	return otpRecord, nil
}

// GetByRecipientAndPurposeIsUsed retrieves the latest valid OTP for recipient and purpose
func (r *OTPRepository) GetByRecipientAndPurposeIsUsed(ctx context.Context, recipient string, purpose otp.OTPPurpose) (*otp.OTP, error) {
	query := `
		SELECT id, type, purpose, recipient, code, reference, is_used, expires_at, created_at, used_at
		FROM otps
		WHERE recipient = $1 AND purpose = $2 AND is_used = true 
		ORDER BY created_at DESC
		LIMIT 1
	`

	otpRecord := &otp.OTP{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, recipient, purpose).Scan(
		&otpRecord.ID,
		&otpRecord.Type,
		&otpRecord.Purpose,
		&otpRecord.Recipient,
		&otpRecord.Code,
		&otpRecord.Reference,
		&otpRecord.IsUsed,
		&otpRecord.ExpiresAt,
		&otpRecord.CreatedAt,
		&otpRecord.UsedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("no valid OTP found")
		}
		r.logger.WithError(err).Error("failed to get OTP by recipient and purpose")
		return nil, errors.NewDatabaseError("failed to get OTP")
	}

	return otpRecord, nil
}

// Update updates an existing OTP record
func (r *OTPRepository) Update(ctx context.Context, otpRecord *otp.OTP) error {
	query := `
		UPDATE otps 
		SET is_used = $1, used_at = $2
		WHERE id = $3
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		otpRecord.IsUsed,
		otpRecord.UsedAt,
		otpRecord.ID,
	)

	if err != nil {
		r.logger.WithError(err).Error("failed to update OTP record")
		return errors.NewDatabaseError("failed to update OTP record")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("OTP record not found")
	}

	r.logger.WithField("otp_id", otpRecord.ID).Info("OTP record updated")
	return nil
}

// DeleteExpired deletes all expired OTP records
func (r *OTPRepository) DeleteExpired(ctx context.Context) error {
	query := `
		DELETE FROM otps 
		WHERE expires_at < NOW()
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete expired OTP records")
		return errors.NewDatabaseError("failed to delete expired OTP records")
	}

	r.logger.WithField("rows_deleted", result.RowsAffected()).Info("expired OTP records deleted")
	return nil
}

// GetByID retrieves OTP by ID
func (r *OTPRepository) GetByID(ctx context.Context, id string) (*otp.OTP, error) {
	query := `
		SELECT id, type, purpose, recipient, code, reference, is_used, expires_at, created_at, used_at
		FROM otps
		WHERE id = $1
	`

	otpRecord := &otp.OTP{}
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, id).Scan(
		&otpRecord.ID,
		&otpRecord.Type,
		&otpRecord.Purpose,
		&otpRecord.Recipient,
		&otpRecord.Code,
		&otpRecord.Reference,
		&otpRecord.IsUsed,
		&otpRecord.ExpiresAt,
		&otpRecord.CreatedAt,
		&otpRecord.UsedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("OTP not found")
		}
		r.logger.WithError(err).Error("failed to get OTP by ID")
		return nil, errors.NewDatabaseError("failed to get OTP")
	}

	return otpRecord, nil
}

// ListForAdmin retrieves OTPs with pagination and filters for admin
func (r *OTPRepository) ListForAdmin(ctx context.Context, limit, offset int, search, purpose, dateFrom, dateTo string) ([]*otp.OTP, error) {
	query := `
		SELECT id, type, purpose, recipient, code, reference, is_used, expires_at, created_at, used_at
		FROM otps
		WHERE 1=1
	`
	args := []interface{}{}
	argIndex := 1

	// Add filters
	if search != "" {
		query += fmt.Sprintf(" AND recipient ILIKE $%d", argIndex)
		args = append(args, "%"+search+"%")
		argIndex++
	}

	if purpose != "" {
		query += fmt.Sprintf(" AND purpose = $%d", argIndex)
		args = append(args, purpose)
		argIndex++
	}

	if dateFrom != "" {
		query += fmt.Sprintf(" AND DATE(created_at) >= $%d", argIndex)
		args = append(args, dateFrom)
		argIndex++
	}

	if dateTo != "" {
		query += fmt.Sprintf(" AND DATE(created_at) <= $%d", argIndex)
		args = append(args, dateTo)
		argIndex++
	}

	// Order by created_at DESC
	query += " ORDER BY created_at DESC"

	// Add pagination
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, limit)
		argIndex++
	}

	if offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIndex)
		args = append(args, offset)
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to list OTPs for admin")
		return nil, errors.NewDatabaseError("failed to list OTPs")
	}
	defer rows.Close()

	var otps []*otp.OTP
	for rows.Next() {
		otpRecord := &otp.OTP{}
		err := rows.Scan(
			&otpRecord.ID,
			&otpRecord.Type,
			&otpRecord.Purpose,
			&otpRecord.Recipient,
			&otpRecord.Code,
			&otpRecord.Reference,
			&otpRecord.IsUsed,
			&otpRecord.ExpiresAt,
			&otpRecord.CreatedAt,
			&otpRecord.UsedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan OTP record")
			return nil, errors.NewDatabaseError("failed to scan OTP record")
		}
		otps = append(otps, otpRecord)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating OTP rows")
		return nil, errors.NewDatabaseError("error iterating OTP rows")
	}

	return otps, nil
}

// CountForAdmin returns total count of OTPs with filters for admin
func (r *OTPRepository) CountForAdmin(ctx context.Context, search, purpose, dateFrom, dateTo string) (int64, error) {
	query := `
		SELECT COUNT(*)
		FROM otps
		WHERE 1=1
	`
	args := []interface{}{}
	argIndex := 1

	// Add same filters as ListForAdmin
	if search != "" {
		query += fmt.Sprintf(" AND recipient ILIKE $%d", argIndex)
		args = append(args, "%"+search+"%")
		argIndex++
	}

	if purpose != "" {
		query += fmt.Sprintf(" AND purpose = $%d", argIndex)
		args = append(args, purpose)
		argIndex++
	}

	if dateFrom != "" {
		query += fmt.Sprintf(" AND DATE(created_at) >= $%d", argIndex)
		args = append(args, dateFrom)
		argIndex++
	}

	if dateTo != "" {
		query += fmt.Sprintf(" AND DATE(created_at) <= $%d", argIndex)
		args = append(args, dateTo)
	}

	var count int64
	err := dbutil.QueryRowWithSchema(ctx, r.pool, query, args...).Scan(&count)
	if err != nil {
		r.logger.WithError(err).Error("failed to count OTPs for admin")
		return 0, errors.NewDatabaseError("failed to count OTPs")
	}

	return count, nil
}
