package postgres

import (
	"blacking-api/internal/domain/sms_provider"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type SMSProviderRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

func NewSMSProviderRepository(pool *pgxpool.Pool, logger logger.Logger) *SMSProviderRepository {
	return &SMSProviderRepository{
		pool:   pool,
		logger: logger,
	}
}

func (r *SMSProviderRepository) Create(ctx context.Context, req *sms_provider.SMSProviderRequest) error {
	query := `INSERT INTO sms_provider (name, provider_name_id, api_key, secret_key, otp_sender, sms_sender, prefix_otp, active, inactive) 
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`
	_, err := dbutil.ExecWithSchema(
		ctx, r.pool, query,
		req.Name,
		req.ProviderNameID,
		req.APIKey,
		req.SecretKey,
		req.OTPSender,
		req.SMSSender,
		req.PrefixOTP,
		true,  // active
		false, // inactive
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to create SMS provider")
		return errors.NewDatabaseError("failed to create SMS provider")
	}

	return nil
}

func (r *SMSProviderRepository) FindAll(ctx context.Context, limit int, offset int, whereClause string, args []interface{}) ([]*sms_provider.SMSProvider, int64, error) {
	var smsProviders []*sms_provider.SMSProvider

	// Build the main query
	query := `
		SELECT sp.id, sp.name, sp.provider_name_id, sp.api_key, sp.secret_key, 
		       sp.otp_sender, sp.sms_sender, sp.prefix_otp, sp.balance, 
		       sp.is_sms, sp.is_otp, sp.active, sp.inactive, sp.created_at, sp.updated_at
		FROM sms_provider sp
		WHERE sp.inactive = false
	`

	// Add additional where conditions if provided
	if whereClause != "" {
		query += " AND " + whereClause[6:] // Remove "WHERE " prefix
	}

	query += " ORDER BY sp.created_at DESC"

	// Add pagination if limit > 0
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", len(args)+1, len(args)+2)
		args = append(args, limit, offset)
	}

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query, args...)
	if err != nil {
		r.logger.WithError(err).Error("failed to find all SMS providers")
		return nil, 0, errors.NewDatabaseError("failed to find SMS providers")
	}
	defer rows.Close()

	for rows.Next() {
		sp := &sms_provider.SMSProvider{}
		err := rows.Scan(
			&sp.ID, &sp.Name, &sp.ProviderNameID, &sp.APIKey, &sp.SecretKey,
			&sp.OTPSender, &sp.SMSSender, &sp.PrefixOTP, &sp.Balance,
			&sp.IsSMS, &sp.IsOTP, &sp.Active, &sp.Inactive, &sp.CreatedAt, &sp.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan SMS provider")
			return nil, 0, errors.NewDatabaseError("failed to scan SMS provider")
		}
		smsProviders = append(smsProviders, sp)
	}

	// Get total count
	countQuery := `
		SELECT COUNT(*)
		FROM sms_provider sp
		WHERE sp.inactive = false
	`

	// Add additional where conditions for count if provided
	if whereClause != "" {
		countQuery += " AND " + whereClause[6:] // Remove "WHERE " prefix
	}

	var total int64
	countArgs := args
	if limit > 0 {
		// Remove limit and offset from count args
		countArgs = args[:len(args)-2]
	}

	row := dbutil.QueryRowWithSchema(ctx, r.pool, countQuery, countArgs...)
	err = row.Scan(&total)
	if err != nil {
		r.logger.WithError(err).Error("failed to count SMS providers")
		return nil, 0, errors.NewDatabaseError("failed to count SMS providers")
	}

	return smsProviders, total, nil
}

func (r *SMSProviderRepository) FindByID(ctx context.Context, id int64) (*sms_provider.SMSProvider, error) {
	query := `
		SELECT id, name, provider_name_id, api_key, secret_key, 
		       otp_sender, sms_sender, prefix_otp, balance, 
		       is_sms, is_otp, active, inactive, created_at, updated_at
		FROM sms_provider
		WHERE id = $1 AND inactive = false
	`

	sp := &sms_provider.SMSProvider{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, id)
	err := row.Scan(
		&sp.ID, &sp.Name, &sp.ProviderNameID, &sp.APIKey, &sp.SecretKey,
		&sp.OTPSender, &sp.SMSSender, &sp.PrefixOTP, &sp.Balance,
		&sp.IsSMS, &sp.IsOTP, &sp.Active, &sp.Inactive, &sp.CreatedAt, &sp.UpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("SMS provider not found")
		}
		r.logger.WithError(err).Error("failed to find SMS provider by ID")
		return nil, errors.NewDatabaseError("failed to find SMS provider")
	}

	return sp, nil
}

func (r *SMSProviderRepository) FindByNameDuplicate(ctx context.Context, name string) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM sms_provider WHERE UPPER(name) = UPPER($1) AND inactive = false)`
	var exists bool

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, name)
	err := row.Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check SMS provider name duplicate")
		return false, errors.NewDatabaseError("failed to check SMS provider name")
	}

	return exists, nil
}

func (r *SMSProviderRepository) FindByNameDuplicateAndIdNot(ctx context.Context, name string, id int64) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM sms_provider WHERE UPPER(name) = UPPER($1) AND id != $2 AND inactive = false)`
	var exists bool

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, name, id)
	err := row.Scan(&exists)
	if err != nil {
		r.logger.WithError(err).Error("failed to check SMS provider name duplicate")
		return false, errors.NewDatabaseError("failed to check SMS provider name")
	}

	return exists, nil
}

func (r *SMSProviderRepository) Update(ctx context.Context, id int64, req *sms_provider.SMSProviderRequest) error {
	query := `UPDATE sms_provider 
			SET name = $1, provider_name_id = $2, api_key = $3, secret_key = $4, 
			    otp_sender = $5, sms_sender = $6, prefix_otp = $7, updated_at = CURRENT_TIMESTAMP
			WHERE id = $8 AND inactive = false`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		req.Name, req.ProviderNameID, req.APIKey, req.SecretKey,
		req.OTPSender, req.SMSSender, req.PrefixOTP, id,
	)
	if err != nil {
		r.logger.WithError(err).Error("failed to update SMS provider")
		return errors.NewDatabaseError("failed to update SMS provider")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("SMS provider not found")
	}

	return nil
}

func (r *SMSProviderRepository) UpdateStatus(ctx context.Context, id int64, name string, status bool) error {
	query := fmt.Sprintf(`
        UPDATE sms_provider 
        SET %s = $1
        WHERE id = $2 AND inactive = false
    `, name)

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, status, id)
	if err != nil {
		r.logger.Error("Failed to update SMS provider status", "error", err, "column", name)
		return err
	}

	if result.RowsAffected() == 0 {
		r.logger.WithField("id", id).Warn("no rows affected when updating SMS provider status")
		return errors.NewValidationError("SMS provider not found")
	}

	return nil
	//query := `UPDATE sms_provider SET active = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 AND UPPER(name) = UPPER($3) AND inactive = false`
	//
	//result, err := r.db.ExecContext(ctx, query, status, id, name)
	//if err != nil {
	//	r.logger.WithError(err).Error("failed to update SMS provider status")
	//	return errors.NewDatabaseError("failed to update SMS provider status")
	//}
	//
	//rowsAffected, err := result.RowsAffected()
	//if err != nil {
	//	r.logger.WithError(err).Error("failed to get rows affected")
	//	return errors.NewDatabaseError("failed to update SMS provider status")
	//}
	//
	//if rowsAffected == 0 {
	//	return errors.NewNotFoundError("SMS provider not found")
	//}
	//
	//return nil
}

func (r *SMSProviderRepository) Delete(ctx context.Context, id int64) error {
	query := `UPDATE sms_provider SET inactive = true, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND inactive = false`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, id)
	if err != nil {
		r.logger.WithError(err).Error("failed to delete SMS provider")
		return errors.NewDatabaseError("failed to delete SMS provider")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("SMS provider not found")
	}

	return nil
}
