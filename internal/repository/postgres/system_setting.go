package postgres

import (
	"blacking-api/internal/domain/system_setting"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/dbutil"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type SystemSettingRepository struct {
	pool   *pgxpool.Pool
	logger logger.Logger
}

// NewSystemSettingRepository creates a new system setting repository
func NewSystemSettingRepository(pool *pgxpool.Pool, logger logger.Logger) interfaces.SystemSettingRepository {
	return &SystemSettingRepository{
		pool:   pool,
		logger: logger,
	}
}

// GetByKey retrieves a system setting by key
func (r *SystemSettingRepository) GetByKey(ctx context.Context, key string) (*system_setting.SystemSetting, error) {
	query := `
		SELECT id, key, value, description, created_at, updated_at
		FROM system_settings
		WHERE key = $1
	`

	setting := &system_setting.SystemSetting{}
	row := dbutil.QueryRowWithSchema(ctx, r.pool, query, key)
	err := row.Scan(
		&setting.ID,
		&setting.Key,
		&setting.Value,
		&setting.Description,
		&setting.CreatedAt,
		&setting.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.NewNotFoundError("system setting not found")
		}
		r.logger.WithError(err).WithField("key", key).Error("failed to get system setting by key")
		return nil, errors.NewDatabaseError("failed to get system setting")
	}

	return setting, nil
}

// Update updates a system setting
func (r *SystemSettingRepository) Update(ctx context.Context, setting *system_setting.SystemSetting) error {
	query := `
		UPDATE system_settings 
		SET value = $2, description = $3, updated_at = $4
		WHERE key = $1
	`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query,
		setting.Key,
		setting.Value,
		setting.Description,
		setting.UpdatedAt,
	)

	if err != nil {
		r.logger.WithError(err).WithField("key", setting.Key).Error("failed to update system setting")
		return errors.NewDatabaseError("failed to update system setting")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("system setting not found")
	}

	r.logger.WithField("key", setting.Key).Info("system setting updated successfully")
	return nil
}

// Create creates a new system setting
func (r *SystemSettingRepository) Create(ctx context.Context, setting *system_setting.SystemSetting) error {
	query := `
		INSERT INTO system_settings (key, value, description, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		setting.Key,
		setting.Value,
		setting.Description,
		setting.CreatedAt,
		setting.UpdatedAt,
	)
	err := row.Scan(&setting.ID)

	if err != nil {
		r.logger.WithError(err).WithField("key", setting.Key).Error("failed to create system setting")
		return errors.NewDatabaseError("failed to create system setting")
	}

	r.logger.WithField("key", setting.Key).Info("system setting created successfully")
	return nil
}

// Upsert creates or updates a system setting
func (r *SystemSettingRepository) Upsert(ctx context.Context, setting *system_setting.SystemSetting) error {
	query := `
		INSERT INTO system_settings (key, value, description, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (key) 
		DO UPDATE SET 
			value = EXCLUDED.value,
			description = EXCLUDED.description,
			updated_at = EXCLUDED.updated_at
		RETURNING id
	`

	row := dbutil.QueryRowWithSchema(ctx, r.pool, query,
		setting.Key,
		setting.Value,
		setting.Description,
		setting.CreatedAt,
		setting.UpdatedAt,
	)
	err := row.Scan(&setting.ID)

	if err != nil {
		r.logger.WithError(err).WithField("key", setting.Key).Error("failed to upsert system setting")
		return errors.NewDatabaseError("failed to upsert system setting")
	}

	r.logger.WithField("key", setting.Key).Info("system setting upserted successfully")
	return nil
}

// List retrieves all system settings
func (r *SystemSettingRepository) List(ctx context.Context) ([]*system_setting.SystemSetting, error) {
	query := `
		SELECT id, key, value, description, created_at, updated_at
		FROM system_settings
		ORDER BY key
	`

	rows, err := dbutil.QueryWithSchema(ctx, r.pool, query)
	if err != nil {
		r.logger.WithError(err).Error("failed to list system settings")
		return nil, errors.NewDatabaseError("failed to list system settings")
	}
	defer rows.Close()

	var settings []*system_setting.SystemSetting
	for rows.Next() {
		setting := &system_setting.SystemSetting{}
		err := rows.Scan(
			&setting.ID,
			&setting.Key,
			&setting.Value,
			&setting.Description,
			&setting.CreatedAt,
			&setting.UpdatedAt,
		)
		if err != nil {
			r.logger.WithError(err).Error("failed to scan system setting")
			return nil, errors.NewDatabaseError("failed to scan system setting")
		}
		settings = append(settings, setting)
	}

	if err = rows.Err(); err != nil {
		r.logger.WithError(err).Error("error iterating system setting rows")
		return nil, errors.NewDatabaseError("error iterating system setting rows")
	}

	return settings, nil
}

// Delete deletes a system setting by key
func (r *SystemSettingRepository) Delete(ctx context.Context, key string) error {
	query := `DELETE FROM system_settings WHERE key = $1`

	result, err := dbutil.ExecWithSchema(ctx, r.pool, query, key)
	if err != nil {
		r.logger.WithError(err).WithField("key", key).Error("failed to delete system setting")
		return errors.NewDatabaseError("failed to delete system setting")
	}

	if result.RowsAffected() == 0 {
		return errors.NewNotFoundError("system setting not found")
	}

	r.logger.WithField("key", key).Info("system setting deleted successfully")
	return nil
}
