package aws_s3

import (
	"blacking-api/infrastructure/database"
	"blacking-api/internal/domain/aws_s3"
	"bytes"
	"context"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"io"
	"log"
	"math/rand"
	"net/http"
	"os"
	"strings"
	"time"
)

type AWSS3 struct {
	db *database.Database
}

func NewAWSS3Repository(db *database.Database) *AWSS3 {
	return &AWSS3{db: db}
}

func (a *AWSS3) UploadFileToS3(ctx context.Context, pathUpload string, fileReader io.Reader) (*aws_s3.FileUploadResponse, error) {
	bucketName := os.Getenv("AWS_S3_BUCKET_NAME")

	randomFilename := generateRandomString()
	fileKey := fmt.Sprintf("%s%s", pathUpload, randomFilename)

	awsRegion := os.Getenv("AWS_S3_BUCKET_REGION")
	awsAccessKey := os.Getenv("AWS_S3_BUCKET_ACCESS_KEY_ID")
	awsSecretKey := os.Getenv("AWS_S3_BUCKET_SECRET_ACCESS_KEY")

	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(awsRegion),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			awsAccessKey,
			awsSecretKey,
			"",
		)),
	)
	if err != nil {
		log.Println("UploadImageToS3.LoadDefaultConfig.Error:", err)
		return nil, errors.New("failed to create AWS config")
	}

	s3Client := s3.NewFromConfig(cfg)

	body := &bytes.Buffer{}
	content, err := io.Copy(body, fileReader)
	if err != nil {
		log.Println("UploadImageToS3.Copy.Error:", err)
		return nil, err
	}

	input := &s3.PutObjectInput{
		Bucket:        aws.String(bucketName),
		Key:           aws.String(fileKey),
		Body:          bytes.NewReader(body.Bytes()),
		ContentLength: aws.Int64(content),
		ContentType:   aws.String(http.DetectContentType(body.Bytes())),
	}

	_, err = s3Client.PutObject(ctx, input)
	if err != nil {
		log.Println("UploadImageToS3.PutObject.Error:", err)
		return nil, fmt.Errorf("failed to upload file to S3: %w", err)
	}

	fileUrl := ""
	cloudFrontUrl := os.Getenv("AWS_S3_CLOUDFRONT_URL")
	if cloudFrontUrl == "" {
		fileUrl = fmt.Sprintf("https://cdn.irich.info/%s", fileKey)
	} else {
		fileUrl = fmt.Sprintf("%s/%s", cloudFrontUrl, fileKey)
	}

	response := &aws_s3.FileUploadResponse{
		FileUrl: fileUrl,
	}

	return response, nil
}

func (a *AWSS3) DeleteFileFromS3(ctx context.Context, filePathName string) error {
	cloudFrontUrl := os.Getenv("AWS_S3_CLOUDFRONT_URL")
	bucketName := os.Getenv("AWS_S3_BUCKET_NAME")

	awsRegion := os.Getenv("AWS_S3_BUCKET_REGION")
	awsAccessKey := os.Getenv("AWS_S3_BUCKET_ACCESS_KEY_ID")
	awsSecretKey := os.Getenv("AWS_S3_BUCKET_SECRET_ACCESS_KEY")

	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(awsRegion),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			awsAccessKey,
			awsSecretKey,
			"",
		)),
	)
	if err != nil {
		log.Println("DeleteFileFromS3.LoadDefaultConfig.Error:", err)
		return errors.New("failed to create AWS config")
	}

	s3Client := s3.NewFromConfig(cfg)

	fileKey := strings.TrimPrefix(filePathName, cloudFrontUrl)
	fileKey = strings.TrimPrefix(fileKey, "/")

	input := &s3.DeleteObjectInput{
		Bucket: aws.String(bucketName),
		Key:    &fileKey,
	}

	_, err = s3Client.DeleteObject(ctx, input)
	if err != nil {
		log.Println("DeleteFileFromS3.DeleteObject.Error:", err)
		return fmt.Errorf("failed to delete file from S3: %w", err)
	}

	return nil
}

func generateRandomString() string {
	currentTime := time.Now()
	randomPart := rand.Intn(1000) // Generate a random number between 0 and 999
	return fmt.Sprintf("%d%d%d_%d%d%d", currentTime.Year(), currentTime.Month(), currentTime.Day(), currentTime.Hour(), currentTime.Minute(), randomPart)
}
