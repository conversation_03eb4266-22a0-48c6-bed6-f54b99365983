package service

import (
	"blacking-api/internal/domain/theme_setting"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
)

// ThemeSettingService defines the interface for theme setting service operations
type ThemeSettingService interface {
	// GetTheme gets the current theme setting
	GetTheme(ctx context.Context) (*theme_setting.ThemeResponse, error)

	// SaveTheme saves the theme setting
	SaveTheme(ctx context.Context, req theme_setting.SaveThemeRequest, updatedBy int) (*theme_setting.ThemeResponse, error)
}

type themeSettingService struct {
	themeSettingRepo interfaces.ThemeSettingRepository
	logger           logger.Logger
}

// NewThemeSettingService creates a new theme setting service
func NewThemeSettingService(themeSettingRepo interfaces.ThemeSettingRepository, logger logger.Logger) ThemeSettingService {
	return &themeSettingService{
		themeSettingRepo: themeSettingRepo,
		logger:           logger,
	}
}

// GetTheme gets the current theme setting
func (s *themeSettingService) GetTheme(ctx context.Context) (*theme_setting.ThemeResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetTheme")

	setting, err := s.themeSettingRepo.Get(ctx)
	if err != nil {
		if errors.IsNotFoundError(err) {
			// If no theme setting exists, return default
			defaultTheme := &theme_setting.ThemeResponse{
				ThemeValue: "theme-a", // default theme
			}
			log.Info("no theme setting found, returning default")
			return defaultTheme, nil
		}
		log.WithError(err).Error("failed to get theme setting")
		return nil, err
	}

	response := setting.ToResponse()
	log.WithField("theme_value", setting.ThemeValue).Info("theme setting retrieved successfully")
	return &response, nil
}

// SaveTheme saves the theme setting
func (s *themeSettingService) SaveTheme(ctx context.Context, req theme_setting.SaveThemeRequest, updatedBy int) (*theme_setting.ThemeResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "SaveTheme").WithField("theme_value", req.ThemeValue)

	// Create new theme setting
	newSetting := theme_setting.NewThemeSetting(req.ThemeValue, updatedBy)

	// Use upsert to handle both create and update cases
	err := s.themeSettingRepo.Upsert(ctx, newSetting)
	if err != nil {
		log.WithError(err).Error("failed to save theme setting")
		return nil, errors.NewInternalError("failed to save theme setting")
	}

	response := newSetting.ToResponse()
	log.WithField("theme_value", req.ThemeValue).Info("theme setting saved successfully")
	return &response, nil
}