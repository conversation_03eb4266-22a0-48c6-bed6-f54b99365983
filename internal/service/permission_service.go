package service

import (
	"context"
	"strings"

	"blacking-api/internal/domain/permission"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

type PermissionService struct {
	permissionRepo         interfaces.PermissionRepository
	userRolePermissionRepo interfaces.UserRolePermissionRepository
	logger                 logger.Logger
}

func NewPermissionService(
	permissionRepo interfaces.PermissionRepository,
	userRolePermissionRepo interfaces.UserRolePermissionRepository,
	logger logger.Logger,
) *PermissionService {
	return &PermissionService{
		permissionRepo:         permissionRepo,
		userRolePermissionRepo: userRolePermissionRepo,
		logger:                 logger,
	}
}

// GetPermissionMatrixWithUserRole returns all permissions grouped by categories with user role permission data
func (s *PermissionService) GetPermissionMatrixWithUserRole(ctx context.Context, userRoleID int) (*PermissionMatrixResponse, error) {
	// Get all permissions with sub-permissions
	permissions, err := s.permissionRepo.ListWithSubPermissions(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get permissions")
		return nil, errors.NewInternalError("failed to get permissions")
	}

	// Get user role permissions
	userRolePermissions, err := s.userRolePermissionRepo.ListByUserRoleID(ctx, userRoleID)
	if err != nil {
		s.logger.WithError(err).WithField("user_role_id", userRoleID).Error("failed to get user role permissions")
		return nil, errors.NewInternalError("failed to get user role permissions")
	}

	// Convert to map for easier lookup
	permissionMap := make(map[string]*permission.UserRolePermission)
	for _, urp := range userRolePermissions {
		permissionMap[urp.PermissionKey] = urp
	}

	// Add user role permission data to permissions
	for _, perm := range permissions {
		if urp, exists := permissionMap[perm.Key]; exists {
			perm.UserRolePermission = &permission.UserRolePermissionData{
				CanCreate: urp.CanCreate,
				CanView:   urp.CanView,
				CanEdit:   urp.CanEdit,
				CanDelete: urp.CanDelete,
			}
		} else {
			// Default to false if no permission set
			perm.UserRolePermission = &permission.UserRolePermissionData{
				CanCreate: false,
				CanView:   false,
				CanEdit:   false,
				CanDelete: false,
			}
		}

		// Handle sub-permissions
		for i, subPerm := range perm.SubPermissions {
			if urp, exists := permissionMap[subPerm.Key]; exists {
				perm.SubPermissions[i].UserRolePermission = &permission.UserRolePermissionData{
					CanCreate: urp.CanCreate,
					CanView:   urp.CanView,
					CanEdit:   urp.CanEdit,
					CanDelete: urp.CanDelete,
				}
			} else {
				perm.SubPermissions[i].UserRolePermission = &permission.UserRolePermissionData{
					CanCreate: false,
					CanView:   false,
					CanEdit:   false,
					CanDelete: false,
				}
			}
		}
	}

	// Group permissions by key pattern (simple pattern-based grouping)
	permissionsByGroup := make(map[string][]*permission.Permission)

	// Define groups based on permission key patterns
	groupPatterns := []struct {
		key         string
		name        string
		patterns    []string
		description string
	}{
		{"dashboard", "แดชบอร์ด", []string{"dashboard"}, "หน้าแดชบอร์ดและภาพรวม"},
		{"user_management", "จัดการผู้ใช้", []string{"users", "user_roles", "permissions"}, "จัดการผู้ใช้และสิทธิ์"},
		{"members", "จัดการสมาชิก", []string{"members", "member_groups", "member_auth"}, "จัดการสมาชิกและกลุ่มสมาชิก"},
		{"banking", "ธนาคาร", []string{"banking", "deposit", "withdraw", "holding", "payment"}, "ระบบธนาคารและการเงิน"},
		{"reports", "รายงาน", []string{"reports", "audit_logs", "login_report"}, "รายงานและการตรวจสอบ"},
		{"system", "ระบบ", []string{"system", "settings", "faq", "banners", "languages"}, "ตั้งค่าระบบและการจัดการเนื้อหา"},
		{"partners", "พันธมิตร", []string{"partners", "channels", "commission", "referral"}, "จัดการพันธมิตรและค่าคอมมิชชั่น"},
		{"platforms", "แพลตฟอร์ม", []string{"platforms", "algorithms", "auto_bot"}, "จัดการแพลตฟอร์มและระบบอัตโนมัติ"},
	}

	// Initialize all groups
	for _, group := range groupPatterns {
		permissionsByGroup[group.key] = []*permission.Permission{}
	}

	// Classify permissions into groups
	for _, perm := range permissions {
		assigned := false
		for _, group := range groupPatterns {
			for _, pattern := range group.patterns {
				if strings.Contains(strings.ToLower(perm.Key), pattern) {
					permissionsByGroup[group.key] = append(permissionsByGroup[group.key], perm)
					assigned = true
					break
				}
			}
			if assigned {
				break
			}
		}
		// If not assigned to any group, add to general
		if !assigned {
			if permissionsByGroup["general"] == nil {
				permissionsByGroup["general"] = []*permission.Permission{}
			}
			permissionsByGroup["general"] = append(permissionsByGroup["general"], perm)
		}
	}

	// Build response - create groups
	var matrixGroups []PermissionMatrixGroup
	groupID := 1

	// Add predefined groups first
	for _, group := range groupPatterns {
		if len(permissionsByGroup[group.key]) > 0 {
			matrixGroup := PermissionMatrixGroup{
				ID:          groupID,
				Name:        group.name,
				Key:         group.key,
				Description: &group.description,
				Position:    &groupID,
				Permissions: permissionsByGroup[group.key],
			}
			matrixGroups = append(matrixGroups, matrixGroup)
			groupID++
		}
	}

	// Add general group if it has permissions
	if len(permissionsByGroup["general"]) > 0 {
		description := "สิทธิ์ทั่วไปที่ไม่ได้จัดอยู่ในกลุ่มใดเฉพาะ"
		matrixGroup := PermissionMatrixGroup{
			ID:          groupID,
			Name:        "ทั่วไป",
			Key:         "general",
			Description: &description,
			Position:    &groupID,
			Permissions: permissionsByGroup["general"],
		}
		matrixGroups = append(matrixGroups, matrixGroup)
	}

	return &PermissionMatrixResponse{
		Groups: matrixGroups,
	}, nil
}

// GetPermissionMatrix returns all permissions grouped by categories
func (s *PermissionService) GetPermissionMatrix(ctx context.Context) (*PermissionMatrixResponse, error) {
	// Get all permissions with sub-permissions
	permissions, err := s.permissionRepo.ListWithSubPermissions(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get permissions")
		return nil, errors.NewInternalError("failed to get permissions")
	}

	// Group permissions by key pattern (simple pattern-based grouping)
	permissionsByGroup := make(map[string][]*permission.Permission)

	// Define groups based on permission key patterns
	groupPatterns := []struct {
		key         string
		name        string
		patterns    []string
		description string
	}{
		{"dashboard", "แดชบอร์ด", []string{"dashboard"}, "หน้าแดชบอร์ดและภาพรวม"},
		{"user_management", "จัดการผู้ใช้", []string{"users", "user_roles", "permissions"}, "จัดการผู้ใช้และสิทธิ์"},
		{"members", "จัดการสมาชิก", []string{"members", "member_groups", "member_auth"}, "จัดการสมาชิกและกลุ่มสมาชิก"},
		{"banking", "ธนาคาร", []string{"banking", "deposit", "withdraw", "holding", "payment"}, "ระบบธนาคารและการเงิน"},
		{"reports", "รายงาน", []string{"reports", "audit_logs", "login_report"}, "รายงานและการตรวจสอบ"},
		{"system", "ระบบ", []string{"system", "settings", "faq", "banners", "languages"}, "ตั้งค่าระบบและการจัดการเนื้อหา"},
		{"partners", "พันธมิตร", []string{"partners", "channels", "commission", "referral"}, "จัดการพันธมิตรและค่าคอมมิชชั่น"},
		{"platforms", "แพลตฟอร์ม", []string{"platforms", "algorithms", "auto_bot"}, "จัดการแพลตฟอร์มและระบบอัตโนมัติ"},
	}

	// Initialize all groups
	for _, group := range groupPatterns {
		permissionsByGroup[group.key] = []*permission.Permission{}
	}

	// Classify permissions into groups
	for _, perm := range permissions {
		assigned := false
		for _, group := range groupPatterns {
			for _, pattern := range group.patterns {
				if strings.Contains(strings.ToLower(perm.Key), pattern) {
					permissionsByGroup[group.key] = append(permissionsByGroup[group.key], perm)
					assigned = true
					break
				}
			}
			if assigned {
				break
			}
		}
		// If not assigned to any group, add to general
		if !assigned {
			if permissionsByGroup["general"] == nil {
				permissionsByGroup["general"] = []*permission.Permission{}
			}
			permissionsByGroup["general"] = append(permissionsByGroup["general"], perm)
		}
	}

	// Build response - create groups
	var matrixGroups []PermissionMatrixGroup
	groupID := 1

	// Add predefined groups first
	for _, group := range groupPatterns {
		if len(permissionsByGroup[group.key]) > 0 {
			matrixGroup := PermissionMatrixGroup{
				ID:          groupID,
				Name:        group.name,
				Key:         group.key,
				Description: &group.description,
				Position:    &groupID,
				Permissions: permissionsByGroup[group.key],
			}
			matrixGroups = append(matrixGroups, matrixGroup)
			groupID++
		}
	}

	// Add general group if it has permissions
	if len(permissionsByGroup["general"]) > 0 {
		description := "สิทธิ์ทั่วไปที่ไม่ได้จัดอยู่ในกลุ่มใดเฉพาะ"
		matrixGroup := PermissionMatrixGroup{
			ID:          groupID,
			Name:        "ทั่วไป",
			Key:         "general",
			Description: &description,
			Position:    &groupID,
			Permissions: permissionsByGroup["general"],
		}
		matrixGroups = append(matrixGroups, matrixGroup)
	}

	return &PermissionMatrixResponse{
		Groups: matrixGroups,
	}, nil
}

// GetUserRolePermissions returns only permissions that the user role has access to
func (s *PermissionService) GetUserRolePermissions(ctx context.Context, userRoleID int) (*UserRolePermissionsResponse, error) {
	// Get user role permissions
	userRolePermissions, err := s.userRolePermissionRepo.ListByUserRoleID(ctx, userRoleID)
	if err != nil {
		s.logger.WithError(err).WithField("user_role_id", userRoleID).Error("failed to get user role permissions")
		return nil, errors.NewInternalError("failed to get user role permissions")
	}

	if len(userRolePermissions) == 0 {
		// Return empty matrix if user role has no permissions
		return &UserRolePermissionsResponse{
			UserRoleID: userRoleID,
			Matrix: PermissionMatrixResponse{
				Groups: []PermissionMatrixGroup{},
			},
		}, nil
	}

	// Get permission keys that this user role has access to
	permissionKeys := make(map[string]*permission.UserRolePermission)
	var keysList []string
	for _, urp := range userRolePermissions {
		// Only include permissions where user has at least one access (view, create, edit, or delete)
		if urp.CanView || urp.CanCreate || urp.CanEdit || urp.CanDelete {
			permissionKeys[urp.PermissionKey] = urp
			keysList = append(keysList, urp.PermissionKey)
		}
	}

	if len(keysList) == 0 {
		// Return empty matrix if user role has no actual permissions
		return &UserRolePermissionsResponse{
			UserRoleID: userRoleID,
			Matrix: PermissionMatrixResponse{
				Groups: []PermissionMatrixGroup{},
			},
		}, nil
	}

	// Get all permissions with sub-permissions
	allPermissions, err := s.permissionRepo.ListWithSubPermissions(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get permissions")
		return nil, errors.NewInternalError("failed to get permissions")
	}

	// Filter permissions to only include those the user role has access to
	var filteredPermissions []*permission.Permission
	permissionParentMap := make(map[string]*permission.Permission) // key -> parent permission

	for _, perm := range allPermissions {
		// Check if this permission is in user's permission list
		if urp, hasAccess := permissionKeys[perm.Key]; hasAccess {
			// Add user role permission data
			perm.UserRolePermission = &permission.UserRolePermissionData{
				CanCreate: urp.CanCreate,
				CanView:   urp.CanView,
				CanEdit:   urp.CanEdit,
				CanDelete: urp.CanDelete,
			}

			// Filter sub-permissions too
			var filteredSubPermissions []permission.Permission
			for _, subPerm := range perm.SubPermissions {
				if subUrp, hasSubAccess := permissionKeys[subPerm.Key]; hasSubAccess {
					subPerm.UserRolePermission = &permission.UserRolePermissionData{
						CanCreate: subUrp.CanCreate,
						CanView:   subUrp.CanView,
						CanEdit:   subUrp.CanEdit,
						CanDelete: subUrp.CanDelete,
					}
					filteredSubPermissions = append(filteredSubPermissions, subPerm)
				}
			}
			perm.SubPermissions = filteredSubPermissions

			filteredPermissions = append(filteredPermissions, perm)
			permissionParentMap[perm.Key] = perm
		}
	}

	// Group filtered permissions by pattern (same logic as GetPermissionMatrix but with filtered permissions)
	permissionsByGroup := make(map[string][]*permission.Permission)

	// Define groups based on permission key patterns
	groupPatterns := []struct {
		key         string
		name        string
		patterns    []string
		description string
	}{
		{"dashboard", "แดชบอร์ด", []string{"dashboard"}, "หน้าแดชบอร์ดและภาพรวม"},
		{"user_management", "จัดการผู้ใช้", []string{"users", "user_roles", "permissions"}, "จัดการผู้ใช้และสิทธิ์"},
		{"members", "จัดการสมาชิก", []string{"members", "member_groups", "member_auth"}, "จัดการสมาชิกและกลุ่มสมาชิก"},
		{"banking", "ธนาคาร", []string{"banking", "deposit", "withdraw", "holding", "payment"}, "ระบบธนาคารและการเงิน"},
		{"reports", "รายงาน", []string{"reports", "audit_logs", "login_report"}, "รายงานและการตรวจสอบ"},
		{"system", "ระบบ", []string{"system", "settings", "faq", "banners", "languages"}, "ตั้งค่าระบบและการจัดการเนื้อหา"},
		{"partners", "พันธมิตร", []string{"partners", "channels", "commission", "referral"}, "จัดการพันธมิตรและค่าคอมมิชชั่น"},
		{"platforms", "แพลตฟอร์ม", []string{"platforms", "algorithms", "auto_bot"}, "จัดการแพลตฟอร์มและระบบอัตโนมัติ"},
	}

	// Initialize all groups
	for _, group := range groupPatterns {
		permissionsByGroup[group.key] = []*permission.Permission{}
	}

	// Classify filtered permissions into groups
	for _, perm := range filteredPermissions {
		assigned := false
		for _, group := range groupPatterns {
			for _, pattern := range group.patterns {
				if strings.Contains(strings.ToLower(perm.Key), pattern) {
					permissionsByGroup[group.key] = append(permissionsByGroup[group.key], perm)
					assigned = true
					break
				}
			}
			if assigned {
				break
			}
		}
		// If not assigned to any group, add to general
		if !assigned {
			if permissionsByGroup["general"] == nil {
				permissionsByGroup["general"] = []*permission.Permission{}
			}
			permissionsByGroup["general"] = append(permissionsByGroup["general"], perm)
		}
	}

	// Build response - create groups with only permissions user has access to
	var matrixGroups []PermissionMatrixGroup
	groupID := 1

	// Add predefined groups first (only if they have permissions)
	for _, group := range groupPatterns {
		if len(permissionsByGroup[group.key]) > 0 {
			matrixGroup := PermissionMatrixGroup{
				ID:          groupID,
				Name:        group.name,
				Key:         group.key,
				Description: &group.description,
				Position:    &groupID,
				Permissions: permissionsByGroup[group.key],
			}
			matrixGroups = append(matrixGroups, matrixGroup)
			groupID++
		}
	}

	// Add general group if it has permissions
	if len(permissionsByGroup["general"]) > 0 {
		description := "สิทธิ์ทั่วไปที่ไม่ได้จัดอยู่ในกลุ่มใดเฉพาะ"
		matrixGroup := PermissionMatrixGroup{
			ID:          groupID,
			Name:        "ทั่วไป",
			Key:         "general",
			Description: &description,
			Position:    &groupID,
			Permissions: permissionsByGroup["general"],
		}
		matrixGroups = append(matrixGroups, matrixGroup)
	}

	return &UserRolePermissionsResponse{
		UserRoleID: userRoleID,
		Matrix: PermissionMatrixResponse{
			Groups: matrixGroups,
		},
	}, nil
}

// BulkUpdateUserRolePermissions updates multiple permissions for a user role
func (s *PermissionService) BulkUpdateUserRolePermissions(ctx context.Context, userRoleID int, permissions []permission.UserRolePermissionUpdate) error {
	// Validate permissions exist
	for _, perm := range permissions {
		_, err := s.permissionRepo.GetByKey(ctx, perm.PermissionKey)
		if err != nil {
			s.logger.WithError(err).WithField("permission_key", perm.PermissionKey).Error("permission not found")
			return errors.NewValidationError("permission not found: " + perm.PermissionKey)
		}
	}

	// Perform bulk upsert
	err := s.userRolePermissionRepo.BulkUpsert(ctx, userRoleID, permissions)
	if err != nil {
		s.logger.WithError(err).WithField("user_role_id", userRoleID).Error("failed to bulk update user role permissions")
		return errors.NewInternalError("failed to update permissions")
	}

	s.logger.WithField("user_role_id", userRoleID).WithField("permission_count", len(permissions)).Info("user role permissions updated successfully")
	return nil
}

// GetPermissionsByPattern returns permissions that match a specific pattern
func (s *PermissionService) GetPermissionsByPattern(ctx context.Context, pattern string) ([]*permission.Permission, error) {
	// Get all permissions first
	allPermissions, err := s.permissionRepo.List(ctx, 0, 0, "")
	if err != nil {
		s.logger.WithError(err).Error("failed to get all permissions")
		return nil, errors.NewInternalError("failed to get permissions")
	}

	// Filter by pattern
	var matchedPermissions []*permission.Permission
	for _, perm := range allPermissions {
		if strings.Contains(strings.ToLower(perm.Key), strings.ToLower(pattern)) {
			matchedPermissions = append(matchedPermissions, perm)
		}
	}

	return matchedPermissions, nil
}

// GetPermissionsByGroup returns permissions for a specific group ID (from permission matrix)
func (s *PermissionService) GetPermissionsByGroup(ctx context.Context, groupID int) ([]*permission.Permission, error) {
	// Get the permission matrix first
	matrix, err := s.GetPermissionMatrix(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get permission matrix")
		return nil, errors.NewInternalError("failed to get permission matrix")
	}

	// Find the group by ID
	for _, group := range matrix.Groups {
		if group.ID == groupID {
			return group.Permissions, nil
		}
	}

	s.logger.WithField("group_id", groupID).Error("permission group not found")
	return nil, errors.NewNotFoundError("permission group not found")
}

// GetAllPermissions returns all permissions with optional search and pagination
func (s *PermissionService) GetAllPermissions(ctx context.Context, limit, offset int, search string) ([]*permission.Permission, error) {
	permissions, err := s.permissionRepo.List(ctx, limit, offset, search)
	if err != nil {
		s.logger.WithError(err).Error("failed to get all permissions")
		return nil, errors.NewInternalError("failed to get all permissions")
	}

	return permissions, nil
}

// Response types
type PermissionMatrixResponse struct {
	Groups []PermissionMatrixGroup `json:"groups"`
}

type PermissionMatrixGroup struct {
	ID          int                      `json:"id"`
	Name        string                   `json:"name"`
	Key         string                   `json:"key"`
	Description *string                  `json:"description"`
	Position    *int                     `json:"position"`
	Permissions []*permission.Permission `json:"permissions"`
}

type UserRolePermissionsResponse struct {
	UserRoleID int                      `json:"user_role_id"`
	Matrix     PermissionMatrixResponse `json:"matrix"`
}

// BulkUpdateRequest represents the request for bulk updating permissions
type BulkUpdateRequest struct {
	Permissions []permission.UserRolePermissionUpdate `json:"permissions" validate:"required,dive"`
}

// CheckUserPermission checks what actions a user role can perform on a specific permission
func (s *PermissionService) CheckUserPermission(ctx context.Context, userRoleID int, permissionKey string) (*UserPermissionCheck, error) {
	// First, check if the permission exists
	perm, err := s.permissionRepo.GetByKey(ctx, permissionKey)
	if err != nil {
		s.logger.WithError(err).WithField("permission_key", permissionKey).Error("permission not found")
		return nil, errors.NewNotFoundError("permission not found: " + permissionKey)
	}

	// Get user role permission
	userRolePerm, err := s.userRolePermissionRepo.GetByUserRoleAndPermission(ctx, userRoleID, permissionKey)
	if err != nil {
		if errors.IsNotFoundError(err) {
			// No permission set, return default (all false)
			return &UserPermissionCheck{
				PermissionKey:  permissionKey,
				PermissionName: perm.Name,
				UserRoleID:     userRoleID,
				CanCreate:      false,
				CanView:        false,
				CanEdit:        false,
				CanDelete:      false,
				SupportsCreate: perm.SupportsCreate,
				SupportsView:   perm.SupportsView,
				SupportsEdit:   perm.SupportsEdit,
				SupportsDelete: perm.SupportsDelete,
			}, nil
		}
		s.logger.WithError(err).WithField("user_role_id", userRoleID).WithField("permission_key", permissionKey).Error("failed to get user role permission")
		return nil, errors.NewInternalError("failed to check user permission")
	}

	return &UserPermissionCheck{
		PermissionKey:  permissionKey,
		PermissionName: perm.Name,
		UserRoleID:     userRoleID,
		CanCreate:      userRolePerm.CanCreate,
		CanView:        userRolePerm.CanView,
		CanEdit:        userRolePerm.CanEdit,
		CanDelete:      userRolePerm.CanDelete,
		SupportsCreate: perm.SupportsCreate,
		SupportsView:   perm.SupportsView,
		SupportsEdit:   perm.SupportsEdit,
		SupportsDelete: perm.SupportsDelete,
	}, nil
}

// UserPermissionCheck represents the result of a permission check
type UserPermissionCheck struct {
	PermissionKey  string `json:"permission_key"`
	PermissionName string `json:"permission_name"`
	UserRoleID     int    `json:"user_role_id"`
	CanCreate      bool   `json:"can_create"`
	CanView        bool   `json:"can_view"`
	CanEdit        bool   `json:"can_edit"`
	CanDelete      bool   `json:"can_delete"`
	SupportsCreate bool   `json:"supports_create"`
	SupportsView   bool   `json:"supports_view"`
	SupportsEdit   bool   `json:"supports_edit"`
	SupportsDelete bool   `json:"supports_delete"`
}
