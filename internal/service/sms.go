package service

import (
	"context"
	"fmt"

	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"blacking-api/pkg/sms"
)

// SMSService defines the interface for SMS service operations
type SMSService interface {
	// CheckCredit checks remaining SMS credit
	CheckCredit(ctx context.Context) (*SMSCreditResponse, error)

	// SendSMS sends SMS message
	SendSMS(ctx context.Context, req SendSMSRequest) (*SendSMSResponse, error)

	// SendOTPSMS sends OTP via SMS (specific for OTP messages)
	SendOTPSMS(ctx context.Context, phone, code, reference string) error
}

// SMSCreditResponse represents SMS credit response
type SMSCreditResponse struct {
	Credit  float64 `json:"credit"`
	Status  string  `json:"status"`
	Message string  `json:"message"`
}

// SendSMSRequest represents SMS sending request
type SendSMSRequest struct {
	Phone   string `json:"phone" validate:"required"`
	Message string `json:"message" validate:"required"`
}

// SendSMSResponse represents SMS sending response
type SendSMSResponse struct {
	MessageID       string `json:"message_id"`
	Status          string `json:"status"`
	Message         string `json:"message"`
	RemainingCredit string `json:"remaining_credit"`
}

type smsService struct {
	thsmsClient *sms.ThSMSClient
	logger      logger.Logger
}

// NewSMSService creates a new SMS service
func NewSMSService(thsmsClient *sms.ThSMSClient, logger logger.Logger) SMSService {
	return &smsService{
		thsmsClient: thsmsClient,
		logger:      logger,
	}
}

// CheckCredit checks remaining SMS credit
func (s *smsService) CheckCredit(ctx context.Context) (*SMSCreditResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CheckCredit")

	creditResp, err := s.thsmsClient.CheckCredit()
	if err != nil {
		log.WithError(err).Error("failed to check SMS credit")
		return nil, err
	}

	response := &SMSCreditResponse{
		Credit:  creditResp.Credit,
		Status:  creditResp.Status,
		Message: creditResp.Message,
	}

	log.WithField("credit", response.Credit).Info("SMS credit checked successfully")
	return response, nil
}

// SendSMS sends SMS message
func (s *smsService) SendSMS(ctx context.Context, req SendSMSRequest) (*SendSMSResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "SendSMS").WithField("phone", req.Phone)

	// Validate phone number
	if err := sms.ValidatePhone(req.Phone); err != nil {
		log.WithError(err).Error("invalid phone number")
		return nil, err
	}

	// Format phone number
	formattedPhone := sms.FormatPhone(req.Phone)

	// Send SMS
	smsReq := sms.SendSMSRequest{
		Phone:   formattedPhone,
		Message: req.Message,
	}

	smsResp, err := s.thsmsClient.SendSMS(smsReq)
	if err != nil {
		log.WithError(err).Error("failed to send SMS")
		return nil, err
	}

	response := &SendSMSResponse{
		MessageID:       "", // ThSMS API doesn't provide message ID
		Status:          "success",
		Message:         smsResp.Message,
		RemainingCredit: fmt.Sprintf("%d", smsResp.Data.RemainingCredit),
	}

	log.WithField("remaining_credit", response.RemainingCredit).Info("SMS sent successfully")
	return response, nil
}

// SendOTPSMS sends OTP via SMS (specific for OTP messages)
func (s *smsService) SendOTPSMS(ctx context.Context, phone, code, reference string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SendOTPSMS").WithField("phone", phone).WithField("reference", reference)

	// Create OTP message
	message := fmt.Sprintf("Your verification code is: %s (Ref: %s). Valid for 5 minutes. Do not share this code with anyone.", code, reference)

	// Send SMS
	req := SendSMSRequest{
		Phone:   phone,
		Message: message,
	}

	_, err := s.SendSMS(ctx, req)
	if err != nil {
		log.WithError(err).Error("failed to send OTP SMS")
		return errors.NewInternalError("failed to send OTP SMS")
	}

	log.Info("OTP SMS sent successfully")
	return nil
}
