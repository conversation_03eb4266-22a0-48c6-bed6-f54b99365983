package service

import (
	"blacking-api/internal/domain/member_audit_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
)

// MemberAuditLogService defines the interface for member audit log service operations
type MemberAuditLogService interface {
	// LogMemberAction logs a member action to audit log
	LogMemberAction(ctx context.Context, req member_audit_log.CreateAuditLogRequest) error

	// ListAuditLogs retrieves audit logs with pagination and filters
	ListAuditLogs(ctx context.Context, limit, offset int, username string, action string) ([]*member_audit_log.MemberAuditLogResponse, error)

	// GetAuditLogsCount returns the total count of audit logs with filters
	GetAuditLogsCount(ctx context.Context, username string, action string) (int64, error)
}

type memberAuditLogService struct {
	auditLogRepo interfaces.MemberAuditLogRepository
	logger       logger.Logger
}

// NewMemberAuditLogService creates a new member audit log service
func NewMemberAuditLogService(auditLogRepo interfaces.MemberAuditLogRepository, logger logger.Logger) MemberAuditLogService {
	return &memberAuditLogService{
		auditLogRepo: auditLogRepo,
		logger:       logger,
	}
}

// LogMemberAction logs a member action to audit log
func (s *memberAuditLogService) LogMemberAction(ctx context.Context, req member_audit_log.CreateAuditLogRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "LogMemberAction")

	// Create new audit log
	auditLog, err := member_audit_log.NewMemberAuditLog(req)
	if err != nil {
		log.WithError(err).Error("failed to create member audit log domain object")
		return err
	}

	// Save to repository
	if err := s.auditLogRepo.Create(ctx, auditLog); err != nil {
		log.WithError(err).Error("failed to save member audit log")
		return err
	}

	log.WithField("audit_log_id", auditLog.ID).Info("member audit log created successfully")
	return nil
}

// ListAuditLogs retrieves audit logs with pagination and filters
func (s *memberAuditLogService) ListAuditLogs(ctx context.Context, limit, offset int, username string, action string) ([]*member_audit_log.MemberAuditLogResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListAuditLogs")

	// Validate pagination parameters
	if limit <= 0 {
		limit = 10 // default limit
	}
	if limit > 100 {
		limit = 100 // max limit
	}
	if offset < 0 {
		offset = 0
	}

	// Get audit logs from repository
	auditLogs, err := s.auditLogRepo.List(ctx, limit, offset, username, action)
	if err != nil {
		log.WithError(err).Error("failed to get audit logs from repository")
		return nil, err
	}

	// Convert to response format
	responses := make([]*member_audit_log.MemberAuditLogResponse, len(auditLogs))
	for i, auditLog := range auditLogs {
		response := auditLog.ToResponse()
		responses[i] = &response
	}

	log.WithField("count", len(responses)).Info("member audit logs retrieved successfully")
	return responses, nil
}

// GetAuditLogsCount returns the total count of audit logs with filters
func (s *memberAuditLogService) GetAuditLogsCount(ctx context.Context, username string, action string) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetAuditLogsCount")

	count, err := s.auditLogRepo.Count(ctx, username, action)
	if err != nil {
		log.WithError(err).Error("failed to get audit logs count from repository")
		return 0, err
	}

	log.WithField("count", count).Info("member audit logs count retrieved successfully")
	return count, nil
}

// Helper functions for creating audit log requests

// CreateMemberAuditLog creates an audit log for member creation
func CreateMemberAuditLog(memberID int, username string, changedBy int, changedByName string, newValues interface{}) member_audit_log.CreateAuditLogRequest {
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionCreate,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// UpdateMemberAuditLog creates an audit log for member update
func UpdateMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldValues, newValues interface{}) member_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionUpdate,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// DeleteMemberAuditLog creates an audit log for member deletion
func DeleteMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldValues interface{}) member_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionDelete,
		OldValues:     oldValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// ActivateMemberAuditLog creates an audit log for member activation
func ActivateMemberAuditLog(memberID int, username string, changedBy int, changedByName string) member_audit_log.CreateAuditLogRequest {
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionActivate,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// DeactivateMemberAuditLog creates an audit log for member deactivation
func DeactivateMemberAuditLog(memberID int, username string, changedBy int, changedByName string) member_audit_log.CreateAuditLogRequest {
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionDeactivate,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// SuspendMemberAuditLog creates an audit log for member suspension
func SuspendMemberAuditLog(memberID int, username string, changedBy int, changedByName string) member_audit_log.CreateAuditLogRequest {
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionSuspend,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// PasswordChangeMemberAuditLog creates an audit log for member password change
func PasswordChangeMemberAuditLog(memberID int, username string, changedBy int, changedByName string) member_audit_log.CreateAuditLogRequest {
	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionPassword,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// GameCredentialsChangeMemberAuditLog creates an audit log for member game credentials change
func GameCredentialsChangeMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldValues, newValues interface{}) member_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionGameCredentials,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// BalanceUpdateMemberAuditLog creates an audit log for member balance update
func BalanceUpdateMemberAuditLog(memberID int, username string, changedBy int, changedByName string, oldBalance, newBalance float64) member_audit_log.CreateAuditLogRequest {
	oldValues := map[string]interface{}{"balance": oldBalance}
	newValues := map[string]interface{}{"balance": newBalance}

	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)

	return member_audit_log.CreateAuditLogRequest{
		MemberID:      memberID,
		Username:      username,
		Action:        member_audit_log.ActionBalanceUpdate,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}
