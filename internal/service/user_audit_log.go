package service

import (
	"blacking-api/internal/domain/user_audit_log"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
	"encoding/json"
)

// UserAuditLogService defines the interface for user audit log service operations
type UserAuditLogService interface {
	// LogUserAction logs a user action to audit log
	LogUserAction(ctx context.Context, req user_audit_log.CreateAuditLogRequest) error

	// ListAuditLogs retrieves audit logs with pagination and filters
	ListAuditLogs(ctx context.Context, limit, offset int, username string, userRoleID *int, action string) ([]*user_audit_log.UserAuditLogResponse, error)

	// GetAuditLogsCount returns the total count of audit logs with filters
	GetAuditLogsCount(ctx context.Context, username string, userRoleID *int, action string) (int64, error)
}

type userAuditLogService struct {
	auditLogRepo interfaces.UserAuditLogRepository
	logger       logger.Logger
}

// NewUserAuditLogService creates a new user audit log service
func NewUserAuditLogService(auditLogRepo interfaces.UserAuditLogRepository, logger logger.Logger) UserAuditLogService {
	return &userAuditLogService{
		auditLogRepo: auditLogRepo,
		logger:       logger,
	}
}

// LogUserAction logs a user action to audit log
func (s *userAuditLogService) LogUserAction(ctx context.Context, req user_audit_log.CreateAuditLogRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "LogUserAction")

	// Create new audit log
	auditLog, err := user_audit_log.NewUserAuditLog(req)
	if err != nil {
		log.WithError(err).Error("failed to create user audit log domain object")
		return err
	}

	// Save to repository
	if err := s.auditLogRepo.Create(ctx, auditLog); err != nil {
		log.WithError(err).Error("failed to save user audit log")
		return err
	}

	log.WithField("audit_log_id", auditLog.ID).Info("user audit log created successfully")
	return nil
}

// ListAuditLogs retrieves audit logs with pagination and filters
func (s *userAuditLogService) ListAuditLogs(ctx context.Context, limit, offset int, username string, userRoleID *int, action string) ([]*user_audit_log.UserAuditLogResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListAuditLogs")

	// Validate pagination parameters
	if limit <= 0 {
		limit = 10 // default limit
	}
	if limit > 100 {
		limit = 100 // max limit
	}
	if offset < 0 {
		offset = 0
	}

	// Get audit logs from repository
	auditLogs, err := s.auditLogRepo.List(ctx, limit, offset, username, userRoleID, action)
	if err != nil {
		log.WithError(err).Error("failed to get audit logs from repository")
		return nil, err
	}

	// Convert to response format
	var responses []*user_audit_log.UserAuditLogResponse
	for _, auditLog := range auditLogs {
		response := auditLog.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("audit logs retrieved successfully")
	return responses, nil
}

// GetAuditLogsCount returns the total count of audit logs with filters
func (s *userAuditLogService) GetAuditLogsCount(ctx context.Context, username string, userRoleID *int, action string) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetAuditLogsCount")

	count, err := s.auditLogRepo.Count(ctx, username, userRoleID, action)
	if err != nil {
		log.WithError(err).Error("failed to get audit logs count from repository")
		return 0, err
	}

	log.WithField("count", count).Info("audit logs count retrieved successfully")
	return count, nil
}

// Helper functions for creating audit log requests

// CreateUserAuditLog creates an audit log for user creation
func CreateUserAuditLog(userID int, username string, changedBy int, changedByName string, newValues interface{}) user_audit_log.CreateAuditLogRequest {
	newValuesJSON := marshalToJSON(newValues)

	return user_audit_log.CreateAuditLogRequest{
		UserID:        userID,
		Username:      username,
		Action:        user_audit_log.ActionCreate,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// UpdateUserAuditLog creates an audit log for user update
func UpdateUserAuditLog(userID int, username string, changedBy int, changedByName string, oldValues, newValues interface{}) user_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)
	newValuesJSON := marshalToJSON(newValues)

	return user_audit_log.CreateAuditLogRequest{
		UserID:        userID,
		Username:      username,
		Action:        user_audit_log.ActionUpdate,
		OldValues:     oldValuesJSON,
		NewValues:     newValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// DeleteUserAuditLog creates an audit log for user deletion
func DeleteUserAuditLog(userID int, username string, changedBy int, changedByName string, oldValues interface{}) user_audit_log.CreateAuditLogRequest {
	oldValuesJSON := marshalToJSON(oldValues)

	return user_audit_log.CreateAuditLogRequest{
		UserID:        userID,
		Username:      username,
		Action:        user_audit_log.ActionDelete,
		OldValues:     oldValuesJSON,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// ActivateUserAuditLog creates an audit log for user activation
func ActivateUserAuditLog(userID int, username string, changedBy int, changedByName string) user_audit_log.CreateAuditLogRequest {
	return user_audit_log.CreateAuditLogRequest{
		UserID:        userID,
		Username:      username,
		Action:        user_audit_log.ActionActivate,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// DeactivateUserAuditLog creates an audit log for user deactivation
func DeactivateUserAuditLog(userID int, username string, changedBy int, changedByName string) user_audit_log.CreateAuditLogRequest {
	return user_audit_log.CreateAuditLogRequest{
		UserID:        userID,
		Username:      username,
		Action:        user_audit_log.ActionDeactivate,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// SuspendUserAuditLog creates an audit log for user suspension
func SuspendUserAuditLog(userID int, username string, changedBy int, changedByName string) user_audit_log.CreateAuditLogRequest {
	return user_audit_log.CreateAuditLogRequest{
		UserID:        userID,
		Username:      username,
		Action:        user_audit_log.ActionSuspend,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// PasswordChangeAuditLog creates an audit log for password change
func PasswordChangeAuditLog(userID int, username string, changedBy int, changedByName string) user_audit_log.CreateAuditLogRequest {
	return user_audit_log.CreateAuditLogRequest{
		UserID:        userID,
		Username:      username,
		Action:        user_audit_log.ActionPassword,
		ChangedBy:     changedBy,
		ChangedByName: changedByName,
	}
}

// marshalToJSON converts interface to JSON string pointer
func marshalToJSON(data interface{}) *string {
	if data == nil {
		return nil
	}

	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return nil
	}

	jsonStr := string(jsonBytes)
	return &jsonStr
}
