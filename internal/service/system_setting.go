package service

import (
	"blacking-api/internal/domain/system_setting"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"net/http"
	"strconv"
)

// SystemSettingService defines the interface for system setting service operations
type SystemSettingService interface {
	// GetLoginAttemptLimit gets the maximum login attempt limit
	GetLoginAttemptLimit(ctx context.Context) (*system_setting.LoginAttemptLimitResponse, error)

	// UpdateLoginAttemptLimit updates the maximum login attempt limit
	UpdateLoginAttemptLimit(ctx context.Context, req system_setting.UpdateLoginAttemptLimitRequest) error

	// GetSystemSetting gets a system setting by key
	GetSystemSetting(ctx context.Context, key string) (*system_setting.SystemSettingResponse, error)

	// UpdateSystemSetting updates a system setting
	UpdateSystemSetting(ctx context.Context, key string, req system_setting.UpdateSystemSettingRequest) error

	// ListSystemSettings lists all system settings
	ListSystemSettings(ctx context.Context) ([]*system_setting.SystemSettingResponse, error)

	// GetGeneralSettings gets all general settings grouped by category
	GetGeneralSettings(ctx context.Context) (*system_setting.GeneralSettingsResponse, error)

	// UpdateGeneralSettings updates general settings
	UpdateGeneralSettings(ctx context.Context, req system_setting.UpdateGeneralSettingsRequest) error

	// GetSEOSettings gets SEO settings
	GetSEOSettings(ctx context.Context) (*system_setting.SEOSettingsResponse, error)

	// UpdateSEOSettings updates SEO settings
	UpdateSEOSettings(ctx context.Context, req system_setting.UpdateSEOSettingsRequest) error

	// GetSEOSettings gets SEO settings
	GetSiteImageSettings(ctx context.Context) (*system_setting.SiteImageSettingsResponse, error)

	// UpdateSiteImageSettings updates SiteImage settings
	UpdateSiteImageSettings(ctx context.Context, req system_setting.UpdateSiteImageSettingsRequest) error

	// File upload methods
	FileUpload(ctx context.Context, fileBody *http.Request, fieldName string) (*system_setting.FileUploadResponse, error)
	DeleteFile(ctx context.Context, req *system_setting.DeleteFileRequest) error

	// GetCommissionSettings gets commission settings
	GetCommissionSettings(ctx context.Context) (*system_setting.CommissionSettingsResponse, error)

	// UpdateCommissionSettings updates commission settings
	UpdateCommissionSettings(ctx context.Context, req system_setting.UpdateCommissionSettingsRequest) error

	// GetReferralSettings gets referral settings
	GetReferralSettings(ctx context.Context) (*system_setting.ReferralSettingsResponse, error)

	// UpdateReferralSettings updates referral settings
	UpdateReferralSettings(ctx context.Context, req system_setting.UpdateReferralSettingsRequest) error

	// GetMemberLevelSettings gets member level calculation settings
	GetMemberLevelSettings(ctx context.Context) (*system_setting.MemberLevelSettingsResponse, error)

	// UpdateMemberLevelSettings updates member level calculation settings
	UpdateMemberLevelSettings(ctx context.Context, req system_setting.UpdateMemberLevelSettingsRequest) error

	// GetPlayerAttemptLimitOptions returns player attempt limit options for dropdown
	GetPlayerAttemptLimitOptions(ctx context.Context) (*system_setting.SettingOptionsResponse, error)

	// GetTimeoutOptions returns timeout options for dropdown (deposit/withdraw)
	GetTimeoutOptions(ctx context.Context) (*system_setting.SettingOptionsResponse, error)
}

type systemSettingService struct {
	systemSettingRepo interfaces.SystemSettingRepository
	awsS3Repo         interfaces.AWSS3Repository
	logger            logger.Logger
}

// NewSystemSettingService creates a new system setting service
func NewSystemSettingService(systemSettingRepo interfaces.SystemSettingRepository, awsS3Repo interfaces.AWSS3Repository, logger logger.Logger) SystemSettingService {
	return &systemSettingService{
		systemSettingRepo: systemSettingRepo,
		awsS3Repo:         awsS3Repo,
		logger:            logger,
	}
}

// GetLoginAttemptLimit gets the maximum login attempt limit
func (s *systemSettingService) GetLoginAttemptLimit(ctx context.Context) (*system_setting.LoginAttemptLimitResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetLoginAttemptLimit")

	// Get the setting from repository
	setting, err := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyMaxLoginAttempts)
	if err != nil {
		if errors.IsNotFoundError(err) {
			// If setting doesn't exist, create default and return it
			defaultSetting := system_setting.GetDefaultMaxLoginAttempts()
			if createErr := s.systemSettingRepo.Create(ctx, defaultSetting); createErr != nil {
				log.WithError(createErr).Error("failed to create default login attempt limit setting")
				return nil, createErr
			}

			maxAttempts, parseErr := defaultSetting.GetIntValue()
			if parseErr != nil {
				log.WithError(parseErr).Error("failed to parse default max attempts value")
				return nil, parseErr
			}

			response := &system_setting.LoginAttemptLimitResponse{
				MaxAttempts: maxAttempts,
				Description: "Maximum number of failed login attempts allowed. Set to 0 for unlimited attempts.",
			}

			log.Info("default login attempt limit setting created and returned")
			return response, nil
		}
		log.WithError(err).Error("failed to get login attempt limit setting")
		return nil, err
	}

	// Parse the value to integer
	maxAttempts, err := setting.GetIntValue()
	if err != nil {
		log.WithError(err).Error("failed to parse max attempts value")
		return nil, err
	}

	response := &system_setting.LoginAttemptLimitResponse{
		MaxAttempts: maxAttempts,
		Description: "Maximum number of failed login attempts allowed. Set to 0 for unlimited attempts.",
	}

	log.WithField("max_attempts", maxAttempts).Info("login attempt limit retrieved successfully")
	return response, nil
}

// UpdateLoginAttemptLimit updates the maximum login attempt limit
func (s *systemSettingService) UpdateLoginAttemptLimit(ctx context.Context, req system_setting.UpdateLoginAttemptLimitRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateLoginAttemptLimit")

	// Validate request
	if req.MaxAttempts < 0 {
		return errors.NewValidationError("max_attempts must be 0 or greater")
	}

	// Convert to string
	valueStr := strconv.Itoa(req.MaxAttempts)

	// Create update request
	updateReq := system_setting.UpdateSystemSettingRequest{
		Value: valueStr,
	}

	// Try to get existing setting
	setting, err := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyMaxLoginAttempts)
	if err != nil {
		if errors.IsNotFoundError(err) {
			// Create new setting if it doesn't exist
			description := "Maximum number of failed login attempts allowed. Set to 0 for unlimited attempts."
			newSetting := system_setting.NewSystemSetting(system_setting.KeyMaxLoginAttempts, valueStr, &description)

			if createErr := s.systemSettingRepo.Create(ctx, newSetting); createErr != nil {
				log.WithError(createErr).Error("failed to create login attempt limit setting")
				return createErr
			}

			log.WithField("max_attempts", req.MaxAttempts).Info("login attempt limit setting created successfully")
			return nil
		}
		log.WithError(err).Error("failed to get existing login attempt limit setting")
		return err
	}

	// Update existing setting
	if err := setting.Update(updateReq); err != nil {
		log.WithError(err).Error("failed to update setting domain object")
		return err
	}

	if err := s.systemSettingRepo.Update(ctx, setting); err != nil {
		log.WithError(err).Error("failed to update login attempt limit setting in repository")
		return err
	}

	log.WithField("max_attempts", req.MaxAttempts).Info("login attempt limit updated successfully")
	return nil
}

// GetSystemSetting gets a system setting by key
func (s *systemSettingService) GetSystemSetting(ctx context.Context, key string) (*system_setting.SystemSettingResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetSystemSetting").WithField("key", key)

	setting, err := s.systemSettingRepo.GetByKey(ctx, key)
	if err != nil {
		log.WithError(err).Error("failed to get system setting")
		return nil, err
	}

	response := setting.ToResponse()
	log.Info("system setting retrieved successfully")
	return &response, nil
}

// UpdateSystemSetting updates a system setting (upsert: create if not exists)
func (s *systemSettingService) UpdateSystemSetting(ctx context.Context, key string, req system_setting.UpdateSystemSettingRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateSystemSetting").WithField("key", key)

	// Try to get existing setting
	setting, err := s.systemSettingRepo.GetByKey(ctx, key)
	if err != nil {
		if errors.IsNotFoundError(err) {
			// Setting doesn't exist, create new one
			log.Info("setting not found, creating new setting")
			newSetting := system_setting.NewSystemSetting(key, req.Value, nil)
			if err := s.systemSettingRepo.Create(ctx, newSetting); err != nil {
				log.WithError(err).Error("failed to create new system setting")
				return err
			}
			log.Info("new system setting created successfully")
			return nil
		}
		// Other error occurred
		log.WithError(err).Error("failed to get existing system setting")
		return err
	}

	// Setting exists, update it
	if err := setting.Update(req); err != nil {
		log.WithError(err).Error("failed to update setting domain object")
		return err
	}

	if err := s.systemSettingRepo.Update(ctx, setting); err != nil {
		log.WithError(err).Error("failed to update system setting in repository")
		return err
	}

	log.Info("existing system setting updated successfully")
	return nil
}

// ListSystemSettings lists all system settings
func (s *systemSettingService) ListSystemSettings(ctx context.Context) ([]*system_setting.SystemSettingResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListSystemSettings")

	settings, err := s.systemSettingRepo.List(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list system settings")
		return nil, err
	}

	var responses []*system_setting.SystemSettingResponse
	for _, setting := range settings {
		response := setting.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("system settings listed successfully")
	return responses, nil
}

// GetGeneralSettings gets all general settings grouped by category
func (s *systemSettingService) GetGeneralSettings(ctx context.Context) (*system_setting.GeneralSettingsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetGeneralSettings")

	// Get all settings (using existing ListSystemSettings logic)
	settingsResponse, err := s.ListSystemSettings(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list system settings")
		return nil, err
	}

	// Convert response to settings for processing
	settings := make([]*system_setting.SystemSetting, len(settingsResponse))
	for i, resp := range settingsResponse {
		settings[i] = &system_setting.SystemSetting{
			Key:   resp.Key,
			Value: resp.Value,
		}
	}
	if err != nil {
		log.WithError(err).Error("failed to get system settings")
		return nil, err
	}

	// Convert to map for easy lookup
	settingsMap := make(map[string]string)
	for _, setting := range settings {
		settingsMap[setting.Key] = setting.Value
	}

	// Helper function to get boolean value
	getBool := func(key string, defaultValue bool) bool {
		if value, exists := settingsMap[key]; exists {
			return value == "true"
		}
		return defaultValue
	}

	// Helper function to get string value
	getString := func(key string, defaultValue string) string {
		if value, exists := settingsMap[key]; exists {
			return value
		}
		return defaultValue
	}

	// Helper function to get int value
	getInt := func(key string, defaultValue int) int {
		if value, exists := settingsMap[key]; exists {
			if intValue, err := strconv.Atoi(value); err == nil {
				return intValue
			}
		}
		return defaultValue
	}

	// Build response
	response := &system_setting.GeneralSettingsResponse{
		General: system_setting.GeneralGroup{
			EnableDeposit:              getBool(system_setting.KeyEnableDeposit, true),
			EnableDepositRemark:        getString(system_setting.KeyEnableDepositRemark, ""),
			EnableWithdraw:             getBool(system_setting.KeyEnableWithdraw, true),
			EnableWithdrawRemark:       getString(system_setting.KeyEnableWithdrawRemark, ""),
			EnableGame:                 getBool(system_setting.KeyEnableGame, true),
			EnableGameRemark:           getString(system_setting.KeyEnableGameRemark, ""),
			EnableTruemoney:            getBool(system_setting.KeyEnableTruemoney, false),
			EnableTruemoneyRemark:      getString(system_setting.KeyEnableTruemoneyRemark, ""),
			EnableMemberRegister:       getBool(system_setting.KeyEnableMemberRegister, true),
			EnableMemberRegisterRemark: getString(system_setting.KeyEnableMemberRegisterRemark, ""),
		},
		DepositWithdraw: system_setting.DepositWithdrawGroup{
			EnableSlipVerify:               getBool(system_setting.KeyEnableSlipVerify, false),
			EnableSlipVerifyRemark:         getString(system_setting.KeyEnableSlipVerifyRemark, ""),
			EnablePaymentGatewayOnly:       getBool(system_setting.KeyEnablePaymentGatewayOnly, false),
			EnablePaymentGatewayOnlyRemark: getString(system_setting.KeyEnablePaymentGatewayOnlyRemark, ""),
			EnableHideDepositChannel:       getBool(system_setting.KeyEnableHideDepositChannel, false),
			EnableHideDepositChannelRemark: getString(system_setting.KeyEnableHideDepositChannelRemark, ""),
			EnableShowDepositDecimal:       getBool(system_setting.KeyEnableShowDepositDecimal, false),
			EnableShowDepositDecimalRemark: getString(system_setting.KeyEnableShowDepositDecimalRemark, ""),
			EnableWithdrawPincode:          getBool(system_setting.KeyEnableWithdrawPincode, false),
			EnableWithdrawPincodeRemark:    getString(system_setting.KeyEnableWithdrawPincodeRemark, ""),
			PlayerAttemptLimit:             getInt(system_setting.KeyPlayerAttemptLimit, 0),
			TimeoutDeposit:                 getInt(system_setting.KeyTimeoutDeposit, 15),
			TimeoutWithdraw:                getInt(system_setting.KeyTimeoutWithdraw, 15),
		},
		Analytics: system_setting.AnalyticsGroup{
			EnableGoogleAnalytics: getBool(system_setting.KeyEnableGoogleAnalytics, false),
			GoogleAnalyticsKey:    getString(system_setting.KeyGoogleAnalyticsKey, ""),
			EnableFacebookPixel:   getBool(system_setting.KeyEnableFacebookPixel, false),
			FacebookPixelID:       getString(system_setting.KeyFacebookPixelID, ""),
		},
		Other: system_setting.OtherGroup{
			EnableSettingUsername:       getBool(system_setting.KeyEnableSettingUsername, false),
			EnableSettingUsernameRemark: getString(system_setting.KeyEnableSettingUsernameRemark, ""),
			EnableSettingGender:         getBool(system_setting.KeyEnableSettingGender, false),
			EnableSettingGenderRemark:   getString(system_setting.KeyEnableSettingGenderRemark, ""),
			OTPOption:                   getString(system_setting.KeyOTPOption, "sms"),
			EnableOTPBypass:             getBool(system_setting.KeyEnableOTPBypass, false),
			EnableOTPBypassRemark:       getString(system_setting.KeyEnableOTPBypassRemark, ""),
			CaptchaConfigPublic:         getString(system_setting.KeyCaptchaConfigPublic, ""),
			CaptchaConfigPrivate:        getString(system_setting.KeyCaptchaConfigPrivate, ""),
			AdminLang:                   getString(system_setting.KeyAdminLang, "th"),
			PlayerLang:                  getString(system_setting.KeyPlayerLang, "th"),
		},
	}

	log.Info("general settings retrieved successfully")
	return response, nil
}

// UpdateGeneralSettings updates general settings
func (s *systemSettingService) UpdateGeneralSettings(ctx context.Context, req system_setting.UpdateGeneralSettingsRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateGeneralSettings")

	// Helper function to update a setting
	updateSetting := func(key, value string) error {
		updateReq := system_setting.UpdateSystemSettingRequest{Value: value}
		return s.UpdateSystemSetting(ctx, key, updateReq)
	}

	// Helper function to convert bool to string
	boolToString := func(b bool) string {
		if b {
			return "true"
		}
		return "false"
	}

	// Update General group settings
	if req.General != nil {
		if err := updateSetting(system_setting.KeyEnableDeposit, boolToString(req.General.EnableDeposit)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableDepositRemark, req.General.EnableDepositRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableWithdraw, boolToString(req.General.EnableWithdraw)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableWithdrawRemark, req.General.EnableWithdrawRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableGame, boolToString(req.General.EnableGame)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableGameRemark, req.General.EnableGameRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableTruemoney, boolToString(req.General.EnableTruemoney)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableTruemoneyRemark, req.General.EnableTruemoneyRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableMemberRegister, boolToString(req.General.EnableMemberRegister)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableMemberRegisterRemark, req.General.EnableMemberRegisterRemark); err != nil {
			return err
		}
	}

	// Update DepositWithdraw group settings
	if req.DepositWithdraw != nil {
		if err := updateSetting(system_setting.KeyEnableSlipVerify, boolToString(req.DepositWithdraw.EnableSlipVerify)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableSlipVerifyRemark, req.DepositWithdraw.EnableSlipVerifyRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnablePaymentGatewayOnly, boolToString(req.DepositWithdraw.EnablePaymentGatewayOnly)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnablePaymentGatewayOnlyRemark, req.DepositWithdraw.EnablePaymentGatewayOnlyRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableHideDepositChannel, boolToString(req.DepositWithdraw.EnableHideDepositChannel)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableHideDepositChannelRemark, req.DepositWithdraw.EnableHideDepositChannelRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableShowDepositDecimal, boolToString(req.DepositWithdraw.EnableShowDepositDecimal)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableShowDepositDecimalRemark, req.DepositWithdraw.EnableShowDepositDecimalRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableWithdrawPincode, boolToString(req.DepositWithdraw.EnableWithdrawPincode)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableWithdrawPincodeRemark, req.DepositWithdraw.EnableWithdrawPincodeRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyPlayerAttemptLimit, strconv.Itoa(req.DepositWithdraw.PlayerAttemptLimit)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyTimeoutDeposit, strconv.Itoa(req.DepositWithdraw.TimeoutDeposit)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyTimeoutWithdraw, strconv.Itoa(req.DepositWithdraw.TimeoutWithdraw)); err != nil {
			return err
		}
	}

	// Update Analytics group settings
	if req.Analytics != nil {
		if err := updateSetting(system_setting.KeyEnableGoogleAnalytics, boolToString(req.Analytics.EnableGoogleAnalytics)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyGoogleAnalyticsKey, req.Analytics.GoogleAnalyticsKey); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableFacebookPixel, boolToString(req.Analytics.EnableFacebookPixel)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyFacebookPixelID, req.Analytics.FacebookPixelID); err != nil {
			return err
		}
	}

	// Update Other group settings
	if req.Other != nil {
		if err := updateSetting(system_setting.KeyEnableSettingUsername, boolToString(req.Other.EnableSettingUsername)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableSettingUsernameRemark, req.Other.EnableSettingUsernameRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableSettingGender, boolToString(req.Other.EnableSettingGender)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableSettingGenderRemark, req.Other.EnableSettingGenderRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyOTPOption, req.Other.OTPOption); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableOTPBypass, boolToString(req.Other.EnableOTPBypass)); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyEnableOTPBypassRemark, req.Other.EnableOTPBypassRemark); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyCaptchaConfigPublic, req.Other.CaptchaConfigPublic); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyCaptchaConfigPrivate, req.Other.CaptchaConfigPrivate); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyAdminLang, req.Other.AdminLang); err != nil {
			return err
		}
		if err := updateSetting(system_setting.KeyPlayerLang, req.Other.PlayerLang); err != nil {
			return err
		}
	}

	log.Info("general settings updated successfully")
	return nil
}

// GetSEOSettings gets SEO settings
func (s *systemSettingService) GetSEOSettings(ctx context.Context) (*system_setting.SEOSettingsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetSEOSettings")

	// Get all settings (using existing ListSystemSettings logic)
	settingsResponse, err := s.ListSystemSettings(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list system settings")
		return nil, err
	}

	// Convert response to settings for processing
	settingsMap := make(map[string]string)
	for _, resp := range settingsResponse {
		settingsMap[resp.Key] = resp.Value
	}

	// Helper function to get string value
	getString := func(key string, defaultValue string) string {
		if value, exists := settingsMap[key]; exists {
			return value
		}
		return defaultValue
	}

	// Build response
	response := &system_setting.SEOSettingsResponse{
		SiteTitle:       getString(system_setting.KeySEOSiteTitle, ""),
		Title:           getString(system_setting.KeySEOTitle, ""),
		MetaDescription: getString(system_setting.KeySEOMetaDescription, ""),
		Favicon:         getString(system_setting.KeySEOFavicon, ""),
		FeaturedImage:   getString(system_setting.KeySEOFeaturedImage, ""),
	}

	log.Info("SEO settings retrieved successfully")
	return response, nil
}

// UpdateSEOSettings updates SEO settings
func (s *systemSettingService) UpdateSEOSettings(ctx context.Context, req system_setting.UpdateSEOSettingsRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateSEOSettings")

	// Helper function to update a setting
	updateSetting := func(key, value string) error {
		updateReq := system_setting.UpdateSystemSettingRequest{Value: value}
		return s.UpdateSystemSetting(ctx, key, updateReq)
	}

	// Update SEO settings
	if err := updateSetting(system_setting.KeySEOSiteTitle, req.SiteTitle); err != nil {
		log.WithError(err).Error("failed to update site title")
		return err
	}

	if err := updateSetting(system_setting.KeySEOTitle, req.Title); err != nil {
		log.WithError(err).Error("failed to update title")
		return err
	}

	if err := updateSetting(system_setting.KeySEOMetaDescription, req.MetaDescription); err != nil {
		log.WithError(err).Error("failed to update meta description")
		return err
	}

	// Update favicon only if provided
	if req.Favicon != nil && *req.Favicon != "" {
		if err := updateSetting(system_setting.KeySEOFavicon, *req.Favicon); err != nil {
			log.WithError(err).Error("failed to update favicon")
			return err
		}
	}

	// Update featured image only if provided
	if req.FeaturedImage != nil && *req.FeaturedImage != "" {
		if err := updateSetting(system_setting.KeySEOFeaturedImage, *req.FeaturedImage); err != nil {
			log.WithError(err).Error("failed to update featured image")
			return err
		}
	}

	log.Info("SEO settings updated successfully")
	return nil
}

// GetSiteImageSettings gets SiteImage settings
func (s *systemSettingService) GetSiteImageSettings(ctx context.Context) (*system_setting.SiteImageSettingsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetSiteImageSettings")

	// Get all settings (using existing ListSystemSettings logic)
	settingsResponse, err := s.ListSystemSettings(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list system settings")
		return nil, err
	}

	// Convert response to settings for processing
	settingsMap := make(map[string]string)
	for _, resp := range settingsResponse {
		settingsMap[resp.Key] = resp.Value
	}

	// Helper function to get string value
	getString := func(key string, defaultValue string) string {
		if value, exists := settingsMap[key]; exists {
			return value
		}
		return defaultValue
	}

	// Build response
	response := &system_setting.SiteImageSettingsResponse{
		LogoBeforeLogin:               getString(system_setting.KeySiteImageLogoBeforeLogin, ""),
		LogoAfterLogin:                getString(system_setting.KeySiteImageLogoAfterLogin, ""),
		GameLoading:                   getString(system_setting.KeySiteImageGameLoading, ""),
		LevelIcon:                     getString(system_setting.KeySiteImageLevelIcon, ""),
		NotificationText:              getString(system_setting.KeySiteImageNotificationText, ""),
		NotificationPopup:             getString(system_setting.KeySiteImageNotificationPopup, ""),
		DepositSuccess:                getString(system_setting.KeySiteImageDepositSuccess, ""),
		WithdrawSuccess:               getString(system_setting.KeySiteImageWithdrawSuccess, ""),
		DepositFail:                   getString(system_setting.KeySiteImageDepositFail, ""),
		WithdrawFail:                  getString(system_setting.KeySiteImageWithdrawFail, ""),
		EnableVideoTutorial:           getString(system_setting.KeySiteImageEnableVideoTutorial, ""),
		VideoDeposit:                  getString(system_setting.KeySiteImageVideoDeposit, ""),
		VideoDepositSmall:             getString(system_setting.KeySiteImageVideoDepositSmall, ""),
		VideoDepositDecimal:           getString(system_setting.KeySiteImageVideoDepositDecimal, ""),
		VideoDepositDecimalSmall:      getString(system_setting.KeySiteImageVideoDepositDecimalSmall, ""),
		VideoDepositQR:                getString(system_setting.KeySiteImageVideoDepositQR, ""),
		VideoDepositQRSmall:           getString(system_setting.KeySiteImageVideoDepositQRSmall, ""),
		VideoDepositTruewallet:        getString(system_setting.KeySiteImageVideoDepositTruewallet, ""),
		VideoDepositTruewalletSmall:   getString(system_setting.KeySiteImageVideoDepositTruewalletSmall, ""),
		NetworkTutorialImage:          getString(system_setting.KeySiteNetworkTutorialImage, ""),
		NetworkTutorialText:           getString(system_setting.KeySiteNetworkTutorialText, ""),
		NetworkTutorialMakeMoneyImage: getString(system_setting.KeySiteNetworkTutorialMakeMoneyImage, ""),
	}

	log.Info("SiteImage settings retrieved successfully")
	return response, nil
}

// UpdateSiteImageSettings updates SiteImage settings
func (s *systemSettingService) UpdateSiteImageSettings(ctx context.Context, req system_setting.UpdateSiteImageSettingsRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateSiteImageSettings")

	// Helper function to update a setting
	updateSetting := func(key, value string) error {
		updateReq := system_setting.UpdateSystemSettingRequest{Value: value}
		return s.UpdateSystemSetting(ctx, key, updateReq)
	}

	// Update settings normally (deletion is handled in HTTP layer)
	if req.LogoBeforeLogin != nil {
		if err := updateSetting(system_setting.KeySiteImageLogoBeforeLogin, *req.LogoBeforeLogin); err != nil {
			log.WithError(err).Error("failed to update logo before login")
			return err
		}
	}
	if req.LogoAfterLogin != nil {
		if err := updateSetting(system_setting.KeySiteImageLogoAfterLogin, *req.LogoAfterLogin); err != nil {
			log.WithError(err).Error("failed to update logo after login")
			return err
		}
	}
	if req.GameLoading != nil {
		if err := updateSetting(system_setting.KeySiteImageGameLoading, *req.GameLoading); err != nil {
			log.WithError(err).Error("failed to update game loading")
			return err
		}
	}
	if req.LevelIcon != nil {
		if err := updateSetting(system_setting.KeySiteImageLevelIcon, *req.LevelIcon); err != nil {
			log.WithError(err).Error("failed to update level icon")
			return err
		}
	}
	if req.NotificationText != nil {
		if err := updateSetting(system_setting.KeySiteImageNotificationText, *req.NotificationText); err != nil {
			log.WithError(err).Error("failed to update notification text")
			return err
		}
	}
	if req.NotificationPopup != nil {
		if err := updateSetting(system_setting.KeySiteImageNotificationPopup, *req.NotificationPopup); err != nil {
			log.WithError(err).Error("failed to update notification popup")
			return err
		}
	}
	if req.DepositSuccess != nil {
		if err := updateSetting(system_setting.KeySiteImageDepositSuccess, *req.DepositSuccess); err != nil {
			log.WithError(err).Error("failed to update deposit success")
			return err
		}
	}
	if req.WithdrawSuccess != nil {
		if err := updateSetting(system_setting.KeySiteImageWithdrawSuccess, *req.WithdrawSuccess); err != nil {
			log.WithError(err).Error("failed to update withdraw success")
			return err
		}
	}
	if req.DepositFail != nil {
		if err := updateSetting(system_setting.KeySiteImageDepositFail, *req.DepositFail); err != nil {
			log.WithError(err).Error("failed to update deposit fail")
			return err
		}
	}
	if req.WithdrawFail != nil {
		if err := updateSetting(system_setting.KeySiteImageWithdrawFail, *req.WithdrawFail); err != nil {
			log.WithError(err).Error("failed to update withdraw fail")
			return err
		}
	}
	if req.EnableVideoTutorial != nil {
		if err := updateSetting(system_setting.KeySiteImageEnableVideoTutorial, *req.EnableVideoTutorial); err != nil {
			log.WithError(err).Error("failed to update enable video tutorial")
			return err
		}
	}
	if req.VideoDeposit != nil {
		if err := updateSetting(system_setting.KeySiteImageVideoDeposit, *req.VideoDeposit); err != nil {
			log.WithError(err).Error("failed to update video deposit")
			return err
		}
	}
	if req.VideoDepositSmall != nil {
		if err := updateSetting(system_setting.KeySiteImageVideoDepositSmall, *req.VideoDepositSmall); err != nil {
			log.WithError(err).Error("failed to update video deposit small")
			return err
		}
	}
	if req.VideoDepositDecimal != nil {
		if err := updateSetting(system_setting.KeySiteImageVideoDepositDecimal, *req.VideoDepositDecimal); err != nil {
			log.WithError(err).Error("failed to update video deposit decimal")
			return err
		}
	}
	if req.VideoDepositDecimalSmall != nil {
		if err := updateSetting(system_setting.KeySiteImageVideoDepositDecimalSmall, *req.VideoDepositDecimalSmall); err != nil {
			log.WithError(err).Error("failed to update video deposit decimal small")
			return err
		}
	}
	if req.VideoDepositQR != nil {
		if err := updateSetting(system_setting.KeySiteImageVideoDepositQR, *req.VideoDepositQR); err != nil {
			log.WithError(err).Error("failed to update video deposit QR")
			return err
		}
	}
	if req.VideoDepositQRSmall != nil {
		if err := updateSetting(system_setting.KeySiteImageVideoDepositQRSmall, *req.VideoDepositQRSmall); err != nil {
			log.WithError(err).Error("failed to update video deposit QR small")
			return err
		}
	}
	if req.VideoDepositTruewallet != nil {
		if err := updateSetting(system_setting.KeySiteImageVideoDepositTruewallet, *req.VideoDepositTruewallet); err != nil {
			log.WithError(err).Error("failed to update video deposit TrueWallet")
			return err
		}
	}
	if req.VideoDepositTruewalletSmall != nil {
		if err := updateSetting(system_setting.KeySiteImageVideoDepositTruewalletSmall, *req.VideoDepositTruewalletSmall); err != nil {
			log.WithError(err).Error("failed to update video deposit TrueWallet small")
			return err
		}
	}
	if req.NetworkTutorialImage != nil {
		if err := updateSetting(system_setting.KeySiteNetworkTutorialImage, *req.NetworkTutorialImage); err != nil {
			log.WithError(err).Error("failed to update network tutorial image")
			return err
		}
	}
	if req.NetworkTutorialText != nil {
		if err := updateSetting(system_setting.KeySiteNetworkTutorialText, *req.NetworkTutorialText); err != nil {
			log.WithError(err).Error("failed to update network tutorial text")
			return err
		}
	}
	if req.NetworkTutorialMakeMoneyImage != nil {
		if err := updateSetting(system_setting.KeySiteNetworkTutorialMakeMoneyImage, *req.NetworkTutorialMakeMoneyImage); err != nil {
			log.WithError(err).Error("failed to update network tutorial make money image")
			return err
		}
	}

	log.Info("SEO settings updated successfully")
	return nil
}

// GetCommissionSettings gets commission settings
func (s *systemSettingService) GetCommissionSettings(ctx context.Context) (*system_setting.CommissionSettingsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetCommissionSettings")

	// Get auto approval threshold
	autoApprovalThresholdSetting, err := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyCommissionAutoApprovalThreshold)
	if err != nil {
		// Create default if not exists
		defaultSetting := system_setting.GetDefaultCommissionAutoApprovalThreshold()
		if err := s.systemSettingRepo.Create(ctx, defaultSetting); err != nil {
			log.WithError(err).Error("failed to create default commission auto approval threshold setting")
			return nil, err
		}
		autoApprovalThresholdSetting = defaultSetting
	}

	// Get min withdraw amount
	minWithdrawAmountSetting, err := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyCommissionMinWithdrawAmount)
	if err != nil {
		// Create default if not exists
		defaultSetting := system_setting.GetDefaultCommissionMinWithdrawAmount()
		if err := s.systemSettingRepo.Create(ctx, defaultSetting); err != nil {
			log.WithError(err).Error("failed to create default commission min withdraw amount setting")
			return nil, err
		}
		minWithdrawAmountSetting = defaultSetting
	}

	// Parse values
	autoApprovalThreshold, err := strconv.ParseFloat(autoApprovalThresholdSetting.Value, 64)
	if err != nil {
		log.WithError(err).Error("failed to parse auto approval threshold value")
		return nil, errors.NewInternalError("invalid auto approval threshold value")
	}

	minWithdrawAmount, err := strconv.ParseFloat(minWithdrawAmountSetting.Value, 64)
	if err != nil {
		log.WithError(err).Error("failed to parse min withdraw amount value")
		return nil, errors.NewInternalError("invalid min withdraw amount value")
	}

	response := &system_setting.CommissionSettingsResponse{
		AutoApprovalThreshold: autoApprovalThreshold,
		MinWithdrawAmount:     minWithdrawAmount,
	}

	log.Info("commission settings retrieved successfully")
	return response, nil
}

// UpdateCommissionSettings updates commission settings
func (s *systemSettingService) UpdateCommissionSettings(ctx context.Context, req system_setting.UpdateCommissionSettingsRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateCommissionSettings")

	// Helper function to update a setting
	updateSetting := func(key, value string) error {
		setting, err := s.systemSettingRepo.GetByKey(ctx, key)
		if err != nil {
			log.WithError(err).WithField("key", key).Error("failed to get setting")
			return err
		}

		updateReq := system_setting.UpdateSystemSettingRequest{Value: value}
		if err := setting.Update(updateReq); err != nil {
			log.WithError(err).WithField("key", key).Error("failed to update setting value")
			return err
		}

		if err := s.systemSettingRepo.Update(ctx, setting); err != nil {
			log.WithError(err).WithField("key", key).Error("failed to save updated setting")
			return err
		}

		return nil
	}

	// Update auto approval threshold
	if req.AutoApprovalThreshold != nil {
		value := strconv.FormatFloat(*req.AutoApprovalThreshold, 'f', 2, 64)
		if err := updateSetting(system_setting.KeyCommissionAutoApprovalThreshold, value); err != nil {
			log.WithError(err).Error("failed to update commission auto approval threshold")
			return err
		}
	}

	// Update min withdraw amount
	if req.MinWithdrawAmount != nil {
		value := strconv.FormatFloat(*req.MinWithdrawAmount, 'f', 2, 64)
		if err := updateSetting(system_setting.KeyCommissionMinWithdrawAmount, value); err != nil {
			log.WithError(err).Error("failed to update commission min withdraw amount")
			return err
		}
	}

	log.Info("commission settings updated successfully")
	return nil
}

// GetReferralSettings gets referral settings
func (s *systemSettingService) GetReferralSettings(ctx context.Context) (*system_setting.ReferralSettingsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetReferralSettings")

	// Get auto approval threshold
	autoApprovalThresholdSetting, err := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyReferralAutoApprovalThreshold)
	if err != nil {
		// Create default if not exists
		defaultSetting := system_setting.GetDefaultReferralAutoApprovalThreshold()
		if err := s.systemSettingRepo.Create(ctx, defaultSetting); err != nil {
			log.WithError(err).Error("failed to create default referral auto approval threshold setting")
			return nil, err
		}
		autoApprovalThresholdSetting = defaultSetting
	}

	// Get min withdraw amount
	minWithdrawAmountSetting, err := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyReferralMinWithdrawAmount)
	if err != nil {
		// Create default if not exists
		defaultSetting := system_setting.GetDefaultReferralMinWithdrawAmount()
		if err := s.systemSettingRepo.Create(ctx, defaultSetting); err != nil {
			log.WithError(err).Error("failed to create default referral min withdraw amount setting")
			return nil, err
		}
		minWithdrawAmountSetting = defaultSetting
	}

	// Parse values
	autoApprovalThreshold, err := strconv.ParseFloat(autoApprovalThresholdSetting.Value, 64)
	if err != nil {
		log.WithError(err).Error("failed to parse auto approval threshold value")
		return nil, errors.NewInternalError("invalid auto approval threshold value")
	}

	minWithdrawAmount, err := strconv.ParseFloat(minWithdrawAmountSetting.Value, 64)
	if err != nil {
		log.WithError(err).Error("failed to parse min withdraw amount value")
		return nil, errors.NewInternalError("invalid min withdraw amount value")
	}

	response := &system_setting.ReferralSettingsResponse{
		AutoApprovalThreshold: autoApprovalThreshold,
		MinWithdrawAmount:     minWithdrawAmount,
	}

	log.Info("referral settings retrieved successfully")
	return response, nil
}

// UpdateReferralSettings updates referral settings
func (s *systemSettingService) UpdateReferralSettings(ctx context.Context, req system_setting.UpdateReferralSettingsRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateReferralSettings")

	// Helper function to update a setting
	updateSetting := func(key, value string) error {
		setting, err := s.systemSettingRepo.GetByKey(ctx, key)
		if err != nil {
			log.WithError(err).WithField("key", key).Error("failed to get setting")
			return err
		}

		updateReq := system_setting.UpdateSystemSettingRequest{Value: value}
		if err := setting.Update(updateReq); err != nil {
			log.WithError(err).WithField("key", key).Error("failed to update setting value")
			return err
		}

		if err := s.systemSettingRepo.Update(ctx, setting); err != nil {
			log.WithError(err).WithField("key", key).Error("failed to save updated setting")
			return err
		}

		return nil
	}

	// Update auto approval threshold
	if req.AutoApprovalThreshold != nil {
		value := strconv.FormatFloat(*req.AutoApprovalThreshold, 'f', 2, 64)
		if err := updateSetting(system_setting.KeyReferralAutoApprovalThreshold, value); err != nil {
			log.WithError(err).Error("failed to update referral auto approval threshold")
			return err
		}
	}

	// Update min withdraw amount
	if req.MinWithdrawAmount != nil {
		value := strconv.FormatFloat(*req.MinWithdrawAmount, 'f', 2, 64)
		if err := updateSetting(system_setting.KeyReferralMinWithdrawAmount, value); err != nil {
			log.WithError(err).Error("failed to update referral min withdraw amount")
			return err
		}
	}

	log.Info("referral settings updated successfully")
	return nil
}

// FileUpload uploads a file to S3 for system setting
func (s *systemSettingService) FileUpload(ctx context.Context, fileBody *http.Request, fieldName string) (*system_setting.FileUploadResponse, error) {
	// Use provided field name or default to "file"
	if fieldName == "" {
		fieldName = "file"
	}

	fileReader, _, err := fileBody.FormFile(fieldName)
	if err != nil {
		s.logger.WithError(err).WithField("field_name", fieldName).Error("failed to read file from request")
		return nil, errors.NewValidationError("failed to read file from request")
	}

	pathName := "backoffice/system-settings/"

	fileData, err := s.awsS3Repo.UploadFileToS3(ctx, pathName, fileReader)
	if err != nil {
		s.logger.WithError(err).Error("failed to upload file to S3")
		return nil, errors.NewValidationError("failed to upload file to S3")
	}

	fileUrl := system_setting.FileUploadResponse{
		FileUrl: fileData.FileUrl,
	}

	return &fileUrl, nil
}

// DeleteFile deletes a file from S3 for system setting
func (s *systemSettingService) DeleteFile(ctx context.Context, req *system_setting.DeleteFileRequest) error {
	if err := s.awsS3Repo.DeleteFileFromS3(ctx, req.FileUrl); err != nil {
		s.logger.WithError(err).Error("failed to delete file from S3")
		return errors.NewValidationError("failed to delete file from S3")
	}

	s.logger.WithField("file_url", req.FileUrl).Info("file deleted from S3 successfully")
	return nil
}

// GetMemberLevelSettings gets member level calculation settings
func (s *systemSettingService) GetMemberLevelSettings(ctx context.Context) (*system_setting.MemberLevelSettingsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberLevelSettings")

	// Get all member level settings
	upgradeType, _ := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyMemberLevelUpgradeType)
	upgradeTime, _ := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyMemberLevelUpgradeTime)
	upgradeDay, _ := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyMemberLevelUpgradeDay)
	downgradeType, _ := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyMemberLevelDowngradeType)
	downgradeTime, _ := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyMemberLevelDowngradeTime)
	downgradeDay, _ := s.systemSettingRepo.GetByKey(ctx, system_setting.KeyMemberLevelDowngradeDay)

	response := &system_setting.MemberLevelSettingsResponse{
		UpgradeType:   "daily",
		UpgradeTime:   "03:00",
		DowngradeType: "daily",
		DowngradeTime: "04:30",
	}

	// Set actual values if they exist
	if upgradeType != nil && upgradeType.Value != "" {
		response.UpgradeType = upgradeType.Value
	}
	if upgradeTime != nil && upgradeTime.Value != "" {
		response.UpgradeTime = upgradeTime.Value
	}
	if downgradeType != nil && downgradeType.Value != "" {
		response.DowngradeType = downgradeType.Value
	}
	if downgradeTime != nil && downgradeTime.Value != "" {
		response.DowngradeTime = downgradeTime.Value
	}

	// Parse upgrade day
	if upgradeDay != nil && upgradeDay.Value != "" {
		if day, err := strconv.Atoi(upgradeDay.Value); err == nil {
			response.UpgradeDay = &day
		}
	}

	// Parse downgrade day
	if downgradeDay != nil && downgradeDay.Value != "" {
		if day, err := strconv.Atoi(downgradeDay.Value); err == nil {
			response.DowngradeDay = &day
		}
	}

	log.Info("member level settings retrieved successfully")
	return response, nil
}

// UpdateMemberLevelSettings updates member level calculation settings
func (s *systemSettingService) UpdateMemberLevelSettings(ctx context.Context, req system_setting.UpdateMemberLevelSettingsRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateMemberLevelSettings")

	// Note: Time validation removed as upgrade_time and downgrade_time are now optional (display only)

	// Update settings
	updateReq := system_setting.UpdateSystemSettingRequest{}

	// Update upgrade settings
	updateReq.Value = req.UpgradeType
	if err := s.UpdateSystemSetting(ctx, system_setting.KeyMemberLevelUpgradeType, updateReq); err != nil {
		log.WithError(err).Error("failed to update upgrade type")
		return err
	}

	updateReq.Value = req.UpgradeTime
	if err := s.UpdateSystemSetting(ctx, system_setting.KeyMemberLevelUpgradeTime, updateReq); err != nil {
		log.WithError(err).Error("failed to update upgrade time")
		return err
	}

	if req.UpgradeDay != nil {
		updateReq.Value = strconv.Itoa(*req.UpgradeDay)
	} else {
		updateReq.Value = ""
	}
	if err := s.UpdateSystemSetting(ctx, system_setting.KeyMemberLevelUpgradeDay, updateReq); err != nil {
		log.WithError(err).Error("failed to update upgrade day")
		return err
	}

	// Update downgrade settings
	updateReq.Value = req.DowngradeType
	if err := s.UpdateSystemSetting(ctx, system_setting.KeyMemberLevelDowngradeType, updateReq); err != nil {
		log.WithError(err).Error("failed to update downgrade type")
		return err
	}

	updateReq.Value = req.DowngradeTime
	if err := s.UpdateSystemSetting(ctx, system_setting.KeyMemberLevelDowngradeTime, updateReq); err != nil {
		log.WithError(err).Error("failed to update downgrade time")
		return err
	}

	if req.DowngradeDay != nil {
		updateReq.Value = strconv.Itoa(*req.DowngradeDay)
	} else {
		updateReq.Value = ""
	}

	if err := s.UpdateSystemSetting(ctx, system_setting.KeyMemberLevelDowngradeDay, updateReq); err != nil {
		log.WithError(err).Error("failed to update downgrade day")
		return err
	}

	log.Info("member level settings updated successfully")
	return nil
}

// GetPlayerAttemptLimitOptions returns player attempt limit options for dropdown
func (s *systemSettingService) GetPlayerAttemptLimitOptions(ctx context.Context) (*system_setting.SettingOptionsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetPlayerAttemptLimitOptions")

	options := []system_setting.SettingOption{
		{
			Label: "ปิดใช้งาน",
			Value: "0",
		},
		{
			Label: "3",
			Value: "3",
		},
		{
			Label: "5",
			Value: "5",
		},
		{
			Label: "10",
			Value: "10",
		},
		{
			Label: "15",
			Value: "15",
		},
		{
			Label: "30",
			Value: "30",
		},
	}

	response := &system_setting.SettingOptionsResponse{
		Options: options,
	}

	log.WithField("options_count", len(options)).Info("player attempt limit options retrieved successfully")
	return response, nil
}

// GetTimeoutOptions returns timeout options for dropdown (deposit/withdraw)
func (s *systemSettingService) GetTimeoutOptions(ctx context.Context) (*system_setting.SettingOptionsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetTimeoutOptions")

	options := []system_setting.SettingOption{
		{
			Label: "15",
			Value: "15",
		},
		{
			Label: "30",
			Value: "30",
		},
	}

	response := &system_setting.SettingOptionsResponse{
		Options: options,
	}

	log.WithField("options_count", len(options)).Info("timeout options retrieved successfully")
	return response, nil
}
