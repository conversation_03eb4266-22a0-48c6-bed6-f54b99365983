package service

import (
	"blacking-api/internal/domain/response"
	"blacking-api/internal/domain/sms_provider"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"strings"
)

type SMSProviderService interface {
	CreateSMSProvider(ctx context.Context, req *sms_provider.SMSProviderRequest) error
	FindAllSMSProviders(ctx context.Context, req *sms_provider.SMSProviderSearchRequest) (*response.SuccessWithPagination, error)
	FindSMSProviderByID(ctx context.Context, id int64) (*sms_provider.SMSProviderByIdResponse, error)
	UpdateSMSProvider(ctx context.Context, id int64, req *sms_provider.SMSProviderRequest) error
	UpdateSMSProviderStatus(ctx context.Context, id int64, req *sms_provider.UpdateStatusRequest) error
	DeleteSMSProvider(ctx context.Context, id int64) error
}

type smsProviderService struct {
	smsProviderRepo     interfaces.SMSProviderRepository
	smsProviderNameRepo interfaces.SMSProviderNameRepository
	logger              logger.Logger
}

func NewSMSProviderService(
	smsProviderRepo interfaces.SMSProviderRepository,
	smsProviderNameRepo interfaces.SMSProviderNameRepository,
	logger logger.Logger,
) SMSProviderService {
	return &smsProviderService{
		smsProviderRepo:     smsProviderRepo,
		smsProviderNameRepo: smsProviderNameRepo,
		logger:              logger,
	}
}

func (s *smsProviderService) CreateSMSProvider(ctx context.Context, req *sms_provider.SMSProviderRequest) error {
	// Check for duplicate name
	nameDuplicate, err := s.smsProviderRepo.FindByNameDuplicate(ctx, req.Name)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate name")
		return err
	}
	if nameDuplicate {
		s.logger.Error("duplicate name")
		return errors.NewValidationError("name already exists")
	}

	// Check if provider name ID exists
	providerNameExists, err := s.smsProviderNameRepo.FindIdExists(ctx, req.ProviderNameID)
	if err != nil {
		s.logger.WithError(err).Error("failed to check if provider name ID exists")
		return err
	}
	if !providerNameExists {
		s.logger.Error("provider name ID does not exist")
		return errors.NewValidationError("provider name ID does not exist")
	}

	// Encrypt sensitive data
	encrypt, err := EncodeSMSProvider(req)
	if err != nil {
		s.logger.WithError(err).Error("failed to encode SMS provider")
		return err
	}

	// Create SMS provider
	err = s.smsProviderRepo.Create(ctx, encrypt)
	if err != nil {
		s.logger.WithError(err).Error("failed to create SMS provider")
		return err
	}

	return nil
}

func (s *smsProviderService) FindAllSMSProviders(ctx context.Context, req *sms_provider.SMSProviderSearchRequest) (*response.SuccessWithPagination, error) {
	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		s.logger.WithError(err).Error("failed to set pagination limits")
		return nil, err
	}

	pagination := helper.CalculatePagination(req.Page, req.Limit)

	var conditions []string
	var queryArgs []interface{}

	if req.Name != nil && *req.Name != "" {
		conditions = append(conditions, "UPPER(sp.name) ILIKE UPPER($1)")
		queryArgs = append(queryArgs, "%"+*req.Name+"%")
	}
	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	smsProviders, total, err := s.smsProviderRepo.FindAll(ctx, pagination.Limit, pagination.Offset, whereClause, queryArgs)
	if err != nil {
		s.logger.WithError(err).Error("failed to find all SMS providers")
		return nil, err
	}

	// Get all provider names to map IDs to names
	providerNames, err := s.smsProviderNameRepo.List(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to list SMS provider names")
		return nil, err
	}

	// Create a map of provider name IDs to names
	providerNameMap := make(map[int64]string)
	for _, pn := range providerNames {
		providerNameMap[pn.ID] = pn.Name
	}

	content := make([]*sms_provider.SMSProviderListResponse, len(smsProviders))
	if len(smsProviders) != 0 {
		for i, sp := range smsProviders {
			providerName := providerNameMap[sp.ProviderNameID]
			content[i] = sp.ToListResponse(providerName)
		}
	}

	responseData := &response.SuccessWithPagination{
		Message:    "SMS Providers retrieved successfully",
		Content:    content,
		Page:       int64(pagination.Page),
		Limit:      int64(pagination.Limit),
		TotalPages: helper.CalculateTotalPages(total, pagination.Limit),
		TotalItems: total,
	}

	return responseData, nil
}

func (s *smsProviderService) FindSMSProviderByID(ctx context.Context, id int64) (*sms_provider.SMSProviderByIdResponse, error) {
	smsProvider, err := s.smsProviderRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find SMS provider by ID")
		return nil, err
	}
	if smsProvider == nil {
		return nil, errors.NewNotFoundError("SMS provider not found")
	}

	decodedProvider, err := DecodeSMSProvider(smsProvider)
	if err != nil {
		s.logger.WithError(err).Error("failed to decode SMS provider")
		return nil, err
	}

	return decodedProvider.ToByIdResponse(), nil
}

func (s *smsProviderService) UpdateSMSProvider(ctx context.Context, id int64, req *sms_provider.SMSProviderRequest) error {
	// Check for duplicate name
	nameDuplicate, err := s.smsProviderRepo.FindByNameDuplicateAndIdNot(ctx, req.Name, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate name")
		return err
	}
	if nameDuplicate {
		s.logger.Error("duplicate name")
		return errors.NewValidationError("name already exists")
	}

	// Check if provider name ID exists
	providerNameExists, err := s.smsProviderNameRepo.FindIdExists(ctx, req.ProviderNameID)
	if err != nil {
		s.logger.WithError(err).Error("failed to check if provider name ID exists")
		return err
	}
	if !providerNameExists {
		s.logger.Error("provider name ID does not exist")
		return errors.NewValidationError("provider name ID does not exist")
	}

	// Update SMS provider
	err = s.smsProviderRepo.Update(ctx, id, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to update SMS provider")
		return err
	}

	return nil
}

func (s *smsProviderService) UpdateSMSProviderStatus(ctx context.Context, id int64, req *sms_provider.UpdateStatusRequest) error {
	err := s.smsProviderRepo.UpdateStatus(ctx, id, req.Name, req.Status)
	if err != nil {
		s.logger.WithError(err).Error("failed to update SMS provider status")
		return err
	}

	return nil
}

func (s *smsProviderService) DeleteSMSProvider(ctx context.Context, id int64) error {
	err := s.smsProviderRepo.Delete(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to delete SMS provider")
		return err
	}

	return nil
}

func EncodeSMSProvider(req *sms_provider.SMSProviderRequest) (*sms_provider.SMSProviderRequest, error) {
	apiKeyEncrypt, err := helper.Encrypt(req.APIKey)
	if err != nil {
		return nil, err
	}

	secretKeyEncrypt, err := helper.Encrypt(req.SecretKey)
	if err != nil {
		return nil, err
	}

	req.APIKey = apiKeyEncrypt
	req.SecretKey = secretKeyEncrypt

	return req, nil
}

func DecodeSMSProvider(provider *sms_provider.SMSProvider) (*sms_provider.SMSProvider, error) {
	apiKeyDecrypt, err := helper.Decrypt(provider.APIKey)
	if err != nil {
		return nil, err
	}

	secretKeyDecrypt, err := helper.Decrypt(provider.SecretKey)
	if err != nil {
		return nil, err
	}

	provider.APIKey = apiKeyDecrypt
	provider.SecretKey = secretKeyDecrypt

	return provider, nil
}
