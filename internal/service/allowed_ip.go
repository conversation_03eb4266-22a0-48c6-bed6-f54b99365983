package service

import (
	"blacking-api/internal/domain/allowed_ip"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
)

// AllowedIPService defines the interface for allowed IP service operations
type AllowedIPService interface {
	// ListAllowedIPs retrieves allowed IPs with pagination
	ListAllowedIPs(ctx context.Context, limit, offset int) ([]*allowed_ip.AllowedIPResponse, error)

	// GetAllowedIPsCount returns the total count of allowed IPs
	GetAllowedIPsCount(ctx context.Context) (int64, error)

	// SyncIPs synchronizes IP addresses (activate requested IPs, deactivate others)
	SyncIPs(ctx context.Context, req allowed_ip.SyncIPsRequest) error

	// IsIPAllowed checks if an IP address is allowed
	IsIPAllowed(ctx context.Context, ipAddress string) (bool, error)
}

type allowedIPService struct {
	allowedIPRepo interfaces.AllowedIPRepository
	logger        logger.Logger
}

// NewAllowedIPService creates a new allowed IP service
func NewAllowedIPService(allowedIPRepo interfaces.AllowedIPRepository, logger logger.Logger) AllowedIPService {
	return &allowedIPService{
		allowedIPRepo: allowedIPRepo,
		logger:        logger,
	}
}

// ListAllowedIPs retrieves allowed IPs with pagination
func (s *allowedIPService) ListAllowedIPs(ctx context.Context, limit, offset int) ([]*allowed_ip.AllowedIPResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListAllowedIPs")

	// Validate pagination parameters
	if limit <= 0 {
		limit = 10 // default limit
	}
	if limit > 100 {
		limit = 100 // max limit
	}
	if offset < 0 {
		offset = 0
	}

	// Get allowed IPs from repository
	allowedIPs, err := s.allowedIPRepo.List(ctx, limit, offset)
	if err != nil {
		log.WithError(err).Error("failed to get allowed IPs from repository")
		return nil, err
	}

	// Convert to response format
	var responses []*allowed_ip.AllowedIPResponse
	for _, allowedIP := range allowedIPs {
		response := allowedIP.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("allowed IPs retrieved successfully")
	return responses, nil
}

// GetAllowedIPsCount returns the total count of allowed IPs
func (s *allowedIPService) GetAllowedIPsCount(ctx context.Context) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetAllowedIPsCount")

	count, err := s.allowedIPRepo.Count(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get allowed IPs count from repository")
		return 0, err
	}

	log.WithField("count", count).Info("allowed IPs count retrieved successfully")
	return count, nil
}

// SyncIPs synchronizes IP addresses (activate requested IPs, deactivate others)
func (s *allowedIPService) SyncIPs(ctx context.Context, req allowed_ip.SyncIPsRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SyncIPs")

	// Validate request
	if len(req.IPAddresses) == 0 {
		return errors.NewValidationError("at least one IP address is required")
	}

	// Validate each IP address
	for _, ipItem := range req.IPAddresses {
		if !allowed_ip.IsValidIP(ipItem.Name) {
			return errors.NewValidationError("invalid IP address: " + ipItem.Name)
		}
	}

	// Get all existing IPs from database
	allExistingIPs, err := s.allowedIPRepo.List(ctx, 1000, 0) // Get all IPs (assuming max 1000)
	if err != nil {
		log.WithError(err).Error("failed to get existing IPs")
		return err
	}

	// Create a map of existing IPs for quick lookup
	existingIPMap := make(map[string]*allowed_ip.AllowedIP)
	for _, ip := range allExistingIPs {
		existingIPMap[ip.IPAddress] = ip
	}

	// Create a set of requested IPs for quick lookup
	requestedIPSet := make(map[string]bool)
	for _, ipItem := range req.IPAddresses {
		requestedIPSet[ipItem.Name] = true
	}

	// Process requested IPs - activate or create
	for _, ipItem := range req.IPAddresses {
		ipAddress := ipItem.Name
		if existingIP, exists := existingIPMap[ipAddress]; exists {
			// IP exists - ensure it's active
			if !existingIP.IsActive() {
				existingIP.Activate()
				if err := s.allowedIPRepo.Update(ctx, existingIP); err != nil {
					log.WithError(err).WithField("ip_address", ipAddress).Error("failed to activate existing IP")
					return err
				}
				log.WithField("ip_address", ipAddress).Info("IP activated")
			} else {
				log.WithField("ip_address", ipAddress).Info("IP already active")
			}
		} else {
			// IP doesn't exist - create new one
			createReq := allowed_ip.CreateAllowedIPRequest{
				IPAddress: ipAddress,
			}

			newIP, err := allowed_ip.NewAllowedIP(createReq)
			if err != nil {
				log.WithError(err).WithField("ip_address", ipAddress).Error("failed to create new IP domain object")
				return err
			}

			if err := s.allowedIPRepo.Create(ctx, newIP); err != nil {
				log.WithError(err).WithField("ip_address", ipAddress).Error("failed to create new IP")
				return err
			}
			log.WithField("ip_address", ipAddress).Info("new IP created")
		}
	}

	// Process existing IPs not in the request - deactivate them
	for ipAddress, existingIP := range existingIPMap {
		if !requestedIPSet[ipAddress] && existingIP.IsActive() {
			// IP not in request and currently active - deactivate it
			existingIP.Deactivate()
			if err := s.allowedIPRepo.Update(ctx, existingIP); err != nil {
				log.WithError(err).WithField("ip_address", ipAddress).Error("failed to deactivate IP")
				return err
			}
			log.WithField("ip_address", ipAddress).Info("IP deactivated")
		}
	}

	log.WithField("ip_count", len(req.IPAddresses)).Info("IP sync operation completed successfully")
	return nil
}

// IsIPAllowed checks if an IP address is allowed
func (s *allowedIPService) IsIPAllowed(ctx context.Context, ipAddress string) (bool, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "IsIPAllowed").WithField("ip_address", ipAddress)

	if !allowed_ip.IsValidIP(ipAddress) {
		return false, errors.NewValidationError("invalid IP address format")
	}

	isAllowed, err := s.allowedIPRepo.IsIPAllowed(ctx, ipAddress)
	if err != nil {
		log.WithError(err).Error("failed to check if IP is allowed")
		return false, err
	}

	log.WithField("is_allowed", isAllowed).Info("IP allowance check completed")
	return isAllowed, nil
}
