package service

import (
	"blacking-api/internal/domain/otp"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strconv"
)

// OTPService defines the interface for OTP service operations
type OTPService interface {
	SendOTP(ctx context.Context, req otp.SendOTPRequest) (*otp.OTPResponse, error)
	VerifyOTP(ctx context.Context, req otp.VerifyOTPRequest) (*otp.OTP, error)
	GetLastVerifyOTP(ctx context.Context, req otp.GetLastVerifyOTPRequest) (*otp.OTP, error)
	GetInUsedOTP(ctx context.Context, req otp.GetInUsedOTPRequest) (*otp.OTP, error)
	CleanupExpiredOTPs(ctx context.Context) error
	GetOTPOptions(ctx context.Context) (*otp.OTPOptionsResponse, error)

	// Admin operations
	ListOTPsForAdmin(ctx context.Context, limit, offset int, search, username, userID string, referUserIDs []string, purpose, dateFrom, dateTo string) ([]*otp.AdminOTPResponse, int64, error)
}

type otpService struct {
	otpRepo    interfaces.OTPRepository
	memberRepo interfaces.MemberRepository
	smsService SMSService
	logger     logger.Logger
}

// NewOTPService creates a new OTP service
func NewOTPService(otpRepo interfaces.OTPRepository, memberRepo interfaces.MemberRepository, smsService SMSService, logger logger.Logger) OTPService {
	return &otpService{
		otpRepo:    otpRepo,
		memberRepo: memberRepo,
		smsService: smsService,
		logger:     logger,
	}
}

// SendOTP sends OTP to recipient
func (s *otpService) SendOTP(ctx context.Context, req otp.SendOTPRequest) (*otp.OTPResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "SendOTP")

	// Create new OTP
	otpRecord, err := otp.NewOTP(req.Type, req.Purpose, req.Recipient)
	if err != nil {
		log.WithError(err).Error("failed to create OTP")
		return nil, err
	}

	// Save to repository
	if err := s.otpRepo.Create(ctx, otpRecord); err != nil {
		log.WithError(err).Error("failed to save OTP to repository")
		return nil, err
	}
	response := otpRecord.ToResponse()

	// Send OTP via appropriate channel
	if err := s.sendOTPMessage(ctx, otpRecord); err != nil {
		log.WithError(err).Error("failed to send OTP to provider not working")
		response.Message = "โปรติดต่อแอดมินเพื่อรับรหัสยืนยัน"
	}

	log.WithField("otp_id", response.ID).WithField("recipient", response.Recipient).Info("OTP sent successfully")
	return &response, nil
}

// VerifyOTP verifies OTP code
func (s *otpService) VerifyOTP(ctx context.Context, req otp.VerifyOTPRequest) (*otp.OTP, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "VerifyOTP")

	// Get latest valid OTP for recipient and purpose
	otpRecord, err := s.otpRepo.GetByRecipientAndPurpose(ctx, req.Recipient, req.Purpose)
	if err != nil {
		log.WithError(err).Error("failed to get OTP record")
		return nil, err
	}

	// Verify the OTP code
	if err := otpRecord.Verify(req.Code); err != nil {
		log.WithError(err).Error("OTP verification failed")
		return nil, err
	}

	// Update OTP record as used
	if err := s.otpRepo.Update(ctx, otpRecord); err != nil {
		log.WithError(err).Error("failed to update OTP record")
		return nil, err
	}

	log.WithField("otp_id", otpRecord.ID).WithField("recipient", otpRecord.Recipient).Info("OTP verified successfully")
	return otpRecord, nil
}

// GetLastVerifyOTP verifies OTP code
func (s *otpService) GetLastVerifyOTP(ctx context.Context, req otp.GetLastVerifyOTPRequest) (*otp.OTP, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetLastVerifyOTP")

	// Get latest valid OTP for recipient and purpose
	otpRecord, err := s.otpRepo.GetByRecipientAndPurpose(ctx, req.Recipient, req.Purpose)
	if err != nil {
		log.WithError(err).Error("failed to get OTP record")
		return nil, err
	}
	log.WithField("otp_id", otpRecord.ID).WithField("recipient", otpRecord.Recipient).Info("OTP retrieved successfully")
	return otpRecord, nil
}

// GetInUsedOTP verifies OTP code
func (s *otpService) GetInUsedOTP(ctx context.Context, req otp.GetInUsedOTPRequest) (*otp.OTP, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetInUsedOTP")

	// Get latest valid OTP for recipient and purpose
	otpRecord, err := s.otpRepo.GetByRecipientAndPurposeIsUsed(ctx, req.Recipient, req.Purpose)
	if err != nil {
		log.WithError(err).Error("failed to get OTP record")
		return nil, err
	}
	log.WithField("otp_id", otpRecord.ID).WithField("recipient", otpRecord.Recipient).Info("OTP retrieved successfully")
	return otpRecord, nil
}

// CleanupExpiredOTPs removes expired OTP records
func (s *otpService) CleanupExpiredOTPs(ctx context.Context) error {
	log := s.logger.WithContext(ctx).WithField("operation", "CleanupExpiredOTPs")

	if err := s.otpRepo.DeleteExpired(ctx); err != nil {
		log.WithError(err).Error("failed to cleanup expired OTPs")
		return err
	}

	log.Info("expired OTPs cleaned up successfully")
	return nil
}

// sendOTPMessage sends OTP via appropriate channel (SMS/Email)
func (s *otpService) sendOTPMessage(ctx context.Context, otpRecord *otp.OTP) error {
	switch otpRecord.Type {
	case otp.OTPTypePhone:
		return s.sendSMS(ctx, otpRecord)
	case otp.OTPTypeEmail:
		return s.sendEmail(ctx, otpRecord)
	default:
		return fmt.Errorf("unsupported OTP type: %s", otpRecord.Type)
	}
}

// sendSMS sends OTP via SMS using ThSMS service
func (s *otpService) sendSMS(ctx context.Context, otpRecord *otp.OTP) error {
	log := s.logger.WithContext(ctx).WithField("operation", "sendSMS")

	if s.smsService == nil {
		// Fallback to development mode if SMS service is not configured
		message := fmt.Sprintf("Your verification code is: %s (Ref: %s). Valid for 5 minutes.", otpRecord.Code, otpRecord.Reference)
		log.WithField("phone", otpRecord.Recipient).
			WithField("message", message).
			Info("SMS OTP sent (development mode - SMS service not configured)")
		return nil
	}

	// Send OTP via SMS service
	err := s.smsService.SendOTPSMS(ctx, otpRecord.Recipient, otpRecord.Code, otpRecord.Reference)
	if err != nil {
		log.WithError(err).Error("failed to send OTP SMS")
		return err
	}

	log.WithField("phone", otpRecord.Recipient).WithField("reference", otpRecord.Reference).Info("OTP SMS sent successfully")
	return nil
}

// sendEmail sends OTP via Email (placeholder implementation)
func (s *otpService) sendEmail(ctx context.Context, otpRecord *otp.OTP) error {
	log := s.logger.WithContext(ctx).WithField("operation", "sendEmail")

	// TODO: Implement actual email sending logic
	// For now, just log the OTP code for development
	subject := "Your Verification Code"
	message := fmt.Sprintf("Your verification code is: %s. This code will expire in 5 minutes.", otpRecord.Code)

	log.WithField("email", otpRecord.Recipient).
		WithField("subject", subject).
		WithField("message", message).
		Info("Email OTP sent (development mode)")

	// In production, integrate with email provider like:
	// - AWS SES
	// - SendGrid
	// - Mailgun

	return nil
}

// ListOTPsForAdmin retrieves OTPs with member information for admin
func (s *otpService) ListOTPsForAdmin(ctx context.Context, limit, offset int, search, username, userID string, referUserIDs []string, purpose, dateFrom, dateTo string) ([]*otp.AdminOTPResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListOTPsForAdmin")

	// Get total count
	totalCount, err := s.otpRepo.CountForAdmin(ctx, search, purpose, dateFrom, dateTo)
	if err != nil {
		log.WithError(err).Error("failed to count OTPs for admin")
		return nil, 0, err
	}

	// Get OTP records
	otps, err := s.otpRepo.ListForAdmin(ctx, limit, offset, search, purpose, dateFrom, dateTo)
	if err != nil {
		log.WithError(err).Error("failed to list OTPs for admin")
		return nil, 0, err
	}

	// Convert to admin response format with member information
	var adminOTPs []*otp.AdminOTPResponse
	for _, otpRecord := range otps {
		adminOTP := &otp.AdminOTPResponse{
			ID:        otpRecord.ID,
			Type:      otpRecord.Type,
			Purpose:   otpRecord.Purpose,
			Recipient: otpRecord.Recipient, // Full phone number for admin
			Code:      otpRecord.Code,      // OTP code for admin/support use
			Reference: otpRecord.Reference,
			IsUsed:    otpRecord.IsUsed,
			ExpiresAt: otpRecord.ExpiresAt,
			CreatedAt: otpRecord.CreatedAt,
			UsedAt:    otpRecord.UsedAt,
		}

		// Try to find member by phone number (only for verified OTPs)
		if otpRecord.Type == otp.OTPTypePhone && otpRecord.IsUsed {
			member, err := s.memberRepo.GetByPhone(ctx, otpRecord.Recipient)
			if err == nil && member != nil {
				adminOTP.MemberID = &member.ID
				adminOTP.Username = &member.Username
				adminOTP.FirstName = member.FirstName
				adminOTP.LastName = member.LastName
				adminOTP.Gender = member.Gender
				adminOTP.ReferCode = &member.ReferCode
				adminOTP.RegisterReferCode = member.RegisterReferCode
				adminOTP.IsEnable = &member.IsEnable
				adminOTP.RegisterStatus = &member.RegisterStatus
				adminOTP.CreatedMemberAt = &member.CreatedAt
			}
		}

		adminOTPs = append(adminOTPs, adminOTP)
	}

	// Apply additional filters based on member information
	if username != "" || userID != "" || len(referUserIDs) > 0 {
		var filteredOTPs []*otp.AdminOTPResponse
		for _, adminOTP := range adminOTPs {
			include := true

			if username != "" && (adminOTP.Username == nil || *adminOTP.Username != username) {
				include = false
			}

			if userID != "" && (adminOTP.MemberID == nil || strconv.Itoa(*adminOTP.MemberID) != userID) {
				include = false
			}

			// Filter by referUserIDs array - check if member's ReferUserID is in the array
			if len(referUserIDs) > 0 {
				// ถ้ามี referUserIDs filter แต่ไม่มี member หรือไม่มี ReferUserID ให้ exclude
				if adminOTP.MemberID == nil {
					include = false
				} else {
					// Get member details to check ReferUserID
					member, err := s.memberRepo.GetByID(ctx, strconv.Itoa(*adminOTP.MemberID))
					if err != nil || member == nil || member.ReferUserID == nil {
						include = false
					} else {
						// Check if member's ReferUserID is in the referUserIDs array
						found := false
						for _, refID := range referUserIDs {
							if strconv.Itoa(*member.ReferUserID) == refID {
								found = true
								break
							}
						}
						if !found {
							include = false
						}
					}
				}
			}

			if include {
				filteredOTPs = append(filteredOTPs, adminOTP)
			}
		}
		adminOTPs = filteredOTPs
		totalCount = int64(len(adminOTPs)) // Update count for filtered results
	}

	log.WithField("total_count", totalCount).WithField("returned_count", len(adminOTPs)).Info("OTPs listed for admin successfully")
	return adminOTPs, totalCount, nil
}

// GetOTPOptions returns available OTP options for dropdown
func (s *otpService) GetOTPOptions(ctx context.Context) (*otp.OTPOptionsResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetOTPOptions")

	options := []otp.OTPOption{
		{
			Label: "CAPTCHA",
			Value: "captcha",
		},
		{
			Label: "OTP",
			Value: "otp",
		},
		{
			Label: "OTP & CAPTCHA",
			Value: "otp,captcha",
		},
	}

	response := &otp.OTPOptionsResponse{
		Options: options,
	}

	log.WithField("options_count", len(options)).Info("OTP options retrieved successfully")
	return response, nil
}
