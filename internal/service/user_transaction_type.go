package service

import (
	"blacking-api/internal/domain/user_transaction_type"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
)

type UserTransactionTypeService interface {
	ListUserTransactionTypes(ctx context.Context) ([]*user_transaction_type.UserTransactionTypeResponse, error)
}

type userTransactionTypeService struct {
	userTransactionTypeRepo interfaces.UserTransactionTypeRepository
	logger                  logger.Logger
}

func NewUserTransactionTypeService(userTransactionTypeRepo interfaces.UserTransactionTypeRepository, logger logger.Logger) UserTransactionTypeService {
	return &userTransactionTypeService{
		userTransactionTypeRepo: userTransactionTypeRepo,
		logger:                  logger,
	}
}

func (s *userTransactionTypeService) ListUserTransactionTypes(ctx context.Context) ([]*user_transaction_type.UserTransactionTypeResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListUserTransactionTypes")

	transactionTypes, err := s.userTransactionTypeRepo.List(ctx)
	if err != nil {
		log.WithError(err).Error("failed to retrieve user transaction types from repository")
		return nil, err
	}

	// Convert to response format
	var responses []*user_transaction_type.UserTransactionTypeResponse
	for _, transactionType := range transactionTypes {
		response := transactionType.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("user transaction types retrieved successfully")
	return responses, nil
}
