package service

import (
	"blacking-api/internal/domain/banking"
	"blacking-api/internal/domain/response"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
	"fmt"
	"strings"
)

type BankingService interface {
	ListBankings(ctx context.Context, req *banking.BankingSearchRequest) (*response.SuccessWithPagination, error)
}

type bankingService struct {
	bankingRepo interfaces.BankingRepository
	logger      logger.Logger
}

func NewBankingService(bankingRepo interfaces.BankingRepository, logger logger.Logger) BankingService {
	return &bankingService{
		bankingRepo: bankingRepo,
		logger:      logger,
	}
}

func (s *bankingService) ListBankings(ctx context.Context, req *banking.BankingSearchRequest) (*response.SuccessWithPagination, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListBankings")

	if err := helper.UnlimitPagination(&req.<PERSON>, &req.Limit); err != nil {
		return nil, err
	}

	pagination := helper.CalculatePagination(req.Page, req.Limit)

	var conditions []string
	var queryArgs []interface{}

	if req.Name != nil && strings.TrimSpace(*req.Name) != "" {
		conditions = append(conditions, fmt.Sprintf("UPPER(name) ILIKE UPPER($%d)", len(queryArgs)+1))
		queryArgs = append(queryArgs, "%"+*req.Name+"%")
	}

	if req.ShortName != nil && strings.TrimSpace(*req.ShortName) != "" {
		conditions = append(conditions, fmt.Sprintf("UPPER(short_name) ILIKE UPPER($%d)", len(queryArgs)+1))
		queryArgs = append(queryArgs, "%"+*req.ShortName+"%")
	}

	if req.Code != nil && strings.TrimSpace(*req.Code) != "" {
		conditions = append(conditions, fmt.Sprintf("code ILIKE $%d", len(queryArgs)+1))
		queryArgs = append(queryArgs, "%"+*req.Code+"%")
	}

	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	bankings, total, err := s.bankingRepo.List(ctx, pagination.Limit, pagination.Offset, whereClause, queryArgs)
	if err != nil {
		log.WithError(err).Error("failed to list bankings")
		return nil, err
	}

	responses := &response.SuccessWithPagination{
		Message:    "List bankings successfully",
		Content:    bankings,
		Page:       int64(pagination.Page),
		Limit:      int64(pagination.Limit),
		TotalPages: helper.CalculateTotalPages(total, pagination.Limit),
		TotalItems: total,
	}

	return responses, nil
}
