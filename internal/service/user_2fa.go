package service

import (
	"blacking-api/internal/domain/user_2fa"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"encoding/base64"
	"strconv"

	"github.com/skip2/go-qrcode"
)

// User2FAService defines the interface for user 2FA service operations
type User2FAService interface {
	// Setup2FA initiates 2FA setup for a user
	Setup2FA(ctx context.Context, req user_2fa.Setup2FARequest) (*user_2fa.Setup2FAResponse, error)

	// Verify2FA verifies a 2FA code during setup
	Verify2FA(ctx context.Context, req user_2fa.Verify2FARequest) error

	// Enable2FA enables 2FA for a user after verification
	Enable2FA(ctx context.Context, req user_2fa.Enable2FARequest) error

	// Disable2FA disables 2FA for a user
	Disable2FA(ctx context.Context, req user_2fa.Disable2FARequest) error

	// Get2FAStatus gets the 2FA status for a user
	Get2FAStatus(ctx context.Context, userID string) (*user_2fa.User2FAStatusResponse, error)

	// ValidateLogin2FA validates 2FA code during login
	ValidateLogin2FA(ctx context.Context, userID, code string) (bool, error)

	// GenerateBackupCodes generates backup codes for a user
	GenerateBackupCodes(ctx context.Context, userID string) ([]string, error)

	// ValidateBackupCode validates a backup code
	ValidateBackupCode(ctx context.Context, userID, code string) (bool, error)

	// Reset2FA resets 2FA configuration for a user (admin only)
	Reset2FA(ctx context.Context, req user_2fa.Reset2FARequest) error
}

type user2FAService struct {
	user2FARepo interfaces.User2FARepository
	userRepo    interfaces.UserRepository
	logger      logger.Logger
}

// NewUser2FAService creates a new user 2FA service
func NewUser2FAService(user2FARepo interfaces.User2FARepository, userRepo interfaces.UserRepository, logger logger.Logger) User2FAService {
	return &user2FAService{
		user2FARepo: user2FARepo,
		userRepo:    userRepo,
		logger:      logger,
	}
}

// Setup2FA initiates 2FA setup for a user
func (s *user2FAService) Setup2FA(ctx context.Context, req user_2fa.Setup2FARequest) (*user_2fa.Setup2FAResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "Setup2FA").WithField("user_id", req.UserID)

	// Validate request
	if err := user_2fa.ValidateSetupRequest(req); err != nil {
		log.WithError(err).Error("invalid setup 2FA request")
		return nil, err
	}

	// Check if user exists
	user, err := s.userRepo.GetByID(ctx, strconv.Itoa(req.UserID))
	if err != nil {
		log.WithError(err).Error("failed to get user")
		return nil, err
	}

	// Check if user exists
	oldUser2FA, _ := s.user2FARepo.GetByUserID(ctx, strconv.Itoa(req.UserID))
	if oldUser2FA != nil && oldUser2FA.IsEnabled {
		log.WithError(err).Error("cannot setup 2FA for user with existing 2FA")
		return nil, errors.NewValidationError("cannot setup 2FA for user with existing 2FA")
	}

	// Generate TOTP secret
	key, err := user_2fa.GenerateSecret("YourApp", user.Username)
	if err != nil {
		log.WithError(err).Error("failed to generate TOTP secret")
		return nil, err
	}

	// Create or update 2FA configuration (but don't enable yet)
	user2FA := user_2fa.NewUser2FA(req.UserID, key.Secret())

	// Check if user already has 2FA setup
	existing, err := s.user2FARepo.GetByUserID(ctx, strconv.Itoa(req.UserID))
	if err != nil && !errors.IsNotFoundError(err) {
		log.WithError(err).Error("failed to check existing 2FA")
		return nil, err
	}

	if existing != nil {
		// Update existing configuration
		existing.UpdateSecret(key.Secret())
		existing.Disable() // Keep disabled until verified
		if err := s.user2FARepo.Update(ctx, existing); err != nil {
			log.WithError(err).Error("failed to update 2FA configuration")
			return nil, err
		}
	} else {
		// Create new configuration
		if err := s.user2FARepo.Create(ctx, user2FA); err != nil {
			log.WithError(err).Error("failed to create 2FA configuration")
			return nil, err
		}
	}

	// Generate QR code URL
	qrCodeURL, err := user_2fa.GenerateQRCode(key)
	if err != nil {
		log.WithError(err).Error("failed to generate QR code")
		return nil, err
	}

	qrCode, _ := qrcode.Encode(qrCodeURL, qrcode.Medium, 256)
	base64String := base64.StdEncoding.EncodeToString(qrCode)

	// Return as data URL for easy use in HTML

	response := &user_2fa.Setup2FAResponse{
		SecretKey: key.Secret(),
		QRCodeURL: qrCodeURL,
		QRCode:    "data:image/png;base64," + base64String, // Base64 QR code can be added later if needed
	}

	log.Info("2FA setup initiated successfully")
	return response, nil
}

// Verify2FA verifies a 2FA code during setup
func (s *user2FAService) Verify2FA(ctx context.Context, req user_2fa.Verify2FARequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "Verify2FA").WithField("user_id", req.UserID)

	// Validate request
	if err := user_2fa.ValidateVerifyRequest(req); err != nil {
		log.WithError(err).Error("invalid verify 2FA request")
		return err
	}

	// Get user's 2FA configuration
	user2FA, err := s.user2FARepo.GetByUserID(ctx, strconv.Itoa(req.UserID))
	if err != nil {
		log.WithError(err).Error("failed to get user 2FA configuration")
		return err
	}

	// Validate the code
	if !user_2fa.ValidateCode(user2FA.SecretKey, req.Code) {
		log.Error("invalid 2FA code")
		return errors.NewValidationError("invalid 2FA code")
	}

	log.Info("2FA code verified successfully")
	return nil
}

// Enable2FA enables 2FA for a user after verification
func (s *user2FAService) Enable2FA(ctx context.Context, req user_2fa.Enable2FARequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "Enable2FA").WithField("user_id", req.UserID)

	// Validate request
	if err := user_2fa.ValidateEnableRequest(req); err != nil {
		log.WithError(err).Error("invalid enable 2FA request")
		return err
	}

	// Get user's 2FA configuration
	user2FA, err := s.user2FARepo.GetByUserID(ctx, strconv.Itoa(req.UserID))
	if err != nil {
		log.WithError(err).Error("failed to get user 2FA configuration")
		return err
	}

	// Validate the code
	if !user_2fa.ValidateCode(user2FA.SecretKey, req.Code) {
		log.Error("invalid 2FA code")
		return errors.NewValidationError("invalid 2FA code")
	}

	// Enable 2FA
	user2FA.Enable()

	// Generate backup codes
	user2FA.GenerateBackupCodes()

	// Update in database
	if err := s.user2FARepo.Update(ctx, user2FA); err != nil {
		log.WithError(err).Error("failed to enable 2FA")
		return err
	}

	log.Info("2FA enabled successfully")
	return nil
}

// Disable2FA disables 2FA for a user
func (s *user2FAService) Disable2FA(ctx context.Context, req user_2fa.Disable2FARequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "Disable2FA").WithField("user_id", req.UserID)

	// Validate request
	if err := user_2fa.ValidateDisableRequest(req); err != nil {
		log.WithError(err).Error("invalid disable 2FA request")
		return err
	}

	// Get user's 2FA configuration
	user2FA, err := s.user2FARepo.GetByUserID(ctx, strconv.Itoa(req.UserID))
	if err != nil {
		log.WithError(err).Error("failed to get user 2FA configuration")
		return err
	}

	// Validate the code
	if !user_2fa.ValidateCode(user2FA.SecretKey, req.Code) {
		log.Error("invalid 2FA code")
		return errors.NewValidationError("invalid 2FA code")
	}

	// Disable 2FA
	user2FA.Disable()

	// Update in database
	if err := s.user2FARepo.Update(ctx, user2FA); err != nil {
		log.WithError(err).Error("failed to disable 2FA")
		return err
	}

	log.Info("2FA disabled successfully")
	return nil
}

// Get2FAStatus gets the 2FA status for a user
func (s *user2FAService) Get2FAStatus(ctx context.Context, userID string) (*user_2fa.User2FAStatusResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "Get2FAStatus").WithField("user_id", userID)

	// Convert string userID to int for domain model
	userIDInt, err := strconv.Atoi(userID)
	if err != nil {
		log.WithError(err).Error("invalid user ID format")
		return nil, errors.NewValidationError("invalid user ID format")
	}

	// Get user's 2FA configuration
	user2FA, err := s.user2FARepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.IsNotFoundError(err) {
			// User doesn't have 2FA setup
			response := &user_2fa.User2FAStatusResponse{
				UserID:    userIDInt,
				IsEnabled: false,
				HasSetup:  false,
			}
			return response, nil
		}
		log.WithError(err).Error("failed to get user 2FA configuration")
		return nil, err
	}

	response := user2FA.ToStatusResponse()
	log.Info("2FA status retrieved successfully")
	return &response, nil
}

// ValidateLogin2FA validates 2FA code during login
func (s *user2FAService) ValidateLogin2FA(ctx context.Context, userID, code string) (bool, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ValidateLogin2FA").WithField("user_id", userID)

	// Get user's 2FA configuration
	user2FA, err := s.user2FARepo.GetByUserID(ctx, userID)
	if err != nil {
		log.WithError(err).Error("failed to get user 2FA configuration")
		return false, err
	}

	// Check if 2FA is enabled
	if !user2FA.IsEnabled {
		log.Error("2FA is not enabled for user")
		return false, errors.NewValidationError("2FA is not enabled")
	}

	// Try TOTP code first
	if user_2fa.ValidateCode(user2FA.SecretKey, code) {
		log.Info("2FA TOTP code validated successfully")
		return true, nil
	}

	// Try backup code
	if user2FA.ValidateBackupCode(code) {
		// Update backup codes in database (backup code was removed)
		if err := s.user2FARepo.Update(ctx, user2FA); err != nil {
			log.WithError(err).Error("failed to update backup codes")
			// Don't fail the login, just log the error
		}
		log.Info("2FA backup code validated successfully")
		return true, nil
	}

	log.Error("invalid 2FA code")
	return false, nil
}

// GenerateBackupCodes generates backup codes for a user
func (s *user2FAService) GenerateBackupCodes(ctx context.Context, userID string) ([]string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GenerateBackupCodes").WithField("user_id", userID)

	// Get user's 2FA configuration
	user2FA, err := s.user2FARepo.GetByUserID(ctx, userID)
	if err != nil {
		log.WithError(err).Error("failed to get user 2FA configuration")
		return nil, err
	}

	// Generate new backup codes
	backupCodes := user2FA.GenerateBackupCodes()

	// Update in database
	if err := s.user2FARepo.Update(ctx, user2FA); err != nil {
		log.WithError(err).Error("failed to update backup codes")
		return nil, err
	}

	log.Info("backup codes generated successfully")
	return backupCodes, nil
}

// ValidateBackupCode validates a backup code
func (s *user2FAService) ValidateBackupCode(ctx context.Context, userID, code string) (bool, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ValidateBackupCode").WithField("user_id", userID)

	// Get user's 2FA configuration
	user2FA, err := s.user2FARepo.GetByUserID(ctx, userID)
	if err != nil {
		log.WithError(err).Error("failed to get user 2FA configuration")
		return false, err
	}

	// Validate backup code
	isValid := user2FA.ValidateBackupCode(code)
	if isValid {
		// Update backup codes in database (backup code was removed)
		if err := s.user2FARepo.Update(ctx, user2FA); err != nil {
			log.WithError(err).Error("failed to update backup codes")
			return false, err
		}
		log.Info("backup code validated successfully")
	} else {
		log.Error("invalid backup code")
	}

	return isValid, nil
}

// Reset2FA resets 2FA configuration for a user (admin only)
func (s *user2FAService) Reset2FA(ctx context.Context, req user_2fa.Reset2FARequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "Reset2FA").WithField("user_id", req.UserID).WithField("admin_id", req.AdminID)

	// Validate request
	if err := user_2fa.ValidateReset2FARequest(req); err != nil {
		log.WithError(err).Error("invalid reset 2FA request")
		return err
	}

	// Check if admin user exists and has permission
	admin, err := s.userRepo.GetByID(ctx, req.AdminID)
	if err != nil {
		log.WithError(err).Error("failed to get admin user")
		return err
	}

	// Check if target user exists
	targetUser, err := s.userRepo.GetByID(ctx, strconv.Itoa(req.UserID))
	if err != nil {
		log.WithError(err).Error("failed to get target user")
		return err
	}

	// Get user's 2FA configuration
	user2FA, err := s.user2FARepo.GetByUserID(ctx, strconv.Itoa(req.UserID))
	if err != nil {
		log.WithError(err).Error("failed to get user 2FA configuration")
		return err
	}

	// Reset 2FA configuration
	user2FA.Reset()

	// Update in database
	if err := s.user2FARepo.Update(ctx, user2FA); err != nil {
		log.WithError(err).Error("failed to reset 2FA")
		return err
	}

	log.WithFields(map[string]interface{}{
		"target_username": targetUser.Username,
		"admin_username":  admin.Username,
	}).Info("2FA reset successfully by admin")

	return nil
}
