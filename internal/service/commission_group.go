package service

import (
	"context"

	"blacking-api/internal/domain/commission_group"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

// CommissionGroupService defines the interface for commission group service operations
type CommissionGroupService interface {
	CreateCommissionGroup(ctx context.Context, req commission_group.CreateCommissionGroupRequest) (*commission_group.CommissionGroupResponse, error)
	GetCommissionGroupByID(ctx context.Context, id int) (*commission_group.CommissionGroupResponse, error)
	GetDefaultCommissionGroup(ctx context.Context) (*commission_group.CommissionGroupResponse, error)
	UpdateCommissionGroup(ctx context.Context, id int, req commission_group.UpdateCommissionGroupRequest) (*commission_group.CommissionGroupResponse, error)
	DeleteCommissionGroup(ctx context.Context, id int) error
	ListCommissionGroups(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*commission_group.CommissionGroupResponse, int64, error)
	ListActiveCommissionGroups(ctx context.Context) ([]*commission_group.CommissionGroupResponse, error)
	ListCommissionGroupsForDropdown(ctx context.Context) ([]*commission_group.CommissionGroupDropdownResponse, error)
	SetDefaultCommissionGroup(ctx context.Context, id int) error
}

type commissionGroupService struct {
	commissionGroupRepo interfaces.CommissionGroupRepository
	logger              logger.Logger
}

// NewCommissionGroupService creates a new commission group service
func NewCommissionGroupService(commissionGroupRepo interfaces.CommissionGroupRepository, logger logger.Logger) CommissionGroupService {
	return &commissionGroupService{
		commissionGroupRepo: commissionGroupRepo,
		logger:              logger,
	}
}

// CreateCommissionGroup creates a new commission group
func (s *commissionGroupService) CreateCommissionGroup(ctx context.Context, req commission_group.CreateCommissionGroupRequest) (*commission_group.CommissionGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateCommissionGroup")

	// Check if commission group name already exists
	existingCommissionGroup, err := s.commissionGroupRepo.GetByName(ctx, req.Name)
	if err == nil && existingCommissionGroup != nil {
		log.WithField("name", req.Name).Error("commission group name already exists")
		return nil, errors.NewValidationError("commission group name already exists")
	}

	// Create new commission group
	newCommissionGroup, err := commission_group.NewCommissionGroup(req)
	if err != nil {
		log.WithError(err).Error("failed to create commission group domain object")
		return nil, err
	}

	// Save to repository
	if err := s.commissionGroupRepo.Create(ctx, newCommissionGroup); err != nil {
		log.WithError(err).Error("failed to save commission group to repository")
		return nil, err
	}

	response := newCommissionGroup.ToResponse()
	log.WithField("commission_group_id", response.ID).WithField("name", req.Name).Info("commission group created successfully")
	return &response, nil
}

// GetCommissionGroupByID retrieves commission group by ID
func (s *commissionGroupService) GetCommissionGroupByID(ctx context.Context, id int) (*commission_group.CommissionGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetCommissionGroupByID")

	cg, err := s.commissionGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("commission_group_id", id).Error("failed to get commission group by ID")
		return nil, err
	}

	response := cg.ToResponse()
	return &response, nil
}

// GetDefaultCommissionGroup retrieves the default commission group
func (s *commissionGroupService) GetDefaultCommissionGroup(ctx context.Context) (*commission_group.CommissionGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetDefaultCommissionGroup")

	cg, err := s.commissionGroupRepo.GetDefault(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get default commission group")
		return nil, err
	}

	response := cg.ToResponse()
	return &response, nil
}

// UpdateCommissionGroup updates an existing commission group
func (s *commissionGroupService) UpdateCommissionGroup(ctx context.Context, id int, req commission_group.UpdateCommissionGroupRequest) (*commission_group.CommissionGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateCommissionGroup")

	// Get existing commission group
	cg, err := s.commissionGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("commission_group_id", id).Error("failed to get commission group for update")
		return nil, err
	}

	// Check if new name conflicts with existing commission group (excluding current commission group)
	if cg.Name != req.Name {
		existingCommissionGroup, err := s.commissionGroupRepo.GetByName(ctx, req.Name)
		if err == nil && existingCommissionGroup != nil && existingCommissionGroup.ID != id {
			log.WithField("name", req.Name).Error("commission group name already exists")
			return nil, errors.NewValidationError("commission group name already exists")
		}
	}

	// Update commission group
	if err := cg.Update(req); err != nil {
		log.WithError(err).Error("failed to update commission group domain object")
		return nil, err
	}

	// Save to repository
	if err := s.commissionGroupRepo.Update(ctx, cg); err != nil {
		log.WithError(err).Error("failed to save updated commission group to repository")
		return nil, err
	}

	response := cg.ToResponse()
	log.WithField("commission_group_id", response.ID).Info("commission group updated successfully")
	return &response, nil
}

// DeleteCommissionGroup soft deletes a commission group
func (s *commissionGroupService) DeleteCommissionGroup(ctx context.Context, id int) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteCommissionGroup")

	if err := s.commissionGroupRepo.Delete(ctx, id); err != nil {
		log.WithError(err).WithField("commission_group_id", id).Error("failed to delete commission group")
		return err
	}

	log.WithField("commission_group_id", id).Info("commission group deleted successfully")
	return nil
}

// ListCommissionGroups retrieves commission groups with pagination, search, and sorting
func (s *commissionGroupService) ListCommissionGroups(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*commission_group.CommissionGroupResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListCommissionGroups")

	// Get total count
	total, err := s.commissionGroupRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count commission groups")
		return nil, 0, err
	}

	// Get commission groups
	commissionGroups, err := s.commissionGroupRepo.List(ctx, limit, offset, search, sortBy, sortOrder)
	if err != nil {
		log.WithError(err).Error("failed to list commission groups")
		return nil, 0, err
	}

	// Convert to response format
	var responses []*commission_group.CommissionGroupResponse
	for _, cg := range commissionGroups {
		response := cg.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("total", total).WithField("returned", len(responses)).Info("commission groups listed successfully")
	return responses, total, nil
}

// ListActiveCommissionGroups retrieves only active commission groups
func (s *commissionGroupService) ListActiveCommissionGroups(ctx context.Context) ([]*commission_group.CommissionGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListActiveCommissionGroups")

	commissionGroups, err := s.commissionGroupRepo.ListActive(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list active commission groups")
		return nil, err
	}

	// Convert to response format
	var responses []*commission_group.CommissionGroupResponse
	for _, cg := range commissionGroups {
		response := cg.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("active commission groups listed successfully")
	return responses, nil
}

// ListCommissionGroupsForDropdown retrieves commission groups for dropdown filter
func (s *commissionGroupService) ListCommissionGroupsForDropdown(ctx context.Context) ([]*commission_group.CommissionGroupDropdownResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListCommissionGroupsForDropdown")

	dropdownItems, err := s.commissionGroupRepo.ListForDropdown(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list commission groups for dropdown")
		return nil, err
	}

	log.WithField("count", len(dropdownItems)).Info("commission groups for dropdown listed successfully")
	return dropdownItems, nil
}

// SetDefaultCommissionGroup sets a commission group as default
func (s *commissionGroupService) SetDefaultCommissionGroup(ctx context.Context, id int) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SetDefaultCommissionGroup")

	// Verify the commission group exists
	_, err := s.commissionGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("commission_group_id", id).Error("commission group not found")
		return err
	}

	if err := s.commissionGroupRepo.SetDefault(ctx, id); err != nil {
		log.WithError(err).WithField("commission_group_id", id).Error("failed to set default commission group")
		return err
	}

	log.WithField("commission_group_id", id).Info("commission group set as default successfully")
	return nil
}
