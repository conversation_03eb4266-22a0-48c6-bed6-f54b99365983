package service

import (
	"blacking-api/internal/domain/promotion_web"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
	"io"
)

type PromotionWebService interface {
	// Promotion Web CRUD operations
	CreatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebCreateRequest) (int64, error)
	GetPromotionWebList(ctx context.Context, req promotion_web.PromotionWebGetListRequest) ([]promotion_web.PromotionWebGetListResponse, int64, error)
	GetPromotionWebById(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error)
	UpdatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebUpdateRequest) error
	DeletePromotionWeb(ctx context.Context, req promotion_web.DeletePromotionWebRequest) error
	CancelPromotionWeb(ctx context.Context, req promotion_web.CancelPromotionWebRequest) error

	// Promotion Web User operations
	GetPromotionWebUserToCancel(ctx context.Context, promotionWebId int64) ([]promotion_web.GetPromotionWebIdToCancel, error)
	GetUserPromotionWebByUserId(ctx context.Context, userId int64) (*promotion_web.PromotionWebUserByUserIdResponse, error)
	GetUserPromotionWebList(ctx context.Context, req promotion_web.PromotionWebUserGetListRequest) ([]promotion_web.PromotionWebUserGetListResponse, int64, error)
	GetPromotionWebUserById(ctx context.Context, req promotion_web.GetPromotionWebUserById) (*promotion_web.GetPromotionWebUserByIdResponse, error)
	PromotionWebUserGetListByUserId(ctx context.Context, req promotion_web.PromotionWebUserGetListByUserIdRequest) ([]promotion_web.PromotionWebUserGetListByUserIdResponse, int64, error)

	// Promotion Web User management
	CancelPromotionWebUserById(ctx context.Context, req promotion_web.CancelPromotionWebUserById) error
	ExpiredPromotionWebUserByIds(ctx context.Context, req promotion_web.CancelPromotionWebUserByPromotionWebId) error
	PromotionConfirmUpdatePromotionWebUser(ctx context.Context, confirmId int64, promotionWebUserId int64) error

	// Utility operations
	GetExpiredPromotionWeb(ctx context.Context, today string) ([]promotion_web.PromotionWebExpired, error)
	PromotionWebGetSildeListOnlyActive(ctx context.Context) ([]promotion_web.PromotionWebGetSildeListOnlyActive, error)
	UpdatePromotionWebPriorityOrderCreate(ctx context.Context, id int64) error
	SortPromotionWebPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest) error

	// File upload operations
	UploadImageToCloudflare(ctx context.Context, pathUpload string, filename string, fileReader io.Reader) (*promotion_web.CloudFlareUploadCreateBody, error)
}

type promotionWebService struct {
	promotionWebRepo interfaces.PromotionWebRepository
	logger           logger.Logger
}

func NewPromotionWebService(promotionWebRepo interfaces.PromotionWebRepository, logger logger.Logger) PromotionWebService {
	return &promotionWebService{
		promotionWebRepo: promotionWebRepo,
		logger:           logger,
	}
}

// CreatePromotionWeb creates a new promotion web
func (s *promotionWebService) CreatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebCreateRequest) (int64, error) {
	s.logger.WithField("name", req.Name).Info("creating promotion web")

	id, err := s.promotionWebRepo.CreatePromotionWeb(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to create promotion web")
		return 0, err
	}

	s.logger.WithField("id", id).Info("promotion web created successfully")
	return id, nil
}

// GetPromotionWebList retrieves a list of promotion webs with pagination and filtering
func (s *promotionWebService) GetPromotionWebList(ctx context.Context, req promotion_web.PromotionWebGetListRequest) ([]promotion_web.PromotionWebGetListResponse, int64, error) {
	s.logger.WithField("page", req.Page).WithField("limit", req.Limit).Info("getting promotion web list")

	list, total, err := s.promotionWebRepo.GetPromotionWebList(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get promotion web list")
		return nil, 0, err
	}

	s.logger.WithField("total", total).WithField("count", len(list)).Info("promotion web list retrieved successfully")
	return list, total, nil
}

// GetPromotionWebById retrieves a promotion web by ID
func (s *promotionWebService) GetPromotionWebById(ctx context.Context, id int64) (*promotion_web.PromotionWebGetByIdResponse, error) {
	s.logger.WithField("id", id).Info("getting promotion web by ID")

	result, err := s.promotionWebRepo.GetPromotionWebById(ctx, id)
	if err != nil {
		s.logger.WithError(err).WithField("id", id).Error("failed to get promotion web by ID")
		return nil, err
	}

	s.logger.WithField("id", id).Info("promotion web retrieved successfully")
	return result, nil
}

// UpdatePromotionWeb updates an existing promotion web
func (s *promotionWebService) UpdatePromotionWeb(ctx context.Context, req promotion_web.PromotionWebUpdateRequest) error {
	s.logger.WithField("id", req.Id).Info("updating promotion web")

	err := s.promotionWebRepo.UpdatePromotionWeb(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to update promotion web")
		return err
	}

	s.logger.WithField("id", req.Id).Info("promotion web updated successfully")
	return nil
}

// DeletePromotionWeb soft deletes a promotion web
func (s *promotionWebService) DeletePromotionWeb(ctx context.Context, req promotion_web.DeletePromotionWebRequest) error {
	s.logger.WithField("id", req.Id).Info("deleting promotion web")

	err := s.promotionWebRepo.DeletePromotionWeb(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to delete promotion web")
		return err
	}

	s.logger.WithField("id", req.Id).Info("promotion web deleted successfully")
	return nil
}

// CancelPromotionWeb cancels a promotion web
func (s *promotionWebService) CancelPromotionWeb(ctx context.Context, req promotion_web.CancelPromotionWebRequest) error {
	s.logger.WithField("id", req.Id).Info("canceling promotion web")

	err := s.promotionWebRepo.CancelPromotionWeb(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to cancel promotion web")
		return err
	}

	s.logger.WithField("id", req.Id).Info("promotion web canceled successfully")
	return nil
}

// GetPromotionWebUserToCancel retrieves promotion web users to cancel
func (s *promotionWebService) GetPromotionWebUserToCancel(ctx context.Context, promotionWebId int64) ([]promotion_web.GetPromotionWebIdToCancel, error) {
	s.logger.WithField("promotion_web_id", promotionWebId).Info("getting promotion web users to cancel")

	result, err := s.promotionWebRepo.GetPromotionWebUserToCancel(ctx, promotionWebId)
	if err != nil {
		s.logger.WithError(err).WithField("promotion_web_id", promotionWebId).Error("failed to get promotion web users to cancel")
		return nil, err
	}

	s.logger.WithField("promotion_web_id", promotionWebId).WithField("count", len(result)).Info("promotion web users to cancel retrieved successfully")
	return result, nil
}

// GetUserPromotionWebByUserId retrieves user promotion web by user ID
func (s *promotionWebService) GetUserPromotionWebByUserId(ctx context.Context, userId int64) (*promotion_web.PromotionWebUserByUserIdResponse, error) {
	s.logger.WithField("user_id", userId).Info("getting user promotion web by user ID")

	result, err := s.promotionWebRepo.GetUserPromotionWebByUserId(ctx, userId)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userId).Error("failed to get user promotion web by user ID")
		return nil, err
	}

	s.logger.WithField("user_id", userId).Info("user promotion web retrieved successfully")
	return result, nil
}

// GetUserPromotionWebList retrieves a list of promotion web users
func (s *promotionWebService) GetUserPromotionWebList(ctx context.Context, req promotion_web.PromotionWebUserGetListRequest) ([]promotion_web.PromotionWebUserGetListResponse, int64, error) {
	s.logger.WithField("page", req.Page).WithField("limit", req.Limit).Info("getting user promotion web list")

	list, total, err := s.promotionWebRepo.GetUserPromotionWebList(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to get user promotion web list")
		return nil, 0, err
	}

	s.logger.WithField("total", total).WithField("count", len(list)).Info("user promotion web list retrieved successfully")
	return list, total, nil
}

// GetPromotionWebUserById retrieves a promotion web user by ID
func (s *promotionWebService) GetPromotionWebUserById(ctx context.Context, req promotion_web.GetPromotionWebUserById) (*promotion_web.GetPromotionWebUserByIdResponse, error) {
	s.logger.WithField("id", req.Id).Info("getting promotion web user by ID")

	result, err := s.promotionWebRepo.GetPromotionWebUserById(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to get promotion web user by ID")
		return nil, err
	}

	s.logger.WithField("id", req.Id).Info("promotion web user retrieved successfully")
	return result, nil
}

// PromotionWebUserGetListByUserId retrieves promotion web users by user ID
func (s *promotionWebService) PromotionWebUserGetListByUserId(ctx context.Context, req promotion_web.PromotionWebUserGetListByUserIdRequest) ([]promotion_web.PromotionWebUserGetListByUserIdResponse, int64, error) {
	s.logger.WithField("user_id", req.UserId).WithField("page", req.Page).Info("getting promotion web user list by user ID")

	list, total, err := s.promotionWebRepo.PromotionWebUserGetListByUserId(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", req.UserId).Error("failed to get promotion web user list by user ID")
		return nil, 0, err
	}

	s.logger.WithField("user_id", req.UserId).WithField("total", total).WithField("count", len(list)).Info("promotion web user list by user ID retrieved successfully")
	return list, total, nil
}

// CancelPromotionWebUserById cancels a promotion web user by ID
func (s *promotionWebService) CancelPromotionWebUserById(ctx context.Context, req promotion_web.CancelPromotionWebUserById) error {
	s.logger.WithField("id", req.Id).Info("canceling promotion web user by ID")

	err := s.promotionWebRepo.CancelPromotionWebUserById(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("id", req.Id).Error("failed to cancel promotion web user by ID")
		return err
	}

	s.logger.WithField("id", req.Id).Info("promotion web user canceled successfully")
	return nil
}

// ExpiredPromotionWebUserByIds expires promotion web users by promotion web ID
func (s *promotionWebService) ExpiredPromotionWebUserByIds(ctx context.Context, req promotion_web.CancelPromotionWebUserByPromotionWebId) error {
	s.logger.WithField("promotion_web_id", req.PromotionWebId).Info("expiring promotion web users by promotion web ID")

	err := s.promotionWebRepo.ExpiredPromotionWebUserByIds(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("promotion_web_id", req.PromotionWebId).Error("failed to expire promotion web users by promotion web ID")
		return err
	}

	s.logger.WithField("promotion_web_id", req.PromotionWebId).Info("promotion web users expired successfully")
	return nil
}

// PromotionConfirmUpdatePromotionWebUser updates promotion web user confirmation
func (s *promotionWebService) PromotionConfirmUpdatePromotionWebUser(ctx context.Context, confirmId int64, promotionWebUserId int64) error {
	s.logger.WithField("confirm_id", confirmId).WithField("promotion_web_user_id", promotionWebUserId).Info("updating promotion web user confirmation")

	err := s.promotionWebRepo.PromotionConfirmUpdatePromotionWebUser(ctx, confirmId, promotionWebUserId)
	if err != nil {
		s.logger.WithError(err).WithField("confirm_id", confirmId).Error("failed to update promotion web user confirmation")
		return err
	}

	s.logger.WithField("confirm_id", confirmId).Info("promotion web user confirmation updated successfully")
	return nil
}

// GetExpiredPromotionWeb retrieves expired promotion webs
func (s *promotionWebService) GetExpiredPromotionWeb(ctx context.Context, today string) ([]promotion_web.PromotionWebExpired, error) {
	s.logger.WithField("date", today).Info("getting expired promotion webs")

	result, err := s.promotionWebRepo.GetExpiredPromotionWeb(ctx, today)
	if err != nil {
		s.logger.WithError(err).WithField("date", today).Error("failed to get expired promotion webs")
		return nil, err
	}

	s.logger.WithField("date", today).WithField("count", len(result)).Info("expired promotion webs retrieved successfully")
	return result, nil
}

// PromotionWebGetSildeListOnlyActive retrieves active promotion webs for slides
func (s *promotionWebService) PromotionWebGetSildeListOnlyActive(ctx context.Context) ([]promotion_web.PromotionWebGetSildeListOnlyActive, error) {
	s.logger.Info("getting active promotion web slide list")

	result, err := s.promotionWebRepo.PromotionWebGetSildeListOnlyActive(ctx)
	if err != nil {
		s.logger.WithError(err).Error("failed to get active promotion web slide list")
		return nil, err
	}

	s.logger.WithField("count", len(result)).Info("active promotion web slide list retrieved successfully")
	return result, nil
}

// UpdatePromotionWebPriorityOrderCreate updates promotion web priority order
func (s *promotionWebService) UpdatePromotionWebPriorityOrderCreate(ctx context.Context, id int64) error {
	s.logger.WithField("id", id).Info("updating promotion web priority order")

	err := s.promotionWebRepo.UpdatePromotionWebPriorityOrderCreate(ctx, id)
	if err != nil {
		s.logger.WithError(err).WithField("id", id).Error("failed to update promotion web priority order")
		return err
	}

	s.logger.WithField("id", id).Info("promotion web priority order updated successfully")
	return nil
}

// SortPromotionWebPriorityOrder sorts promotion web priority order
func (s *promotionWebService) SortPromotionWebPriorityOrder(ctx context.Context, req promotion_web.DragSortRequest) error {
	s.logger.WithField("from_id", req.FromItemId).WithField("to_id", req.ToItemId).Info("sorting promotion web priority order")

	err := s.promotionWebRepo.SortPromotionWebPriorityOrder(ctx, req)
	if err != nil {
		s.logger.WithError(err).WithField("from_id", req.FromItemId).WithField("to_id", req.ToItemId).Error("failed to sort promotion web priority order")
		return err
	}

	s.logger.WithField("from_id", req.FromItemId).WithField("to_id", req.ToItemId).Info("promotion web priority order sorted successfully")
	return nil
}

// UploadImageToCloudflare uploads an image to Cloudflare
func (s *promotionWebService) UploadImageToCloudflare(ctx context.Context, pathUpload string, filename string, fileReader io.Reader) (*promotion_web.CloudFlareUploadCreateBody, error) {
	s.logger.WithField("filename", filename).WithField("path", pathUpload).Info("uploading image to Cloudflare")

	result, err := s.promotionWebRepo.UploadImageToCloudflare(ctx, pathUpload, filename, fileReader)
	if err != nil {
		s.logger.WithError(err).WithField("filename", filename).Error("failed to upload image to Cloudflare")
		return nil, err
	}

	s.logger.WithField("filename", filename).WithField("image_id", result.ImageId).Info("image uploaded to Cloudflare successfully")
	return result, nil
}
