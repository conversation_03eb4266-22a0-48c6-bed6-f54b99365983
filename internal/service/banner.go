package service

import (
	"blacking-api/internal/domain/banner"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/auth"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
)

type BannerService interface {
	CreateBanner(ctx context.Context, req banner.CreateBannerRequest) (*banner.BannerResponse, error)
	GetBannerByID(ctx context.Context, id string) (*banner.BannerResponse, error)
	UpdateBanner(ctx context.Context, id string, req banner.UpdateBannerRequest) (*banner.BannerResponse, error)
	DeleteBanner(ctx context.Context, id string) error
	ListBanners(ctx context.Context, limit, offset int, search string) ([]*banner.BannerResponse, int64, error)
	ReorderBanners(ctx context.Context, req banner.ReorderRequest) error
	FileUpload(ctx context.Context, fileBody *http.Request, fieldName string) (*banner.FileUploadResponse, error)
	DeleteFile(ctx context.Context, req *banner.DeleteFileRequest) error
}

type bannerService struct {
	bannerRepo interfaces.BannerRepository
	userRepo   interfaces.UserRepository
	awsS3Repo  interfaces.AWSS3Repository
	logger     logger.Logger
}

func NewBannerService(bannerRepo interfaces.BannerRepository, userRepo interfaces.UserRepository, awsS3Repo interfaces.AWSS3Repository, logger logger.Logger) BannerService {
	return &bannerService{
		bannerRepo: bannerRepo,
		userRepo:   userRepo,
		awsS3Repo:  awsS3Repo,
		logger:     logger,
	}
}

func (s *bannerService) CreateBanner(ctx context.Context, req banner.CreateBannerRequest) (*banner.BannerResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateBanner")

	// Create new banner
	newBanner, err := banner.NewBanner(req)
	if err != nil {
		log.WithError(err).Error("failed to create banner domain object")
		return nil, err
	}

	// Save to repository
	if err := s.bannerRepo.Create(ctx, newBanner); err != nil {
		log.WithError(err).Error("failed to save banner to repository")
		return nil, err
	}

	response := newBanner.ToResponse()
	log.WithField("banner_id", response.ID).Info("banner created successfully")
	return &response, nil
}

func (s *bannerService) GetBannerByID(ctx context.Context, id string) (*banner.BannerResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetBannerByID").WithField("banner_id", id)

	banner, err := s.bannerRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get banner from repository")
		return nil, err
	}

	response := banner.ToResponse()
	log.Info("banner retrieved successfully")
	return &response, nil
}

func (s *bannerService) UpdateBanner(ctx context.Context, id string, req banner.UpdateBannerRequest) (*banner.BannerResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateBanner").WithField("banner_id", id)

	// Get existing banner
	existingBanner, err := s.bannerRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get existing banner")
		return nil, err
	}

	// Update banner
	if err := existingBanner.Update(req); err != nil {
		log.WithError(err).Error("failed to update banner domain object")
		return nil, err
	}

	// Save to repository
	if err := s.bannerRepo.Update(ctx, existingBanner); err != nil {
		log.WithError(err).Error("failed to update banner in repository")
		return nil, err
	}

	response := existingBanner.ToResponse()
	log.Info("banner updated successfully")
	return &response, nil
}

func (s *bannerService) DeleteBanner(ctx context.Context, id string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteBanner").WithField("banner_id", id)

	// Get existing banner
	existingBanner, err := s.bannerRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get existing banner")
		return err
	}
	var username string
	if ginCtx, ok := ctx.(*gin.Context); ok {
		username = auth.GetUsernameFromContext(ginCtx)
	} else {
		username = "system" // fallback for non-HTTP contexts
	}

	// Deactivate banner (soft delete)
	existingBanner.Deactivate(username)
	// Save to repository
	if err := s.bannerRepo.Update(ctx, existingBanner); err != nil {
		log.WithError(err).Error("failed to delete banner in repository")
		return err
	}

	log.Info("banner deleted successfully")
	return nil
}

func (s *bannerService) ListBanners(ctx context.Context, limit, offset int, search string) ([]*banner.BannerResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListBanners")

	// Get banners from repository
	banners, err := s.bannerRepo.List(ctx, limit, offset, search)
	if err != nil {
		log.WithError(err).Error("failed to list banners from repository")
		return nil, 0, err
	}

	// Get total count
	total, err := s.bannerRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count banners from repository")
		return nil, 0, err
	}

	// Convert to response
	var responses []*banner.BannerResponse
	for _, b := range banners {
		response := b.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).WithField("total", total).Info("banners listed successfully")
	return responses, total, nil
}

func (s *bannerService) ReorderBanners(ctx context.Context, req banner.ReorderRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ReorderBanners")

	if err := s.bannerRepo.Reorder(ctx, req.BannerIDs); err != nil {
		log.WithError(err).Error("failed to reorder banners in repository")
		return err
	}

	log.WithField("banner_count", len(req.BannerIDs)).Info("banners reordered successfully")
	return nil
}

// FileUpload uploads a file to S3 for banner
func (s *bannerService) FileUpload(ctx context.Context, fileBody *http.Request, fieldName string) (*banner.FileUploadResponse, error) {
	// Use provided field name or default to "file"
	if fieldName == "" {
		fieldName = "file"
	}

	fileReader, _, err := fileBody.FormFile(fieldName)
	if err != nil {
		s.logger.WithError(err).WithField("field_name", fieldName).Error("failed to read file from request")
		return nil, errors.NewValidationError("failed to read file from request")
	}

	pathName := "backoffice/banners/"

	fileData, err := s.awsS3Repo.UploadFileToS3(ctx, pathName, fileReader)
	if err != nil {
		s.logger.WithError(err).Error("failed to upload file to S3")
		return nil, errors.NewValidationError("failed to upload file to S3")
	}

	fileUrl := banner.FileUploadResponse{
		FileUrl: fileData.FileUrl,
	}

	return &fileUrl, nil
}

// DeleteFile deletes a file from S3 for banner
func (s *bannerService) DeleteFile(ctx context.Context, req *banner.DeleteFileRequest) error {
	if err := s.awsS3Repo.DeleteFileFromS3(ctx, req.FileUrl); err != nil {
		s.logger.WithError(err).Error("failed to delete file from S3")
		return errors.NewValidationError("failed to delete file from S3")
	}

	s.logger.WithField("file_url", req.FileUrl).Info("file deleted from S3 successfully")
	return nil
}
