package service

import (
	"blacking-api/internal/domain/holding_account"
	"blacking-api/internal/domain/response"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"strings"
)

type HodlingAccountService interface {
	CreateHoldingAccount(ctx context.Context, req *holding_account.HoldingAccountRequest) error
	FindAllHoldingAccount(ctx context.Context, req *holding_account.HoldingAccountSearchRequest) (*response.SuccessWithPagination, error)
	FindHoldingAccountByID(ctx context.Context, id int64) (*holding_account.HoldingAccountByIdResponse, error)
	UpdateHoldingAccount(ctx context.Context, id int64, req *holding_account.HoldingAccountRequest) error
	ActiveHoldingAccount(ctx context.Context, id int64, status bool) error
	DeleteHoldingAccount(ctx context.Context, id int64) error
}

type hodlingAccountService struct {
	holdingAccountRepository interfaces.HoldingAccountRepository
	bankingRepo              interfaces.BankingRepository
	logger                   logger.Logger
}

func NewHodlingAccountService(
	holdingAccountRepository interfaces.HoldingAccountRepository,
	bankingRepo interfaces.BankingRepository,
	logger logger.Logger,
) HodlingAccountService {
	return &hodlingAccountService{
		holdingAccountRepository: holdingAccountRepository,
		bankingRepo:              bankingRepo,
		logger:                   logger,
	}
}

func (s *hodlingAccountService) CreateHoldingAccount(ctx context.Context, req *holding_account.HoldingAccountRequest) error {

	bankingExists, err := s.bankingRepo.FindIdExists(ctx, int64(req.BankingID))
	if err != nil {
		s.logger.WithError(err).Error("failed to check if banking exists")
		return err
	}
	if !bankingExists {
		s.logger.WithError(err).Error("banking not exists")
		return errors.NewValidationError("banking ID does not exist")
	}

	accountNumberDuplicate, err := s.holdingAccountRepository.FindByAccountNumberDuplicate(ctx, req.AccountNumber)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate account number")
		return err
	}
	if accountNumberDuplicate {
		s.logger.WithError(err).Error("duplicate account number")
		return errors.NewValidationError("account number already exists")
	}

	if err := s.holdingAccountRepository.Create(ctx, req); err != nil {
		s.logger.WithError(err).Error("failed to create holding account")
		return err
	}

	return nil
}

func (s *hodlingAccountService) FindAllHoldingAccount(ctx context.Context, req *holding_account.HoldingAccountSearchRequest) (*response.SuccessWithPagination, error) {
	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		s.logger.WithError(err).Error("failed to set pagination limits")
		return nil, err
	}

	pagination := helper.CalculatePagination(req.Page, req.Limit)

	var conditions []string
	var queryArgs []interface{}

	if req.AccountName != nil && *req.AccountName != "" {
		conditions = append(conditions, "UPPER(account_name) ILIKE UPPER($1)")
		queryArgs = append(queryArgs, "%"+*req.AccountName+"%")
	}

	if req.BankingID != nil && *req.BankingID > 0 {
		conditions = append(conditions, "banking_id = $2")
		queryArgs = append(queryArgs, *req.BankingID)
	}

	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	holdingAccounts, total, err := s.holdingAccountRepository.FindAll(ctx, pagination.Limit, pagination.Offset, whereClause, queryArgs)
	if err != nil {
		s.logger.WithError(err).Error("failed to find holding accounts")
		return nil, err
	}

	content := make([]*holding_account.HoldingAccountListResponse, len(holdingAccounts))
	if len(holdingAccounts) != 0 {
		for i, a := range holdingAccounts {
			content[i] = a.ToListResponse()
		}
	}

	responseData := &response.SuccessWithPagination{
		Message:    "Holding accounts retrieved successfully",
		Content:    content,
		Page:       int64(pagination.Page),
		Limit:      int64(pagination.Limit),
		TotalPages: helper.CalculateTotalPages(total, pagination.Limit),
		TotalItems: total,
	}

	return responseData, nil
}

func (s *hodlingAccountService) FindHoldingAccountByID(ctx context.Context, id int64) (*holding_account.HoldingAccountByIdResponse, error) {
	holdingAccount, err := s.holdingAccountRepository.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find holding account by ID")
		return nil, err
	}
	if holdingAccount == nil {
		return nil, errors.NewNotFoundError("holding account not found")
	}

	return holdingAccount.ToByIdResponse(), nil
}

func (s *hodlingAccountService) UpdateHoldingAccount(ctx context.Context, id int64, req *holding_account.HoldingAccountRequest) error {
	if id == 0 {
		return errors.NewValidationError("holding account ID cannot be zero")
	}

	bankingExists, err := s.bankingRepo.FindIdExists(ctx, int64(req.BankingID))
	if err != nil {
		s.logger.WithError(err).Error("failed to check if banking exists")
		return err
	}
	if !bankingExists {
		s.logger.WithError(err).Error("banking not exists")
		return errors.NewValidationError("banking ID does not exist")
	}

	accountNumberDuplicate, err := s.holdingAccountRepository.FindByAccountNumberDuplicateAndIdNot(ctx, req.AccountNumber, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate account number")
		return err
	}
	if accountNumberDuplicate {
		s.logger.WithError(err).Error("duplicate account number")
		return errors.NewValidationError("account number already exists")
	}

	if err := s.holdingAccountRepository.Update(ctx, id, req); err != nil {
		s.logger.WithError(err).Error("failed to update holding account")
		return err
	}

	return nil
}

func (s *hodlingAccountService) ActiveHoldingAccount(ctx context.Context, id int64, status bool) error {
	if id == 0 {
		return errors.NewValidationError("holding account ID cannot be zero")
	}

	if err := s.holdingAccountRepository.Active(ctx, id, status); err != nil {
		s.logger.WithError(err).Error("failed to update holding account status")
		return err
	}

	return nil
}

func (s *hodlingAccountService) DeleteHoldingAccount(ctx context.Context, id int64) error {
	if id == 0 {
		return errors.NewValidationError("holding account ID cannot be zero")
	}

	if err := s.holdingAccountRepository.Delete(ctx, id); err != nil {
		s.logger.WithError(err).Error("failed to delete holding account")
		return err
	}

	return nil
}
