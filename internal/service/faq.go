package service

import (
	"blacking-api/internal/domain/faq"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
)

type FAQService interface {
	CreateFAQ(ctx context.Context, req faq.CreateFAQRequest) (*faq.FAQResponse, error)
	GetFAQByID(ctx context.Context, id string) (*faq.FAQResponse, error)
	UpdateFAQ(ctx context.Context, id string, req faq.UpdateFAQRequest) (*faq.FAQResponse, error)
	DeleteFAQ(ctx context.Context, id string) error
	ListFAQs(ctx context.Context, limit, offset int, search string) ([]*faq.FAQResponse, error)
	GetFAQsCount(ctx context.Context, search string) (int64, error)
	ReorderFAQs(ctx context.Context, req faq.ReorderRequest) error
}

type faqService struct {
	FAQRepo interfaces.FAQRepository
	logger  logger.Logger
}

func NewFAQService(FAQRepo interfaces.FAQRepository, logger logger.Logger) FAQService {
	return &faqService{
		FAQRepo: FAQRepo,
		logger:  logger,
	}
}

func (s *faqService) CreateFAQ(ctx context.Context, req faq.CreateFAQRequest) (*faq.FAQResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateFAQ")

	// Create new user
	newFAQ, err := faq.NewFAQ(req)
	if err != nil {
		log.WithError(err).Error("failed to create faq domain object")
		return nil, err
	}

	// Save to repository
	if err := s.FAQRepo.Create(ctx, newFAQ); err != nil {
		log.WithError(err).Error("failed to save faq to repository")
		return nil, err
	}

	response := newFAQ.ToResponse()
	log.WithField("faq_id", response.ID).Info("faq created successfully")
	return &response, nil
}

func (s *faqService) GetFAQByID(ctx context.Context, id string) (*faq.FAQResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetFAQByID").WithField("faq_id", id)

	if id == "" {
		return nil, errors.NewValidationError("faq ID is required")
	}

	u, err := s.FAQRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get faq by ID")
		return nil, err
	}

	response := u.ToResponse()
	return &response, nil
}

func (s *faqService) UpdateFAQ(ctx context.Context, id string, req faq.UpdateFAQRequest) (*faq.FAQResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateFAQ").WithField("faq_id", id)

	if id == "" {
		return nil, errors.NewValidationError("faq ID is required")
	}

	// Get existing user
	u, err := s.FAQRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get faq for update")
		return nil, err
	}

	// Update user
	if err := u.Update(req); err != nil {
		log.WithError(err).Error("failed to update faq domain object")
		return nil, err
	}

	// Save to repository
	if err := s.FAQRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save updated faq")
		return nil, err
	}

	response := u.ToResponse()
	log.Info("faq updated successfully")
	return &response, nil
}

func (s *faqService) DeleteFAQ(ctx context.Context, id string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteFAQ").WithField("faq_id", id)

	if id == "" {
		return errors.NewValidationError("faq ID is required")
	}

	if err := s.FAQRepo.Delete(ctx, id); err != nil {
		log.WithError(err).Error("failed to delete faq")
		return err
	}

	log.Info("user deleted successfully")
	return nil
}

func (s *faqService) ListFAQs(ctx context.Context, limit, offset int, search string) ([]*faq.FAQResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListFAQs")

	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	users, err := s.FAQRepo.List(ctx, limit, offset, search)
	if err != nil {
		log.WithError(err).Error("failed to list faq")
		return nil, err
	}

	responses := make([]*faq.FAQResponse, len(users))
	for i, u := range users {
		response := u.ToResponse()
		responses[i] = &response
	}

	return responses, nil
}

func (s *faqService) GetFAQsCount(ctx context.Context, search string) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetFAQsCount")

	count, err := s.FAQRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to get faq count")
		return 0, err
	}

	return count, nil
}

func (s *faqService) ReorderFAQs(ctx context.Context, req faq.ReorderRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ReorderFAQs")

	// Validate request
	if len(req.FAQIDs) == 0 {
		return errors.NewValidationError("faq_ids cannot be empty")
	}

	if len(req.FAQIDs) > 100 {
		return errors.NewValidationError("cannot reorder more than 100 faqs at once")
	}

	// Call repository
	err := s.FAQRepo.Reorder(ctx, req.FAQIDs)
	if err != nil {
		log.WithError(err).Error("failed to reorder faqs")
		return err
	}

	log.WithFields(map[string]interface{}{
		"user_role_ids": req.FAQIDs,
		"count":         len(req.FAQIDs),
	}).Info("faqs reordered successfully")

	return nil
}
