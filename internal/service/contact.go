package service

import (
	"blacking-api/internal/domain/contact"
	"blacking-api/internal/domain/response"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"net/http"
	"strings"
)

type ContactService interface {
	UploadImage(ctx context.Context, fileBody *http.Request) (*contact.FileUploadResponse, error)
	DeleteImage(ctx context.Context, req *contact.DeleteFileRequest) error
	CreateContact(ctx context.Context, req *contact.CreateContactRequest) error
	FindAllContacts(ctx context.Context, req *contact.ContactSearchRequest) (*response.SuccessWithPagination, error)
	FindContactByID(ctx context.Context, id int64) (*contact.ByIdContactRequest, error)
	UpdateContact(ctx context.Context, id int64, req *contact.CreateContactRequest) error
	UpdateContactStatus(ctx context.Context, id int64, req *contact.UpdateStatusRequest) error
	DeleteContact(ctx context.Context, id int64) error
}

type contactService struct {
	contactRepo interfaces.ContactRepository
	awsS3Repo   interfaces.AWSS3Repository
	logger      logger.Logger
}

func NewContactService(
	contactRepo interfaces.ContactRepository,
	awsS3Repo interfaces.AWSS3Repository,
	logger logger.Logger,
) ContactService {
	return &contactService{
		contactRepo: contactRepo,
		awsS3Repo:   awsS3Repo,
		logger:      logger,
	}
}

func (s *contactService) UploadImage(ctx context.Context, fileBody *http.Request) (*contact.FileUploadResponse, error) {
	fileReader, _, err := fileBody.FormFile("file")
	if err != nil {
		s.logger.WithError(err).Error("failed to read file from request")
		return nil, errors.NewValidationError("failed to read file from request")
	}

	pathName := "backoffice/contact/"

	fileData, err := s.awsS3Repo.UploadFileToS3(ctx, pathName, fileReader)
	if err != nil {
		s.logger.WithError(err).Error("failed to upload file to S3")
		return nil, errors.NewValidationError("failed to upload file to S3")
	}

	fileUrl := contact.FileUploadResponse{
		FileUrl: fileData.FileUrl,
	}

	return &fileUrl, nil
}

func (s *contactService) DeleteImage(ctx context.Context, req *contact.DeleteFileRequest) error {
	if err := s.awsS3Repo.DeleteFileFromS3(ctx, req.FileUrl); err != nil {
		s.logger.WithError(err).Error("failed to delete file from S3")
		return errors.NewValidationError("failed to delete file from S3")
	}

	return nil
}

func (s *contactService) CreateContact(ctx context.Context, req *contact.CreateContactRequest) error {
	nameDuplicate, err := s.contactRepo.FindByNameDuplicate(ctx, req.Name)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate name")
		return err
	}
	if nameDuplicate {
		s.logger.Error("duplicate name")
		return errors.NewValidationError("name already exists")
	}

	err = s.contactRepo.Create(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to create contact")
		return err
	}

	return nil
}

func (s *contactService) FindAllContacts(ctx context.Context, req *contact.ContactSearchRequest) (*response.SuccessWithPagination, error) {
	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		s.logger.WithError(err).Error("failed to set pagination limits")
		return nil, err
	}

	pagination := helper.CalculatePagination(req.Page, req.Limit)

	var conditions []string
	var queryArgs []interface{}

	if req.Name != nil && *req.Name != "" {
		conditions = append(conditions, "UPPER(name) ILIKE UPPER($1)")
		queryArgs = append(queryArgs, "%"+*req.Name+"%")
	}
	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	contacts, total, err := s.contactRepo.FindAll(ctx, pagination.Limit, pagination.Offset, whereClause, queryArgs)
	if err != nil {
		s.logger.WithError(err).Error("failed to find all contacts")
		return nil, err
	}

	content := make([]*contact.ListContactResponse, len(contacts))
	if len(contacts) != 0 {
		for i, c := range contacts {
			content[i] = c.ToListResponse()
		}
	}

	responseData := &response.SuccessWithPagination{
		Message:    "Contacts retrieved successfully",
		Content:    content,
		Page:       int64(pagination.Page),
		Limit:      int64(pagination.Limit),
		TotalPages: helper.CalculateTotalPages(total, pagination.Limit),
		TotalItems: total,
	}

	return responseData, nil
}

func (s *contactService) FindContactByID(ctx context.Context, id int64) (*contact.ByIdContactRequest, error) {

	if id == 0 {
		return nil, errors.NewValidationError("contact ID cannot be zero")
	}

	contactEntity, err := s.contactRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find contact by ID")
		return nil, err
	}

	return contactEntity.ToByIdResponse(), nil
}

func (s *contactService) UpdateContact(ctx context.Context, id int64, req *contact.CreateContactRequest) error {

	if id == 0 {
		return errors.NewValidationError("contact ID cannot be zero")
	}

	nameDuplicate, err := s.contactRepo.FindByNameDuplicateAndIdNot(ctx, req.Name, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate name")
		return err
	}
	if nameDuplicate {
		s.logger.Error("duplicate name")
		return errors.NewValidationError("name already exists")
	}

	// Get existing contact to check for image URL changes
	existingContact, err := s.contactRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find existing contact")
		return err
	}
	if existingContact == nil {
		return errors.NewNotFoundError("contact not found")
	}

	// Check if image URL has changed and delete old image if necessary
	if existingContact.ImageURL != "" && existingContact.ImageURL != req.ImageURL {
		deleteReq := &contact.DeleteFileRequest{
			FileUrl: existingContact.ImageURL,
		}
		if err := s.DeleteImage(ctx, deleteReq); err != nil {
			s.logger.WithError(err).Warn("failed to delete old image, continuing with update")
		}
	}

	err = s.contactRepo.Update(ctx, id, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to update contact")
		return err
	}

	return nil
}

func (s *contactService) UpdateContactStatus(ctx context.Context, id int64, req *contact.UpdateStatusRequest) error {

	if id == 0 {
		return errors.NewValidationError("contact ID cannot be zero")
	}

	err := s.contactRepo.UpdateStatus(ctx, id, req.Name, req.Status)
	if err != nil {
		s.logger.WithError(err).Error("failed to update contact status")
		return err
	}

	return nil
}

func (s *contactService) DeleteContact(ctx context.Context, id int64) error {

	if id == 0 {
		return errors.NewValidationError("contact ID cannot be zero")
	}

	err := s.contactRepo.Delete(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to delete contact")
		return err
	}

	return nil
}
