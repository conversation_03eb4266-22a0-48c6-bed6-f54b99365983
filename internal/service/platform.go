package service

import (
	"context"

	"blacking-api/internal/domain/platform"
	"blacking-api/pkg/logger"
)

// PlatformService defines the interface for platform service operations
type PlatformService interface {
	GetPlatformByID(ctx context.Context, id string) (*platform.PlatformResponse, error)
	ListPlatforms(ctx context.Context) ([]*platform.PlatformResponse, error)
	GetPlatformsByType(ctx context.Context, platformType platform.PlatformType) ([]*platform.PlatformResponse, error)
	GetPlatformTypes(ctx context.Context) []platform.PlatformType
}

type platformService struct {
	logger logger.Logger
}

// NewPlatformService creates a new platform service
func NewPlatformService(logger logger.Logger) PlatformService {
	return &platformService{
		logger: logger,
	}
}

// GetPlatformByID retrieves platform by ID
func (s *platformService) GetPlatformByID(ctx context.Context, id string) (*platform.PlatformResponse, error) {
	log := s.logger.WithContext(ctx).With<PERSON><PERSON>("operation", "GetPlatformByID")

	p, err := platform.GetPlatformByID(id)
	if err != nil {
		log.WithError(err).WithField("platform_id", id).Error("failed to get platform by ID")
		return nil, err
	}

	response := p.ToResponse()
	return &response, nil
}

// ListPlatforms retrieves all fixed platforms
func (s *platformService) ListPlatforms(ctx context.Context) ([]*platform.PlatformResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListPlatforms")

	platforms := platform.GetFixedPlatforms()

	// Convert to response format
	var responses []*platform.PlatformResponse
	for _, p := range platforms {
		response := p.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("platforms listed successfully")
	return responses, nil
}

// GetPlatformsByType retrieves platforms by type
func (s *platformService) GetPlatformsByType(ctx context.Context, platformType platform.PlatformType) ([]*platform.PlatformResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetPlatformsByType")

	// Validate platform type
	if err := platform.ValidatePlatformType(platformType); err != nil {
		log.WithError(err).WithField("platform_type", platformType).Error("invalid platform type")
		return nil, err
	}

	allPlatforms := platform.GetFixedPlatforms()
	var filteredPlatforms []*platform.Platform

	// Filter by type
	for _, p := range allPlatforms {
		if p.Type == platformType {
			filteredPlatforms = append(filteredPlatforms, p)
		}
	}

	// Convert to response format
	var responses []*platform.PlatformResponse
	for _, p := range filteredPlatforms {
		response := p.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("platform_type", platformType).WithField("count", len(responses)).Info("platforms by type retrieved successfully")
	return responses, nil
}

// GetPlatformTypes returns all available platform types
func (s *platformService) GetPlatformTypes(ctx context.Context) []platform.PlatformType {
	log := s.logger.WithContext(ctx).WithField("operation", "GetPlatformTypes")

	types := platform.GetPlatformTypes()
	log.WithField("count", len(types)).Info("platform types retrieved successfully")
	return types
}
