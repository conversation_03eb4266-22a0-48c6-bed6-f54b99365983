package service

import (
	"blacking-api/internal/domain/algorithm"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
)

type AlgorithmService interface {
	ListAlgorithms(ctx context.Context) ([]*algorithm.AlgorithmResponse, error)
}

type algorithmService struct {
	algorithmRepo interfaces.AlgorithmRepository
	logger        logger.Logger
}

func NewAlgorithmService(algorithmRepo interfaces.AlgorithmRepository, logger logger.Logger) AlgorithmService {
	return &algorithmService{
		algorithmRepo: algorithmRepo,
		logger:        logger,
	}
}

func (s *algorithmService) ListAlgorithms(ctx context.Context) ([]*algorithm.AlgorithmResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListAlgorithms")

	algorithms, err := s.algorithmRepo.List(ctx)
	if err != nil {
		log.Errorf("failed to list algorithms: %v", err)
		return nil, err
	}

	responses := make([]*algorithm.AlgorithmResponse, len(algorithms))
	for i, alg := range algorithms {
		response := alg.ToResponse()
		responses[i] = &response
	}

	return responses, nil
}
