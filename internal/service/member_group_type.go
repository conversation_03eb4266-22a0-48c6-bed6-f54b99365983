package service

import (
	"context"
	"net/http"

	"blacking-api/internal/domain/member_group_type"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

// parseBoolString converts interface{} values to bool
// Accepts: "true", "1", "yes", true -> true
// Accepts: "false", "0", "no", false, nil -> false
func parseBoolString(v interface{}) bool {
	if v == nil {
		return false
	}

	switch val := v.(type) {
	case bool:
		return val
	case string:
		switch val {
		case "true", "1", "yes", "True", "TRUE", "YES":
			return true
		default:
			return false
		}
	case *string:
		if val == nil {
			return false
		}
		switch *val {
		case "true", "1", "yes", "True", "TRUE", "YES":
			return true
		default:
			return false
		}
	default:
		return false
	}
}

// MemberGroupTypeService defines the interface for member group type service operations
type MemberGroupTypeService interface {
	CreateMemberGroupType(ctx context.Context, req member_group_type.CreateMemberGroupTypeRequest) (*member_group_type.MemberGroupTypeResponse, error)
	GetMemberGroupTypeByID(ctx context.Context, id int) (*member_group_type.MemberGroupTypeResponse, error)
	UpdateMemberGroupType(ctx context.Context, id int, req member_group_type.UpdateMemberGroupTypeRequest) (*member_group_type.MemberGroupTypeResponse, error)
	DeleteMemberGroupType(ctx context.Context, id int) error
	ListMemberGroupTypes(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*member_group_type.MemberGroupTypeResponse, int64, error)
	ListActiveMemberGroupTypes(ctx context.Context) ([]*member_group_type.MemberGroupTypeResponse, error)
	ListMemberGroupTypesForDropdown(ctx context.Context) ([]*member_group_type.MemberGroupTypeDropdownResponse, error)
	ReorderMemberGroupType(ctx context.Context, id int, direction string) error

	// File upload methods
	FileUpload(ctx context.Context, fileBody *http.Request, fieldName string) (*member_group_type.FileUploadResponse, error)
	DeleteFile(ctx context.Context, req *member_group_type.DeleteFileRequest) error
}

type memberGroupTypeService struct {
	memberGroupTypeRepo interfaces.MemberGroupTypeRepository
	awsS3Repo           interfaces.AWSS3Repository
	logger              logger.Logger
}

// NewMemberGroupTypeService creates a new member group type service
func NewMemberGroupTypeService(memberGroupTypeRepo interfaces.MemberGroupTypeRepository, awsS3Repo interfaces.AWSS3Repository, logger logger.Logger) MemberGroupTypeService {
	return &memberGroupTypeService{
		memberGroupTypeRepo: memberGroupTypeRepo,
		awsS3Repo:           awsS3Repo,
		logger:              logger,
	}
}

// CreateMemberGroupType creates a new member group type
func (s *memberGroupTypeService) CreateMemberGroupType(ctx context.Context, req member_group_type.CreateMemberGroupTypeRequest) (*member_group_type.MemberGroupTypeResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateMemberGroupType")

	// Check if member group type name already exists
	existingMemberGroupType, err := s.memberGroupTypeRepo.GetByName(ctx, req.Name)
	if err == nil && existingMemberGroupType != nil {
		log.WithField("name", req.Name).Error("member group type name already exists")
		return nil, errors.NewValidationError("member group type name already exists")
	}

	// Get next position if not provided
	if req.Position == 0 {
		nextPosition, err := s.memberGroupTypeRepo.GetNextPosition(ctx)
		if err != nil {
			log.WithError(err).Error("failed to get next position")
			return nil, err
		}
		req.Position = nextPosition
	}

	// Create new member group type
	newMemberGroupType, err := member_group_type.NewMemberGroupType(req)
	if err != nil {
		log.WithError(err).Error("failed to create member group type domain object")
		return nil, err
	}

	// Save to repository
	if err := s.memberGroupTypeRepo.Create(ctx, newMemberGroupType); err != nil {
		log.WithError(err).Error("failed to save member group type to repository")
		return nil, err
	}

	response := newMemberGroupType.ToResponse()
	log.WithField("member_group_type_id", response.ID).WithField("name", req.Name).Info("member group type created successfully")
	return &response, nil
}

// GetMemberGroupTypeByID retrieves member group type by ID
func (s *memberGroupTypeService) GetMemberGroupTypeByID(ctx context.Context, id int) (*member_group_type.MemberGroupTypeResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberGroupTypeByID")

	mgt, err := s.memberGroupTypeRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("member_group_type_id", id).Error("failed to get member group type by ID")
		return nil, err
	}

	response := mgt.ToResponse()
	return &response, nil
}

// UpdateMemberGroupType updates an existing member group type
func (s *memberGroupTypeService) UpdateMemberGroupType(ctx context.Context, id int, req member_group_type.UpdateMemberGroupTypeRequest) (*member_group_type.MemberGroupTypeResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateMemberGroupType")

	// Get existing member group type
	mgt, err := s.memberGroupTypeRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("member_group_type_id", id).Error("failed to get member group type for update")
		return nil, err
	}

	// Store old image URLs for deletion if needed
	var oldImage1, oldImage2, oldBgImage *string
	if mgt.Image1 != nil {
		oldImage1 = mgt.Image1
	}
	if mgt.Image2 != nil {
		oldImage2 = mgt.Image2
	}
	if mgt.BgImage != nil {
		oldBgImage = mgt.BgImage
	}

	// Merge request with existing data (preserve existing values if not provided)
	mergedReq := s.mergeUpdateRequest(mgt, req)

	// Check if new name conflicts with existing member group type (excluding current member group type)
	if mgt.Name != mergedReq.Name {
		existingMemberGroupType, err := s.memberGroupTypeRepo.GetByName(ctx, mergedReq.Name)
		if err == nil && existingMemberGroupType != nil && existingMemberGroupType.ID != id {
			log.WithField("name", mergedReq.Name).Error("member group type name already exists")
			return nil, errors.NewValidationError("member group type name already exists")
		}
	}

	// Update member group type
	if err := mgt.Update(mergedReq); err != nil {
		log.WithError(err).Error("failed to update member group type domain object")
		return nil, err
	}

	// Save to repository
	if err := s.memberGroupTypeRepo.Update(ctx, mgt); err != nil {
		log.WithError(err).Error("failed to save updated member group type to repository")
		return nil, err
	}

	if parseBoolString(req.Image1Delete) && oldImage1 != nil {
		log.WithField("file_url", *oldImage1).Info("deleting image1 file")
		if err := s.DeleteFile(ctx, &member_group_type.DeleteFileRequest{FileUrl: *oldImage1}); err != nil {
			log.WithError(err).WithField("file_url", *oldImage1).Warn("failed to delete old image1 file")
		} else {
			log.WithField("file_url", *oldImage1).Info("successfully deleted image1 file")
		}
	} else if req.Image1 != nil && oldImage1 != nil && *req.Image1 != *oldImage1 {
		// New file uploaded, delete old one
		log.WithField("file_url", *oldImage1).Info("deleting replaced image1 file")
		if err := s.DeleteFile(ctx, &member_group_type.DeleteFileRequest{FileUrl: *oldImage1}); err != nil {
			log.WithError(err).WithField("file_url", *oldImage1).Warn("failed to delete replaced image1 file")
		} else {
			log.WithField("file_url", *oldImage1).Info("successfully deleted replaced image1 file")
		}
	}

	if parseBoolString(req.Image2Delete) && oldImage2 != nil {
		log.WithField("file_url", *oldImage2).Info("deleting image2 file")
		if err := s.DeleteFile(ctx, &member_group_type.DeleteFileRequest{FileUrl: *oldImage2}); err != nil {
			log.WithError(err).WithField("file_url", *oldImage2).Warn("failed to delete old image2 file")
		} else {
			log.WithField("file_url", *oldImage2).Info("successfully deleted image2 file")
		}
	} else if req.Image2 != nil && oldImage2 != nil && *req.Image2 != *oldImage2 {
		// New file uploaded, delete old one
		log.WithField("file_url", *oldImage2).Info("deleting replaced image2 file")
		if err := s.DeleteFile(ctx, &member_group_type.DeleteFileRequest{FileUrl: *oldImage2}); err != nil {
			log.WithError(err).WithField("file_url", *oldImage2).Warn("failed to delete replaced image2 file")
		} else {
			log.WithField("file_url", *oldImage2).Info("successfully deleted replaced image2 file")
		}
	}

	if parseBoolString(req.BgImageDelete) && oldBgImage != nil {
		log.WithField("file_url", *oldBgImage).Info("deleting bg_image file")
		if err := s.DeleteFile(ctx, &member_group_type.DeleteFileRequest{FileUrl: *oldBgImage}); err != nil {
			log.WithError(err).WithField("file_url", *oldBgImage).Warn("failed to delete old bg_image file")
		} else {
			log.WithField("file_url", *oldBgImage).Info("successfully deleted bg_image file")
		}
	} else if req.BgImage != nil && oldBgImage != nil && *req.BgImage != *oldBgImage {
		// New file uploaded, delete old one
		log.WithField("file_url", *oldBgImage).Info("deleting replaced bg_image file")
		if err := s.DeleteFile(ctx, &member_group_type.DeleteFileRequest{FileUrl: *oldBgImage}); err != nil {
			log.WithError(err).WithField("file_url", *oldBgImage).Warn("failed to delete replaced bg_image file")
		} else {
			log.WithField("file_url", *oldBgImage).Info("successfully deleted replaced bg_image file")
		}
	}

	response := mgt.ToResponse()
	log.WithField("member_group_type_id", response.ID).Info("member group type updated successfully")
	return &response, nil
}

// DeleteMemberGroupType soft deletes a member group type
func (s *memberGroupTypeService) DeleteMemberGroupType(ctx context.Context, id int) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteMemberGroupType")

	if err := s.memberGroupTypeRepo.Delete(ctx, id); err != nil {
		log.WithError(err).WithField("member_group_type_id", id).Error("failed to delete member group type")
		return err
	}

	log.WithField("member_group_type_id", id).Info("member group type deleted successfully")
	return nil
}

// ListMemberGroupTypes retrieves member group types with pagination, search, and sorting
func (s *memberGroupTypeService) ListMemberGroupTypes(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*member_group_type.MemberGroupTypeResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListMemberGroupTypes")

	// Get total count
	total, err := s.memberGroupTypeRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count member group types")
		return nil, 0, err
	}

	// Get member group types
	memberGroupTypes, err := s.memberGroupTypeRepo.List(ctx, limit, offset, search, sortBy, sortOrder)
	if err != nil {
		log.WithError(err).Error("failed to list member group types")
		return nil, 0, err
	}

	// Convert to response format
	var responses []*member_group_type.MemberGroupTypeResponse
	for _, mgt := range memberGroupTypes {
		response := mgt.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("total", total).WithField("returned", len(responses)).Info("member group types listed successfully")
	return responses, total, nil
}

// ListActiveMemberGroupTypes retrieves only active member group types
func (s *memberGroupTypeService) ListActiveMemberGroupTypes(ctx context.Context) ([]*member_group_type.MemberGroupTypeResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListActiveMemberGroupTypes")

	memberGroupTypes, err := s.memberGroupTypeRepo.ListActive(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list active member group types")
		return nil, err
	}

	// Convert to response format
	var responses []*member_group_type.MemberGroupTypeResponse
	for _, mgt := range memberGroupTypes {
		response := mgt.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("active member group types listed successfully")
	return responses, nil
}

// ListMemberGroupTypesForDropdown retrieves member group types for dropdown filter
func (s *memberGroupTypeService) ListMemberGroupTypesForDropdown(ctx context.Context) ([]*member_group_type.MemberGroupTypeDropdownResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListMemberGroupTypesForDropdown")

	dropdownItems, err := s.memberGroupTypeRepo.ListForDropdown(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list member group types for dropdown")
		return nil, err
	}

	log.WithField("count", len(dropdownItems)).Info("member group types for dropdown listed successfully")
	return dropdownItems, nil
}

// ReorderMemberGroupType reorders a member group type (move up or down)
func (s *memberGroupTypeService) ReorderMemberGroupType(ctx context.Context, id int, direction string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ReorderMemberGroupType")

	// Verify the member group type exists
	_, err := s.memberGroupTypeRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("member_group_type_id", id).Error("member group type not found")
		return err
	}

	// Reorder based on direction
	if direction == "up" {
		err = s.memberGroupTypeRepo.ReorderUp(ctx, id)
	} else if direction == "down" {
		err = s.memberGroupTypeRepo.ReorderDown(ctx, id)
	} else {
		return errors.NewValidationError("invalid direction, must be 'up' or 'down'")
	}

	if err != nil {
		log.WithError(err).WithField("member_group_type_id", id).WithField("direction", direction).Error("failed to reorder member group type")
		return err
	}

	log.WithField("member_group_type_id", id).WithField("direction", direction).Info("member group type reordered successfully")
	return nil
}

// FileUpload uploads a file to S3 for member group type
func (s *memberGroupTypeService) FileUpload(ctx context.Context, fileBody *http.Request, fieldName string) (*member_group_type.FileUploadResponse, error) {
	// Use provided field name or default to "file"
	if fieldName == "" {
		fieldName = "file"
	}

	fileReader, _, err := fileBody.FormFile(fieldName)
	if err != nil {
		s.logger.WithError(err).WithField("field_name", fieldName).Error("failed to read file from request")
		return nil, errors.NewValidationError("failed to read file from request")
	}

	pathName := "backoffice/member-group-types/"

	fileData, err := s.awsS3Repo.UploadFileToS3(ctx, pathName, fileReader)
	if err != nil {
		s.logger.WithError(err).Error("failed to upload file to S3")
		return nil, errors.NewValidationError("failed to upload file to S3")
	}

	fileUrl := member_group_type.FileUploadResponse{
		FileUrl: fileData.FileUrl,
	}

	return &fileUrl, nil
}

// DeleteFile deletes a file from S3 for member group type
func (s *memberGroupTypeService) DeleteFile(ctx context.Context, req *member_group_type.DeleteFileRequest) error {
	if err := s.awsS3Repo.DeleteFileFromS3(ctx, req.FileUrl); err != nil {
		s.logger.WithError(err).Error("failed to delete file from S3")
		return errors.NewValidationError("failed to delete file from S3")
	}

	s.logger.WithField("file_url", req.FileUrl).Info("file deleted from S3 successfully")
	return nil
}

// mergeUpdateRequest merges update request with existing data to preserve unmodified fields
func (s *memberGroupTypeService) mergeUpdateRequest(existing *member_group_type.MemberGroupType, req member_group_type.UpdateMemberGroupTypeRequest) member_group_type.UpdateMemberGroupTypeRequest {
	merged := member_group_type.UpdateMemberGroupTypeRequest{
		// Use existing values as defaults
		Name:                       existing.Name,
		Position:                   existing.Position,
		ShowInLobby:                existing.ShowInLobby,
		BadgeBgColor:               existing.BadgeBgColor,
		BadgeBorderColor:           existing.BadgeBorderColor,
		Image1:                     existing.Image1,
		Image2:                     existing.Image2,
		BgImage:                    existing.BgImage,
		Image1Delete:               req.Image1Delete,
		Image2Delete:               req.Image2Delete,
		BgImageDelete:              req.BgImageDelete,
		VipEnabled:                 existing.VipEnabled,
		VipPersonalCustomerService: existing.VipPersonalCustomerService,
		VipMaxDailyWithdraw:        existing.VipMaxDailyWithdraw,
		VipEventParticipation:      existing.VipEventParticipation,
		BonusEnabled:               existing.BonusEnabled,
		BonusBirthday:              existing.BonusBirthday,
		BonusLevelMaintenance:      existing.BonusLevelMaintenance,
		BonusLevelUpgrade:          existing.BonusLevelUpgrade,
		BonusFestival:              existing.BonusFestival,
		CashbackEnabled:            existing.CashbackEnabled,
		CashbackSports:             existing.CashbackSports,
		CashbackCasino:             existing.CashbackCasino,
		CashbackFishing:            existing.CashbackFishing,
		CashbackSlot:               existing.CashbackSlot,
		CashbackLottery:            existing.CashbackLottery,
		CashbackCard:               existing.CashbackCard,
		CashbackOther:              existing.CashbackOther,
		UpgradeBettingAmount:       existing.UpgradeBettingAmount,
		UpgradeDepositAmount:       existing.UpgradeDepositAmount,
		UpgradeCalculationType:     existing.UpgradeCalculationType,
		UpgradeDays:                existing.UpgradeDays,
		UpgradeConditionType:       existing.UpgradeConditionType,
		DowngradeBettingAmount:     existing.DowngradeBettingAmount,
		DowngradeDepositAmount:     existing.DowngradeDepositAmount,
		DowngradeCalculationType:   existing.DowngradeCalculationType,
		DowngradeDays:              existing.DowngradeDays,
		DowngradeConditionType:     existing.DowngradeConditionType,
	}

	// Override with provided values (only non-zero values)
	if req.Name != "" {
		merged.Name = req.Name
	}
	if req.Position != 0 {
		merged.Position = req.Position
	}
	// Note: Boolean fields are tricky because false is zero value
	// We need to check if they were explicitly set in the request
	// For now, we'll assume they're always provided if the form field exists
	merged.ShowInLobby = req.ShowInLobby
	if req.BadgeBgColor != "" {
		merged.BadgeBgColor = req.BadgeBgColor
	}
	if req.BadgeBorderColor != "" {
		merged.BadgeBorderColor = req.BadgeBorderColor
	}
	if req.Image1 != nil {
		merged.Image1 = req.Image1
	}
	if req.Image2 != nil {
		merged.Image2 = req.Image2
	}
	if req.BgImage != nil {
		merged.BgImage = req.BgImage
	}
	// Boolean fields - only update if they were explicitly provided in the request
	// Note: The handler uses GetPostForm to check field existence, so we can safely use the values
	// For now, we'll update all boolean fields since the handler only sets them if they exist
	merged.VipEnabled = req.VipEnabled
	merged.VipPersonalCustomerService = req.VipPersonalCustomerService
	merged.VipMaxDailyWithdraw = req.VipMaxDailyWithdraw
	merged.VipEventParticipation = req.VipEventParticipation
	merged.BonusEnabled = req.BonusEnabled
	merged.BonusBirthday = req.BonusBirthday
	merged.BonusLevelMaintenance = req.BonusLevelMaintenance
	merged.BonusLevelUpgrade = req.BonusLevelUpgrade
	merged.BonusFestival = req.BonusFestival
	merged.CashbackEnabled = req.CashbackEnabled
	merged.CashbackSports = req.CashbackSports
	merged.CashbackCasino = req.CashbackCasino
	merged.CashbackFishing = req.CashbackFishing
	merged.CashbackSlot = req.CashbackSlot
	merged.CashbackLottery = req.CashbackLottery
	merged.CashbackCard = req.CashbackCard
	merged.CashbackOther = req.CashbackOther
	if req.UpgradeBettingAmount != 0 {
		merged.UpgradeBettingAmount = req.UpgradeBettingAmount
	}
	if req.UpgradeDepositAmount != 0 {
		merged.UpgradeDepositAmount = req.UpgradeDepositAmount
	}
	if req.UpgradeCalculationType != "" {
		merged.UpgradeCalculationType = req.UpgradeCalculationType
	}
	if req.UpgradeDays != 0 {
		merged.UpgradeDays = req.UpgradeDays
	}
	if req.UpgradeConditionType != "" {
		merged.UpgradeConditionType = req.UpgradeConditionType
	}
	if req.DowngradeBettingAmount != 0 {
		merged.DowngradeBettingAmount = req.DowngradeBettingAmount
	}
	if req.DowngradeDepositAmount != 0 {
		merged.DowngradeDepositAmount = req.DowngradeDepositAmount
	}
	if req.DowngradeCalculationType != "" {
		merged.DowngradeCalculationType = req.DowngradeCalculationType
	}
	if req.DowngradeDays != 0 {
		merged.DowngradeDays = req.DowngradeDays
	}
	if req.DowngradeConditionType != "" {
		merged.DowngradeConditionType = req.DowngradeConditionType
	}

	return merged
}
