package service

import (
	"blacking-api/internal/domain/report"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
	"encoding/csv"
	"fmt"
	"strings"
)

// LoginReportService defines the interface for login report service operations
type LoginReportService interface {
	// GetMemberLoginReport retrieves member login report with filters
	GetMemberLoginReport(ctx context.Context, filter report.LoginReportFilter) (*report.MemberLoginReportResponse, error)

	// GetAdminLoginReport retrieves admin login report with filters
	GetAdminLoginReport(ctx context.Context, filter report.LoginReportFilter) (*report.AdminLoginReportResponse, error)

	// GetDuplicateIPReport retrieves duplicate IP report
	GetDuplicateIPReport(ctx context.Context, filter report.LoginReportFilter) ([]*report.DuplicateIPReport, error)

	// GenerateMemberLoginCSV generates CSV content for member login report
	GenerateMemberLoginCSV(ctx context.Context, filter report.LoginReportFilter) (string, error)

	// GenerateAdminLoginCSV generates CSV content for admin login report
	GenerateAdminLoginCSV(ctx context.Context, filter report.LoginReportFilter) (string, error)

	// Filter options for dropdowns
	GetDistinctUserAgents(ctx context.Context) ([]string, error)
	GetDistinctIPAddresses(ctx context.Context) ([]string, error)
	GetDistinctMemberUsernames(ctx context.Context) ([]string, error)
	GetDistinctAdminUsernames(ctx context.Context) ([]string, error)
}

type loginReportService struct {
	loginReportRepo interfaces.LoginReportRepository
	logger          logger.Logger
}

// NewLoginReportService creates a new login report service
func NewLoginReportService(loginReportRepo interfaces.LoginReportRepository, logger logger.Logger) LoginReportService {
	return &loginReportService{
		loginReportRepo: loginReportRepo,
		logger:          logger,
	}
}

// GetMemberLoginReport retrieves member login report with filters
func (s *loginReportService) GetMemberLoginReport(ctx context.Context, filter report.LoginReportFilter) (*report.MemberLoginReportResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberLoginReport")

	// Validate and set defaults
	filter.ValidateAndSetDefaults()

	// Get reports
	reports, err := s.loginReportRepo.GetMemberLoginReport(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to get member login report")
		return nil, err
	}

	// Get total count
	total, err := s.loginReportRepo.CountMemberLoginReport(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to count member login report")
		return nil, err
	}

	// Check if there are duplicate IPs in the current result set
	hasDuplicateIPs := false
	if !filter.DuplicateIP {
		duplicateReports, err := s.loginReportRepo.GetDuplicateIPReport(ctx, filter)
		if err == nil && len(duplicateReports) > 0 {
			hasDuplicateIPs = true
		}
	}

	response := &report.MemberLoginReportResponse{
		Reports:         reports,
		Total:           total,
		Limit:           filter.Limit,
		Offset:          filter.Offset,
		HasDuplicateIPs: hasDuplicateIPs,
	}

	log.WithField("count", len(reports)).WithField("total", total).Info("member login report retrieved successfully")
	return response, nil
}

// GetAdminLoginReport retrieves admin login report with filters
func (s *loginReportService) GetAdminLoginReport(ctx context.Context, filter report.LoginReportFilter) (*report.AdminLoginReportResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetAdminLoginReport")

	// Validate and set defaults
	filter.ValidateAndSetDefaults()

	// Get reports
	reports, err := s.loginReportRepo.GetAdminLoginReport(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to get admin login report")
		return nil, err
	}

	// Get total count
	total, err := s.loginReportRepo.CountAdminLoginReport(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to count admin login report")
		return nil, err
	}

	response := &report.AdminLoginReportResponse{
		Reports: reports,
		Total:   total,
		Limit:   filter.Limit,
		Offset:  filter.Offset,
	}

	log.WithField("count", len(reports)).WithField("total", total).Info("admin login report retrieved successfully")
	return response, nil
}

// GetDuplicateIPReport retrieves duplicate IP report with detailed login attempts
func (s *loginReportService) GetDuplicateIPReport(ctx context.Context, filter report.LoginReportFilter) ([]*report.DuplicateIPReport, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetDuplicateIPReport")

	// Validate and set defaults
	filter.ValidateAndSetDefaults()

	// Get duplicate IP reports
	reports, err := s.loginReportRepo.GetDuplicateIPReport(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to get duplicate IP report")
		return nil, err
	}

	// Get detailed login attempts for each duplicate IP
	for _, report := range reports {
		details, err := s.loginReportRepo.GetDuplicateIPLoginDetails(ctx, report.IPAddress, filter)
		if err != nil {
			log.WithError(err).WithField("ip_address", report.IPAddress).Warn("failed to get login details for duplicate IP")
			// Continue with other IPs even if one fails
			continue
		}
		report.LoginDetails = details
	}

	log.WithField("count", len(reports)).Info("duplicate IP report with details retrieved successfully")
	return reports, nil
}

// GenerateMemberLoginCSV generates CSV content for member login report
func (s *loginReportService) GenerateMemberLoginCSV(ctx context.Context, filter report.LoginReportFilter) (string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GenerateMemberLoginCSV")

	// Remove pagination for CSV export (get all records)
	filter.Limit = 10000 // Set a reasonable max limit
	filter.Offset = 0

	// Validate and set defaults
	filter.ValidateAndSetDefaults()

	// Get all reports for CSV
	reports, err := s.loginReportRepo.GetMemberLoginReport(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to get member login report for CSV")
		return "", err
	}

	if len(reports) == 0 {
		return "", fmt.Errorf("no data found for the specified criteria")
	}

	// Generate CSV content
	var csvContent strings.Builder
	writer := csv.NewWriter(&csvContent)

	// Write header
	header := reports[0].CSVHeader()
	if err := writer.Write(header); err != nil {
		log.WithError(err).Error("failed to write CSV header")
		return "", err
	}

	// Write data rows
	for _, report := range reports {
		row := report.CSVRow()
		if err := writer.Write(row); err != nil {
			log.WithError(err).Error("failed to write CSV row")
			return "", err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		log.WithError(err).Error("failed to flush CSV writer")
		return "", err
	}

	log.WithField("rows", len(reports)).Info("member login CSV generated successfully")
	return csvContent.String(), nil
}

// GenerateAdminLoginCSV generates CSV content for admin login report
func (s *loginReportService) GenerateAdminLoginCSV(ctx context.Context, filter report.LoginReportFilter) (string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GenerateAdminLoginCSV")

	// Remove pagination for CSV export (get all records)
	filter.Limit = 10000 // Set a reasonable max limit
	filter.Offset = 0

	// Validate and set defaults
	filter.ValidateAndSetDefaults()

	// Get all reports for CSV
	reports, err := s.loginReportRepo.GetAdminLoginReport(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to get admin login report for CSV")
		return "", err
	}

	if len(reports) == 0 {
		return "", fmt.Errorf("no data found for the specified criteria")
	}

	// Generate CSV content
	var csvContent strings.Builder
	writer := csv.NewWriter(&csvContent)

	// Write header
	header := reports[0].CSVHeader()
	if err := writer.Write(header); err != nil {
		log.WithError(err).Error("failed to write CSV header")
		return "", err
	}

	// Write data rows
	for _, report := range reports {
		row := report.CSVRow()
		if err := writer.Write(row); err != nil {
			log.WithError(err).Error("failed to write CSV row")
			return "", err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		log.WithError(err).Error("failed to flush CSV writer")
		return "", err
	}

	log.WithField("rows", len(reports)).Info("admin login CSV generated successfully")
	return csvContent.String(), nil
}

// GetDistinctUserAgents retrieves all distinct user agents for dropdown
func (s *loginReportService) GetDistinctUserAgents(ctx context.Context) ([]string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetDistinctUserAgents")

	userAgents, err := s.loginReportRepo.GetDistinctUserAgents(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get distinct user agents")
		return nil, err
	}

	log.WithField("count", len(userAgents)).Info("distinct user agents retrieved successfully")
	return userAgents, nil
}

// GetDistinctIPAddresses retrieves all distinct IP addresses for dropdown
func (s *loginReportService) GetDistinctIPAddresses(ctx context.Context) ([]string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetDistinctIPAddresses")

	ipAddresses, err := s.loginReportRepo.GetDistinctIPAddresses(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get distinct IP addresses")
		return nil, err
	}

	log.WithField("count", len(ipAddresses)).Info("distinct IP addresses retrieved successfully")
	return ipAddresses, nil
}

// GetDistinctMemberUsernames retrieves all distinct member usernames for dropdown
func (s *loginReportService) GetDistinctMemberUsernames(ctx context.Context) ([]string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetDistinctMemberUsernames")

	usernames, err := s.loginReportRepo.GetDistinctMemberUsernames(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get distinct member usernames")
		return nil, err
	}

	log.WithField("count", len(usernames)).Info("distinct member usernames retrieved successfully")
	return usernames, nil
}

// GetDistinctAdminUsernames retrieves all distinct admin usernames for dropdown
func (s *loginReportService) GetDistinctAdminUsernames(ctx context.Context) ([]string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetDistinctAdminUsernames")

	usernames, err := s.loginReportRepo.GetDistinctAdminUsernames(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get distinct admin usernames")
		return nil, err
	}

	log.WithField("count", len(usernames)).Info("distinct admin usernames retrieved successfully")
	return usernames, nil
}
