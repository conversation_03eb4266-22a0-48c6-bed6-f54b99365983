package service

import (
	"blacking-api/internal/domain/payment_method"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
)

type PaymentMethodService interface {
	CreatePaymentMethod(ctx context.Context, req *payment_method.PaymentMethodRequest) error
	ListPaymentMethods(ctx context.Context) ([]*payment_method.PaymentMethodResponse, error)
	FindPaymentMethodByID(ctx context.Context, id int64) (*payment_method.PaymentMethodResponse, error)
	UpdatePaymentMethod(ctx context.Context, id int64, req *payment_method.PaymentMethodRequest) error
	UpdatePaymentMethodStatus(ctx context.Context, id int64, req *payment_method.UpdateStatusRequest) error
	DeletePaymentMethod(ctx context.Context, id int64) error
}

type paymentMethodService struct {
	paymentMethodRepo interfaces.PaymentMethodRepository
	logger            logger.Logger
}

func NewPaymentMethodService(paymentMethodRepo interfaces.PaymentMethodRepository, logger logger.Logger) PaymentMethodService {
	return &paymentMethodService{
		paymentMethodRepo: paymentMethodRepo,
		logger:            logger,
	}
}

func (s *paymentMethodService) CreatePaymentMethod(ctx context.Context, req *payment_method.PaymentMethodRequest) error {

	nameDuplicate, err := s.paymentMethodRepo.FindByNameDuplicate(ctx, req.Name)
	if err != nil {
		s.logger.WithError(err).Error("error finding payment method by name")
		return err
	}
	if nameDuplicate {
		s.logger.Error("duplicate payment method name")
		return errors.NewValidationError("payment method name already exists")
	}

	err = s.paymentMethodRepo.Create(ctx, req)
	if err != nil {
		s.logger.WithError(err).Error("error creating payment method")
		return err
	}

	return nil
}

func (s *paymentMethodService) ListPaymentMethods(ctx context.Context) ([]*payment_method.PaymentMethodResponse, error) {

	paymentMethods, err := s.paymentMethodRepo.List(ctx)
	if err != nil {
		s.logger.WithError(err).Error("error listing payment methods")
		return nil, err
	}

	responses := make([]*payment_method.PaymentMethodResponse, len(paymentMethods))
	for i, pm := range paymentMethods {
		response := pm.ToResponse()
		responses[i] = response
	}

	return responses, nil
}

func (s *paymentMethodService) FindPaymentMethodByID(ctx context.Context, id int64) (*payment_method.PaymentMethodResponse, error) {

	paymentMethod, err := s.paymentMethodRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("error finding payment method by ID")
		return nil, err
	}

	return paymentMethod.ToResponse(), nil
}

func (s *paymentMethodService) UpdatePaymentMethod(ctx context.Context, id int64, req *payment_method.PaymentMethodRequest) error {
	nameDuplicate, err := s.paymentMethodRepo.FindByNameDuplicateAndIdNot(ctx, req.Name, id)
	if err != nil {
		s.logger.WithError(err).Error("error checking duplicate payment method name")
		return err
	}
	if nameDuplicate {
		s.logger.Error("duplicate payment method name")
		return errors.NewValidationError("payment method name already exists")
	}

	err = s.paymentMethodRepo.Update(ctx, id, req)
	if err != nil {
		s.logger.WithError(err).Error("error updating payment method")
		return err
	}

	return nil
}

func (s *paymentMethodService) UpdatePaymentMethodStatus(ctx context.Context, id int64, req *payment_method.UpdateStatusRequest) error {
	if id == 0 {
		return errors.NewValidationError("payment method ID cannot be zero")
	}

	err := s.paymentMethodRepo.UpdateStatus(ctx, id, req.Name, req.Status)
	if err != nil {
		s.logger.WithError(err).Error("error updating payment method status")
		return err
	}

	return nil
}

func (s *paymentMethodService) DeletePaymentMethod(ctx context.Context, id int64) error {
	if id == 0 {
		return errors.NewValidationError("payment method ID cannot be zero")
	}

	err := s.paymentMethodRepo.Delete(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("error deleting payment method")
		return err
	}

	return nil
}
