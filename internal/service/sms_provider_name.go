package service

import (
	"blacking-api/internal/domain/sms_provider_name"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
)

type SMSProviderNameService interface {
	ListSMSProviderNames(ctx context.Context) ([]*sms_provider_name.SMSProviderNameResponse, error)
}

type smsProviderNameService struct {
	smsProviderNameRepo interfaces.SMSProviderNameRepository
	logger              logger.Logger
}

func NewSMSProviderNameService(smsProviderNameRepo interfaces.SMSProviderNameRepository, logger logger.Logger) SMSProviderNameService {
	return &smsProviderNameService{
		smsProviderNameRepo: smsProviderNameRepo,
		logger:              logger,
	}
}

func (s *smsProviderNameService) ListSMSProviderNames(ctx context.Context) ([]*sms_provider_name.SMSProviderNameResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListSMSProviderNames")

	smsProviderNames, err := s.smsProviderNameRepo.List(ctx)
	if err != nil {
		log.Errorf("failed to list SMS provider names: %v", err)
		return nil, err
	}

	responses := make([]*sms_provider_name.SMSProviderNameResponse, len(smsProviderNames))
	for i, spn := range smsProviderNames {
		response := spn.ToResponse()
		responses[i] = &response
	}

	return responses, nil
}
