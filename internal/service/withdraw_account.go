package service

import (
	"blacking-api/internal/domain/response"
	"blacking-api/internal/domain/withdraw_account"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"net/http"
	"strings"
)

type WithdrawAccountService interface {
	FileUpload(ctx context.Context, fileBody *http.Request) (*withdraw_account.FileUploadResponse, error)
	DeleteFile(ctx context.Context, req *withdraw_account.DeleteFileRequest) error
	CreateWithdrawAccount(ctx context.Context, req *withdraw_account.WithdrawAccountRequest) error
	FindAllWithdrawAccount(ctx context.Context, req *withdraw_account.WithdrawAccountSearchRequest) (*response.SuccessWithPagination, error)
	FindWithdrawAccountByID(ctx context.Context, id int64) (*withdraw_account.WithdrawAccountByIdResponse, error)
	FindWithdrawAccountSettingAlgorithmByID(ctx context.Context, id int64) (*withdraw_account.WithdrawAccountSettingAlgorithmResponse, error)
	UpdateWithdrawAccount(ctx context.Context, id int64, req *withdraw_account.WithdrawAccountRequest) error
	UpdateWithdrawAccountAutoBot(ctx context.Context, id int64, autoBotID int64) error
	UpdateWithdrawAccountAlgorithm(ctx context.Context, id int64, req *withdraw_account.WithdrawAccountSettingAlgorithmRequest) error
	ActiveWithdrawAccount(ctx context.Context, id int64, status bool) error
	DeleteWithdrawAccount(ctx context.Context, id int64) error
}

type withdrawAccountService struct {
	withdrawAccountRepository interfaces.WithdrawAccountRepository
	bankingRepo               interfaces.BankingRepository
	algorithmRepo             interfaces.AlgorithmRepository
	autoBotRepo               interfaces.AutoBotRepository
	awsS3Repo                 interfaces.AWSS3Repository
	logger                    logger.Logger
}

func NewWithdrawAccountService(
	withdrawAccountRepository interfaces.WithdrawAccountRepository,
	bankingRepo interfaces.BankingRepository,
	algorithmRepo interfaces.AlgorithmRepository,
	autoBotRepo interfaces.AutoBotRepository,
	awsS3Repo interfaces.AWSS3Repository,
	logger logger.Logger,
) WithdrawAccountService {
	return &withdrawAccountService{
		withdrawAccountRepository: withdrawAccountRepository,
		bankingRepo:               bankingRepo,
		algorithmRepo:             algorithmRepo,
		autoBotRepo:               autoBotRepo,
		awsS3Repo:                 awsS3Repo,
		logger:                    logger,
	}
}

func (s *withdrawAccountService) FileUpload(ctx context.Context, fileBody *http.Request) (*withdraw_account.FileUploadResponse, error) {
	fileReader, _, err := fileBody.FormFile("file")
	if err != nil {
		s.logger.WithError(err).Error("failed to read file from request")
		return nil, errors.NewValidationError("failed to read file from request")
	}

	pathName := "backoffice/"

	fileData, err := s.awsS3Repo.UploadFileToS3(ctx, pathName, fileReader)
	if err != nil {
		s.logger.WithError(err).Error("failed to upload file to S3")
		return nil, errors.NewValidationError("failed to upload file to S3")
	}

	filerUrl := withdraw_account.FileUploadResponse{
		FileUrl: fileData.FileUrl,
	}

	return &filerUrl, nil
}

func (s *withdrawAccountService) DeleteFile(ctx context.Context, req *withdraw_account.DeleteFileRequest) error {

	if err := s.awsS3Repo.DeleteFileFromS3(ctx, req.FileUrl); err != nil {
		s.logger.WithError(err).Error("failed to delete file from S3")
		return errors.NewValidationError("failed to delete file from S3")
	}

	return nil
}

func (s *withdrawAccountService) CreateWithdrawAccount(ctx context.Context, req *withdraw_account.WithdrawAccountRequest) error {

	bankingExists, err := s.bankingRepo.FindIdExists(ctx, int64(req.BankingID))
	if err != nil {
		s.logger.WithError(err).Error("failed to check if banking exists")
		return err
	}
	if !bankingExists {
		s.logger.WithError(err).Error("banking not exists")
		return errors.NewValidationError("banking ID does not exist")
	}

	accountNumberDuplicate, err := s.withdrawAccountRepository.FindByAccountNumberDuplicate(ctx, req.AccountNumber)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate account number")
		return err
	}
	if accountNumberDuplicate {
		s.logger.WithError(err).Error("account number already exists")
		return errors.NewValidationError("account number already exists")
	}

	if err := s.withdrawAccountRepository.Create(ctx, req); err != nil {
		s.logger.WithError(err).Error("failed to create withdraw account")
		return err
	}

	return nil
}

func (s *withdrawAccountService) FindAllWithdrawAccount(ctx context.Context, req *withdraw_account.WithdrawAccountSearchRequest) (*response.SuccessWithPagination, error) {

	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		s.logger.WithError(err).Error("failed to set pagination limits")
		return nil, err
	}

	pagination := helper.CalculatePagination(req.Page, req.Limit)

	var conditions []string
	var queryArgs []interface{}

	if req.AccountName != nil && *req.AccountName != "" {
		conditions = append(conditions, "UPPER(account_name) ILIKE UPPER($1)")
		queryArgs = append(queryArgs, "%"+*req.AccountName+"%")
	}

	if req.BankingID != nil && *req.BankingID > 0 {
		conditions = append(conditions, "banking_id = $2")
		queryArgs = append(queryArgs, *req.BankingID)
	}

	if req.AlgorithmID != nil && *req.AlgorithmID > 0 {
		conditions = append(conditions, "algorithm_id = $3")
		queryArgs = append(queryArgs, *req.AlgorithmID)
	}

	if req.AutoBotID != nil && *req.AutoBotID > 0 {
		conditions = append(conditions, "auto_bot_id = $4")
		queryArgs = append(queryArgs, *req.AutoBotID)
	}

	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	accounts, total, err := s.withdrawAccountRepository.FindAll(ctx, pagination.Limit, pagination.Offset, whereClause, queryArgs)
	if err != nil {
		s.logger.WithError(err).Error("failed to find accounts")
		return nil, err
	}

	content := make([]*withdraw_account.WithdrawAccountListResponse, len(accounts))
	if len(accounts) != 0 {
		for i, a := range accounts {
			content[i] = a.ToListResponse()
		}
	}

	responseData := &response.SuccessWithPagination{
		Message:    "Withdraw accounts retrieved successfully",
		Content:    content,
		Page:       int64(pagination.Page),
		Limit:      int64(pagination.Limit),
		TotalPages: helper.CalculateTotalPages(total, pagination.Limit),
		TotalItems: total,
	}

	return responseData, nil
}

func (s *withdrawAccountService) FindWithdrawAccountByID(ctx context.Context, id int64) (*withdraw_account.WithdrawAccountByIdResponse, error) {

	if id == 0 {
		return nil, errors.NewValidationError("withdraw account ID cannot be zero")
	}

	account, err := s.withdrawAccountRepository.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find withdraw account")
		return nil, err
	}
	if account == nil {
		return nil, errors.NewNotFoundError("withdraw account not found")
	}

	return account.ToByIdResponse(), nil
}

func (s *withdrawAccountService) FindWithdrawAccountSettingAlgorithmByID(ctx context.Context, id int64) (*withdraw_account.WithdrawAccountSettingAlgorithmResponse, error) {

	if id == 0 {
		return nil, errors.NewValidationError("withdraw account ID cannot be zero")
	}

	account, err := s.withdrawAccountRepository.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find withdraw account algorithm settings")
		return nil, err
	}
	if account == nil {
		return nil, errors.NewNotFoundError("withdraw account algorithm settings not found")
	}

	decrypt, err := DecryptSettingAlgorithm(account)
	if err != nil {
		s.logger.WithError(err).Error("failed to decrypt withdraw account algorithm settings")
		return nil, errors.NewValidationError("failed to decrypt withdraw account algorithm settings")
	}

	return decrypt.ToSettingAlgorithmResponse(), nil
}

func (s *withdrawAccountService) UpdateWithdrawAccount(ctx context.Context, id int64, req *withdraw_account.WithdrawAccountRequest) error {

	if id == 0 {
		return errors.NewValidationError("withdraw account ID cannot be zero")
	}

	bankingExists, err := s.bankingRepo.FindIdExists(ctx, int64(req.BankingID))
	if err != nil {
		s.logger.WithError(err).Error("failed to check if banking ID exists")
		return err
	}
	if !bankingExists {
		s.logger.WithError(err).Error("banking ID does not exist")
		return errors.NewValidationError("banking ID does not exist")
	}

	accountNumberDuplicate, err := s.withdrawAccountRepository.FindByAccountNumberDuplicateAndIdNot(ctx, req.AccountNumber, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate account number excluding current ID")
		return err
	}
	if accountNumberDuplicate {
		s.logger.WithError(err).Error("account number already exists")
		return errors.NewValidationError("account number already exists for another account")
	}

	if err := s.withdrawAccountRepository.Update(ctx, id, req); err != nil {
		s.logger.WithError(err).Error("failed to update withdraw account")
		return err
	}

	return nil
}

func (s *withdrawAccountService) UpdateWithdrawAccountAutoBot(ctx context.Context, id int64, autoBotID int64) error {

	autoBotExists, err := s.autoBotRepo.FindIdExists(ctx, autoBotID)
	if err != nil {
		s.logger.WithError(err).Error("failed to check if auto bot ID exists")
		return err
	}
	if !autoBotExists {
		s.logger.WithField("auto_bot_id", autoBotID).Error("auto bot ID does not exist")
		return errors.NewValidationError("auto bot ID does not exist")
	}

	if err := s.withdrawAccountRepository.UpdateAutoBot(ctx, id, autoBotID); err != nil {
		s.logger.WithError(err).Error("failed to update withdraw account")
		return err
	}

	return nil
}

func (s *withdrawAccountService) UpdateWithdrawAccountAlgorithm(ctx context.Context, id int64, req *withdraw_account.WithdrawAccountSettingAlgorithmRequest) error {

	algorithmExists, err := s.algorithmRepo.FindIdExists(ctx, int64(req.AlgorithmID))
	if err != nil {
		s.logger.WithError(err).Error("failed to check if algorithm ID exists")
		return err
	}
	if !algorithmExists {
		s.logger.WithError(err).Error("algorithm ID does not exist")
		return errors.NewValidationError("algorithm ID does not exist")
	}

	encypt, err := EncryptSettingAlgorithm(req)
	if err != nil {
		s.logger.WithError(err).Error("failed to encrypt withdraw account algorithm settings")
		return err
	}

	if err := s.withdrawAccountRepository.UpdateAlgorithm(ctx, id, encypt); err != nil {
		s.logger.WithError(err).Error("failed to update withdraw account with algorithm ID")
		return err
	}

	return nil
}

func (s *withdrawAccountService) ActiveWithdrawAccount(ctx context.Context, id int64, status bool) error {

	if id == 0 {
		return errors.NewValidationError("withdraw account ID cannot be zero")
	}

	if err := s.withdrawAccountRepository.Active(ctx, id, status); err != nil {
		s.logger.WithError(err).Error("failed to activate withdraw account")
		return err
	}

	return nil
}

func (s *withdrawAccountService) DeleteWithdrawAccount(ctx context.Context, id int64) error {

	if id == 0 {
		return errors.NewValidationError("withdraw account ID cannot be zero")
	}
	if err := s.withdrawAccountRepository.Delete(ctx, id); err != nil {
		s.logger.WithError(err).Error("failed to delete withdraw account")
		return err
	}

	return nil
}

func EncryptSettingAlgorithm(req *withdraw_account.WithdrawAccountSettingAlgorithmRequest) (*withdraw_account.WithdrawAccountSettingAlgorithmRequest, error) {
	idfNumberEncrypt, err := helper.Encrypt(req.IdentificationNumber)
	if err != nil {
		return nil, err
	}
	laserIdEncrypt, err := helper.Encrypt(req.LaserId)
	if err != nil {
		return nil, err
	}
	pinEncrypt, err := helper.Encrypt(req.Pin)
	if err != nil {
		return nil, err
	}
	ekeyEncrypt, err := helper.Encrypt(req.EncryptionKey)
	if err != nil {
		return nil, err
	}
	dvIdEncrypt, err := helper.Encrypt(req.DeviceId)
	if err != nil {
		return nil, err
	}
	pbtEncrypt, err := helper.Encrypt(req.PushBulletToken)
	if err != nil {
		return nil, err
	}
	pbnEncrypt, err := helper.Encrypt(req.PushBulletNickname)
	if err != nil {
		return nil, err
	}

	req.IdentificationNumber = idfNumberEncrypt
	req.LaserId = laserIdEncrypt
	req.Pin = pinEncrypt
	req.EncryptionKey = ekeyEncrypt
	req.DeviceId = dvIdEncrypt
	req.PushBulletToken = pbtEncrypt
	req.PushBulletNickname = pbnEncrypt

	return req, nil
}

func DecryptSettingAlgorithm(req *withdraw_account.WithdrawAccount) (*withdraw_account.WithdrawAccount, error) {
	if req.IdentificationNumber == nil ||
		req.LaserId == nil ||
		req.Pin == nil ||
		req.EncryptionKey == nil ||
		req.DeviceId == nil ||
		req.PushBulletToken == nil ||
		req.PushBulletNickname == nil {
		return req, nil
	}

	idfNumberDecrypt, err := helper.Decrypt(*req.IdentificationNumber)
	if err != nil {
		return nil, err
	}
	laserIdDecrypt, err := helper.Decrypt(*req.LaserId)
	if err != nil {
		return nil, err
	}
	pinDecrypt, err := helper.Decrypt(*req.Pin)
	if err != nil {
		return nil, err
	}
	ekeyDecrypt, err := helper.Decrypt(*req.EncryptionKey)
	if err != nil {
		return nil, err
	}
	dvIdDecrypt, err := helper.Decrypt(*req.DeviceId)
	if err != nil {
		return nil, err
	}
	pbtDecrypt, err := helper.Decrypt(*req.PushBulletToken)
	if err != nil {
		return nil, err
	}
	pbnDecrypt, err := helper.Decrypt(*req.PushBulletNickname)
	if err != nil {
		return nil, err
	}

	req.IdentificationNumber = &idfNumberDecrypt
	req.LaserId = &laserIdDecrypt
	req.Pin = &pinDecrypt
	req.EncryptionKey = &ekeyDecrypt
	req.DeviceId = &dvIdDecrypt
	req.PushBulletToken = &pbtDecrypt
	req.PushBulletNickname = &pbnDecrypt

	return req, nil
}
