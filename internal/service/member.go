package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"blacking-api/internal/domain/login_attempt"
	"blacking-api/internal/domain/member"
	"blacking-api/internal/domain/otp"
	"blacking-api/internal/domain/system_setting"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"

	"github.com/gin-gonic/gin"
)

type MemberService interface {
	CreateMember(ctx context.Context, req member.CreateMemberRequest, adminId string, adminName string) (*member.MemberResponse, error)
	GetMemberByID(ctx context.Context, id string) (*member.MemberResponse, error)
	GetMemberByUsername(ctx context.Context, username string) (*member.MemberResponse, error)
	GetMemberByGameUsername(ctx context.Context, gameUsername string) (*member.MemberResponse, error)
	UpdateMember(ctx context.Context, id string, req member.UpdateMemberRequest, adminId string, adminName string) (*member.MemberResponse, error)
	DeleteMember(ctx context.Context, id string, adminId string, adminName string) error
	ListMembers(ctx context.Context, limit, offset int, search string) ([]*member.MemberResponse, int64, error)
	ListMembersWithFilter(ctx context.Context, limit, offset int, filter *member.MemberFilter) ([]*member.MemberResponse, int64, error)
	ValidateMemberCredentials(ctx *gin.Context, username, password string) (*member.MemberResponse, error)

	// OTP-based registration methods
	SendRegistrationOTP(ctx context.Context, req member.VerifySendOTPRequest) (*member.RegistrationOTPResponse, error)
	VerifyRegistrationOTP(ctx context.Context, req member.VerifyRegistrationOTPRequest, clientIP string) (bool, error)
	CompleteRegistration(ctx context.Context, req member.CompleteRegistrationRequest, clientIP string) (*member.MemberResponse, error)
}

type memberService struct {
	memberRepo            interfaces.MemberRepository
	systemSettingService  SystemSettingService
	loginAttemptRepo      interfaces.LoginAttemptRepository
	memberAuditLogService MemberAuditLogService
	otpService            OTPService
	referralGroupRepo     interfaces.ReferralGroupRepository
	memberGroupRepo       interfaces.MemberGroupRepository
	logger                logger.Logger
}

func NewMemberService(memberRepo interfaces.MemberRepository, loginAttemptRepo interfaces.LoginAttemptRepository, systemSettingService SystemSettingService, memberAuditLogService MemberAuditLogService, otpService OTPService, referralGroupRepo interfaces.ReferralGroupRepository, memberGroupRepo interfaces.MemberGroupRepository, logger logger.Logger) MemberService {
	return &memberService{
		memberRepo:            memberRepo,
		loginAttemptRepo:      loginAttemptRepo,
		systemSettingService:  systemSettingService,
		memberAuditLogService: memberAuditLogService,
		otpService:            otpService,
		referralGroupRepo:     referralGroupRepo,
		memberGroupRepo:       memberGroupRepo,
		logger:                logger,
	}
}

func (s *memberService) CreateMember(ctx context.Context, req member.CreateMemberRequest, adminId string, adminName string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateMember")

	adminIdInt, err := strconv.Atoi(adminId)
	if err != nil {
		return nil, errors.NewValidationError("invalid admin ID")
	}

	// Check if phone already exists
	existingMember, err := s.memberRepo.GetByPhone(ctx, req.Phone)
	if err == nil && existingMember != nil {
		log.WithField("phone", req.Phone).Error("phone already exists")
		return nil, errors.NewValidationError("phone already exists")
	}

	// Validate member_group_id
	log.WithField("member_group_id", req.MemberGroupID).Info("validating member group ID")
	_, err = s.memberGroupRepo.GetByID(ctx, req.MemberGroupID)
	if err != nil {
		log.WithError(err).WithField("member_group_id", req.MemberGroupID).Error("invalid member group ID")
		return nil, errors.NewValidationError("invalid member group ID")
	}

	// Validate referral_group_id if provided
	if req.ReferralGroupID > 0 {
		_, err := s.referralGroupRepo.GetByID(ctx, req.ReferralGroupID)
		if err != nil {
			log.WithError(err).WithField("referral_group_id", req.ReferralGroupID).Error("invalid referral group ID")
			return nil, errors.NewValidationError("invalid referral group ID")
		}
	}

	// Create new member (without systemSettingRepo dependency)
	newMember, err := member.NewMember(req, adminIdInt, s.memberRepo)
	if err != nil {
		log.WithError(err).Error("failed to create member domain object")
		return nil, err
	}

	// Save to repository (this will set the ID and temp username)
	if err := s.memberRepo.Create(ctx, newMember, "admin"); err != nil {
		log.WithError(err).Error("failed to save member to repository")
		return nil, err
	}

	// Get member prefix setting from service layer
	prefixSetting, err := s.systemSettingService.GetSystemSetting(ctx, system_setting.KeyMemberPrefix)
	if err != nil {
		// Create default if not exists
		defaultSetting := system_setting.GetDefaultMemberPrefix()
		updateReq := system_setting.UpdateSystemSettingRequest{
			Value: defaultSetting.Value,
		}
		if err := s.systemSettingService.UpdateSystemSetting(ctx, system_setting.KeyMemberPrefix, updateReq); err != nil {
			log.WithError(err).Error("failed to create default member prefix setting")
			return nil, err
		}
		// Get the setting again after creation
		prefixSetting, err = s.systemSettingService.GetSystemSetting(ctx, system_setting.KeyMemberPrefix)
		if err != nil {
			log.WithError(err).Error("failed to get member prefix setting after creation")
			return nil, err
		}
	}

	// Generate username with prefix and update immediately
	newMember.GenerateUsername(prefixSetting.Value)
	if err := s.memberRepo.Update(ctx, newMember); err != nil {
		log.WithError(err).Error("failed to update member username")
		return nil, err
	}

	// Log member creation to audit trail
	auditReq := CreateMemberAuditLog(newMember.ID, newMember.Username, adminIdInt, adminName, newMember)
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log member creation audit")
	}

	response := newMember.ToResponse()
	log.WithField("member_id", response.ID).WithField("username", response.Username).Info("member created successfully")
	return &response, nil
}

// SendRegistrationOTP sends OTP for member registration verification
func (s *memberService) SendRegistrationOTP(ctx context.Context, req member.VerifySendOTPRequest) (*member.RegistrationOTPResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "SendRegistrationOTP")

	// Check if username already exists
	existingMember, err := s.memberRepo.GetByPhone(ctx, req.Phone)
	if err == nil && existingMember != nil {
		return nil, errors.NewValidationError("username already exists")
	}

	lastOtpReq := otp.GetLastVerifyOTPRequest{
		Type:      otp.OTPTypePhone,
		Purpose:   otp.OTPPurposeMemberRegistration,
		Recipient: req.Phone,
	}

	oOtp, _ := s.otpService.GetLastVerifyOTP(ctx, lastOtpReq)
	if oOtp != nil {
		if oOtp.ExpiresAt.After(time.Now()) {
			now := time.Now()
			remainingTime := time.Until(oOtp.ExpiresAt)
			totalSeconds := int(remainingTime.Seconds())
			remainingMinutes := totalSeconds / 60
			remainingSeconds := totalSeconds % 60

			// Debug log to check timezone issues
			logger.WithField("now", now).
				WithField("expires_at", oOtp.ExpiresAt).
				WithField("remaining_seconds", totalSeconds).
				Info("OTP time debug")

			var timeMsg string
			if remainingMinutes > 0 {
				timeMsg = fmt.Sprintf("%d นา  %d  นา", remainingMinutes, remainingSeconds)
			} else {
				timeMsg = fmt.Sprintf("%d  นา", remainingSeconds)
			}

			return nil, errors.NewValidationError(fmt.Sprintf("OTP ref=%s already sent, please wait %s for it to expire", oOtp.Reference, timeMsg))
		}
	}

	// Send OTP
	otpReq := otp.SendOTPRequest{
		Type:      otp.OTPTypePhone,
		Purpose:   otp.OTPPurposeMemberRegistration,
		Recipient: req.Phone,
	}

	otpResponse, err := s.otpService.SendOTP(ctx, otpReq)
	if err != nil {
		log.WithError(err).Error("failed to send registration OTP")
		return nil, err
	}

	message := "OTP sent successfully to your phone number"
	if otpResponse.Message != "" {
		message = otpResponse.Message
	}

	response := &member.RegistrationOTPResponse{
		Message:   message,
		Phone:     maskPhone(req.Phone),
		Reference: otpResponse.Reference,
		ExpiresIn: 300, // 5 minutes
	}

	log.WithField("phone", req.Phone).Info("registration OTP sent successfully")
	return response, nil
}

// VerifyRegistrationOTP verifies OTP and creates member account
func (s *memberService) VerifyRegistrationOTP(ctx context.Context, req member.VerifyRegistrationOTPRequest, clientIP string) (bool, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "VerifyRegistrationOTP")

	// Verify OTP
	verifyReq := otp.VerifyOTPRequest{
		Type:      otp.OTPTypePhone,
		Purpose:   otp.OTPPurposeMemberRegistration,
		Recipient: req.Phone,
		Code:      req.Code,
	}

	otpRecord, err := s.otpService.VerifyOTP(ctx, verifyReq)
	if err != nil {
		log.WithError(err).Error("OTP verification failed")
		return false, err
	}

	// Verify reference matches
	if otpRecord.Reference != req.Reference {
		return false, errors.NewValidationError("invalid reference code")
	}

	return true, nil
}

// CompleteRegistration verifies OTP and creates member account with full registration data
func (s *memberService) CompleteRegistration(ctx context.Context, req member.CompleteRegistrationRequest, clientIP string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CompleteRegistration")

	// Check if username already exists (validate the username field)
	existingMember, err := s.memberRepo.GetByUsername(ctx, req.Username)
	if err == nil && existingMember != nil {
		return nil, errors.NewValidationError("username already exists")
	}

	// Check if phone already exists
	existingMemberByPhone, err := s.memberRepo.GetByPhone(ctx, req.Phone)
	if err == nil && existingMemberByPhone != nil {
		return nil, errors.NewValidationError("phone number already registered")
	}

	lastOtpReq := otp.GetInUsedOTPRequest{
		Type:      otp.OTPTypePhone,
		Purpose:   otp.OTPPurposeMemberRegistration,
		Recipient: req.Phone,
	}

	otpRecord, err := s.otpService.GetInUsedOTP(ctx, lastOtpReq)
	if err != nil {
		log.WithError(err).Error("failed to get last OTP record")
		return nil, err
	}

	// Verify reference matches
	if !otpRecord.IsUsed {
		return nil, errors.NewValidationError("please vertify OTP first")
	}

	// Create new member from complete registration
	newMember, err := member.NewMemberFromCompleteRegistration(req, clientIP, s.memberRepo)
	if err != nil {
		log.WithError(err).Error("failed to create member from complete registration")
		return nil, err
	}

	// Save to repository
	if err := s.memberRepo.Create(ctx, newMember, clientIP); err != nil {
		log.WithError(err).Error("failed to save registered member to repository")
		return nil, err
	}

	// Log member registration to audit trail
	auditReq := CreateMemberAuditLog(newMember.ID, newMember.Username, 0, "Complete Registration", newMember) // Use 0 for system
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log member registration audit")
	}

	response := newMember.ToResponse()
	log.WithField("member_id", response.ID).WithField("username", response.Username).Info("member registration completed successfully")
	return &response, nil
}

// maskPhone masks phone number for display
func maskPhone(phone string) string {
	if len(phone) >= 4 {
		return phone[:3] + "****" + phone[len(phone)-2:]
	}
	return "***"
}

func (s *memberService) GetMemberByID(ctx context.Context, id string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberByID").WithField("member_id", id)

	m, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get member by ID")
		return nil, err
	}

	response := m.ToResponse()
	return &response, nil
}

func (s *memberService) GetMemberByUsername(ctx context.Context, username string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberByUsername").WithField("username", username)

	m, err := s.memberRepo.GetByUsername(ctx, username)
	if err != nil {
		log.WithError(err).Error("failed to get member by username")
		return nil, err
	}

	response := m.ToResponse()
	return &response, nil
}

func (s *memberService) GetMemberByGameUsername(ctx context.Context, gameUsername string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetMemberByGameUsername").WithField("game_username", gameUsername)

	m, err := s.memberRepo.GetByGameUsername(ctx, gameUsername)
	if err != nil {
		log.WithError(err).Error("failed to get member by game username")
		return nil, err
	}

	response := m.ToResponse()
	return &response, nil
}

func (s *memberService) UpdateMember(ctx context.Context, id string, req member.UpdateMemberRequest, adminId string, adminName string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateMember").WithField("member_id", id)

	// Get existing member
	existingMember, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get existing member")
		return nil, err
	}

	// Keep old values for audit log
	oldValues := *existingMember

	// Update member
	if err := existingMember.Update(req); err != nil {
		log.WithError(err).Error("failed to update member domain object")
		return nil, err
	}

	// Save to repository
	if err := s.memberRepo.Update(ctx, existingMember); err != nil {
		log.WithError(err).Error("failed to save updated member to repository")
		return nil, err
	}

	// Log member update to audit trail
	adminIdInt, err := strconv.Atoi(adminId)
	if err != nil {
		log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
		adminIdInt = 0 // Use 0 as fallback
	}
	auditReq := UpdateMemberAuditLog(existingMember.ID, existingMember.Username, adminIdInt, adminId, oldValues, *existingMember)
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log member update audit")
	}

	response := existingMember.ToResponse()
	log.WithField("member_id", response.ID).Info("member updated successfully")
	return &response, nil
}

func (s *memberService) DeleteMember(ctx context.Context, id string, adminId string, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteMember").WithField("member_id", id)

	// Check if member exists and get member data for audit log
	existingMember, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get member for deletion")
		return err
	}

	// Soft delete member
	if err := s.memberRepo.Delete(ctx, id); err != nil {
		log.WithError(err).Error("failed to delete member")
		return err
	}

	// Log member deletion to audit trail
	adminIdInt, err := strconv.Atoi(adminId)
	if err != nil {
		log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
		adminIdInt = 0 // Use 0 as fallback
	}
	auditReq := DeleteMemberAuditLog(existingMember.ID, existingMember.Username, adminIdInt, adminName, *existingMember)
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log member deletion audit")
	}

	log.WithField("member_id", id).Info("member deleted successfully")
	return nil
}

func (s *memberService) ListMembers(ctx context.Context, limit, offset int, search string) ([]*member.MemberResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListMembers")

	// Get members
	members, err := s.memberRepo.List(ctx, limit, offset, search)
	if err != nil {
		log.WithError(err).Error("failed to list members")
		return nil, 0, err
	}

	// Get total count
	total, err := s.memberRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count members")
		return nil, 0, err
	}

	// Convert to response
	responses := make([]*member.MemberResponse, len(members))
	for i, m := range members {
		response := m.ToResponse()
		responses[i] = &response
	}

	log.WithField("count", len(responses)).WithField("total", total).Info("members listed successfully")
	return responses, total, nil
}

func (s *memberService) ListMembersWithFilter(ctx context.Context, limit, offset int, filter *member.MemberFilter) ([]*member.MemberResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListMembersWithFilter")

	// Get members with filter
	members, err := s.memberRepo.ListWithFilter(ctx, limit, offset, filter)
	if err != nil {
		log.WithError(err).Error("failed to list members with filter")
		return nil, 0, err
	}

	// Get total count with filter
	total, err := s.memberRepo.CountWithFilter(ctx, filter)
	if err != nil {
		log.WithError(err).Error("failed to count members with filter")
		return nil, 0, err
	}

	// Convert to response
	responses := make([]*member.MemberResponse, len(members))
	for i, m := range members {
		response := m.ToResponse()
		responses[i] = &response
	}

	log.WithField("count", len(responses)).WithField("total", total).Info("members with filter listed successfully")
	return responses, total, nil
}

func (s *memberService) ValidateMemberCredentials(ctx *gin.Context, username, password string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ValidateMemberCredentials").WithField("username", username)

	if username == "" || password == "" {
		return nil, errors.NewValidationError("username and password are required")
	}

	// Get login attempt limit from system settings
	loginLimit, err := s.systemSettingService.GetLoginAttemptLimit(ctx.Request.Context())
	if err != nil {
		log.WithError(err).Warn("failed to get login attempt limit, continuing without limit")
		loginLimit = &system_setting.LoginAttemptLimitResponse{MaxAttempts: 0} // No limit
	}

	u, err := s.memberRepo.GetByUsername(ctx.Request.Context(), username)
	if err != nil {
		// Record failed login attempt
		s.recordLoginAttempt(ctx, username, false)

		if errors.GetAppError(err) != nil && errors.GetAppError(err).Type == errors.NotFoundError {
			return nil, errors.NewUnauthorizedError("invalid credentials")
		}
		log.WithError(err).Error("failed to get user for credential validation")
		return nil, err
	}

	// Check if user is locked due to too many failed attempts
	if loginLimit.MaxAttempts > 0 && !u.IsEnable {
		summary, err := s.loginAttemptRepo.GetAttemptsSummary(ctx.Request.Context(), username, loginLimit.MaxAttempts)
		if err != nil {
			log.WithError(err).Warn("failed to get login attempts summary")
		} else if summary.IsLocked {
			log.WithField("failed_attempts", summary.FailedAttempts).Warn("user is locked due to too many failed login attempts")
			return nil, errors.NewUnauthorizedError("account is temporarily locked due to too many failed login attempts")
		}
	}

	if !u.CheckPassword(password) {
		// Record failed login attempt
		s.recordLoginAttempt(ctx, username, false)

		// Check if this failed attempt should lock the account
		if loginLimit.MaxAttempts > 0 {
			failedCount, err := s.loginAttemptRepo.GetFailedAttemptsCount(ctx.Request.Context(), username, time.Now().Add(-24*time.Hour))
			if err == nil && failedCount >= loginLimit.MaxAttempts {
				// Disable user account by setting is_enable = false
				falseValue := false
				u.IsEnable = falseValue
				u.UpdatedAt = time.Now()
				if updateErr := s.memberRepo.Update(ctx.Request.Context(), u); updateErr != nil {
					log.WithError(updateErr).Error("failed to disable user account after max login attempts")
				} else {
					// Log audit trail for account lockout
					s.logAccountLockout(ctx.Request.Context(), strconv.Itoa(u.ID), u.Username, failedCount)
					log.WithField("failed_attempts", failedCount).Warn("user account disabled due to max login attempts exceeded")
				}
			}
		}

		return nil, errors.NewUnauthorizedError("invalid credentials")
	}

	if !u.IsActive() {
		// Record failed login attempt (account not active)
		s.recordLoginAttempt(ctx, username, false)
		return nil, errors.NewUnauthorizedError("user account is not active")
	}

	// Record successful login attempt
	s.recordLoginAttempt(ctx, username, true)

	s.loginAttemptRepo.ClearFailedAttempts(ctx.Request.Context(), username)

	response := u.ToResponse()
	log.Info("user credentials validated successfully")
	return &response, nil
}

// recordLoginAttempt records a login attempt (success or failure)
func (s *memberService) recordLoginAttempt(ctx *gin.Context, username string, success bool) {
	// Extract IP address and user agent from gin context
	ipAddress := ctx.ClientIP()
	userAgent := ctx.Request.UserAgent()

	// For admin users, set isAdmin=true, isMember=false
	// This is for admin login attempts
	attempt := login_attempt.NewLoginAttempt(username, ipAddress, userAgent, success, false, true)
	if err := s.loginAttemptRepo.Create(ctx.Request.Context(), attempt); err != nil {
		s.logger.WithError(err).WithField("username", username).Error("failed to record login attempt")
	}
}

// logAccountLockout logs an audit trail when account is locked due to max login attempts
func (s *memberService) logAccountLockout(ctx context.Context, memberID, username string, failedAttempts int) {
	if s.memberAuditLogService != nil {
		memberIDInt, err := strconv.Atoi(memberID)
		if err != nil {
			s.logger.WithError(err).Warn("failed to convert member ID to integer for audit log")
			memberIDInt = 0 // Use 0 as fallback
		}
		changedBy := 0 // System action (use 0 for system)
		changedByName := "System"
		oldValues := map[string]interface{}{
			"is_enable": true,
		}
		newValues := map[string]interface{}{
			"is_enable":       false,
			"lockout_reason":  "max_login_attempts_exceeded",
			"failed_attempts": failedAttempts,
		}

		auditReq := UpdateMemberAuditLog(memberIDInt, username, changedBy, changedByName, oldValues, newValues)
		if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
			s.logger.WithError(err).Warn("failed to log member account lockout audit")
		}
	}
}
