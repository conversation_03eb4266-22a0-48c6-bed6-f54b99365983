package service

import (
	"context"

	"blacking-api/internal/domain/referral_group"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
)

// ReferralGroupService defines the interface for referral group service operations
type ReferralGroupService interface {
	CreateReferralGroup(ctx context.Context, req referral_group.CreateReferralGroupRequest) (*referral_group.ReferralGroupResponse, error)
	GetReferralGroupByID(ctx context.Context, id int) (*referral_group.ReferralGroupResponse, error)
	GetDefaultReferralGroup(ctx context.Context) (*referral_group.ReferralGroupResponse, error)
	UpdateReferralGroup(ctx context.Context, id int, req referral_group.UpdateReferralGroupRequest) (*referral_group.ReferralGroupResponse, error)
	DeleteReferralGroup(ctx context.Context, id int) error
	ListReferralGroups(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*referral_group.ReferralGroupResponse, int64, error)
	ListActiveReferralGroups(ctx context.Context) ([]*referral_group.ReferralGroupResponse, error)
	ListReferralGroupsForDropdown(ctx context.Context) ([]string, error)
	SetDefaultReferralGroup(ctx context.Context, id int) error
}

type referralGroupService struct {
	referralGroupRepo interfaces.ReferralGroupRepository
	logger            logger.Logger
}

// NewReferralGroupService creates a new referral group service
func NewReferralGroupService(referralGroupRepo interfaces.ReferralGroupRepository, logger logger.Logger) ReferralGroupService {
	return &referralGroupService{
		referralGroupRepo: referralGroupRepo,
		logger:            logger,
	}
}

// CreateReferralGroup creates a new referral group
func (s *referralGroupService) CreateReferralGroup(ctx context.Context, req referral_group.CreateReferralGroupRequest) (*referral_group.ReferralGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateReferralGroup")

	// Note: Code field removed, no need to check for duplicate codes

	// Create new referral group
	newReferralGroup, err := referral_group.NewReferralGroup(req)
	if err != nil {
		log.WithError(err).Error("failed to create referral group domain object")
		return nil, err
	}

	// Save to repository
	if err := s.referralGroupRepo.Create(ctx, newReferralGroup); err != nil {
		log.WithError(err).Error("failed to save referral group to repository")
		return nil, err
	}

	// Get the created referral group
	createdReferralGroup, err := s.referralGroupRepo.GetByID(ctx, newReferralGroup.ID)
	if err != nil {
		log.WithError(err).Error("failed to get created referral group")
		return nil, err
	}

	response := createdReferralGroup.ToResponse()
	log.WithField("referral_group_id", response.ID).WithField("name", req.Name).Info("referral group created successfully")
	return &response, nil
}

// GetReferralGroupByID retrieves referral group by ID
func (s *referralGroupService) GetReferralGroupByID(ctx context.Context, id int) (*referral_group.ReferralGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetReferralGroupByID")

	rg, err := s.referralGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("referral_group_id", id).Error("failed to get referral group by ID")
		return nil, err
	}

	response := rg.ToResponse()
	return &response, nil
}

// GetDefaultReferralGroup retrieves the default referral group
func (s *referralGroupService) GetDefaultReferralGroup(ctx context.Context) (*referral_group.ReferralGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetDefaultReferralGroup")

	rg, err := s.referralGroupRepo.GetDefault(ctx)
	if err != nil {
		log.WithError(err).Error("failed to get default referral group")
		return nil, err
	}

	response := rg.ToResponse()
	return &response, nil
}

// UpdateReferralGroup updates an existing referral group
func (s *referralGroupService) UpdateReferralGroup(ctx context.Context, id int, req referral_group.UpdateReferralGroupRequest) (*referral_group.ReferralGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateReferralGroup")

	// Get existing referral group
	rg, err := s.referralGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("referral_group_id", id).Error("failed to get referral group for update")
		return nil, err
	}

	// Note: Code field removed, no need to check for duplicate codes

	// Update referral group
	if err := rg.Update(req); err != nil {
		log.WithError(err).Error("failed to update referral group domain object")
		return nil, err
	}

	// Save to repository
	if err := s.referralGroupRepo.Update(ctx, rg); err != nil {
		log.WithError(err).Error("failed to save updated referral group to repository")
		return nil, err
	}

	// Get the updated referral group
	updatedReferralGroup, err := s.referralGroupRepo.GetByID(ctx, rg.ID)
	if err != nil {
		log.WithError(err).Error("failed to get updated referral group")
		return nil, err
	}

	response := updatedReferralGroup.ToResponse()
	log.WithField("referral_group_id", response.ID).Info("referral group updated successfully")
	return &response, nil
}

// DeleteReferralGroup soft deletes a referral group
func (s *referralGroupService) DeleteReferralGroup(ctx context.Context, id int) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteReferralGroup")

	if err := s.referralGroupRepo.Delete(ctx, id); err != nil {
		log.WithError(err).WithField("referral_group_id", id).Error("failed to delete referral group")
		return err
	}

	log.WithField("referral_group_id", id).Info("referral group deleted successfully")
	return nil
}

// ListReferralGroups retrieves referral groups with pagination, search, and sorting
func (s *referralGroupService) ListReferralGroups(ctx context.Context, limit, offset int, search, sortBy, sortOrder string) ([]*referral_group.ReferralGroupResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListReferralGroups")

	// Get total count
	total, err := s.referralGroupRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count referral groups")
		return nil, 0, err
	}

	// Get referral groups
	referralGroups, err := s.referralGroupRepo.List(ctx, limit, offset, search, sortBy, sortOrder)
	if err != nil {
		log.WithError(err).Error("failed to list referral groups")
		return nil, 0, err
	}

	// Convert to response format
	var responses []*referral_group.ReferralGroupResponse
	for _, rg := range referralGroups {
		response := rg.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("total", total).WithField("returned", len(responses)).Info("referral groups listed successfully")
	return responses, total, nil
}

// ListActiveReferralGroups retrieves only active referral groups
func (s *referralGroupService) ListActiveReferralGroups(ctx context.Context) ([]*referral_group.ReferralGroupResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListActiveReferralGroups")

	referralGroups, err := s.referralGroupRepo.ListActive(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list active referral groups")
		return nil, err
	}

	// Convert to response format
	var responses []*referral_group.ReferralGroupResponse
	for _, rg := range referralGroups {
		response := rg.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("active referral groups listed successfully")
	return responses, nil
}

// ListReferralGroupsForDropdown retrieves unique referral group names for dropdown filter
func (s *referralGroupService) ListReferralGroupsForDropdown(ctx context.Context) ([]string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListReferralGroupsForDropdown")

	names, err := s.referralGroupRepo.ListForDropdown(ctx)
	if err != nil {
		log.WithError(err).Error("failed to list referral groups for dropdown")
		return nil, err
	}

	log.WithField("count", len(names)).Info("referral groups for dropdown listed successfully")
	return names, nil
}

// SetDefaultReferralGroup sets a referral group as default
func (s *referralGroupService) SetDefaultReferralGroup(ctx context.Context, id int) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SetDefaultReferralGroup")

	// Verify the referral group exists
	_, err := s.referralGroupRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).WithField("referral_group_id", id).Error("referral group not found")
		return err
	}

	if err := s.referralGroupRepo.SetDefault(ctx, id); err != nil {
		log.WithError(err).WithField("referral_group_id", id).Error("failed to set default referral group")
		return err
	}

	log.WithField("referral_group_id", id).Info("referral group set as default successfully")
	return nil
}
