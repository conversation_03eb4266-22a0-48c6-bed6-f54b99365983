package service

import (
	"log"
	"net/http"
)

type ErrorResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func (e ErrorResponse) Error() string {
	return e.Message
}

func badRequest(msg string) error {
	return ErrorResponse{
		Code:    http.StatusBadRequest,
		Message: msg,
	}
}

func errorWithData(msg string, data interface{}) error {
	return ErrorResponse{
		Code:    http.StatusBadRequest,
		Message: msg,
		Data:    data,
	}
}

func notFound(msg string) error {
	return ErrorResponse{
		Code:    http.StatusNotFound,
		Message: msg,
	}
}

func unauthorized(msg string) error {
	return ErrorResponse{
		Code:    http.StatusUnauthorized,
		Message: msg,
	}
}

func internalServerError(msg error) error {

	log.Println(msg)

	// if os.Getenv("GIN_MODE") != "release" {
	return ErrorResponse{
		Code:    http.StatusInternalServerError,
		Message: msg.Error(),
	}
	// }

	// return ErrorResponse{
	// 	Code:    http.StatusInternalServerError,
	// 	Message: "เกิดข้อผิดพลาดบางอย่าง กรุณาลองใหม่อีกครั้ง",
	// }
}
