package service

import (
	"blacking-api/internal/domain/user_transaction_direction"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
)

type UserTransactionDirectionService interface {
	ListUserTransactionDirections(ctx context.Context) ([]*user_transaction_direction.UserTransactionDirectionResponse, error)
}

type userTransactionDirectionService struct {
	userTransactionDirectionRepo interfaces.UserTransactionDirectionRepository
	logger                       logger.Logger
}

func NewUserTransactionDirectionService(userTransactionDirectionRepo interfaces.UserTransactionDirectionRepository, logger logger.Logger) UserTransactionDirectionService {
	return &userTransactionDirectionService{
		userTransactionDirectionRepo: userTransactionDirectionRepo,
		logger:                       logger,
	}
}

func (s *userTransactionDirectionService) ListUserTransactionDirections(ctx context.Context) ([]*user_transaction_direction.UserTransactionDirectionResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListUserTransactionDirections")

	transactionDirections, err := s.userTransactionDirectionRepo.List(ctx)
	if err != nil {
		log.WithError(err).Error("failed to retrieve user transaction directions from repository")
		return nil, err
	}

	// Convert to response format
	var responses []*user_transaction_direction.UserTransactionDirectionResponse
	for _, transactionDirection := range transactionDirections {
		response := transactionDirection.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("user transaction directions retrieved successfully")
	return responses, nil
}
