package service

import (
	"blacking-api/internal/domain/language"
	"blacking-api/pkg/logger"
	"context"
)

// LanguageService defines the interface for language service operations
type LanguageService interface {
	// GetSupportedLanguages gets all supported languages
	GetSupportedLanguages(ctx context.Context) (*language.LanguageResponse, error)
	
	// GetActiveLanguages gets only active languages
	GetActiveLanguages(ctx context.Context) (*language.LanguageResponse, error)
	
	// GetLanguageByCode gets a language by its code
	GetLanguageByCode(ctx context.Context, code string) (*language.Language, error)
	
	// ValidateLanguageCode validates if a language code is supported
	ValidateLanguageCode(ctx context.Context, code string) bool
}

type languageService struct {
	logger logger.Logger
}

// NewLanguageService creates a new language service
func NewLanguageService(logger logger.Logger) LanguageService {
	return &languageService{
		logger: logger,
	}
}

// GetSupportedLanguages gets all supported languages
func (s *languageService) GetSupportedLanguages(ctx context.Context) (*language.LanguageResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetSupportedLanguages")

	languages := language.GetSupportedLanguages()
	
	response := &language.LanguageResponse{
		Languages: languages,
		Total:     len(languages),
	}

	log.WithField("total", len(languages)).Info("supported languages retrieved successfully")
	return response, nil
}

// GetActiveLanguages gets only active languages
func (s *languageService) GetActiveLanguages(ctx context.Context) (*language.LanguageResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetActiveLanguages")

	languages := language.GetActiveLanguages()
	
	response := &language.LanguageResponse{
		Languages: languages,
		Total:     len(languages),
	}

	log.WithField("total", len(languages)).Info("active languages retrieved successfully")
	return response, nil
}

// GetLanguageByCode gets a language by its code
func (s *languageService) GetLanguageByCode(ctx context.Context, code string) (*language.Language, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetLanguageByCode").WithField("code", code)

	lang := language.GetLanguageByCode(code)
	if lang == nil {
		log.Error("language not found")
		return nil, nil
	}

	log.Info("language retrieved successfully")
	return lang, nil
}

// ValidateLanguageCode validates if a language code is supported
func (s *languageService) ValidateLanguageCode(ctx context.Context, code string) bool {
	log := s.logger.WithContext(ctx).WithField("operation", "ValidateLanguageCode").WithField("code", code)

	isValid := language.ValidateLanguageCode(code)
	
	log.WithField("is_valid", isValid).Info("language code validation completed")
	return isValid
}
