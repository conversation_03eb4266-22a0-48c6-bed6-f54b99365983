package service

import (
	"blacking-api/internal/domain/permission"
	userrole "blacking-api/internal/domain/user_role"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
)

type UserRoleService interface {
	CreateUserRole(ctx context.Context, req userrole.CreateUserRoleRequest) (*userrole.UserRoleResponse, error)
	GetUserRoleByID(ctx context.Context, id string) (*userrole.UserRoleResponse, error)
	UpdateUserRole(ctx context.Context, id string, req userrole.UpdateUserRoleRequest) (*userrole.UserRoleResponse, error)
	DeleteUserRole(ctx context.Context, id string) error
	ListUserRoles(ctx context.Context, limit, offset int, search string) ([]*userrole.UserRoleResponse, error)
	ListUserRolesForDropdown(ctx context.Context) ([]*userrole.UserRoleDropdownResponse, error)
	GetUserRolesCount(ctx context.Context, search string) (int64, error)
	ActivateUserRole(ctx context.Context, id string) error
	DeactivateUserRole(ctx context.Context, id string) error
	SuspendUserRole(ctx context.Context, id string) error
	BulkToggleLockIP(ctx context.Context, req userrole.BulkToggleLockIPRequest) error
	BulkToggle2FA(ctx context.Context, req userrole.BulkToggle2FARequest) error
	BulkListLockIP(ctx context.Context) ([]*userrole.BulkLockIPResponse, error)
	BulkList2FA(ctx context.Context) ([]*userrole.Bulk2FAResponse, error)
	ReorderUserRoles(ctx context.Context, req userrole.ReorderRequest) error
	CloneUserRole(ctx context.Context, id string, req userrole.CreateUserRoleRequest) (*userrole.UserRoleResponse, error)
}

type userRoleService struct {
	userRoleRepo           interfaces.UserRoleRepository
	userRolePermissionRepo interfaces.UserRolePermissionRepository
	logger                 logger.Logger
}

func NewUserRoleService(userRoleRepo interfaces.UserRoleRepository, userRolePermissionRepo interfaces.UserRolePermissionRepository, logger logger.Logger) UserRoleService {
	return &userRoleService{
		userRoleRepo:           userRoleRepo,
		userRolePermissionRepo: userRolePermissionRepo,
		logger:                 logger,
	}
}

func (s *userRoleService) CreateUserRole(ctx context.Context, req userrole.CreateUserRoleRequest) (*userrole.UserRoleResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreateUserRole")

	// Create new user
	newUserRole, err := userrole.NewUserRole(req)
	if err != nil {
		log.WithError(err).Error("failed to create user role domain object")
		return nil, err
	}

	// Save to repository
	if err := s.userRoleRepo.Create(ctx, newUserRole); err != nil {
		log.WithError(err).Error("failed to save user role to repository")
		return nil, err
	}

	response := newUserRole.ToResponse()
	log.WithField("user_id", response.ID).Info("user role created successfully")
	return &response, nil
}

func (s *userRoleService) GetUserRoleByID(ctx context.Context, id string) (*userrole.UserRoleResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUserRoleByID").WithField("user_id", id)

	if id == "" {
		return nil, errors.NewValidationError("user role ID is required")
	}

	u, err := s.userRoleRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user role by ID")
		return nil, err
	}

	response := u.ToResponse()
	return &response, nil
}

func (s *userRoleService) UpdateUserRole(ctx context.Context, id string, req userrole.UpdateUserRoleRequest) (*userrole.UserRoleResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdateUserRole").WithField("user_id", id)

	if id == "" {
		return nil, errors.NewValidationError("user role ID is required")
	}

	// Get existing user
	u, err := s.userRoleRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user role for update")
		return nil, err
	}

	// Update user
	if err := u.Update(req); err != nil {
		log.WithError(err).Error("failed to update user role domain object")
		return nil, err
	}

	// Save to repository
	if err := s.userRoleRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save updated user role")
		return nil, err
	}

	response := u.ToResponse()
	log.Info("user role updated successfully")
	return &response, nil
}

func (s *userRoleService) DeleteUserRole(ctx context.Context, id string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteUserRole").WithField("user_id", id)

	if id == "" {
		return errors.NewValidationError("user role ID is required")
	}

	if err := s.userRoleRepo.Delete(ctx, id); err != nil {
		log.WithError(err).Error("failed to delete user role")
		return err
	}

	log.Info("user deleted successfully")
	return nil
}

func (s *userRoleService) ListUserRoles(ctx context.Context, limit, offset int, search string) ([]*userrole.UserRoleResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListUserRoles")

	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	users, err := s.userRoleRepo.List(ctx, limit, offset, search)
	if err != nil {
		log.WithError(err).Error("failed to list users role")
		return nil, err
	}

	responses := make([]*userrole.UserRoleResponse, len(users))
	for i, u := range users {
		response := u.ToResponse()
		responses[i] = &response
	}

	return responses, nil
}

func (s *userRoleService) GetUserRolesCount(ctx context.Context, search string) (int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetUserRolesCount")

	count, err := s.userRoleRepo.Count(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to get users role count")
		return 0, err
	}

	return count, nil
}

// ListUserRolesForDropdown retrieves user roles for dropdown filter
func (s *userRoleService) ListUserRolesForDropdown(ctx context.Context) ([]*userrole.UserRoleDropdownResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListUserRolesForDropdown")

	// Use same method as regular list to ensure consistency
	userRoles, err := s.ListUserRoles(ctx, 0, 0, "")
	if err != nil {
		log.WithError(err).Error("failed to list user roles for dropdown")
		return nil, err
	}

	log.WithField("total_user_roles", len(userRoles)).Info("fetched user roles from ListUserRoles")

	var responses []*userrole.UserRoleDropdownResponse
	for _, ur := range userRoles {
		// Convert UserRoleResponse to DropdownResponse
		if ur.Name != nil && *ur.Name != "" {
			responses = append(responses, &userrole.UserRoleDropdownResponse{
				ID:   ur.ID,
				Name: *ur.Name,
			})
		}
	}

	log.WithField("count", len(responses)).Info("user roles for dropdown listed successfully")
	return responses, nil
}

func (s *userRoleService) BulkToggleLockIP(ctx context.Context, req userrole.BulkToggleLockIPRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "BulkToggleLockIP")

	// Validate request
	if len(req.Toggles) == 0 {
		return errors.NewValidationError("toggles cannot be empty")
	}

	if len(req.Toggles) > 100 {
		return errors.NewValidationError("cannot update more than 100 user roles at once")
	}

	// Call repository
	err := s.userRoleRepo.BulkToggleLockIP(ctx, req.Toggles)
	if err != nil {
		log.WithError(err).Error("failed to bulk toggle lock IP")
		return err
	}

	log.WithFields(map[string]interface{}{
		"toggles": req.Toggles,
	}).Info("bulk toggle lock IP completed successfully")

	return nil
}

func (s *userRoleService) BulkToggle2FA(ctx context.Context, req userrole.BulkToggle2FARequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "BulkToggle2FA")

	// Validate request
	if len(req.Toggles) == 0 {
		return errors.NewValidationError("toggles cannot be empty")
	}

	if len(req.Toggles) > 100 {
		return errors.NewValidationError("cannot update more than 100 user roles at once")
	}

	// Call repository
	err := s.userRoleRepo.BulkToggle2FA(ctx, req.Toggles)
	if err != nil {
		log.WithError(err).Error("failed to bulk toggle 2FA")
		return err
	}

	log.WithFields(map[string]interface{}{
		"toggles": req.Toggles,
	}).Info("bulk toggle 2FA completed successfully")

	return nil
}

func (s *userRoleService) ActivateUserRole(ctx context.Context, id string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ActivateUserRole").WithField("user_id", id)

	u, err := s.userRoleRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user role for activation")
		return err
	}

	u.Activate()

	if err := s.userRoleRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save activated user role")
		return err
	}

	log.Info("user role activated successfully")
	return nil
}

func (s *userRoleService) DeactivateUserRole(ctx context.Context, id string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeactivateUserRole").WithField("user_id", id)

	u, err := s.userRoleRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user role for deactivation")
		return err
	}

	u.Deactivate()

	if err := s.userRoleRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save deactivated user")
		return err
	}

	log.Info("user deactivated successfully")
	return nil
}

func (s *userRoleService) SuspendUserRole(ctx context.Context, id string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SuspendUserRole").WithField("user_id", id)

	u, err := s.userRoleRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user role for suspension")
		return err
	}

	u.Suspend()

	if err := s.userRoleRepo.Update(ctx, u); err != nil {
		log.WithError(err).Error("failed to save suspended user role")
		return err
	}

	log.Info("user role suspended successfully")
	return nil
}

func (s *userRoleService) BulkListLockIP(ctx context.Context) ([]*userrole.BulkLockIPResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "BulkListLockIP")

	userRoles, err := s.userRoleRepo.BulkListLockIP(ctx)
	if err != nil {
		log.WithError(err).Error("failed to bulk list lock IP")
		return nil, err
	}

	responses := make([]*userrole.BulkLockIPResponse, len(userRoles))
	for i, u := range userRoles {
		response := u.ToBulkLockIPResponse()
		responses[i] = &response
	}

	log.WithField("count", len(responses)).Info("bulk list lock IP completed successfully")
	return responses, nil
}

func (s *userRoleService) BulkList2FA(ctx context.Context) ([]*userrole.Bulk2FAResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "BulkList2FA")

	userRoles, err := s.userRoleRepo.BulkList2FA(ctx)
	if err != nil {
		log.WithError(err).Error("failed to bulk list 2FA")
		return nil, err
	}

	responses := make([]*userrole.Bulk2FAResponse, len(userRoles))
	for i, u := range userRoles {
		response := u.ToBulk2FAResponse()
		responses[i] = &response
	}

	log.WithField("count", len(responses)).Info("bulk list 2FA completed successfully")
	return responses, nil
}

func (s *userRoleService) ReorderUserRoles(ctx context.Context, req userrole.ReorderRequest) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ReorderUserRoles")

	// Validate request
	if len(req.UserRoleIDs) == 0 {
		return errors.NewValidationError("user_role_ids cannot be empty")
	}

	if len(req.UserRoleIDs) > 100 {
		return errors.NewValidationError("cannot reorder more than 100 user roles at once")
	}

	// Call repository
	err := s.userRoleRepo.Reorder(ctx, req.UserRoleIDs)
	if err != nil {
		log.WithError(err).Error("failed to reorder user roles")
		return err
	}

	log.WithFields(map[string]interface{}{
		"user_role_ids": req.UserRoleIDs,
		"count":         len(req.UserRoleIDs),
	}).Info("user roles reordered successfully")

	return nil
}

func (s *userRoleService) CloneUserRole(ctx context.Context, id string, req userrole.CreateUserRoleRequest) (*userrole.UserRoleResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CloneUserRole").WithField("user_role_id", id)

	u, err := s.userRoleRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get user role for clone")
		return nil, err
	}

	permissions, err := s.userRolePermissionRepo.ListByUserRoleID(ctx, u.ID)
	if err != nil {
		log.WithError(err).Error("failt to get permission source")
		return nil, err
	}

	req.Is2FA = &u.Is2FA
	req.IsLockIP = &u.IsLockIP

	newUserRole, err := userrole.NewUserRole(req)
	if err != nil {
		log.WithError(err).Error("failed to create user role domain object")
		return nil, err
	}

	if err := s.userRoleRepo.Create(ctx, newUserRole); err != nil {
		log.WithError(err).Error("failed to create user role domain object")
		return nil, err
	}

	// Convert UserRolePermission to UserRolePermissionUpdate for BulkUpsert
	permissionUpdates := make([]permission.UserRolePermissionUpdate, len(permissions))
	for i, perm := range permissions {
		permissionUpdates[i] = permission.UserRolePermissionUpdate{
			PermissionKey: perm.PermissionKey,
			CanCreate:     perm.CanCreate,
			CanView:       perm.CanView,
			CanEdit:       perm.CanEdit,
			CanDelete:     perm.CanDelete,
		}
	}

	s.userRolePermissionRepo.BulkUpsert(ctx, newUserRole.ID, permissionUpdates)

	userRoleResponse := newUserRole.ToResponse()
	return &userRoleResponse, nil
}
