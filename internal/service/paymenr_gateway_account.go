package service

import (
	"blacking-api/internal/domain/payment_gateway_account"
	"blacking-api/internal/domain/response"
	"blacking-api/internal/helper"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
	"context"
	"strings"
)

type PaymentGatewayAccountService interface {
	CreatePaymentGatewayAccount(ctx context.Context, req *payment_gateway_account.PaymentGatewayAccountRequest) error
	FindAllPaymentGatewayAccount(ctx context.Context, req *payment_gateway_account.PaymentGatewayAccountSearchRequest) (*response.SuccessWithPagination, error)
	FindPaymentGatewayAccountByID(ctx context.Context, id int64) (*payment_gateway_account.PaymentGatewayAccountByIdResponse, error)
	UpdatePaymentGatewayAccount(ctx context.Context, id int64, req *payment_gateway_account.PaymentGatewayAccountRequest) error
	UpdatePaymentGatewayAccountStatus(ctx context.Context, id int64, req *payment_gateway_account.UpdateStatusRequest) error
	DeletePaymentGatewayAccount(ctx context.Context, id int64) error
}

type paymentGatewayAccountService struct {
	paymentGatewayAccountRepository interfaces.PaymentGatewayAccountRepository
	logger                          logger.Logger
}

func NewPaymentGatewayAccountService(
	paymentGatewayAccountRepository interfaces.PaymentGatewayAccountRepository,
	logger logger.Logger,
) PaymentGatewayAccountService {
	return &paymentGatewayAccountService{
		paymentGatewayAccountRepository: paymentGatewayAccountRepository,
		logger:                          logger,
	}
}

func (s *paymentGatewayAccountService) CreatePaymentGatewayAccount(ctx context.Context, req *payment_gateway_account.PaymentGatewayAccountRequest) error {
	accountNameDuplicate, err := s.paymentGatewayAccountRepository.FindByAccountNameDuplicate(ctx, req.AccountName)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate account name")
		return err
	}
	if accountNameDuplicate {
		s.logger.Error("duplicate account name")
		return errors.NewValidationError("account name already exists")
	}

	encrypt, err := EncodePaymentGatewayAccount(req)
	if err != nil {
		s.logger.WithError(err).Error("failed to encode payment gateway account")
		return err
	}

	err = s.paymentGatewayAccountRepository.Create(ctx, encrypt)
	if err != nil {
		s.logger.WithError(err).Error("failed to create payment gateway account")
		return err
	}

	return nil
}

func (s *paymentGatewayAccountService) FindAllPaymentGatewayAccount(ctx context.Context, req *payment_gateway_account.PaymentGatewayAccountSearchRequest) (*response.SuccessWithPagination, error) {
	if err := helper.UnlimitPagination(&req.Page, &req.Limit); err != nil {
		s.logger.WithError(err).Error("failed to set pagination limits")
		return nil, err
	}

	pagination := helper.CalculatePagination(req.Page, req.Limit)

	var conditions []string
	var queryArgs []interface{}

	if req.AccountName != nil && *req.AccountName != "" {
		conditions = append(conditions, "UPPER(account_name) ILIKE UPPER($1)")
		queryArgs = append(queryArgs, "%"+*req.AccountName+"%")
	}
	var whereClause string
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	accounts, total, err := s.paymentGatewayAccountRepository.FindAll(ctx, pagination.Limit, pagination.Offset, whereClause, queryArgs)
	if err != nil {
		s.logger.WithError(err).Error("failed to find all payment gateway accounts")
		return nil, err
	}

	content := make([]*payment_gateway_account.PaymentGatewayAccountListResponse, len(accounts))
	if len(accounts) != 0 {
		for i, a := range accounts {
			content[i] = a.ToListResponse()
		}
	}

	responseData := &response.SuccessWithPagination{
		Message:    "Payment Gateway Accounts retrieved successfully",
		Content:    content,
		Page:       int64(pagination.Page),
		Limit:      int64(pagination.Limit),
		TotalPages: helper.CalculateTotalPages(total, pagination.Limit),
		TotalItems: total,
	}

	return responseData, nil
}

func (s *paymentGatewayAccountService) FindPaymentGatewayAccountByID(ctx context.Context, id int64) (*payment_gateway_account.PaymentGatewayAccountByIdResponse, error) {

	account, err := s.paymentGatewayAccountRepository.FindByID(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to find payment gateway account by ID")
		return nil, err
	}
	if account == nil {
		return nil, errors.NewNotFoundError("payment gateway account not found")
	}

	decodedAccount, err := DecodePaymentGatewayAccount(account)
	if err != nil {
		s.logger.WithError(err).Error("failed to decode payment gateway account")
		return nil, err
	}

	return decodedAccount.ToByIdResponse(), nil
}

func (s *paymentGatewayAccountService) UpdatePaymentGatewayAccount(ctx context.Context, id int64, req *payment_gateway_account.PaymentGatewayAccountRequest) error {
	accountNameDuplicate, err := s.paymentGatewayAccountRepository.FindByAccountNameDuplicateAndIdNot(ctx, req.AccountName, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to check for duplicate account name")
		return err
	}
	if accountNameDuplicate {
		s.logger.Error("duplicate account name")
		return errors.NewValidationError("account name already exists")
	}

	err = s.paymentGatewayAccountRepository.Update(ctx, id, req)
	if err != nil {
		s.logger.WithError(err).Error("failed to update payment gateway account")
		return err
	}

	return nil
}

func (s *paymentGatewayAccountService) UpdatePaymentGatewayAccountStatus(ctx context.Context, id int64, req *payment_gateway_account.UpdateStatusRequest) error {
	if id == 0 {
		return errors.NewValidationError("payment gateway account ID cannot be zero")
	}

	err := s.paymentGatewayAccountRepository.UpdateStatus(ctx, id, req.Name, req.Status)
	if err != nil {
		s.logger.WithError(err).Error("failed to update payment gateway account status")
		return err
	}

	return nil
}

func (s *paymentGatewayAccountService) DeletePaymentGatewayAccount(ctx context.Context, id int64) error {
	if id == 0 {
		return errors.NewValidationError("payment gateway account ID cannot be zero")
	}

	err := s.paymentGatewayAccountRepository.Delete(ctx, id)
	if err != nil {
		s.logger.WithError(err).Error("failed to delete payment gateway account")
		return err
	}

	return nil
}

func EncodePaymentGatewayAccount(req *payment_gateway_account.PaymentGatewayAccountRequest) (*payment_gateway_account.PaymentGatewayAccountRequest, error) {

	cEncrypt, err := helper.Encrypt(req.Code)
	if err != nil {
		return nil, err
	}

	skEncrypt, err := helper.Encrypt(req.SecretKey)
	if err != nil {
		return nil, err
	}

	sktEncrypt, err := helper.Encrypt(req.SecretKeyTwo)
	if err != nil {
		return nil, err
	}

	fuNumberEncrypt, err := helper.Encrypt(req.FirstUsername)
	if err != nil {
		return nil, err
	}

	suNumberEncrypt, err := helper.Encrypt(req.SecondUsername)
	if err != nil {
		return nil, err
	}

	fpEncrypt, err := helper.Encrypt(req.FirstPassword)
	if err != nil {
		return nil, err
	}

	spEncrypt, err := helper.Encrypt(req.SecondPassword)
	if err != nil {
		return nil, err
	}

	req.Code = cEncrypt
	req.SecretKey = skEncrypt
	req.SecretKeyTwo = sktEncrypt
	req.FirstUsername = fuNumberEncrypt
	req.SecondUsername = suNumberEncrypt
	req.FirstPassword = fpEncrypt
	req.SecondPassword = spEncrypt

	return req, nil
}

func DecodePaymentGatewayAccount(account *payment_gateway_account.PaymentGatewayAccount) (*payment_gateway_account.PaymentGatewayAccount, error) {
	cDecrypt, err := helper.Decrypt(account.Code)
	if err != nil {
		return nil, err
	}

	skDecrypt, err := helper.Decrypt(account.SecretKey)
	if err != nil {
		return nil, err
	}

	sktDecrypt, err := helper.Decrypt(account.SecretKeyTwo)
	if err != nil {
		return nil, err
	}

	fuNumberDecrypt, err := helper.Decrypt(account.FirstUsername)
	if err != nil {
		return nil, err
	}

	suNumberDecrypt, err := helper.Decrypt(account.SecondUsername)
	if err != nil {
		return nil, err
	}

	fpDecrypt, err := helper.Decrypt(account.FirstPassword)
	if err != nil {
		return nil, err
	}

	spDecrypt, err := helper.Decrypt(account.SecondPassword)
	if err != nil {
		return nil, err
	}

	account.Code = cDecrypt
	account.SecretKey = skDecrypt
	account.SecretKeyTwo = sktDecrypt
	account.FirstUsername = fuNumberDecrypt
	account.SecondUsername = suNumberDecrypt
	account.FirstPassword = fpDecrypt
	account.SecondPassword = spDecrypt

	return account, nil
}
