package service

import (
	"blacking-api/internal/storage"
	"blacking-api/pkg/logger"
	"context"
	"io"
)

// UploadService defines the interface for upload service operations
type UploadService interface {
	// UploadImage uploads an image file
	UploadImage(ctx context.Context, reader io.Reader, fileName string, folder string) (*storage.FileInfo, error)

	// UploadFile uploads any file type
	UploadFile(ctx context.Context, reader io.Reader, fileName string, folder string, allowedExts []string) (*storage.FileInfo, error)

	// DeleteFile deletes a file
	DeleteFile(ctx context.Context, path string) error

	// GetFileURL returns the public URL for a file
	GetFileURL(ctx context.Context, path string) (string, error)

	// GetFileInfo returns file information
	GetFileInfo(ctx context.Context, path string) (*storage.FileInfo, error)

	// ListFiles lists files in a folder
	ListFiles(ctx context.Context, folder string) ([]*storage.FileInfo, error)
}

type uploadService struct {
	storage storage.Storage
	logger  logger.Logger
	config  UploadConfig
}

// UploadConfig represents upload service configuration
type UploadConfig struct {
	MaxImageSize int64    `json:"max_image_size"` // Maximum image size in bytes
	MaxFileSize  int64    `json:"max_file_size"`  // Maximum file size in bytes
	ImageExts    []string `json:"image_exts"`     // Allowed image extensions
	DefaultExts  []string `json:"default_exts"`   // Default allowed extensions
}

// NewUploadService creates a new upload service
func NewUploadService(storage storage.Storage, config UploadConfig, logger logger.Logger) UploadService {
	return &uploadService{
		storage: storage,
		logger:  logger,
		config:  config,
	}
}

// UploadImage uploads an image file
func (s *uploadService) UploadImage(ctx context.Context, reader io.Reader, fileName string, folder string) (*storage.FileInfo, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UploadImage").WithField("file_name", fileName)
	options := storage.UploadOptions{
		Folder:      folder,
		MaxSize:     s.config.MaxImageSize,
		AllowedExts: s.config.ImageExts,
		Metadata: map[string]string{
			"type": "image",
		},
	}

	fileInfo, err := s.storage.Upload(ctx, reader, fileName, options)
	if err != nil {
		log.WithError(err).Error("failed to upload image")
		return nil, err
	}

	log.WithField("file_path", fileInfo.Path).Info("image uploaded successfully")
	return fileInfo, nil
}

// UploadFile uploads any file type
func (s *uploadService) UploadFile(ctx context.Context, reader io.Reader, fileName string, folder string, allowedExts []string) (*storage.FileInfo, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UploadFile").WithField("file_name", fileName)

	// Use provided extensions or default
	exts := allowedExts
	if len(exts) == 0 {
		exts = s.config.DefaultExts
	}

	options := storage.UploadOptions{
		Folder:      folder,
		MaxSize:     s.config.MaxFileSize,
		AllowedExts: exts,
		Metadata: map[string]string{
			"type": "file",
		},
	}

	fileInfo, err := s.storage.Upload(ctx, reader, fileName, options)
	if err != nil {
		log.WithError(err).Error("failed to upload file")
		return nil, err
	}

	log.WithField("file_path", fileInfo.Path).Info("file uploaded successfully")
	return fileInfo, nil
}

// DeleteFile deletes a file
func (s *uploadService) DeleteFile(ctx context.Context, path string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "DeleteFile").WithField("path", path)

	err := s.storage.Delete(ctx, path)
	if err != nil {
		log.WithError(err).Error("failed to delete file")
		return err
	}

	log.Info("file deleted successfully")
	return nil
}

// GetFileURL returns the public URL for a file
func (s *uploadService) GetFileURL(ctx context.Context, path string) (string, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetFileURL").WithField("path", path)

	url, err := s.storage.GetURL(ctx, path)
	if err != nil {
		log.WithError(err).Error("failed to get file URL")
		return "", err
	}

	log.WithField("url", url).Info("file URL retrieved successfully")
	return url, nil
}

// GetFileInfo returns file information
func (s *uploadService) GetFileInfo(ctx context.Context, path string) (*storage.FileInfo, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "GetFileInfo").WithField("path", path)

	fileInfo, err := s.storage.GetFileInfo(ctx, path)
	if err != nil {
		log.WithError(err).Error("failed to get file info")
		return nil, err
	}

	log.Info("file info retrieved successfully")
	return fileInfo, nil
}

// ListFiles lists files in a folder
func (s *uploadService) ListFiles(ctx context.Context, folder string) ([]*storage.FileInfo, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListFiles").WithField("folder", folder)

	files, err := s.storage.ListFiles(ctx, folder)
	if err != nil {
		log.WithError(err).Error("failed to list files")
		return nil, err
	}

	log.WithField("count", len(files)).Info("files listed successfully")
	return files, nil
}

// GetDefaultUploadConfig returns default upload configuration
func GetDefaultUploadConfig() UploadConfig {
	return UploadConfig{
		MaxImageSize: 10 * 1024 * 1024, // 10MB
		MaxFileSize:  50 * 1024 * 1024, // 50MB
		ImageExts:    []string{".jpg", ".jpeg", ".png", ".gif", ".webp"},
		DefaultExts:  []string{".jpg", ".jpeg", ".png", ".gif", ".webp", ".pdf", ".txt", ".json"},
	}
}
