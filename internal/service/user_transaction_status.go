package service

import (
	"blacking-api/internal/domain/user_transaction_status"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
)

type UserTransactionStatusService interface {
	ListUserTransactionStatuses(ctx context.Context) ([]*user_transaction_status.UserTransactionStatusResponse, error)
}

type userTransactionStatusService struct {
	userTransactionStatusRepo interfaces.UserTransactionStatusRepository
	logger                    logger.Logger
}

func NewUserTransactionStatusService(userTransactionStatusRepo interfaces.UserTransactionStatusRepository, logger logger.Logger) UserTransactionStatusService {
	return &userTransactionStatusService{
		userTransactionStatusRepo: userTransactionStatusRepo,
		logger:                    logger,
	}
}

func (s *userTransactionStatusService) ListUserTransactionStatuses(ctx context.Context) ([]*user_transaction_status.UserTransactionStatusResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListUserTransactionStatuses")

	transactionStatuses, err := s.userTransactionStatusRepo.List(ctx)
	if err != nil {
		log.WithError(err).Error("failed to retrieve user transaction statuses from repository")
		return nil, err
	}

	// Convert to response format
	var responses []*user_transaction_status.UserTransactionStatusResponse
	for _, transactionStatus := range transactionStatuses {
		response := transactionStatus.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("count", len(responses)).Info("user transaction statuses retrieved successfully")
	return responses, nil
}
