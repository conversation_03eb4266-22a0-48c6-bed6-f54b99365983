package service

import (
	"blacking-api/internal/domain/auto_bot"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/logger"
	"context"
)

type AutoBotService interface {
	ListAutoBots(ctx context.Context) ([]*auto_bot.AutoBotResponse, error)
}

type autoBotService struct {
	autoBotRepo interfaces.AutoBotRepository
	logger      logger.Logger
}

func NewAutoBotService(autoBotRepo interfaces.AutoBotRepository, logger logger.Logger) AutoBotService {
	return &autoBotService{
		autoBotRepo: autoBotRepo,
		logger:      logger,
	}
}

func (s *autoBotService) ListAutoBots(ctx context.Context) ([]*auto_bot.AutoBotResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListAutoBots")

	autoBots, err := s.autoBotRepo.List(ctx)
	if err != nil {
		log.Errorf("failed to list auto bots: %v", err)
		return nil, err
	}

	responses := make([]*auto_bot.AutoBotResponse, len(autoBots))
	for i, ab := range autoBots {
		response := ab.ToResponse()
		responses[i] = &response
	}

	return responses, nil
}
