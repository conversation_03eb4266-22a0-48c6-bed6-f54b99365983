package service

import (
	"context"
	"strconv"
	"time"

	"blacking-api/internal/domain/member"
	"blacking-api/internal/domain/platform"
	"blacking-api/internal/repository/interfaces"
	"blacking-api/pkg/errors"
	"blacking-api/pkg/logger"
)

// PartnerService defines the interface for partner service operations
type PartnerService interface {
	CreatePartner(ctx context.Context, req member.CreatePartnerRequest, clientIP string) (*member.MemberResponse, error)
	UpdatePartner(ctx context.Context, id string, req member.UpdatePartnerRequest) (*member.MemberResponse, error)
	ChangePartnerPassword(ctx context.Context, id string, req member.ChangePartnerPasswordRequest, adminId, adminName string) error
	SuspendPartner(ctx context.Context, id string, adminId, adminName string) error
	ListPartners(ctx context.Context, limit, offset int, search string) ([]*member.MemberResponse, int64, error)
	ListPartnersForDropdown(ctx context.Context) ([]*member.PartnerDropdownResponse, error)
}

type partnerService struct {
	memberRepo            interfaces.MemberRepository
	channelRepo           interfaces.ChannelRepository
	memberGroupRepo       interfaces.MemberGroupRepository
	referralGroupRepo     interfaces.ReferralGroupRepository
	memberAuditLogService MemberAuditLogService
	logger                logger.Logger
}

// NewPartnerService creates a new partner service
func NewPartnerService(
	memberRepo interfaces.MemberRepository,
	channelRepo interfaces.ChannelRepository,
	memberGroupRepo interfaces.MemberGroupRepository,
	referralGroupRepo interfaces.ReferralGroupRepository,
	memberAuditLogService MemberAuditLogService,
	logger logger.Logger,
) PartnerService {
	return &partnerService{
		memberRepo:            memberRepo,
		channelRepo:           channelRepo,
		memberGroupRepo:       memberGroupRepo,
		referralGroupRepo:     referralGroupRepo,
		memberAuditLogService: memberAuditLogService,
		logger:                logger,
	}
}

// CreatePartner creates a new partner
func (s *partnerService) CreatePartner(ctx context.Context, req member.CreatePartnerRequest, clientIP string) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "CreatePartner")

	// Validate member_group_id
	if req.MemberGroupID > 0 {
		_, err := s.memberGroupRepo.GetByID(ctx, req.MemberGroupID)
		if err != nil {
			log.WithError(err).WithField("member_group_id", req.MemberGroupID).Error("invalid member group ID")
			return nil, errors.NewValidationError("invalid member group ID")
		}
	}

	// Validate referral_group_id
	if req.ReferralGroupID != nil && *req.ReferralGroupID > 0 {
		_, err := s.referralGroupRepo.GetByID(ctx, *req.ReferralGroupID)
		if err != nil {
			log.WithError(err).WithField("referral_group_id", *req.ReferralGroupID).Error("invalid referral group ID")
			return nil, errors.NewValidationError("invalid referral group ID")
		}
	}

	// Validate channel exists
	_, err := s.channelRepo.GetByID(ctx, req.ChannelID)
	if err != nil {
		log.WithError(err).WithField("channel_id", req.ChannelID).Error("channel not found")
		return nil, errors.NewValidationError("invalid channel_id")
	}

	// Validate platform_id
	if !platform.IsValidPlatformID(req.PlatformID) {
		log.WithField("platform_id", req.PlatformID).Error("invalid platform ID")
		return nil, errors.NewValidationError("invalid platform ID")
	}

	// Check if phone already exists
	existingMember, err := s.memberRepo.GetByPhone(ctx, req.Phone)
	if err == nil && existingMember != nil {
		log.WithField("phone", req.Phone).Error("phone already exists")
		return nil, errors.NewValidationError("phone already exists")
	}

	// Check if username (phone) already exists
	existingMember, err = s.memberRepo.GetByUsername(ctx, req.Phone)
	if err == nil && existingMember != nil {
		log.WithField("username", req.Phone).Error("username already exists")
		return nil, errors.NewValidationError("username already exists")
	}

	// Create partner
	newPartner, err := member.NewPartner(req)
	if err != nil {
		log.WithError(err).Error("failed to create partner domain object")
		return nil, err
	}

	// Save to repository
	if err := s.memberRepo.Create(ctx, newPartner, clientIP); err != nil {
		log.WithError(err).Error("failed to save partner to repository")
		return nil, err
	}

	response := newPartner.ToResponse()
	log.WithField("partner_id", response.ID).WithField("phone", req.Phone).Info("partner created successfully")
	return &response, nil
}

// UpdatePartner updates an existing partner
func (s *partnerService) UpdatePartner(ctx context.Context, id string, req member.UpdatePartnerRequest) (*member.MemberResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "UpdatePartner").WithField("partner_id", id)

	// Get existing partner
	existingPartner, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get existing partner")
		return nil, err
	}

	// Verify it's a partner
	if !existingPartner.IsPartner {
		log.Error("member is not a partner")
		return nil, errors.NewValidationError("member is not a partner")
	}

	// Validate member_group_id if provided
	if req.MemberGroupID != nil && *req.MemberGroupID > 0 {
		_, err := s.memberGroupRepo.GetByID(ctx, *req.MemberGroupID)
		if err != nil {
			log.WithError(err).WithField("member_group_id", *req.MemberGroupID).Error("invalid member group ID")
			return nil, errors.NewValidationError("invalid member group ID")
		}
	}

	// Validate referral_group_id if provided
	if req.ReferralGroupID != nil && *req.ReferralGroupID > 0 {
		_, err := s.referralGroupRepo.GetByID(ctx, *req.ReferralGroupID)
		if err != nil {
			log.WithError(err).WithField("referral_group_id", *req.ReferralGroupID).Error("invalid referral group ID")
			return nil, errors.NewValidationError("invalid referral group ID")
		}
	}

	// Validate channel_id if provided
	if req.ChannelID != nil && *req.ChannelID > 0 {
		_, err := s.channelRepo.GetByID(ctx, *req.ChannelID)
		if err != nil {
			log.WithError(err).WithField("channel_id", *req.ChannelID).Error("channel not found")
			return nil, errors.NewValidationError("invalid channel_id")
		}
	}

	// Validate platform_id if provided
	if req.PlatformID != nil && *req.PlatformID != "" {
		if !platform.IsValidPlatformID(*req.PlatformID) {
			log.WithField("platform_id", *req.PlatformID).Error("invalid platform ID")
			return nil, errors.NewValidationError("invalid platform ID")
		}
	}

	// Check if phone already exists (if phone is being updated)
	if req.Phone != nil && *req.Phone != *existingPartner.Phone {
		existingMember, err := s.memberRepo.GetByPhone(ctx, *req.Phone)
		if err == nil && existingMember != nil {
			log.WithField("phone", *req.Phone).Error("phone already exists")
			return nil, errors.NewValidationError("phone already exists")
		}
	}

	// Update partner
	if err := existingPartner.UpdatePartner(req); err != nil {
		log.WithError(err).Error("failed to update partner domain object")
		return nil, err
	}

	// Save to repository
	if err := s.memberRepo.Update(ctx, existingPartner); err != nil {
		log.WithError(err).Error("failed to save updated partner to repository")
		return nil, err
	}

	response := existingPartner.ToResponse()
	log.WithField("partner_id", response.ID).Info("partner updated successfully")
	return &response, nil
}

// ListPartners retrieves partners with pagination and search by first_name
func (s *partnerService) ListPartners(ctx context.Context, limit, offset int, search string) ([]*member.MemberResponse, int64, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListPartners")

	// Get total count of partners
	total, err := s.memberRepo.CountPartners(ctx, search)
	if err != nil {
		log.WithError(err).Error("failed to count partners")
		return nil, 0, err
	}

	// Get partners
	partners, err := s.memberRepo.ListPartners(ctx, limit, offset, search)
	if err != nil {
		log.WithError(err).Error("failed to list partners")
		return nil, 0, err
	}

	// Convert to response format
	var responses []*member.MemberResponse
	for _, p := range partners {
		response := p.ToResponse()
		responses = append(responses, &response)
	}

	log.WithField("total", total).WithField("returned", len(responses)).Info("partners listed successfully")
	return responses, total, nil
}

// ListPartnersForDropdown retrieves all partners for dropdown usage (lightweight response)
func (s *partnerService) ListPartnersForDropdown(ctx context.Context) ([]*member.PartnerDropdownResponse, error) {
	log := s.logger.WithContext(ctx).WithField("operation", "ListPartnersForDropdown")

	// Get all partners without pagination (for dropdown)
	partners, err := s.memberRepo.ListPartners(ctx, 1000, 0, "") // Get up to 1000 partners
	if err != nil {
		log.WithError(err).Error("failed to list partners for dropdown")
		return nil, err
	}

	// Convert to dropdown response format
	var responses []*member.PartnerDropdownResponse
	for _, p := range partners {
		response := p.ToDropdownResponse()
		responses = append(responses, &response)
	}

	log.WithField("returned", len(responses)).Info("partners for dropdown listed successfully")
	return responses, nil
}

// ChangePartnerPassword changes partner password
func (s *partnerService) ChangePartnerPassword(ctx context.Context, id string, req member.ChangePartnerPasswordRequest, adminId, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "ChangePartnerPassword").WithField("partner_id", id)

	// Get existing partner
	existingPartner, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get existing partner")
		return err
	}

	// Verify it's a partner
	if !existingPartner.IsPartner {
		log.Error("member is not a partner")
		return errors.NewValidationError("member is not a partner")
	}

	// Change password
	if err := existingPartner.ChangePassword(req.NewPassword); err != nil {
		log.WithError(err).Error("failed to change partner password")
		return err
	}

	// Save to repository
	if err := s.memberRepo.Update(ctx, existingPartner); err != nil {
		log.WithError(err).Error("failed to save partner password change")
		return err
	}

	// Log password change to audit trail
	adminIdInt, err := strconv.Atoi(adminId)
	if err != nil {
		log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
		adminIdInt = 0 // Use 0 as fallback
	}
	auditReq := PasswordChangeMemberAuditLog(existingPartner.ID, existingPartner.Username, adminIdInt, adminName)
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log partner password change audit")
	}

	log.WithField("partner_id", id).Info("partner password changed successfully")
	return nil
}

// SuspendPartner suspends a partner by setting IsEnable to false
func (s *partnerService) SuspendPartner(ctx context.Context, id string, adminId, adminName string) error {
	log := s.logger.WithContext(ctx).WithField("operation", "SuspendPartner").WithField("partner_id", id)

	// Get existing partner
	existingPartner, err := s.memberRepo.GetByID(ctx, id)
	if err != nil {
		log.WithError(err).Error("failed to get existing partner")
		return err
	}

	// Verify it's a partner
	if !existingPartner.IsPartner {
		log.Error("member is not a partner")
		return errors.NewValidationError("member is not a partner")
	}

	// Suspend partner by setting IsEnable to false
	existingPartner.IsEnable = false
	existingPartner.UpdatedAt = time.Now()

	// Save to repository
	if err := s.memberRepo.Update(ctx, existingPartner); err != nil {
		log.WithError(err).Error("failed to save suspended partner to repository")
		return err
	}

	// Log partner suspension to audit trail
	adminIdInt, err := strconv.Atoi(adminId)
	if err != nil {
		log.WithError(err).Warn("failed to convert admin ID to integer for audit log")
		adminIdInt = 0
	}
	auditReq := SuspendMemberAuditLog(existingPartner.ID, existingPartner.Username, adminIdInt, adminName)
	if err := s.memberAuditLogService.LogMemberAction(ctx, auditReq); err != nil {
		log.WithError(err).Warn("failed to log partner suspension audit")
	}

	log.WithField("partner_id", id).Info("partner suspended successfully")
	return nil
}
