package app

import (
	"blacking-api/internal/repository/aws_s3"
	"blacking-api/migrations/list"
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"blacking-api/infrastructure/database"
	"blacking-api/internal/config"
	httphandler "blacking-api/internal/handler/http"
	"blacking-api/internal/middleware"
	"blacking-api/internal/repository/postgres"
	"blacking-api/internal/router"
	"blacking-api/internal/service"
	"blacking-api/pkg/logger"
	"blacking-api/pkg/sms"

	gmEngine "github.com/ShkrutDenis/go-migrations/engine"
	gmConfig "github.com/ShkrutDenis/go-migrations/engine/config"
	"github.com/gin-gonic/gin"
)

type Application struct {
	config *config.Config
	logger logger.Logger
	db     *database.Database
	server *http.Server
}

func NewApplication() (*Application, error) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// Initialize logger
	log := logger.New(logger.Config{
		Level:  cfg.Log.Level,
		Format: cfg.Log.Format,
	})

	// Set Gin mode
	gin.SetMode(cfg.Server.Mode)

	// Initialize database
	db, err := database.New(cfg, log)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	app := &Application{
		config: cfg,
		logger: log,
		db:     db,
	}

	return app, nil
}

func (a *Application) setupDependencies() *router.Handlers {
	// Initialize repositories
	userRepo := postgres.NewUserRepository(a.db.PGXPool, a.logger)
	userRoleRepo := postgres.NewUserRoleRepository(a.db.PGXPool, a.logger)
	memberRepo := postgres.NewMemberRepository(a.db.PGXPool, a.logger)
	memberAuditLogRepo := postgres.NewMemberAuditLogRepository(a.db.PGXPool, a.logger)
	userAuditLogRepo := postgres.NewUserAuditLogRepository(a.db.PGXPool, a.logger)
	allowedIPRepo := postgres.NewAllowedIPRepository(a.db.PGXPool, a.logger)
	systemSettingRepo := postgres.NewSystemSettingRepository(a.db.PGXPool, a.logger)
	themeSettingRepo := postgres.NewThemeSettingRepository(a.db.PGXPool, a.logger)
	user2FARepo := postgres.NewUser2FARepository(a.db.PGXPool, a.logger)
	loginAttemptRepo := postgres.NewLoginAttemptRepository(a.db.PGXPool, a.logger)
	loginReportRepo := postgres.NewLoginReportRepository(a.db.PGXPool, a.logger)
	otpRepo := postgres.NewOTPRepository(a.db.PGXPool, a.logger)
	bannerRepo := postgres.NewBannerRepository(a.db.PGXPool, a.logger)
	channelRepo := postgres.NewChannelRepository(a.db.PGXPool, a.logger)
	memberGroupTypeRepo := postgres.NewMemberGroupTypeRepository(a.db.PGXPool, a.logger)
	commissionGroupRepo := postgres.NewCommissionGroupRepository(a.db.PGXPool, a.logger)
	memberGroupRepo := postgres.NewMemberGroupRepository(a.db.PGXPool, a.logger)
	referralGroupRepo := postgres.NewReferralGroupRepository(a.db.PGXPool, a.logger)
	permissionRepo := postgres.NewPermissionRepository(a.db.PGXPool, a.logger)
	// permissionGroupRepo removed - no longer using permission groups
	userRolePermissionRepo := postgres.NewUserRolePermissionRepository(a.db.PGXPool, a.logger)
	faqRepo := postgres.NewFAQRepository(a.db.PGXPool, a.logger)

	// Initialize DateTime repository
	datetimeRepo := postgres.NewDateTimeRepository(a.db.PGXPool, a.logger)

	// Initialize PromotionWeb repository
	promotionWebRepo := postgres.NewPromotionWebRepository(a.db.PGXPool, a.logger, datetimeRepo)

	// Initialize SMS service
	var smsService service.SMSService
	if a.config.SMS.Provider == "thsms" {
		thsmsConfig := sms.ThSMSConfig{
			Sender:  a.config.SMS.ThSMS.Sender,
			Token:   a.config.SMS.ThSMS.Token,
			BaseURL: a.config.SMS.ThSMS.BaseURL,
		}
		thsmsClient := sms.NewThSMSClient(thsmsConfig, a.logger)
		smsService = service.NewSMSService(thsmsClient, a.logger)
	} else {
		// Use mock SMS service for development
		smsService = nil // Will fallback to development mode in OTP service
	}

	// Initialize aws S3 first
	awsS3Repo := aws_s3.NewAWSS3Repository(a.db)

	// Initialize services
	userAuditLogService := service.NewUserAuditLogService(userAuditLogRepo, a.logger)
	memberAuditLogService := service.NewMemberAuditLogService(memberAuditLogRepo, a.logger)
	allowedIPService := service.NewAllowedIPService(allowedIPRepo, a.logger)
	systemSettingService := service.NewSystemSettingService(systemSettingRepo, awsS3Repo, a.logger)
	themeSettingService := service.NewThemeSettingService(themeSettingRepo, a.logger)
	user2FAService := service.NewUser2FAService(user2FARepo, userRepo, a.logger)
	languageService := service.NewLanguageService(a.logger)
	userService := service.NewUserService(userRepo, userRoleRepo, userAuditLogService, systemSettingService, loginAttemptRepo, a.logger)
	userRoleService := service.NewUserRoleService(userRoleRepo, userRolePermissionRepo, a.logger)
	otpService := service.NewOTPService(otpRepo, memberRepo, smsService, a.logger)
	platformService := service.NewPlatformService(a.logger)
	channelService := service.NewChannelService(channelRepo, a.logger)
	memberGroupTypeService := service.NewMemberGroupTypeService(memberGroupTypeRepo, awsS3Repo, a.logger)
	commissionGroupService := service.NewCommissionGroupService(commissionGroupRepo, a.logger)

	referralGroupService := service.NewReferralGroupService(referralGroupRepo, a.logger)
	partnerService := service.NewPartnerService(memberRepo, channelRepo, memberGroupRepo, referralGroupRepo, memberAuditLogService, a.logger)
	memberService := service.NewMemberService(memberRepo, loginAttemptRepo, systemSettingService, memberAuditLogService, otpService, referralGroupRepo, memberGroupRepo, a.logger)
	bannerService := service.NewBannerService(bannerRepo, userRepo, awsS3Repo, a.logger)
	permissionService := service.NewPermissionService(permissionRepo, userRolePermissionRepo, a.logger)
	faqService := service.NewFAQService(faqRepo, a.logger)
	loginReportService := service.NewLoginReportService(loginReportRepo, a.logger)
	promotionWebService := service.NewPromotionWebService(promotionWebRepo, a.logger)

	// Initialize handlers
	userHandler := httphandler.NewUserHandler(userService, a.logger)
	healthHandler := httphandler.NewHealthHandler(a.logger)
	userRoleHandler := httphandler.NewUserRoleHandler(userRoleService, a.logger)
	memberHandler := httphandler.NewMemberHandler(memberService)
	userAuditLogHandler := httphandler.NewUserAuditLogHandler(userAuditLogService, a.logger)
	memberAuditLogHandler := httphandler.NewMemberAuditLogHandler(memberAuditLogService)
	allowedIPHandler := httphandler.NewAllowedIPHandler(allowedIPService, a.logger)
	systemSettingHandler := httphandler.NewSystemSettingHandler(systemSettingService, a.logger)
	themeSettingHandler := httphandler.NewThemeSettingHandler(themeSettingService, a.logger)
	languageHandler := httphandler.NewLanguageHandler(languageService, a.logger)
	bannerHandler := httphandler.NewBannerHandler(bannerService)
	permissionHandler := httphandler.NewPermissionHandler(permissionService, userRoleService, a.logger)
	faqHandler := httphandler.NewFAQHandler(faqService, a.logger)
	memberRegistrationHandler := httphandler.NewMemberRegistrationHandler(memberService)
	loginReportHandler := httphandler.NewLoginReportHandler(loginReportService)
	otpHandler := httphandler.NewOTPHandler(otpService, a.logger)
	platformHandler := httphandler.NewPlatformHandler(platformService, a.logger)
	channelHandler := httphandler.NewChannelHandler(channelService, a.logger)
	memberGroupTypeHandler := httphandler.NewMemberGroupTypeHandler(memberGroupTypeService, a.logger)
	commissionGroupHandler := httphandler.NewCommissionGroupHandler(commissionGroupService, a.logger)

	referralGroupHandler := httphandler.NewReferralGroupHandler(referralGroupService, a.logger)
	partnerHandler := httphandler.NewPartnerHandler(partnerService, a.logger)
	promotionWebHandler := httphandler.NewPromotionWebHandler(promotionWebService, a.logger)

	// Initialize middlewares
	authMiddleware := middleware.NewAuthMiddleware(userService, userRoleService, user2FAService, memberService)
	jwtMiddleware := authMiddleware.NewUserJWT()
	memberJwtMiddleware := authMiddleware.NewMemberJWT()

	user2FAHandler := httphandler.NewUser2FAHandler(user2FAService, userService, jwtMiddleware, a.logger)

	// initialize BankingHandler
	bankingRepo := postgres.NewBankingRepository(a.db.PGXPool, a.logger)
	bankingService := service.NewBankingService(bankingRepo, a.logger)
	bankingHandler := httphandler.NewBankingHandler(bankingService, a.logger)

	// Initialize PaymentMethodHandler
	paymentMethodRepo := postgres.NewPaymentMethodRepository(a.db.PGXPool, a.logger)
	paymentMethodService := service.NewPaymentMethodService(paymentMethodRepo, a.logger)
	paymentMethodHandler := httphandler.NewPaymentMethodHandler(paymentMethodService, a.logger)

	// Initialize AlgorithmHandler
	algorithmRepo := postgres.NewAlgorithmRepository(a.db.PGXPool, a.logger)
	algorithmService := service.NewAlgorithmService(algorithmRepo, a.logger)
	algorithmHandler := httphandler.NewAlgorithmHandler(algorithmService, a.logger)

	// Initialize SMSProviderNameHandler
	smsProviderNameRepo := postgres.NewSMSProviderNameRepository(a.db.PGXPool, a.logger)
	smsProviderNameService := service.NewSMSProviderNameService(smsProviderNameRepo, a.logger)
	smsProviderNameHandler := httphandler.NewSMSProviderNameHandler(smsProviderNameService, a.logger)

	// Initialize SMSProviderHandler
	smsProviderRepo := postgres.NewSMSProviderRepository(a.db.PGXPool, a.logger)
	smsProviderService := service.NewSMSProviderService(smsProviderRepo, smsProviderNameRepo, a.logger)
	smsProviderHandler := httphandler.NewSMSProviderHandler(smsProviderService, a.logger)

	// Initialize ContactHandler
	contactRepo := postgres.NewContactRepository(a.db.PGXPool, a.logger)
	contactService := service.NewContactService(contactRepo, awsS3Repo, a.logger)
	contactHandler := httphandler.NewContactHandler(contactService, a.logger)

	// Initialize AutoBotHandler
	autoBotRepo := postgres.NewAutoBotRepository(a.db.PGXPool, a.logger)
	autoBotService := service.NewAutoBotService(autoBotRepo, a.logger)
	autoBotHandler := httphandler.NewAutoBotHandler(autoBotService, a.logger)

	// Initialize DepositAccountHandler
	depositAccountRepo := postgres.NewDepositAccountRepository(a.db.PGXPool, a.logger)
	depositAccountService := service.NewDepositAccountService(depositAccountRepo, bankingRepo, paymentMethodRepo, algorithmRepo, autoBotRepo, a.logger)
	depositAccountHandler := httphandler.NewDepositAccountHandler(depositAccountService, a.logger)

	// Initialize MemberGroupService (moved here to access depositAccountRepo)
	memberGroupService := service.NewMemberGroupService(memberGroupRepo, userRoleRepo, commissionGroupRepo, memberGroupTypeRepo, depositAccountRepo, awsS3Repo, a.logger)
	memberGroupHandler := httphandler.NewMemberGroupHandler(memberGroupService, a.logger)

	// Initialize WithdrawAccountHandler
	withdrawAccountRepo := postgres.NewWithdrawAccountRepository(a.db.PGXPool, a.logger)
	withdrawAccountService := service.NewWithdrawAccountService(withdrawAccountRepo, bankingRepo, algorithmRepo, autoBotRepo, awsS3Repo, a.logger)
	withdrawAccountHandler := httphandler.NewWithdrawAccountHandler(withdrawAccountService, a.logger)

	// Initialize HoldingAccountHandler
	holdingAccountRepo := postgres.NewHoldingAccountRepository(a.db.PGXPool, a.logger)
	holdingAccountService := service.NewHodlingAccountService(holdingAccountRepo, bankingRepo, a.logger)
	holdingAccountHandler := httphandler.NewHoldingAccountHandler(holdingAccountService, a.logger)

	// Initialize PaymentGatewayAccountHandler
	paymentGatewayAccountRepo := postgres.NewPaymentGatewayAccountRepository(a.db.PGXPool, a.logger)
	paymentGatewayAccountService := service.NewPaymentGatewayAccountService(paymentGatewayAccountRepo, a.logger)
	paymentGatewayAccountHandler := httphandler.NewPaymentGatewayAccountHandler(paymentGatewayAccountService, a.logger)

	// Initialize UserTransactionTypeHandler
	userTransactionTypeRepo := postgres.NewUserTransactionTypeRepository(a.db.PGXPool, a.logger)
	userTransactionTypeService := service.NewUserTransactionTypeService(userTransactionTypeRepo, a.logger)
	userTransactionTypeHandler := httphandler.NewUserTransactionTypeHandler(userTransactionTypeService)

	// Initialize UserTransactionDirectionHandler
	userTransactionDirectionRepo := postgres.NewUserTransactionDirectionRepository(a.db.PGXPool, a.logger)
	userTransactionDirectionService := service.NewUserTransactionDirectionService(userTransactionDirectionRepo, a.logger)
	userTransactionDirectionHandler := httphandler.NewUserTransactionDirectionHandler(userTransactionDirectionService)

	// Initialize UserTransactionStatusHandler
	userTransactionStatusRepo := postgres.NewUserTransactionStatusRepository(a.db.PGXPool, a.logger)
	userTransactionStatusService := service.NewUserTransactionStatusService(userTransactionStatusRepo, a.logger)
	userTransactionStatusHandler := httphandler.NewUserTransactionStatusHandler(userTransactionStatusService)

	return &router.Handlers{
		UserHandler:                     userHandler,
		HealthHandler:                   healthHandler,
		JwtMiddleware:                   jwtMiddleware,
		MemberJwtMiddleware:             memberJwtMiddleware,
		UserRoleHandler:                 userRoleHandler,
		MemberHandler:                   memberHandler,
		MemberAuditLogHandler:           memberAuditLogHandler,
		UserAuditLogHandler:             userAuditLogHandler,
		AllowedIPHandler:                allowedIPHandler,
		SystemSettingHandler:            systemSettingHandler,
		ThemeSettingHandler:          themeSettingHandler,
		User2FAHandler:                  user2FAHandler,
		LanguageHandler:                 languageHandler,
		BannerHandler:                   bannerHandler,
		PermissionHandler:               permissionHandler,
		BankingHandler:                  bankingHandler,
		PaymentMethodHandler:            paymentMethodHandler,
		AlgorithmHandler:                algorithmHandler,
		AutoBotHandler:                  autoBotHandler,
		FAQHandler:                      faqHandler,
		MemberRegistrationHandler:       memberRegistrationHandler,
		LoginReportHandler:              loginReportHandler,
		OTPHandler:                      otpHandler,
		PlatformHandler:                 platformHandler,
		ChannelHandler:                  channelHandler,
		MemberGroupTypeHandler:          memberGroupTypeHandler,
		CommissionGroupHandler:          commissionGroupHandler,
		MemberGroupHandler:              memberGroupHandler,
		ReferralGroupHandler:            referralGroupHandler,
		PartnerHandler:                  partnerHandler,
		SMSProviderNameHandler:          smsProviderNameHandler,
		SMSProviderHandler:              smsProviderHandler,
		ContactHandler:                  contactHandler,
		DepositAccountHandler:           depositAccountHandler,
		WithdrawAccountHandler:          withdrawAccountHandler,
		HoldingAccountHandler:           holdingAccountHandler,
		PaymentGatewayAccountHandler:    paymentGatewayAccountHandler,
		PromotionWebHandler:             promotionWebHandler,
		UserTransactionTypeHandler:      userTransactionTypeHandler,
		UserTransactionDirectionHandler: userTransactionDirectionHandler,
		UserTransactionStatusHandler:    userTransactionStatusHandler,
	}
}

func (a *Application) Start() error {
	a.logger.Info("Starting application...")

	// Setup dependencies
	handlers := a.setupDependencies()

	// Setup router
	r := router.Setup(handlers, a.logger)

	// Create HTTP server
	a.server = &http.Server{
		Addr:         a.config.GetServerAddress(),
		Handler:      r,
		ReadTimeout:  time.Duration(a.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(a.config.Server.WriteTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		a.logger.Infof("Server starting on %s", a.config.GetServerAddress())
		if err := a.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			a.logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	a.logger.Info("Application started successfully")
	return nil
}

func (a *Application) Stop() error {
	a.logger.Info("Shutting down application...")

	// Create a context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if a.server != nil {
		if err := a.server.Shutdown(ctx); err != nil {
			a.logger.Errorf("Server forced to shutdown: %v", err)
			return err
		}
		a.logger.Info("HTTP server stopped")
	}

	// Close database connection
	if a.db != nil {
		a.db.Close()
		a.logger.Info("Database connection closed")
	}

	a.logger.Info("Application stopped successfully")
	return nil
}

func (a *Application) Migrate() error {
	a.logger.Info("Running database migrations...")

	// Set environment variables from config for go-migrations
	os.Setenv("DB_DRIVER", "postgres")
	os.Setenv("DB_HOST", a.config.Database.Host)
	os.Setenv("DB_PORT", fmt.Sprintf("%d", a.config.Database.Port))
	os.Setenv("DB_NAME", a.config.Database.DBName)
	os.Setenv("DB_USER", a.config.Database.Username)
	os.Setenv("DB_PASSWORD", a.config.Database.Password)
	os.Setenv("DB_SSL_MODE", a.config.Database.SSLMode)

	// Create migration engine
	e := gmEngine.NewEngine()

	// Configure the engine with explicit .env path
	config := gmConfig.Config{
		Verbose:    true,
		IsRollback: false,
		EnvFile:    ".env",
		EnvPath:    "./",
	}
	e.WithConfig(config)

	// Run migrations
	migrations := []*list.CreateInitTables{
		&list.CreateInitTables{},
	}

	for _, migration := range migrations {
		a.logger.Infof("Running migration: %s", migration.GetName())
	}

	a.logger.Info("Database migrations completed")
	return nil
}

func Run() error {
	app, err := NewApplication()
	if err != nil {
		return err
	}

	// Start the application
	if err := app.Start(); err != nil {
		return err
	}

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	app.logger.Info("Received shutdown signal")

	// Stop the application
	return app.Stop()
}

func RunMigration() error {
	app, err := NewApplication()
	if err != nil {
		return err
	}
	defer app.db.Close()

	return app.Migrate()
}
