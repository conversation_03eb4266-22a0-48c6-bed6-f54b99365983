package storage

import (
	"context"
	"io"
	"time"
)

// FileInfo represents uploaded file information
type FileInfo struct {
	FileName     string    `json:"file_name"`
	OriginalName string    `json:"original_name"`
	Size         int64     `json:"size"`
	ContentType  string    `json:"content_type"`
	URL          string    `json:"url"`
	Path         string    `json:"path"`
	UploadedAt   time.Time `json:"uploaded_at"`
}

// UploadOptions represents options for file upload
type UploadOptions struct {
	Folder      string            `json:"folder"`       // Folder/prefix for organizing files
	MaxSize     int64             `json:"max_size"`     // Maximum file size in bytes
	AllowedExts []string          `json:"allowed_exts"` // Allowed file extensions
	Metadata    map[string]string `json:"metadata"`     // Additional metadata
}

// Storage defines the interface for file storage operations
type Storage interface {
	// Upload uploads a file and returns file information
	Upload(ctx context.Context, reader io.Reader, fileName string, options UploadOptions) (*FileInfo, error)

	// Delete deletes a file by its path
	Delete(ctx context.Context, path string) error

	// GetURL returns the public URL for a file
	GetURL(ctx context.Context, path string) (string, error)

	// GetSignedURL returns a signed URL for private file access (useful for S3)
	GetSignedURL(ctx context.Context, path string, expiration time.Duration) (string, error)

	// Exists checks if a file exists
	Exists(ctx context.Context, path string) (bool, error)

	// GetFileInfo returns file information
	GetFileInfo(ctx context.Context, path string) (*FileInfo, error)

	// ListFiles lists files in a folder
	ListFiles(ctx context.Context, folder string) ([]*FileInfo, error)

	// GetStorageType returns the storage type (local, s3, etc.)
	GetStorageType() string
}

// Config represents storage configuration
type Config struct {
	Type     string `json:"type"`      // "local" or "s3"
	BasePath string `json:"base_path"` // Base path for local storage
	BaseURL  string `json:"base_url"`  // Base URL for serving files

	// AWS S3 Configuration
	S3Config *S3Config `json:"s3_config,omitempty"`
}

// S3Config represents AWS S3 configuration
type S3Config struct {
	Region          string `json:"region"`
	Bucket          string `json:"bucket"`
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
	Endpoint        string `json:"endpoint,omitempty"`       // For S3-compatible services
	UsePathStyle    bool   `json:"use_path_style,omitempty"` // For S3-compatible services
	PublicRead      bool   `json:"public_read,omitempty"`    // Make uploaded files publicly readable
}

// ValidationError represents a file validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e ValidationError) Error() string {
	return e.Message
}

// StorageError represents a storage operation error
type StorageError struct {
	Operation string `json:"operation"`
	Message   string `json:"message"`
	Err       error  `json:"-"`
}

func (e StorageError) Error() string {
	if e.Err != nil {
		return e.Message + ": " + e.Err.Error()
	}
	return e.Message
}

func (e StorageError) Unwrap() error {
	return e.Err
}

// NewStorage creates a new storage instance based on configuration
func NewStorage(config Config) (Storage, error) {
	switch config.Type {
	case "local":
		return NewLocalStorage(config.BasePath, config.BaseURL), nil
	case "s3":
		if config.S3Config == nil {
			return nil, StorageError{
				Operation: "create_storage",
				Message:   "S3 configuration is required for S3 storage type",
			}
		}
		return NewS3Storage(config.S3Config), nil
	default:
		return nil, StorageError{
			Operation: "create_storage",
			Message:   "unsupported storage type: " + config.Type,
		}
	}
}
