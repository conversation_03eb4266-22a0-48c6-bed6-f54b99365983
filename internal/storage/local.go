package storage

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
)

// LocalStorage implements Storage interface for local file system
type LocalStorage struct {
	basePath string
	baseURL  string
}

// NewLocalStorage creates a new local storage instance
func NewLocalStorage(basePath, baseURL string) *LocalStorage {
	return &LocalStorage{
		basePath: basePath,
		baseURL:  baseURL,
	}
}

// Upload uploads a file to local storage
func (ls *LocalStorage) Upload(ctx context.Context, reader io.Reader, fileName string, options UploadOptions) (*FileInfo, error) {
	// Validate file extension
	if len(options.AllowedExts) > 0 {
		ext := strings.ToLower(filepath.Ext(fileName))
		allowed := false
		for _, allowedExt := range options.AllowedExts {
			if ext == strings.ToLower(allowedExt) {
				allowed = true
				break
			}
		}
		if !allowed {
			return nil, ValidationError{
				Field:   "file_extension",
				Message: fmt.Sprintf("file extension %s is not allowed", ext),
			}
		}
	}

	// Generate unique filename
	ext := filepath.Ext(fileName)
	baseName := strings.TrimSuffix(fileName, ext)
	uniqueFileName := fmt.Sprintf("%s_%s%s", baseName, uuid.New().String()[:8], ext)

	// Create folder path
	var folderPath string
	if options.Folder != "" {
		folderPath = filepath.Join(ls.basePath, options.Folder)
	} else {
		folderPath = ls.basePath
	}

	// Ensure directory exists
	if err := os.MkdirAll(folderPath, 0755); err != nil {
		return nil, StorageError{
			Operation: "create_directory",
			Message:   "failed to create upload directory",
			Err:       err,
		}
	}

	// Full file path
	filePath := filepath.Join(folderPath, uniqueFileName)

	// Create file
	file, err := os.Create(filePath)
	if err != nil {
		return nil, StorageError{
			Operation: "create_file",
			Message:   "failed to create file",
			Err:       err,
		}
	}
	defer file.Close()

	// Copy data with size limit
	var written int64
	if options.MaxSize > 0 {
		// Use LimitReader to prevent reading more than MaxSize
		limitedReader := io.LimitReader(reader, options.MaxSize+1)
		written, err = io.Copy(file, limitedReader)
		if err != nil && err != io.EOF {
			os.Remove(filePath) // Clean up
			return nil, StorageError{
				Operation: "write_file",
				Message:   "failed to write file data",
				Err:       err,
			}
		}
		if written > options.MaxSize {
			os.Remove(filePath) // Clean up
			return nil, ValidationError{
				Field:   "file_size",
				Message: fmt.Sprintf("file size exceeds maximum allowed size of %d bytes", options.MaxSize),
			}
		}
	} else {
		written, err = io.Copy(file, reader)
		if err != nil && err != io.EOF {
			os.Remove(filePath) // Clean up
			return nil, StorageError{
				Operation: "write_file",
				Message:   "failed to write file data",
				Err:       err,
			}
		}
	}

	// Get file info
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, StorageError{
			Operation: "get_file_info",
			Message:   "failed to get file information",
			Err:       err,
		}
	}

	// Build relative path for URL
	relativePath := uniqueFileName
	if options.Folder != "" {
		relativePath = filepath.Join(options.Folder, uniqueFileName)
	}

	// Build URL
	url := fmt.Sprintf("%s/%s", strings.TrimRight(ls.baseURL, "/"), strings.ReplaceAll(relativePath, "\\", "/"))

	return &FileInfo{
		FileName:     uniqueFileName,
		OriginalName: fileName,
		Size:         written,
		ContentType:  getContentType(ext),
		URL:          url,
		Path:         relativePath,
		UploadedAt:   fileInfo.ModTime(),
	}, nil
}

// Delete deletes a file from local storage
func (ls *LocalStorage) Delete(ctx context.Context, path string) error {
	fullPath := filepath.Join(ls.basePath, path)

	if err := os.Remove(fullPath); err != nil {
		if os.IsNotExist(err) {
			return StorageError{
				Operation: "delete_file",
				Message:   "file not found",
				Err:       err,
			}
		}
		return StorageError{
			Operation: "delete_file",
			Message:   "failed to delete file",
			Err:       err,
		}
	}

	return nil
}

// GetURL returns the public URL for a file
func (ls *LocalStorage) GetURL(ctx context.Context, path string) (string, error) {
	url := fmt.Sprintf("%s/%s", strings.TrimRight(ls.baseURL, "/"), strings.ReplaceAll(path, "\\", "/"))
	return url, nil
}

// GetSignedURL returns a signed URL (same as GetURL for local storage)
func (ls *LocalStorage) GetSignedURL(ctx context.Context, path string, expiration time.Duration) (string, error) {
	return ls.GetURL(ctx, path)
}

// Exists checks if a file exists
func (ls *LocalStorage) Exists(ctx context.Context, path string) (bool, error) {
	fullPath := filepath.Join(ls.basePath, path)
	_, err := os.Stat(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, StorageError{
			Operation: "check_file_exists",
			Message:   "failed to check file existence",
			Err:       err,
		}
	}
	return true, nil
}

// GetFileInfo returns file information
func (ls *LocalStorage) GetFileInfo(ctx context.Context, path string) (*FileInfo, error) {
	fullPath := filepath.Join(ls.basePath, path)

	fileInfo, err := os.Stat(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, StorageError{
				Operation: "get_file_info",
				Message:   "file not found",
				Err:       err,
			}
		}
		return nil, StorageError{
			Operation: "get_file_info",
			Message:   "failed to get file information",
			Err:       err,
		}
	}

	url, _ := ls.GetURL(ctx, path)
	ext := filepath.Ext(path)

	return &FileInfo{
		FileName:     fileInfo.Name(),
		OriginalName: fileInfo.Name(),
		Size:         fileInfo.Size(),
		ContentType:  getContentType(ext),
		URL:          url,
		Path:         path,
		UploadedAt:   fileInfo.ModTime(),
	}, nil
}

// ListFiles lists files in a folder
func (ls *LocalStorage) ListFiles(ctx context.Context, folder string) ([]*FileInfo, error) {
	folderPath := filepath.Join(ls.basePath, folder)

	entries, err := os.ReadDir(folderPath)
	if err != nil {
		return nil, StorageError{
			Operation: "list_files",
			Message:   "failed to list files",
			Err:       err,
		}
	}

	var files []*FileInfo
	for _, entry := range entries {
		if !entry.IsDir() {
			relativePath := filepath.Join(folder, entry.Name())
			fileInfo, err := ls.GetFileInfo(ctx, relativePath)
			if err == nil {
				files = append(files, fileInfo)
			}
		}
	}

	return files, nil
}

// GetStorageType returns the storage type
func (ls *LocalStorage) GetStorageType() string {
	return "local"
}

// getContentType returns content type based on file extension
func getContentType(ext string) string {
	switch strings.ToLower(ext) {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	case ".svg":
		return "image/svg+xml"
	case ".pdf":
		return "application/pdf"
	case ".txt":
		return "text/plain"
	case ".json":
		return "application/json"
	case ".xml":
		return "application/xml"
	case ".zip":
		return "application/zip"
	default:
		return "application/octet-stream"
	}
}
