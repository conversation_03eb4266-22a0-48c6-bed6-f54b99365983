package helper

const (
	DefaultPageSize = 10
	MaxPageSize     = 100
	MinPageSize     = 1
)

type PaginationParams struct {
	Page   int
	Limit  int
	Offset int
}

func UnlimitPagination(page, limit *int) error {

	if *page <= 0 {
		*page = 1
	}

	if *limit < 0 {
		*limit = 0
	}

	if *page > 0 {
		*page--
	}
	return nil
}

func CalculateTotalPages(total int64, limit int) int64 {
	if limit <= 0 {
		return 0
	}
	if total == 0 {
		return 1
	}
	totalPages := total / int64(limit)
	if total%int64(limit) > 0 {
		totalPages++
	}
	return totalPages
}

func CalculatePagination(requestPage, requestLimit int) PaginationParams {
	page := max(1, requestPage)
	limit := max(DefaultPageSize, requestLimit)

	// Optional: Set maximum limit to prevent abuse
	if limit > MaxPageSize {
		limit = MaxPageSize
	}

	offset := (page - 1) * limit

	return PaginationParams{
		Page:   page,
		Limit:  limit,
		Offset: offset,
	}
}
