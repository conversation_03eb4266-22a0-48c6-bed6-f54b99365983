.PHONY: help build run clean test lint fmt deps migrate docker-build docker-run docker-stop docker-clean

# Variables
APP_NAME=blacking-api
DOCKER_IMAGE=$(APP_NAME):latest
ENV=local

TIMESTAMP := $(shell date +"%Y%m%d%H%M%S")
MIGRATION_PATH := ./migrations/list



# Help command
help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Development commands
build: ## Build the application
	go build -o bin/server cmd/server/main.go
	go build -o bin/migrate cmd/migration/main.go
	go build -o bin/worker cmd/worker/main.go

run: ## Run the server locally
	go run cmd/server/main.go -env=$(ENV)

run-worker: ## Run the worker locally
	go run cmd/worker/main.go -env=$(ENV)

run-migration: ## Run database migrations
	go run cmd/migration/main.go -env=$(ENV)

clean: ## Clean build artifacts
	rm -rf bin/
	go clean

# Dependencies
deps: ## Download dependencies
	go mod download
	go mod tidy

deps-update: ## Update dependencies
	go get -u ./...
	go mod tidy

# Testing
test: ## Run tests
	go test -v ./...

test-coverage: ## Run tests with coverage
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

test-race: ## Run tests with race detection
	go test -race -v ./...

benchmark: ## Run benchmarks
	go test -bench=. -benchmem ./...

# Code quality
lint: ## Run linter
	golangci-lint run

fmt: ## Format code
	go fmt ./...
	goimports -w .

vet: ## Run go vet
	go vet ./...

# Database - Enhanced migration system with safety features
migrate-up: ## Run database migrations up
	go run cmd/migration/main.go $(ENV) up

migrate-down: ## Rollback N migrations (default: 1)
	@if [ -z "$(steps)" ]; then \
		go run cmd/migration/main.go $(ENV) down 1; \
	else \
		go run cmd/migration/main.go $(ENV) down $(steps); \
	fi

migrate-status: ## Show migration status
	go run cmd/migration/main.go $(ENV) status

migrate-validate: ## Validate all migration files
	go run cmd/migration/main.go $(ENV) validate

migrate-dry-run: ## Show what migrations would run (dry run)
	go run cmd/migration/main.go $(ENV) dry-run

migrate-seed: ## Run database seeders
	go run cmd/migration/main.go $(ENV) seed

migrate-create:
	@if [ -z "$(name)" ]; then \
		echo "Please provide MIGRATION_NAME, e.g. make create-migration name=create_users_table"; \
		exit 1; \
	fi
	@name=$(name); \
	prefix=$$(echo $$name | cut -d'_' -f1); \
	suffix=$$(echo $$name | cut -d'_' -f2-); \
	mkdir -p $(MIGRATION_PATH); \
	if echo "$$name" | grep -q "^create_"; then \
		printf "CREATE TABLE $$suffix (\n    id SERIAL PRIMARY KEY,\n    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);" > $(MIGRATION_PATH)/$(TIMESTAMP)_$$name.01.up.sql; \
		echo "DROP TABLE $$suffix;" > $(MIGRATION_PATH)/$(TIMESTAMP)_$$name.02.down.sql; \
	elif echo "$$name" | grep -q "^update_"; then \
		printf "-- Add new column\nALTER TABLE $$suffix \nADD COLUMN mockup VARCHAR(255);\n\n-- Update existing records if needed\n-- UPDATE $$suffix SET mockup = 'default_value' WHERE condition;" > $(MIGRATION_PATH)/$(TIMESTAMP)_$$name.01.up.sql; \
		printf "-- Remove column\nALTER TABLE $$suffix \nDROP COLUMN mockup;" > $(MIGRATION_PATH)/$(TIMESTAMP)_$$name.02.down.sql; \
	else \
		printf "-- Write your migration SQL here\n-- Example:\n-- CREATE TABLE example (\n--     id SERIAL PRIMARY KEY,\n--     name VARCHAR(100) NOT NULL,\n--     status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),\n--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n-- );" > $(MIGRATION_PATH)/$(TIMESTAMP)_$$name.01.up.sql; \
		printf "-- Write your rollback SQL here\n-- Example:\n-- DROP TABLE example;" > $(MIGRATION_PATH)/$(TIMESTAMP)_$$name.02.down.sql; \
	fi; \
	echo "Created migration files:"; \
	echo "  $(MIGRATION_PATH)/$(TIMESTAMP)_$$name.01.up.sql"; \
	echo "  $(MIGRATION_PATH)/$(TIMESTAMP)_$$name.02.down.sql"

migrate-seed-permissions: 
	go run cmd/migration/main.go $(ENV) seed permissions


# Docker commands
docker-build: ## Build Docker image
	docker build -t $(DOCKER_IMAGE) .

docker-run: ## Run with Docker Compose
	docker-compose up -d

docker-run-build: ## Build and run with Docker Compose
	docker-compose up -d --build

docker-logs: ## View Docker logs
	docker-compose logs -f

docker-stop: ## Stop Docker containers
	docker-compose down

docker-clean: ## Clean Docker containers and volumes
	docker-compose down -v
	docker system prune -f

# Development environment
dev-setup: deps ## Setup development environment
	@echo "Setting up development environment..."
	@if ! command -v golangci-lint &> /dev/null; then \
		echo "Installing golangci-lint..."; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
	fi
	@if ! command -v goimports &> /dev/null; then \
		echo "Installing goimports..."; \
		go install golang.org/x/tools/cmd/goimports@latest; \
	fi
	@echo "Development environment setup complete!"

dev-start: ## Start development environment
	docker-compose up -d postgres redis
	sleep 5
	make migrate-up ENV=local
	make run ENV=local

dev-stop: ## Stop development environment
	docker-compose down

# Production-like commands
build-prod: ## Build for production
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-w -s" -o bin/server cmd/server/main.go
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-w -s" -o bin/migrate cmd/migration/main.go
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-w -s" -o bin/worker cmd/worker/main.go

# API testing
api-test: ## Test API endpoints (requires server to be running)
	@echo "Testing health endpoint..."
	curl -f http://localhost:8080/health || (echo "Health check failed" && exit 1)
	@echo "Testing readiness endpoint..."
	curl -f http://localhost:8080/health/ready || (echo "Readiness check failed" && exit 1)
	@echo "API tests passed!"

# Performance testing
load-test: ## Run load tests (requires k6)
	@if command -v k6 &> /dev/null; then \
		k6 run acceptance-test/load-test.js; \
	else \
		echo "k6 not installed. Install from https://k6.io/docs/getting-started/installation/"; \
	fi

# Security
security-scan: ## Run security scanner
	@if command -v gosec &> /dev/null; then \
		gosec ./...; \
	else \
		echo "Installing gosec..."; \
		go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest; \
		gosec ./...; \
	fi

# Git hooks
install-hooks: ## Install git hooks
	@echo "Installing git hooks..."
	@echo '#!/bin/sh\nmake fmt lint test' > .git/hooks/pre-commit
	@chmod +x .git/hooks/pre-commit
	@echo "Git hooks installed!"

# All-in-one commands
check: fmt lint vet test ## Run all code quality checks
	@echo "All checks passed!"

ci: deps check build ## Run CI pipeline locally
	@echo "CI pipeline completed successfully!"

release: clean build-prod docker-build ## Prepare release
	@echo "Release artifacts ready!"

# Default target
.DEFAULT_GOAL := help