# Blacking API Documentation

## Base URL
```
http://localhost:8080/api/v1
```

## Authentication

### Admin JWT Token
```http
Authorization: Bearer <admin_jwt_token>
```

### Member JWT Token  
```http
Authorization: Bearer <member_jwt_token>
```

---

## 1. Health Check

### GET /health
**Description**: Basic health check  
**Authentication**: None  

**Response**:
```json
{
  "status": "ok",
  "timestamp": "2025-01-24T10:30:00Z"
}
```

### GET /health/ready
**Description**: Readiness probe  
**Authentication**: None  

**Response**:
```json
{
  "status": "ready",
  "database": "connected",
  "timestamp": "2025-01-24T10:30:00Z"
}
```

---

## 2. User Management (Admin)

### POST /api/v1/users/login
**Description**: Admin user login  
**Authentication**: None  

**Request**:
```json
{
  "username": "admin",
  "password": "password123"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-01-24T12:30:00Z",
    "user": {
      "id": "1",
      "username": "admin",
      "first_name": "Admin",
      "last_name": "User",
      "status": "active",
      "user_role_id": 1,
      "user_role_name": "Super Admin",
      "is_enable": true,
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-24T10:30:00Z"
    }
  }
}
```

### POST /api/v1/users
**Description**: Create new user  
**Authentication**: Admin JWT  

**Request**:
```json
{
  "username": "newuser",
  "password": "password123",
  "first_name": "John",
  "last_name": "Doe",
  "user_role_id": 2,
  "is_enable": true
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "2",
    "username": "newuser",
    "first_name": "John",
    "last_name": "Doe",
    "status": "active",
    "user_role_id": 2,
    "user_role_name": "Admin",
    "is_enable": true,
    "created_at": "2025-01-24T10:30:00Z",
    "updated_at": "2025-01-24T10:30:00Z"
  }
}
```

### GET /api/v1/users
**Description**: List all users with pagination  
**Authentication**: Admin JWT  

**Query Parameters**:
- `page` (optional): Page number, default: 1
- `limit` (optional): Items per page, default: 10, max: 100
- `search` (optional): Search by username, first_name, or last_name
- `status` (optional): Filter by status (active, inactive, suspended)
- `user_role_id` (optional): Filter by user role ID

**Response**:
```json
{
  "message": "Users retrieved successfully",
  "content": [
    {
      "id": "1",
      "username": "admin",
      "first_name": "Admin",
      "last_name": "User",
      "status": "active",
      "user_role_id": 1,
      "user_role_name": "Super Admin",
      "is_enable": true,
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-24T10:30:00Z"
    }
  ],
  "page": 1,
  "limit": 10,
  "totalPages": 1,
  "totalItems": 1
}
```

### GET /api/v1/users/:id
**Description**: Get user by ID  
**Authentication**: Admin JWT  

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "1",
    "username": "admin",
    "first_name": "Admin",
    "last_name": "User",
    "status": "active",
    "user_role_id": 1,
    "user_role_name": "Super Admin",
    "is_enable": true,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-24T10:30:00Z"
  }
}
```

### PUT /api/v1/users/:id
**Description**: Update user  
**Authentication**: Admin JWT  

**Request**:
```json
{
  "first_name": "Updated Name",
  "last_name": "Updated Last",
  "user_role_id": 2,
  "is_enable": true
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "1",
    "username": "admin",
    "first_name": "Updated Name",
    "last_name": "Updated Last",
    "status": "active",
    "user_role_id": 2,
    "user_role_name": "Admin",
    "is_enable": true,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-24T10:35:00Z"
  }
}
```

### PATCH /api/v1/users/:id/password
**Description**: Change user password  
**Authentication**: Admin JWT  

**Request**:
```json
{
  "new_password": "newpassword123"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Password updated successfully"
}
```

---

## 3. Member Management

### POST /api/v1/members
**Description**: Create new member  
**Authentication**: Admin JWT  

**Request**:
```json
{
  "username": "member001",
  "password": "password123",
  "game_username": "game001",
  "game_password": "gamepass123",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "**********",
  "gender": "male",
  "bank_code": "BBL",
  "bank_number": "**********",
  "member_group_id": 1,
  "referral_group_id": 1,
  "channel_id": 1,
  "platform_id": 1,
  "is_partner": false
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "1",
    "username": "member001",
    "game_username": "game001",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "**********",
    "gender": "male",
    "bank_code": "BBL",
    "bank_number": "**********",
    "balance": 0.0,
    "refer_code": "REF001",
    "login_status": false,
    "operate_status": true,
    "register_status": true,
    "is_enable": true,
    "member_group_id": 1,
    "referral_group_id": 1,
    "channel_id": 1,
    "platform_id": 1,
    "is_partner": false,
    "status": "active",
    "created_at": "2025-01-24T10:30:00Z",
    "updated_at": "2025-01-24T10:30:00Z"
  }
}
```

### GET /api/v1/members
**Description**: List members with pagination and filters  
**Authentication**: Admin JWT  

**Query Parameters**:
- `page` (optional): Page number, default: 1
- `limit` (optional): Items per page, default: 10, max: 100
- `search` (optional): Search by username, first_name, last_name, phone
- `status` (optional): Filter by status (active, inactive)
- `member_group_id` (optional): Filter by member group
- `is_partner` (optional): Filter by partner status (true/false)
- `login_status` (optional): Filter by login status (true/false)

**Response**:
```json
{
  "message": "Members retrieved successfully",
  "content": [
    {
      "id": "1",
      "username": "member001",
      "game_username": "game001",
      "first_name": "John",
      "last_name": "Doe",
      "phone": "**********",
      "gender": "male",
      "balance": 1250.50,
      "refer_code": "REF001",
      "login_status": true,
      "operate_status": true,
      "register_status": true,
      "is_enable": true,
      "last_online": "2025-01-24T09:30:00Z",
      "register_ip": "*************",
      "last_login_ip": "*************",
      "status": "active",
      "created_at": "2025-01-24T10:30:00Z",
      "updated_at": "2025-01-24T10:30:00Z"
    }
  ],
  "page": 1,
  "limit": 10,
  "totalPages": 5,
  "totalItems": 50
}
```

---

## 4. Member Authentication

### POST /api/v1/member-auth/register/send-otp
**Description**: Send OTP for member registration  
**Authentication**: None  

**Request**:
```json
{
  "phone": "**********"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "reference": "ABC123",
    "expires_at": "2025-01-24T10:35:00Z"
  },
  "message": "OTP sent successfully"
}
```

### POST /api/v1/member-auth/register/verify-otp
**Description**: Verify registration OTP  
**Authentication**: None  

**Request**:
```json
{
  "phone": "**********",
  "code": "123456",
  "reference": "ABC123"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "token": "temp_registration_token_here",
    "expires_at": "2025-01-24T11:00:00Z"
  },
  "message": "OTP verified successfully"
}
```

### POST /api/v1/member-auth/register/complete
**Description**: Complete member registration  
**Authentication**: None (requires temp token from OTP verification)  

**Request**:
```json
{
  "token": "temp_registration_token_here",
  "username": "newmember",
  "password": "password123",
  "game_username": "gamenew",
  "game_password": "gamepass123",
  "first_name": "Jane",
  "last_name": "Doe",
  "gender": "female",
  "bank_code": "SCB",
  "bank_number": "**********",
  "register_refer_code": "REF001"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "member": {
      "id": "2",
      "username": "newmember",
      "game_username": "gamenew",
      "first_name": "Jane",
      "last_name": "Doe",
      "phone": "**********",
      "gender": "female",
      "bank_code": "SCB",
      "bank_number": "**********",
      "balance": 0.0,
      "refer_code": "REF002",
      "register_refer_code": "REF001",
      "status": "active",
      "created_at": "2025-01-24T10:45:00Z"
    }
  },
  "message": "Registration completed successfully"
}
```

### POST /api/v1/member-auth/login
**Description**: Member login  
**Authentication**: None  

**Request**:
```json
{
  "username": "member001",
  "password": "password123"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-01-24T12:30:00Z",
    "member": {
      "id": "1",
      "username": "member001",
      "game_username": "game001",
      "first_name": "John",
      "last_name": "Doe",
      "phone": "**********",
      "balance": 1250.50,
      "refer_code": "REF001",
      "status": "active"
    }
  }
}
```

---

## 5. System Settings

### GET /api/v1/system/settings
**Description**: List all system settings  
**Authentication**: Admin JWT  

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "key": "site_name",
      "value": "Blacking Casino",
      "description": "Website name",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-24T10:30:00Z"
    },
    {
      "id": 2,
      "key": "min_deposit",
      "value": "100",
      "description": "Minimum deposit amount",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-24T10:30:00Z"
    }
  ]
}
```

### GET /api/v1/system/settings/:key
**Description**: Get system setting by key  
**Authentication**: Admin JWT  

**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "key": "site_name",
    "value": "Blacking Casino",
    "description": "Website name",
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-24T10:30:00Z"
  }
}
```

### PUT /api/v1/system/settings/:key
**Description**: Update system setting  
**Authentication**: Admin JWT  

**Request**:
```json
{
  "value": "New Casino Name",
  "description": "Updated website name"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "key": "site_name",
    "value": "New Casino Name",
    "description": "Updated website name",
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-24T10:35:00Z"
  }
}
```

### GET /api/v1/system/settings/general
**Description**: Get general settings grouped  
**Authentication**: Admin JWT  

**Response**:
```json
{
  "success": true,
  "data": {
    "general": {
      "site_name": "Blacking Casino",
      "site_description": "Best online casino",
      "site_keywords": "casino, gambling, slots",
      "timezone": "Asia/Bangkok"
    },
    "deposit_withdraw": {
      "min_deposit": "100",
      "max_deposit": "100000",
      "min_withdraw": "200",
      "max_withdraw": "50000",
      "withdraw_fee": "0"
    },
    "analytics": {
      "google_analytics_id": "GA-XXXXXXXXX",
      "facebook_pixel_id": "FB-XXXXXXXXX"
    },
    "other": {
      "maintenance_mode": "false",
      "registration_enabled": "true"
    }
  }
}
```

---

## 6. Banner Management

### POST /api/v1/banners
**Description**: Create new banner  
**Authentication**: Admin JWT  

**Request**:
```json
{
  "name": "Welcome Banner",
  "type": "image",
  "link_url": "https://example.com/promotion",
  "image_url": "/uploads/banners/banner1.jpg",
  "position": 1,
  "status": "active"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Welcome Banner",
    "type": "image",
    "link_url": "https://example.com/promotion",
    "image_url": "/uploads/banners/banner1.jpg",
    "position": 1,
    "status": "active",
    "created_at": "2025-01-24T10:30:00Z",
    "updated_at": "2025-01-24T10:30:00Z"
  }
}
```

### GET /api/v1/banners
**Description**: List banners with pagination  
**Authentication**: Admin JWT  

**Query Parameters**:
- `page` (optional): Page number, default: 1
- `limit` (optional): Items per page, default: 10
- `status` (optional): Filter by status (active, inactive)
- `type` (optional): Filter by type (image, link)

**Response**:
```json
{
  "message": "Banners retrieved successfully",
  "content": [
    {
      "id": 1,
      "name": "Welcome Banner",
      "type": "image",
      "link_url": "https://example.com/promotion",
      "image_url": "/uploads/banners/banner1.jpg",
      "position": 1,
      "status": "active",
      "created_at": "2025-01-24T10:30:00Z",
      "updated_at": "2025-01-24T10:30:00Z"
    }
  ],
  "page": 1,
  "limit": 10,
  "totalPages": 1,
  "totalItems": 3
}
```

---

## 7. Member Groups

### POST /api/v1/member-groups
**Description**: Create member group  
**Authentication**: Admin JWT  

**Request**:
```json
{
  "code": "VIP1",
  "name": "VIP Level 1",
  "member_group_type_id": 1,
  "min_deposit": 1000.0,
  "min_withdraw": 500.0,
  "max_deposit": 100000.0,
  "commission_group_id": 1,
  "deposit_turnover_type": "multiplier",
  "deposit_turnover_amount": 1.5,
  "deposit_turnover_release_type": "percentage",
  "deposit_turnover_release_amount": 100.0,
  "daily_withdraw_limit": 3,
  "daily_withdraw_amount_limit": 50000.0,
  "calculate_min_deposit": true,
  "is_vip": true,
  "status": "active"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "code": "VIP1",
    "name": "VIP Level 1",
    "member_group_type_id": 1,
    "min_deposit": 1000.0,
    "min_withdraw": 500.0,
    "max_deposit": 100000.0,
    "is_default": false,
    "is_vip": true,
    "daily_withdraw_limit": 3,
    "daily_withdraw_amount_limit": 50000.0,
    "commission_group_id": 1,
    "deposit_turnover_type": "multiplier",
    "deposit_turnover_amount": 1.5,
    "deposit_turnover_release_type": "percentage",
    "deposit_turnover_release_amount": 100.0,
    "calculate_min_deposit": true,
    "image": null,
    "status": "active",
    "created_at": "2025-01-24T10:30:00Z",
    "updated_at": "2025-01-24T10:30:00Z"
  }
}
```

### GET /api/v1/member-groups
**Description**: List member groups  
**Authentication**: Admin JWT  

**Response**:
```json
{
  "message": "Member groups retrieved successfully",
  "content": [
    {
      "id": 1,
      "code": "VIP1",
      "name": "VIP Level 1",
      "member_group_type_id": 1,
      "min_deposit": 1000.0,
      "min_withdraw": 500.0,
      "max_deposit": 100000.0,
      "is_default": false,
      "is_vip": true,
      "status": "active",
      "created_at": "2025-01-24T10:30:00Z",
      "updated_at": "2025-01-24T10:30:00Z"
    }
  ],
  "page": 1,
  "limit": 10,
  "totalPages": 1,
  "totalItems": 5
}
```

### GET /api/v1/member-groups/code/:code
**Description**: Get member group by code
**Authentication**: Admin JWT

**Parameters**:
- `code` (string): Member group code

**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "code": "VIP_GOLD",
    "name": "VIP Gold",
    "member_group_type_id": 1,
    "position": 1,
    "display_name": "VIP Gold Member",
    "description": "VIP Gold member group",
    "is_default": false,
    "status": "active",
    "created_at": "2025-01-24T10:30:00Z",
    "updated_at": "2025-01-24T10:30:00Z"
  }
}
```

---

## 8. Member Group Types

### PUT /api/v1/member-group-types/:id
**Description**: Update member group type with image deletion support
**Authentication**: Admin JWT

**Parameters**:
- `id` (integer): Member group type ID

**Request**:
```json
{
  "name": "VIP Premium",
  "position": 1,
  "show_in_lobby": true,
  "badge_bg_color": "#ffcc00",
  "badge_border_color": "#000000",
  "image1": "https://example.com/new-image1.jpg",
  "image2": null,
  "bg_image": "https://example.com/new-bg.jpg",
  "image1_delete": false,
  "image2_delete": "true",
  "bg_image_delete": "1",
  "vip_enabled": true,
  "bonus_enabled": true,
  "cashback_enabled": true
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "VIP Premium",
    "position": 1,
    "show_in_lobby": true,
    "badge_bg_color": "#ffcc00",
    "badge_border_color": "#000000",
    "image1": "https://example.com/new-image1.jpg",
    "image2": null,
    "bg_image": "https://example.com/new-bg.jpg",
    "status": "active",
    "created_at": "2025-01-24T10:30:00Z",
    "updated_at": "2025-01-24T10:35:00Z"
  }
}
```

**Image Deletion Logic**:
- `image1_delete: true|"true"|"1"` - Delete current image1 file and set field to null
- `image2_delete: true|"true"|"1"` - Delete current image2 file and set field to null
- `bg_image_delete: true|"true"|"1"` - Delete current bg_image file and set field to null
- If new image URL provided while old exists, old file is automatically deleted
- Delete flags take precedence over new image URLs
- Accepts boolean `true`/`false` or string `"true"`/`"false"`/`"1"`/`"0"`

---

## 9. Banking Accounts

### POST /api/v1/deposit-account
**Description**: Create deposit account  
**Authentication**: None (Internal system)  

**Request**:
```json
{
  "banking_id": 1,
  "payment_method_id": 1,
  "algorithm_id": 1,
  "auto_bot_id": 1,
  "account_name": "นายสมชาย ใจดี",
  "account_name_display": "สมชาย ใ.",
  "account_number": "**********",
  "phone_number": "**********",
  "auto_transfer": true,
  "push_bullet_nickname": "deposit_bot",
  "push_bullet_token": "pb_token_here",
  "active": true
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "banking_id": 1,
    "banking_name": "กสิกรไทย",
    "banking_image_url": "/uploads/banks/kbank.png",
    "payment_method_id": 1,
    "algorithm_id": 1,
    "algorithm_name": "Round Robin",
    "auto_bot_id": 1,
    "account_name": "นายสมชาย ใจดี",
    "account_name_display": "สมชาย ใ.",
    "account_number": "**********",
    "phone_number": "**********",
    "auto_transfer": true,
    "push_bullet_nickname": "deposit_bot",
    "push_bullet_token": "pb_token_here",
    "active": true,
    "inactive": false,
    "created_at": "2025-01-24T10:30:00Z",
    "updated_at": "2025-01-24T10:30:00Z"
  }
}
```

### GET /api/v1/deposit-account
**Description**: Get all deposit accounts  
**Authentication**: None (Internal system)  

**Query Parameters**:
- `active` (optional): Filter by active status (true/false)
- `banking_id` (optional): Filter by banking ID
- `payment_method_id` (optional): Filter by payment method ID

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "banking_id": 1,
      "banking_name": "กสิกรไทย",
      "banking_image_url": "/uploads/banks/kbank.png",
      "payment_method_id": 1,
      "algorithm_id": 1,
      "algorithm_name": "Round Robin",
      "auto_bot_id": 1,
      "account_name": "นายสมชาย ใจดี",
      "account_name_display": "สมชาย ใ.",
      "account_number": "**********",
      "phone_number": "**********",
      "auto_transfer": true,
      "active": true,
      "inactive": false,
      "created_at": "2025-01-24T10:30:00Z",
      "updated_at": "2025-01-24T10:30:00Z"
    }
  ]
}
```

---

## 9. Reports

### GET /api/v1/reports/member-login
**Description**: Get member login report  
**Authentication**: Admin JWT  

**Query Parameters**:
- `page` (optional): Page number, default: 1
- `limit` (optional): Items per page, default: 10
- `start_date` (optional): Start date (YYYY-MM-DD)
- `end_date` (optional): End date (YYYY-MM-DD)
- `member_id` (optional): Filter by member ID
- `ip_address` (optional): Filter by IP address
- `status` (optional): Filter by login status (success, failed)

**Response**:
```json
{
  "message": "Member login report retrieved successfully",
  "content": [
    {
      "id": 1,
      "member_id": "1",
      "member_username": "member001",
      "ip_address": "*************",
      "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "login_status": "success",
      "login_time": "2025-01-24T10:30:00Z",
      "logout_time": "2025-01-24T12:30:00Z",
      "session_duration": 7200,
      "location": "Bangkok, Thailand"
    }
  ],
  "page": 1,
  "limit": 10,
  "totalPages": 10,
  "totalItems": 95
}
```

---

## Error Responses

### Validation Error
```json
{
  "errors": [
    {
      "field": "username",
      "message": "Username is required"
    },
    {
      "field": "password",
      "message": "Password must be at least 8 characters"
    }
  ]
}
```

### Authentication Error
```json
{
  "message": "Unauthorized access",
  "status": 401
}
```

### Not Found Error
```json
{
  "message": "User not found",
  "status": 404
}
```

### Server Error
```json
{
  "message": "Internal server error",
  "status": 500
}
```

---

## Common Data Types

### Status Enum
- `"active"` - Active/enabled
- `"inactive"` - Inactive/disabled  
- `"suspended"` - Suspended/blocked

### Gender Enum
- `"male"` - Male
- `"female"` - Female
- `"other"` - Other/prefer not to say

### OTP Type Enum
- `"phone"` - SMS OTP
- `"email"` - Email OTP

### OTP Purpose Enum
- `"member_registration"` - Member registration
- `"password_reset"` - Password reset
- `"2fa"` - Two-factor authentication

### Deposit Turnover Type Enum
- `"nil"` - No turnover requirement
- `"multiplier"` - Multiplier-based (e.g., 1.5x)
- `"percentage"` - Percentage-based (e.g., 150%)

### Banner Type Enum
- `"image"` - Image banner
- `"link"` - Link banner

---

## File Upload Endpoints

### POST /api/v1/system/upload
**Description**: Upload system files  
**Authentication**: Admin JWT  
**Content-Type**: multipart/form-data

**Request**:
```
file: <binary_file_data>
type: "banner" | "avatar" | "document"
```

**Response**:
```json
{
  "success": true,
  "data": {
    "filename": "banner_20250124_103000.jpg",
    "url": "/uploads/banners/banner_20250124_103000.jpg",
    "size": 1024000,
    "type": "image/jpeg"
  }
}
```

### DELETE /api/v1/system/file
**Description**: Delete uploaded file  
**Authentication**: Admin JWT  

**Request**:
```json
{
  "filename": "banner_20250124_103000.jpg"
}
```

**Response**:
```json
{
  "success": true,
  "message": "File deleted successfully"
}
```