{"info": {"_postman_id": "blacking-api-users-collection", "name": "Blacking API - User Management", "description": "Postman collection for Blacking API User Management endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8080/api/v1", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/../../health", "host": ["{{base_url}}"], "path": ["..", "..", "health"]}, "description": "Basic health check endpoint"}}, {"name": "Health Ready", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/../../health/ready", "host": ["{{base_url}}"], "path": ["..", "..", "health", "ready"]}, "description": "Readiness check endpoint"}}, {"name": "Health Live", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/../../health/live", "host": ["{{base_url}}"], "path": ["..", "..", "health", "live"]}, "description": "Liveness check endpoint"}}], "description": "Health check endpoints (no authentication required)"}, {"name": "User Authentication", "item": [{"name": "User Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('jwt_token', response.token);", "        console.log('JWT token saved to collection variable');", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/users/login", "host": ["{{base_url}}"], "path": ["users", "login"]}, "description": "Login endpoint for admin users (no authentication required)"}}, {"name": "Refresh <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/users/refresh_token", "host": ["{{base_url}}"], "path": ["users", "refresh_token"]}, "description": "Refresh JWT token (requires valid JWT token)"}}], "description": "User authentication endpoints"}, {"name": "User Management", "item": [{"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"newuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"full_name\": \"New User\",\n    \"user_role_id\": 1,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}, "description": "Create a new user (requires authentication)"}}, {"name": "List Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/users?page=1&limit=10&search=&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "sort_by", "value": "created_at"}, {"key": "sort_order", "value": "desc"}]}, "description": "Get list of users with pagination and search (requires authentication)"}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/users/1", "host": ["{{base_url}}"], "path": ["users", "1"]}, "description": "Get specific user by ID (requires authentication)"}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"updateduser\",\n    \"email\": \"<EMAIL>\",\n    \"full_name\": \"Updated User Name\",\n    \"user_role_id\": 2,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/users/1", "host": ["{{base_url}}"], "path": ["users", "1"]}, "description": "Update user information (requires authentication)"}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/users/1", "host": ["{{base_url}}"], "path": ["users", "1"]}, "description": "Delete user by ID (requires authentication)"}}], "description": "User CRUD operations (all require authentication)"}, {"name": "User Status Management", "item": [{"name": "Activate User", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/users/1/activate", "host": ["{{base_url}}"], "path": ["users", "1", "activate"]}, "description": "Activate a user account (requires authentication)"}}, {"name": "Deactivate User", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/users/1/deactivate", "host": ["{{base_url}}"], "path": ["users", "1", "deactivate"]}, "description": "Deactivate a user account (requires authentication)"}}, {"name": "Suspend User", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/users/1/suspend", "host": ["{{base_url}}"], "path": ["users", "1", "suspend"]}, "description": "Suspend a user account (requires authentication)"}}], "description": "User status management endpoints"}, {"name": "User Password Management", "item": [{"name": "Change User Password", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"oldpassword\",\n    \"new_password\": \"newpassword123\",\n    \"confirm_password\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/users/1/password", "host": ["{{base_url}}"], "path": ["users", "1", "password"]}, "description": "Change user password (requires authentication)"}}], "description": "User password management"}, {"name": "User Role Management", "item": [{"name": "Create User Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Manager\",\n    \"description\": \"Manager role with limited permissions\",\n    \"is_active\": true,\n    \"sort_order\": 2\n}"}, "url": {"raw": "{{base_url}}/user-roles", "host": ["{{base_url}}"], "path": ["user-roles"]}, "description": "Create a new user role (requires authentication)"}}, {"name": "List User Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/user-roles?page=1&limit=10&search=&sort_by=sort_order&sort_order=asc", "host": ["{{base_url}}"], "path": ["user-roles"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "sort_by", "value": "sort_order"}, {"key": "sort_order", "value": "asc"}]}, "description": "Get list of user roles with pagination (requires authentication)"}}, {"name": "List User Roles for Dropdown", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/user-roles/dropdown", "host": ["{{base_url}}"], "path": ["user-roles", "dropdown"]}, "description": "Get simplified list of user roles for dropdown menus (requires authentication)"}}, {"name": "Get User Role by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/user-roles/1", "host": ["{{base_url}}"], "path": ["user-roles", "1"]}, "description": "Get specific user role by ID (requires authentication)"}}, {"name": "Update User Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Senior Manager\",\n    \"description\": \"Senior manager role with extended permissions\",\n    \"is_active\": true,\n    \"sort_order\": 2\n}"}, "url": {"raw": "{{base_url}}/user-roles/1", "host": ["{{base_url}}"], "path": ["user-roles", "1"]}, "description": "Update user role information (requires authentication)"}}, {"name": "Delete User Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/user-roles/1", "host": ["{{base_url}}"], "path": ["user-roles", "1"]}, "description": "Delete user role by ID (requires authentication)"}}, {"name": "Clone User Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Cloned Manager Role\",\n    \"description\": \"Cloned from existing manager role\"\n}"}, "url": {"raw": "{{base_url}}/user-roles/1/clone", "host": ["{{base_url}}"], "path": ["user-roles", "1", "clone"]}, "description": "Clone an existing user role with its permissions (requires authentication)"}}], "description": "User role management endpoints"}, {"name": "User Role Bulk Operations", "item": [{"name": "Bulk List Lock IP", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/user-roles/bulk/lock-ip", "host": ["{{base_url}}"], "path": ["user-roles", "bulk", "lock-ip"]}, "description": "Get list of user roles with lock IP status (requires authentication)"}}, {"name": "Bulk List 2FA", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/user-roles/bulk/2fa", "host": ["{{base_url}}"], "path": ["user-roles", "bulk", "2fa"]}, "description": "Get list of user roles with 2FA status (requires authentication)"}}, {"name": "Bulk Toggle Lock IP", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"user_role_ids\": [1, 2, 3],\n    \"lock_ip\": true\n}"}, "url": {"raw": "{{base_url}}/user-roles/bulk/lock-ip", "host": ["{{base_url}}"], "path": ["user-roles", "bulk", "lock-ip"]}, "description": "Bulk toggle lock IP setting for multiple user roles (requires authentication)"}}, {"name": "Bulk Toggle 2FA", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"user_role_ids\": [1, 2, 3],\n    \"require_2fa\": true\n}"}, "url": {"raw": "{{base_url}}/user-roles/bulk/2fa", "host": ["{{base_url}}"], "path": ["user-roles", "bulk", "2fa"]}, "description": "Bulk toggle 2FA requirement for multiple user roles (requires authentication)"}}, {"name": "Reorder User Roles", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"reorder_data\": [\n        {\"id\": 1, \"sort_order\": 1},\n        {\"id\": 2, \"sort_order\": 2},\n        {\"id\": 3, \"sort_order\": 3}\n    ]\n}"}, "url": {"raw": "{{base_url}}/user-roles/reorder", "host": ["{{base_url}}"], "path": ["user-roles", "reorder"]}, "description": "Reorder user roles by updating sort_order (requires authentication)"}}], "description": "Bulk operations for user roles"}]}