# Server Configuration
SERVER_HOST=localhost
SERVER_PORT=8080
SERVER_MODE=debug
SERVER_READ_TIMEOUT=10
SERVER_WRITE_TIMEOUT=10


# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_USERNAME=postgres
# DATABASE_PASSWORD=123456789
# DATABASE_DBNAME=dev-pool
# DATABASE_SSL_MODE=disable
# DATABASE_SCHEMA=public

# SERVER DB 
DATABASE_HOST=db-blacking-dev-do-user-6617875-0.e.db.ondigitalocean.com
DATABASE_PORT=25061
DATABASE_USERNAME=doadmin
DATABASE_PASSWORD=AVNS_W3jIy_RuYqodxADn9I5
DATABASE_DBNAME=dev-pool
DATABASE_SSL_MODE=require
DATABASE_SCHEMA=public


DATABASE_MAX_OPEN_CONNS=25
DATABASE_MAX_IDLE_CONNS=25

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# JWT Configuration
JWT_SECRET=gIqIO4aeBUiMmsWGEZlm0wZvadt9c2II
JWT_EXPIRE_TIME=3600

# Local dev
DEV_USERNAME=developer
DEV_PASSWORD=s3cret

SMS_PROVIDER=thsms
THSMS_SENDER="WEB OK "
THSMS_TOKEN="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************.jxgdgDwXAqtaun6FYkajKIWEKhbuIKRGCMKXHHQvYZg"
THSMS_BASE_URL=https://thsms.com/api/

AWS_S3_BUCKET_NAME=blacking-images
AWS_S3_BUCKET_ACCESS_KEY_ID=********************
AWS_S3_BUCKET_SECRET_ACCESS_KEY=DyM0sFNblS8sh7G/m8rBdiGdrpaO39UHybxotcPz
AWS_S3_BUCKET_REGION=ap-southeast-1
AWS_S3_CLOUDFRONT_URL=https://cdn.irich.info