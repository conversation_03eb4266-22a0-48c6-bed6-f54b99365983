name: Blacking API Develop CI

on:
  push:
    branches:
      - develop

jobs:
  build:
    name: Build Image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.24'
          cache: true

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v4
        with:
          context: ./
          file: ./Dockerfile
          builder: ${{ steps.buildx.outputs.name }}
          push: true
          tags: devblacking3/blacking-api:dev-latest

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

  rollout:
    needs: [ build ]
    name: Restart Deployment
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
      # Set up the Kubernetes CLI with your DigitalOcean Kubernetes cluster.
      - name: Set up kubectl
        uses: matootie/dokube@v1.4.1
        with:
          personalAccessToken: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          clusterName: k8s-blacking
          version: "1.20.15"
      # Run any kubectl commands you want!
      - name: Restart Deployment
        run: kubectl rollout restart deployment blacking-api-dev --namespace dev