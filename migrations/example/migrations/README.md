# Database Migrations

This directory contains database migration files for the Blacking AG API.

## Migration Structure

### Current Migrations
- `01_create_init_tables.go` - Complete initial database schema

### Backup
- `backup/` - Contains old migration files for reference

## Database Schema Overview

The migration creates a complete agent service database schema with the following components:

### Admin System
- `admin_roles` - Admin role definitions with permissions
- `admins` - Admin users (separate from agent users)

### User System  
- `user_roles` - Roles for agent/player users
- `user_role_permissions` - Granular permissions
- `users` - Agent and player users (linked to agents table)

### Agent System
- `agents` - Agent hierarchy (Company → SSR → SR → MA → AG → PLY)
- `commission_distribution` - Commission percentage settings
- `commission_history` - Commission transaction records
- `credit_transactions` - Credit transfer records

### Default Data
- **Admin Roles**: superadmin, admin, manager, operator
- **Default Admin**: username=`superadmin`, password=`12341234`

## Usage

```bash
# Run migrations (fresh install)
make run-migration ENV=local

# Reset database (drops all tables)
make db-reset ENV=local
```

## Fresh Installation

This migration structure is designed for fresh installations. If you have existing data:

1. Backup your current database
2. Drop existing tables (or use a new database)
3. Run the migration

## Key Features

- **Separation of Concerns**: Admin users separate from agent users
- **Complete Agent Hierarchy**: Supports full agent tree structure
- **Commission System**: Track and distribute commissions
- **Credit Management**: Transfer and track credit transactions
- **Audit Trail**: Created/updated timestamps on all tables
- **Optimized Indexes**: Performance-optimized database indexes

## Migration History

Previous migration files (02-05) have been consolidated into 01 for cleaner initial setup.
Backup files are available in `backup/` directory for reference.