package list

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"log"
	"os"
)

type CreateInitTables struct{}

func (m *CreateInitTables) GetName() string {
	return "CreateInitTables"
}

func (m *CreateInitTables) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Create schema if not exists (except public)
	if schemaName != "public" {
		log.Printf("Migration: Creating schema '%s'", schemaName)
		_, err := con.Exec(ctx, fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName))
		if err != nil {
			panic(fmt.Sprintf("Failed to create schema %s: %v", schemaName, err))
		}
		log.Printf("Migration: Schema '%s' created successfully", schemaName)
	}

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration: search_path set successfully")

	// Verify current schema
	var currentSchema string
	err = con.QueryRow(ctx, "SELECT current_schema()").Scan(&currentSchema)
	if err == nil {
		log.Printf("Migration: Current schema is '%s'", currentSchema)
	}

	// ===== ADMIN SYSTEM TABLES =====

	// Create admin_roles table for admin-specific roles
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS admin_roles (
			id SERIAL PRIMARY KEY,
			name VARCHAR(100) UNIQUE NOT NULL,
			display_name VARCHAR(200) NOT NULL,
			description TEXT,
			permissions JSON,
			is_system_role BOOLEAN DEFAULT FALSE,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create admins table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS admins (
			id SERIAL PRIMARY KEY,
			username VARCHAR(50) UNIQUE NOT NULL,
			email VARCHAR(100) UNIQUE,
			password VARCHAR(255) NOT NULL,
			first_name VARCHAR(100) NOT NULL,
			last_name VARCHAR(100) NOT NULL,
			admin_role_id INT NOT NULL,
			permissions JSON,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
			last_login TIMESTAMP NULL,
			last_ip VARCHAR(45) NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (admin_role_id) REFERENCES admin_roles(id) ON DELETE RESTRICT
		)
	`)
	if err != nil {
		panic(err)
	}

	// ===== USER SYSTEM TABLES =====

	// Create user_roles table for agent/player roles
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_roles (
			id SERIAL PRIMARY KEY,
			position INT NULL,
			name VARCHAR(255) NULL,
			is_2fa BOOLEAN DEFAULT FALSE,
			is_lock_ip BOOLEAN DEFAULT FALSE,
			is_enable BOOLEAN DEFAULT TRUE,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create user_role_permissions table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_role_permissions (
			id SERIAL PRIMARY KEY,
			user_role_id INT NULL,
			name VARCHAR(255) NULL,
			key VARCHAR(100) NULL,
			can_create BOOLEAN DEFAULT FALSE,
			can_view BOOLEAN DEFAULT FALSE,
			can_edit BOOLEAN DEFAULT FALSE,
			can_delete BOOLEAN DEFAULT FALSE,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create users table (for agents/players)
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS users (
			id SERIAL PRIMARY KEY,
			username VARCHAR(50) UNIQUE NOT NULL,
			first_name VARCHAR(100) NOT NULL,
			last_name VARCHAR(100) NOT NULL,
			password VARCHAR(255) NOT NULL,
			user_role_name VARCHAR(50) NULL,
			user_role_id INT NOT NULL,
			agent_code VARCHAR(50) UNIQUE,
			user_type VARCHAR(20) DEFAULT 'agent' CHECK (user_type IN ('agent', 'player')),
			last_online TIMESTAMP NULL,
			last_login TIMESTAMP NULL,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended'))
		)
	`)
	if err != nil {
		panic(err)
	}

	// ===== AGENT SYSTEM TABLES =====

	// Create agents table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS agents (
			id SERIAL PRIMARY KEY,
			line_code VARCHAR(50) UNIQUE NOT NULL,
			parent_code VARCHAR(50) NULL,
			username VARCHAR(100) UNIQUE NOT NULL,
			password VARCHAR(255) NOT NULL,
			full_name VARCHAR(200) NOT NULL,
			email VARCHAR(100) NULL,
			phone VARCHAR(20) NULL,
			agent_type VARCHAR(10) NOT NULL CHECK (agent_type IN ('PLY', 'AG', 'MA', 'SR', 'SSR', 'Company')),
			commission_percent DECIMAL(5,2) DEFAULT 0.00,
			commission_remaining DECIMAL(5,2) DEFAULT 0.00,
			credit_limit DECIMAL(15,2) DEFAULT 0.00,
			current_credit DECIMAL(15,2) DEFAULT 0.00,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
			level_depth INTEGER DEFAULT 1,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			last_login TIMESTAMP NULL,
			last_ip VARCHAR(45) NULL,
			FOREIGN KEY (parent_code) REFERENCES agents(line_code) ON DELETE RESTRICT
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create commission_distribution table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS commission_distribution (
			id SERIAL PRIMARY KEY,
			agent_code VARCHAR(50) NOT NULL,
			parent_code VARCHAR(50) NULL,
			commission_percent DECIMAL(5,2) NOT NULL,
			previous_percent DECIMAL(5,2) NULL,
			effective_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			created_by VARCHAR(50) NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE,
			FOREIGN KEY (parent_code) REFERENCES agents(line_code) ON DELETE SET NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create commission_history table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS commission_history (
			id SERIAL PRIMARY KEY,
			transaction_id VARCHAR(100) NOT NULL,
			agent_code VARCHAR(50) NOT NULL,
			parent_code VARCHAR(50) NULL,
			level_depth INTEGER NOT NULL,
			commission_percent DECIMAL(5,2) NOT NULL,
			transaction_amount DECIMAL(15,2) NOT NULL,
			commission_amount DECIMAL(15,2) NOT NULL,
			commission_type VARCHAR(20) DEFAULT 'standard' CHECK (commission_type IN ('standard', 'bonus', 'penalty')),
			status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'settled', 'cancelled')),
			settled_at TIMESTAMP NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE,
			FOREIGN KEY (parent_code) REFERENCES agents(line_code) ON DELETE SET NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create credit_transactions table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS credit_transactions (
			id SERIAL PRIMARY KEY,
			transaction_id VARCHAR(100) UNIQUE NOT NULL,
			from_agent_code VARCHAR(50) NULL,
			to_agent_code VARCHAR(50) NOT NULL,
			transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('transfer', 'deposit', 'withdraw', 'commission')),
			amount DECIMAL(15,2) NOT NULL,
			balance_before DECIMAL(15,2) NOT NULL,
			balance_after DECIMAL(15,2) NOT NULL,
			reference VARCHAR(255) NULL,
			description TEXT NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled', 'failed')),
			processed_at TIMESTAMP NULL,
			processed_by VARCHAR(50) NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (from_agent_code) REFERENCES agents(line_code) ON DELETE SET NULL,
			FOREIGN KEY (to_agent_code) REFERENCES agents(line_code) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(err)
	}

	// ===== CREATE INDEXES =====

	// Admin indexes
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_admins_username ON admins (username)",
		"CREATE INDEX IF NOT EXISTS idx_admins_email ON admins (email)",
		"CREATE INDEX IF NOT EXISTS idx_admins_status ON admins (status)",
		"CREATE INDEX IF NOT EXISTS idx_admins_admin_role_id ON admins (admin_role_id)",
		"CREATE INDEX IF NOT EXISTS idx_admin_roles_name ON admin_roles (name)",
		"CREATE INDEX IF NOT EXISTS idx_admin_roles_status ON admin_roles (status)",

		// User indexes
		"CREATE INDEX IF NOT EXISTS idx_users_id ON users (id)",
		"CREATE INDEX IF NOT EXISTS idx_users_status ON users (status)",
		"CREATE INDEX IF NOT EXISTS idx_users_username ON users (username)",
		"CREATE INDEX IF NOT EXISTS idx_users_user_role_id ON users (user_role_id)",
		"CREATE INDEX IF NOT EXISTS idx_users_agent_code ON users (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_users_user_type ON users (user_type)",
		"CREATE INDEX IF NOT EXISTS idx_users_created_at ON users (created_at)",

		// Agent indexes
		"CREATE INDEX IF NOT EXISTS idx_agents_line_code ON agents (line_code)",
		"CREATE INDEX IF NOT EXISTS idx_agents_parent_code ON agents (parent_code)",
		"CREATE INDEX IF NOT EXISTS idx_agents_username ON agents (username)",
		"CREATE INDEX IF NOT EXISTS idx_agents_agent_type ON agents (agent_type)",
		"CREATE INDEX IF NOT EXISTS idx_agents_status ON agents (status)",
		"CREATE INDEX IF NOT EXISTS idx_agents_level_depth ON agents (level_depth)",

		// Commission indexes
		"CREATE INDEX IF NOT EXISTS idx_commission_distribution_agent_code ON commission_distribution (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_commission_distribution_parent_code ON commission_distribution (parent_code)",
		"CREATE INDEX IF NOT EXISTS idx_commission_distribution_status ON commission_distribution (status)",
		"CREATE INDEX IF NOT EXISTS idx_commission_history_agent_code ON commission_history (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_commission_history_transaction_id ON commission_history (transaction_id)",
		"CREATE INDEX IF NOT EXISTS idx_commission_history_status ON commission_history (status)",
		"CREATE INDEX IF NOT EXISTS idx_commission_history_created_at ON commission_history (created_at)",

		// Credit indexes
		"CREATE INDEX IF NOT EXISTS idx_credit_transactions_transaction_id ON credit_transactions (transaction_id)",
		"CREATE INDEX IF NOT EXISTS idx_credit_transactions_from_agent_code ON credit_transactions (from_agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_credit_transactions_to_agent_code ON credit_transactions (to_agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_credit_transactions_transaction_type ON credit_transactions (transaction_type)",
		"CREATE INDEX IF NOT EXISTS idx_credit_transactions_status ON credit_transactions (status)",
		"CREATE INDEX IF NOT EXISTS idx_credit_transactions_created_at ON credit_transactions (created_at)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(err)
		}
	}

	// Add foreign key constraint between users and agents (if both exist)
	// Check if constraint already exists
	var constraintExists bool
	constraintCheckQuery := `
		SELECT EXISTS (
			SELECT 1 FROM information_schema.table_constraints 
			WHERE constraint_name = 'fk_users_agent_code' 
			AND table_name = 'users'
			AND table_schema = current_schema()
		)
	`
	err = con.QueryRow(ctx, constraintCheckQuery).Scan(&constraintExists)
	if err != nil {
		log.Printf("Migration: Warning - could not check FK constraint existence: %v", err)
	}

	if !constraintExists {
		_, err = con.Exec(ctx, `
			ALTER TABLE users 
			ADD CONSTRAINT fk_users_agent_code 
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE SET NULL
		`)
		if err != nil {
			log.Printf("Migration: Warning - could not add FK constraint: %v", err)
		} else {
			log.Printf("Migration: FK constraint fk_users_agent_code added successfully")
		}
	} else {
		log.Printf("Migration: FK constraint fk_users_agent_code already exists")
	}
}

func (m *CreateInitTables) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop tables in reverse order
	tables := []string{
		"DROP TABLE IF EXISTS credit_transactions",
		"DROP TABLE IF EXISTS commission_history",
		"DROP TABLE IF EXISTS commission_distribution",
		"DROP TABLE IF EXISTS agents",
		"DROP TABLE IF EXISTS users",
		"DROP TABLE IF EXISTS user_role_permissions",
		"DROP TABLE IF EXISTS user_roles",
		"DROP TABLE IF EXISTS admins",
		"DROP TABLE IF EXISTS admin_roles",
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			panic(err)
		}
	}

	// Drop schema if not public and if empty
	if schemaName != "public" {
		_, err = con.Exec(ctx, fmt.Sprintf("DROP SCHEMA IF EXISTS %s", schemaName))
		// Ignore error if schema is not empty or doesn't exist
	}
}
