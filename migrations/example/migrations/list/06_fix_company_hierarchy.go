package list

import (
	"context"
	"os"

	"github.com/jackc/pgx/v5"
)

type FixCompanyHierarchy struct{}

func (m *FixCompanyHierarchy) GetName() string {
	return "FixCompanyHierarchy"
}

func (m *FixCompanyHierarchy) Up(con pgx.Tx) {
	ctx := context.Background()
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	// Set search path for schema
	_, err := con.Exec(ctx, "SET search_path TO "+schema)
	if err != nil {
		panic(err)
	}

	// Check if company agent exists but has no hierarchy record
	var companyExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS(
			SELECT 1 FROM agents WHERE line_code = 'bk1' AND agent_type = 'Company'
		)
	`).Scan(&companyExists)
	if err != nil {
		panic(err)
	}

	if !companyExists {
		// Company agent doesn't exist, skip
		return
	}

	// Check if hierarchy record already exists
	var hierarchyExists bool
	err = con.QueryRow(ctx, `
		SELECT EXISTS(
			SELECT 1 FROM agent_hierarchy WHERE agent_code = 'bk1' AND depth_level = 0
		)
	`).Scan(&hierarchyExists)
	if err != nil {
		panic(err)
	}

	if hierarchyExists {
		// Hierarchy record already exists, skip
		return
	}

	// Create missing hierarchy record for company agent
	hierarchyQuery := `
		INSERT INTO agent_hierarchy (
			agent_code,
			ancestor_code,
			path,
			depth_level,
			created_at
		) VALUES (
			$1, $2, $3, $4, NOW()
		)
	`

	// Company agent hierarchy record (self-reference with depth 0)
	_, err = con.Exec(ctx, hierarchyQuery,
		"bk1", // $1 - agent_code
		"bk1", // $2 - ancestor_code (self for root)
		"bk1", // $3 - path (just the line code for root)
		0,     // $4 - depth_level (0 for self-reference)
	)

	if err != nil {
		panic(err)
	}
}

func (m *FixCompanyHierarchy) Down(con pgx.Tx) {
	ctx := context.Background()
	schema := os.Getenv("DATABASE_SCHEMA")
	if schema == "" {
		schema = "public"
	}

	// Set search path for schema
	_, err := con.Exec(ctx, "SET search_path TO "+schema)
	if err != nil {
		panic(err)
	}

	// Remove the hierarchy record we added
	_, err = con.Exec(ctx, `
		DELETE FROM agent_hierarchy 
		WHERE agent_code = 'bk1' AND ancestor_code = 'bk1' AND depth_level = 0
	`)
	if err != nil {
		panic(err)
	}
}
