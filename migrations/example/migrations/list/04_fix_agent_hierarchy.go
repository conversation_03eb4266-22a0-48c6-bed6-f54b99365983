package list

import (
	"context"
	"github.com/jackc/pgx/v5"
)

type FixAgentHierarchy struct{}

func (m *FixAgentHierarchy) GetName() string {
	return "FixAgentHierarchy"
}

func (m *FixAgentHierarchy) Up(con pgx.Tx) {
	ctx := context.Background()
	// First, create hierarchy records for all existing agents that don't have them
	// This handles agents that were created before the hierarchy system was in place

	// For root agents (no parent_code)
	_, err := con.Exec(ctx, `
		INSERT INTO agent_hierarchy (agent_code, ancestor_code, depth_level, path)
		SELECT 
			line_code,
			line_code,
			0,
			line_code
		FROM agents 
		WHERE parent_code IS NULL
		AND line_code NOT IN (
			SELECT DISTINCT agent_code FROM agent_hierarchy WHERE depth_level = 0
		)
	`)
	if err != nil {
		panic(err)
	}

	// For agents with parents - we'll need to handle this recursively
	// Create a recursive CTE to build the hierarchy for existing agents
	_, err = con.Exec(ctx, `
		WITH RECURSIVE agent_paths AS (
			-- Base case: agents with parents that already have hierarchy records
			SELECT 
				a.line_code,
				a.parent_code,
				ah.path || '/' || a.line_code as new_path,
				a.level_depth
			FROM agents a
			JOIN agent_hierarchy ah ON a.parent_code = ah.agent_code AND ah.depth_level = 0
			WHERE a.parent_code IS NOT NULL
			AND a.line_code NOT IN (SELECT DISTINCT agent_code FROM agent_hierarchy WHERE depth_level = 0)
			
			UNION ALL
			
			-- Recursive case: agents whose parents now have paths
			SELECT 
				a.line_code,
				a.parent_code,
				ap.new_path || '/' || a.line_code as new_path,
				a.level_depth
			FROM agents a
			JOIN agent_paths ap ON a.parent_code = ap.line_code
			WHERE a.line_code NOT IN (SELECT DISTINCT agent_code FROM agent_hierarchy WHERE depth_level = 0)
		)
		INSERT INTO agent_hierarchy (agent_code, ancestor_code, depth_level, path)
		SELECT DISTINCT
			line_code,
			line_code,
			0,
			new_path
		FROM agent_paths
		WHERE line_code NOT IN (SELECT DISTINCT agent_code FROM agent_hierarchy WHERE depth_level = 0)
	`)
	if err != nil {
		// If the recursive approach fails, we'll create basic records for non-root agents
		// This is a fallback approach
		_, fallbackErr := con.Exec(ctx, `
			INSERT INTO agent_hierarchy (agent_code, ancestor_code, depth_level, path)
			SELECT 
				line_code,
				line_code,
				0,
				line_code
			FROM agents 
			WHERE parent_code IS NOT NULL
			AND line_code NOT IN (
				SELECT DISTINCT agent_code FROM agent_hierarchy WHERE depth_level = 0
			)
		`)
		if fallbackErr != nil {
			panic(fallbackErr)
		}
	}

	// Now create ancestor relationships for all agents
	_, err = con.Exec(ctx, `
		WITH RECURSIVE ancestor_tree AS (
			-- Start with direct parent relationships
			SELECT 
				a.line_code as child_code,
				a.parent_code as ancestor_code,
				1 as depth,
				ah.path
			FROM agents a
			JOIN agent_hierarchy ah ON a.line_code = ah.agent_code AND ah.depth_level = 0
			WHERE a.parent_code IS NOT NULL
			
			UNION ALL
			
			-- Recursively find all ancestors
			SELECT 
				at.child_code,
				a.parent_code as ancestor_code,
				at.depth + 1,
				at.path
			FROM ancestor_tree at
			JOIN agents a ON at.ancestor_code = a.line_code
			WHERE a.parent_code IS NOT NULL
		)
		INSERT INTO agent_hierarchy (agent_code, ancestor_code, depth_level, path)
		SELECT DISTINCT
			child_code,
			ancestor_code,
			depth,
			path
		FROM ancestor_tree
		WHERE NOT EXISTS (
			SELECT 1 FROM agent_hierarchy 
			WHERE agent_code = ancestor_tree.child_code 
			AND ancestor_code = ancestor_tree.ancestor_code
		)
	`)
	if err != nil {
		panic(err)
	}
}

func (m *FixAgentHierarchy) Down(con pgx.Tx) {
	// This migration fixes data, so rollback would remove all hierarchy records
	// which might not be desired. We'll leave the data as is.
	// If needed, the entire agent_hierarchy table can be dropped and recreated.
}
