package list

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"log"
	"os"
)

type CreateGameSystemTables struct{}

func (m *CreateGameSystemTables) GetName() string {
	return "CreateGameSystemTables"
}

func (m *CreateGameSystemTables) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Create game_providers table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS game_providers (
			id SERIAL PRIMARY KEY,
			provider_code VARCHAR(50) UNIQUE NOT NULL,
			provider_name VARCHAR(200) NOT NULL,
			category VARCHAR(20) NOT NULL CHECK (category IN ('sports', 'cards', 'slots')),
			company_percentage DECIMAL(5,2) NOT NULL CHECK (company_percentage >= 0 AND company_percentage <= 100),
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create game_providers table: %v", err))
	}
	log.Printf("Migration: game_providers table created successfully")

	// Create agent_game_commissions table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS agent_game_commissions (
			id SERIAL PRIMARY KEY,
			agent_code VARCHAR(50) NOT NULL,
			provider_code VARCHAR(50) NOT NULL,
			commission_percentage DECIMAL(5,2) NOT NULL CHECK (commission_percentage >= 0 AND commission_percentage <= 100),
			effective_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			effective_to TIMESTAMP NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_by VARCHAR(50) NULL,
			updated_by VARCHAR(50) NULL,
			remark TEXT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(agent_code, provider_code, effective_from),
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE,
			FOREIGN KEY (provider_code) REFERENCES game_providers(provider_code) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create agent_game_commissions table: %v", err))
	}
	log.Printf("Migration: agent_game_commissions table created successfully")

	// Create game_commission_history table for tracking commission calculations
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS game_commission_history (
			id SERIAL PRIMARY KEY,
			agent_code VARCHAR(50) NOT NULL,
			provider_code VARCHAR(50) NOT NULL,
			bet_reference VARCHAR(100) NOT NULL,
			bet_amount DECIMAL(15,2) NOT NULL,
			commission_percentage DECIMAL(5,2) NOT NULL,
			commission_amount DECIMAL(15,2) NOT NULL,
			calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			settlement_date TIMESTAMP NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'calculated' CHECK (status IN ('calculated', 'settled', 'cancelled')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE,
			FOREIGN KEY (provider_code) REFERENCES game_providers(provider_code) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create game_commission_history table: %v", err))
	}
	log.Printf("Migration: game_commission_history table created successfully")

	// Create indexes for better performance
	indexes := []string{
		// Game providers indexes
		"CREATE INDEX IF NOT EXISTS idx_game_providers_provider_code ON game_providers (provider_code)",
		"CREATE INDEX IF NOT EXISTS idx_game_providers_category ON game_providers (category)",
		"CREATE INDEX IF NOT EXISTS idx_game_providers_status ON game_providers (status)",

		// Agent game commissions indexes
		"CREATE INDEX IF NOT EXISTS idx_agent_game_commissions_agent_code ON agent_game_commissions (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_agent_game_commissions_provider_code ON agent_game_commissions (provider_code)",
		"CREATE INDEX IF NOT EXISTS idx_agent_game_commissions_status ON agent_game_commissions (status)",
		"CREATE INDEX IF NOT EXISTS idx_agent_game_commissions_effective_from ON agent_game_commissions (effective_from)",
		"CREATE INDEX IF NOT EXISTS idx_agent_game_commissions_effective_to ON agent_game_commissions (effective_to)",

		// Game commission history indexes
		"CREATE INDEX IF NOT EXISTS idx_game_commission_history_agent_code ON game_commission_history (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_game_commission_history_provider_code ON game_commission_history (provider_code)",
		"CREATE INDEX IF NOT EXISTS idx_game_commission_history_bet_reference ON game_commission_history (bet_reference)",
		"CREATE INDEX IF NOT EXISTS idx_game_commission_history_calculation_date ON game_commission_history (calculation_date)",
		"CREATE INDEX IF NOT EXISTS idx_game_commission_history_status ON game_commission_history (status)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			log.Printf("Migration: Warning - could not create index: %v", err)
		}
	}

	log.Printf("Migration: All game system tables and indexes created successfully")
}

func (m *CreateGameSystemTables) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop tables in reverse dependency order
	tables := []string{
		"DROP TABLE IF EXISTS game_commission_history",
		"DROP TABLE IF EXISTS agent_game_commissions",
		"DROP TABLE IF EXISTS game_providers",
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			panic(fmt.Sprintf("Failed to drop table: %v", err))
		}
	}

	log.Printf("Migration: All game system tables dropped successfully")
}