package list

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"log"
	"os"
)

type CreateAgentPerformanceMetrics struct{}

func (m *CreateAgentPerformanceMetrics) GetName() string {
	return "CreateAgentPerformanceMetrics"
}

func (m *CreateAgentPerformanceMetrics) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Create agent_performance_metrics table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS agent_performance_metrics (
			id SERIAL PRIMARY KEY,
			agent_code VARCHAR(50) NOT NULL,
			metric_date DATE NOT NULL,
			commission_earned DECIMAL(15,2) DEFAULT 0.00,
			total_bet_amount DECIMAL(15,2) DEFAULT 0.00,
			total_commission_amount DECIMAL(15,2) DEFAULT 0.00,
			total_transactions INTEGER DEFAULT 0,
			downline_count INTEGER DEFAULT 0,
			active_downline_count INTEGER DEFAULT 0,
			direct_children_count INTEGER DEFAULT 0,
			total_hierarchy_depth INTEGER DEFAULT 0,
			credit_utilization_percent DECIMAL(5,2) DEFAULT 0.00,
			commission_utilization_percent DECIMAL(5,2) DEFAULT 0.00,
			performance_score DECIMAL(8,2) DEFAULT 0.00,
			ranking_within_parent INTEGER DEFAULT 0,
			ranking_within_level INTEGER DEFAULT 0,
			activity_level VARCHAR(20) DEFAULT 'inactive' CHECK (activity_level IN ('very_active', 'active', 'moderate', 'low', 'inactive')),
			growth_rate_percent DECIMAL(8,2) DEFAULT 0.00,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(agent_code, metric_date),
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create agent_performance_metrics table: %v", err))
	}
	log.Printf("Migration: agent_performance_metrics table created successfully")

	// Create agent_performance_goals table for goal tracking
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS agent_performance_goals (
			id SERIAL PRIMARY KEY,
			agent_code VARCHAR(50) NOT NULL,
			goal_type VARCHAR(50) NOT NULL CHECK (goal_type IN ('commission_earned', 'bet_volume', 'downline_growth', 'performance_score')),
			target_value DECIMAL(15,2) NOT NULL,
			current_value DECIMAL(15,2) DEFAULT 0.00,
			target_period VARCHAR(20) NOT NULL CHECK (target_period IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
			start_date DATE NOT NULL,
			end_date DATE NOT NULL,
			achievement_percent DECIMAL(5,2) DEFAULT 0.00,
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'achieved', 'failed', 'cancelled')),
			created_by VARCHAR(50) NULL,
			description TEXT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create agent_performance_goals table: %v", err))
	}
	log.Printf("Migration: agent_performance_goals table created successfully")

	// Create agent_activity_log table for tracking daily activities
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS agent_activity_log (
			id SERIAL PRIMARY KEY,
			agent_code VARCHAR(50) NOT NULL,
			activity_date DATE NOT NULL,
			login_count INTEGER DEFAULT 0,
			last_login_time TIMESTAMP NULL,
			session_duration_minutes INTEGER DEFAULT 0,
			actions_performed INTEGER DEFAULT 0,
			transactions_created INTEGER DEFAULT 0,
			commissions_set INTEGER DEFAULT 0,
			downline_managed INTEGER DEFAULT 0,
			reports_generated INTEGER DEFAULT 0,
			is_active_day BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(agent_code, activity_date),
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create agent_activity_log table: %v", err))
	}
	log.Printf("Migration: agent_activity_log table created successfully")

	// Create agent_performance_alerts table for automated alerts
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS agent_performance_alerts (
			id SERIAL PRIMARY KEY,
			agent_code VARCHAR(50) NOT NULL,
			alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('low_performance', 'goal_deadline', 'inactive_agent', 'commission_limit', 'credit_limit', 'downline_issue')),
			severity VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
			title VARCHAR(200) NOT NULL,
			message TEXT NOT NULL,
			threshold_value DECIMAL(15,2) NULL,
			current_value DECIMAL(15,2) NULL,
			is_read BOOLEAN DEFAULT FALSE,
			is_dismissed BOOLEAN DEFAULT FALSE,
			action_required BOOLEAN DEFAULT FALSE,
			auto_generated BOOLEAN DEFAULT TRUE,
			expires_at TIMESTAMP NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create agent_performance_alerts table: %v", err))
	}
	log.Printf("Migration: agent_performance_alerts table created successfully")

	// Create indexes for better performance
	indexes := []string{
		// Agent performance metrics indexes
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_metrics_agent_code ON agent_performance_metrics (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_metrics_metric_date ON agent_performance_metrics (metric_date)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_metrics_performance_score ON agent_performance_metrics (performance_score)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_metrics_activity_level ON agent_performance_metrics (activity_level)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_metrics_agent_date ON agent_performance_metrics (agent_code, metric_date)",

		// Agent performance goals indexes
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_goals_agent_code ON agent_performance_goals (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_goals_goal_type ON agent_performance_goals (goal_type)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_goals_status ON agent_performance_goals (status)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_goals_target_period ON agent_performance_goals (target_period)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_goals_end_date ON agent_performance_goals (end_date)",

		// Agent activity log indexes
		"CREATE INDEX IF NOT EXISTS idx_agent_activity_log_agent_code ON agent_activity_log (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_agent_activity_log_activity_date ON agent_activity_log (activity_date)",
		"CREATE INDEX IF NOT EXISTS idx_agent_activity_log_is_active_day ON agent_activity_log (is_active_day)",
		"CREATE INDEX IF NOT EXISTS idx_agent_activity_log_agent_date ON agent_activity_log (agent_code, activity_date)",

		// Agent performance alerts indexes
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_alerts_agent_code ON agent_performance_alerts (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_alerts_alert_type ON agent_performance_alerts (alert_type)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_alerts_severity ON agent_performance_alerts (severity)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_alerts_is_read ON agent_performance_alerts (is_read)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_alerts_created_at ON agent_performance_alerts (created_at)",
		"CREATE INDEX IF NOT EXISTS idx_agent_performance_alerts_expires_at ON agent_performance_alerts (expires_at)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			log.Printf("Migration: Warning - could not create index: %v", err)
		}
	}

	log.Printf("Migration: All agent performance tables and indexes created successfully")
}

func (m *CreateAgentPerformanceMetrics) Down(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path to use the specified schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}

	// Drop tables in reverse dependency order
	tables := []string{
		"DROP TABLE IF EXISTS agent_performance_alerts",
		"DROP TABLE IF EXISTS agent_activity_log",
		"DROP TABLE IF EXISTS agent_performance_goals",
		"DROP TABLE IF EXISTS agent_performance_metrics",
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			panic(fmt.Sprintf("Failed to drop table: %v", err))
		}
	}

	log.Printf("Migration: All agent performance tables dropped successfully")
}