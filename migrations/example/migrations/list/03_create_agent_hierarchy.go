package list

import (
	"context"
	"github.com/jackc/pgx/v5"
)

type CreateAgentHierarchy struct{}

func (m *CreateAgentHierarchy) GetName() string {
	return "CreateAgentHierarchy"
}

func (m *CreateAgentHierarchy) Up(con pgx.Tx) {
	ctx := context.Background()
	// Create agent_hierarchy table
	_, err := con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS agent_hierarchy (
			id SERIAL PRIMARY KEY,
			agent_code VARCHAR(50) NOT NULL,
			ancestor_code VARCHAR(50) NOT NULL,
			depth_level INTEGER NOT NULL,
			path TEXT NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (agent_code) REFERENCES agents(line_code) ON DELETE CASCADE,
			FOREIGN KEY (ancestor_code) REFERENCES agents(line_code) ON DELETE CASCADE,
			UNIQUE(agent_code, ancestor_code)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create indexes for performance
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_agent_hierarchy_agent_code ON agent_hierarchy (agent_code)",
		"CREATE INDEX IF NOT EXISTS idx_agent_hierarchy_ancestor_code ON agent_hierarchy (ancestor_code)",
		"CREATE INDEX IF NOT EXISTS idx_agent_hierarchy_depth_level ON agent_hierarchy (depth_level)",
		"CREATE INDEX IF NOT EXISTS idx_agent_hierarchy_path ON agent_hierarchy (path)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(err)
		}
	}

	// Insert hierarchy record for company if it exists
	_, err = con.Exec(ctx, `
		INSERT INTO agent_hierarchy (agent_code, ancestor_code, depth_level, path)
		SELECT 'company', 'company', 0, 'company'
		WHERE EXISTS (SELECT 1 FROM agents WHERE line_code = 'company')
		AND NOT EXISTS (SELECT 1 FROM agent_hierarchy WHERE agent_code = 'company' AND ancestor_code = 'company')
	`)
	if err != nil {
		panic(err)
	}
}

func (m *CreateAgentHierarchy) Down(con pgx.Tx) {
	ctx := context.Background()
	// Drop agent_hierarchy table
	_, err := con.Exec(ctx, "DROP TABLE IF EXISTS agent_hierarchy")
	if err != nil {
		panic(err)
	}
}
