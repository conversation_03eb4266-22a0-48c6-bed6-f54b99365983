package list

import (
	"context"

	"github.com/jackc/pgx/v5"
)

type CreateCommissionFlowTables struct{}

func (m *CreateCommissionFlowTables) GetName() string {
	return "CreateCommissionFlowTables"
}

func (m *CreateCommissionFlowTables) Up(con pgx.Tx) {
	ctx := context.Background()

	// Create bet_transactions table
	_, err := con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS bet_transactions (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			user_id UUID NOT NULL,
			game_provider_code VARCHAR(50) NOT NULL,
			bet_amount DECIMAL(15,2) NOT NULL CHECK (bet_amount >= 0),
			result_amount DECIMAL(15,2) NOT NULL,
			commission_eligible_amount DECIMAL(15,2) NOT NULL CHECK (commission_eligible_amount >= 0),
			system_revenue DECIMAL(15,2) NOT NULL CHECK (system_revenue >= 0),
			status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'settled', 'cancelled', 'voided')),
			bet_reference VARCHAR(100) NOT NULL,
			game_reference VARCHAR(100),
			settlement_time TIMESTAMP,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW(),
			
			-- Foreign key constraints
			CONSTRAINT fk_bet_transactions_game_provider 
				FOREIGN KEY (game_provider_code) REFERENCES game_providers(provider_code) ON DELETE RESTRICT,
			
			-- Unique constraints
			CONSTRAINT uk_bet_transactions_bet_reference UNIQUE (bet_reference)
		);
	`)
	if err != nil {
		panic(err)
	}

	// Create commission_distributions table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS commission_distributions (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			bet_transaction_id UUID NOT NULL,
			agent_line_code VARCHAR(50) NOT NULL,
			agent_type VARCHAR(20) NOT NULL CHECK (agent_type IN ('AG', 'MA', 'SR', 'SSR', 'Company')),
			commission_percentage DECIMAL(5,2) NOT NULL CHECK (commission_percentage >= 0 AND commission_percentage <= 100),
			commission_amount DECIMAL(15,2) NOT NULL CHECK (commission_amount >= 0),
			cumulative_amount DECIMAL(15,2) NOT NULL CHECK (cumulative_amount >= 0),
			hierarchy_level INTEGER NOT NULL CHECK (hierarchy_level >= 1 AND hierarchy_level <= 5),
			status VARCHAR(20) NOT NULL DEFAULT 'calculated' CHECK (status IN ('pending', 'calculated', 'distributed', 'settled', 'cancelled', 'failed')),
			distribution_reference VARCHAR(100),
			settlement_reference VARCHAR(100),
			distributed_at TIMESTAMP,
			settled_at TIMESTAMP,
			error_message TEXT,
			retry_count INTEGER DEFAULT 0 CHECK (retry_count >= 0),
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW(),
			
			-- Foreign key constraints
			CONSTRAINT fk_commission_distributions_bet_transaction 
				FOREIGN KEY (bet_transaction_id) REFERENCES bet_transactions(id) ON DELETE CASCADE,
			CONSTRAINT fk_commission_distributions_agent 
				FOREIGN KEY (agent_line_code) REFERENCES agents(line_code) ON DELETE RESTRICT,
			
			-- Unique constraints
			CONSTRAINT uk_commission_distributions_bet_agent 
				UNIQUE (bet_transaction_id, agent_line_code)
		);
	`)
	if err != nil {
		panic(err)
	}

	// Create indexes for performance optimization
	queries := []string{
		// Bet transactions indexes
		`CREATE INDEX IF NOT EXISTS idx_bet_transactions_user_provider 
		 ON bet_transactions(user_id, game_provider_code)`,
		
		`CREATE INDEX IF NOT EXISTS idx_bet_transactions_status 
		 ON bet_transactions(status)`,
		
		`CREATE INDEX IF NOT EXISTS idx_bet_transactions_created_at 
		 ON bet_transactions(created_at DESC)`,
		
		`CREATE INDEX IF NOT EXISTS idx_bet_transactions_settlement_time 
		 ON bet_transactions(settlement_time DESC) WHERE settlement_time IS NOT NULL`,
		
		`CREATE INDEX IF NOT EXISTS idx_bet_transactions_game_provider 
		 ON bet_transactions(game_provider_code)`,

		// Commission distributions indexes
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_agent 
		 ON commission_distributions(agent_line_code)`,
		
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_status 
		 ON commission_distributions(status)`,
		
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_bet_id 
		 ON commission_distributions(bet_transaction_id)`,
		
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_agent_type 
		 ON commission_distributions(agent_type)`,
		
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_hierarchy_level 
		 ON commission_distributions(hierarchy_level)`,
		
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_created_at 
		 ON commission_distributions(created_at DESC)`,
		
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_distributed_at 
		 ON commission_distributions(distributed_at DESC) WHERE distributed_at IS NOT NULL`,
		
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_settled_at 
		 ON commission_distributions(settled_at DESC) WHERE settled_at IS NOT NULL`,

		// Composite indexes for common queries
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_agent_status 
		 ON commission_distributions(agent_line_code, status)`,
		
		`CREATE INDEX IF NOT EXISTS idx_commission_distributions_status_retry 
		 ON commission_distributions(status, retry_count) WHERE status = 'failed'`,
	}

	for _, query := range queries {
		_, err := con.Exec(ctx, query)
		if err != nil {
			panic(err)
		}
	}

	// Add comments for documentation
	comments := []string{
		`COMMENT ON TABLE bet_transactions IS 'Stores individual bet transactions that generate commission'`,
		`COMMENT ON TABLE commission_distributions IS 'Stores commission distribution records for each agent in hierarchy'`,
		
		`COMMENT ON COLUMN bet_transactions.system_revenue IS 'Revenue that system gets after provider takes their percentage'`,
		`COMMENT ON COLUMN bet_transactions.commission_eligible_amount IS 'Amount eligible for commission calculation'`,
		`COMMENT ON COLUMN bet_transactions.bet_reference IS 'Unique reference from game provider for this bet'`,
		
		`COMMENT ON COLUMN commission_distributions.cumulative_amount IS 'Total commission amount up to this hierarchy level'`,
		`COMMENT ON COLUMN commission_distributions.commission_amount IS 'Actual commission amount this agent receives'`,
		`COMMENT ON COLUMN commission_distributions.hierarchy_level IS 'Agent hierarchy level (1=AG, 2=MA, 3=SR, 4=SSR, 5=Company)'`,
		`COMMENT ON COLUMN commission_distributions.distribution_reference IS 'Reference for commission distribution transaction'`,
		`COMMENT ON COLUMN commission_distributions.settlement_reference IS 'Reference for final settlement transaction'`,
	}

	for _, comment := range comments {
		_, err := con.Exec(ctx, comment)
		if err != nil {
			panic(err)
		}
	}
}

func (m *CreateCommissionFlowTables) Down(con pgx.Tx) {
	ctx := context.Background()

	// Drop tables in reverse dependency order
	queries := []string{
		`DROP TABLE IF EXISTS commission_distributions CASCADE`,
		`DROP TABLE IF EXISTS bet_transactions CASCADE`,
	}

	for _, query := range queries {
		_, err := con.Exec(ctx, query)
		if err != nil {
			panic(err)
		}
	}
}