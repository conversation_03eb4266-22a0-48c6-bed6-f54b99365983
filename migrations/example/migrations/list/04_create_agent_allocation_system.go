package list

import (
	"context"

	"github.com/jackc/pgx/v5"
)

type CreateAgentAllocationSystem struct{}

func (m *CreateAgentAllocationSystem) GetName() string {
	return "CreateAgentAllocationSystem"
}

func (m *CreateAgentAllocationSystem) Up(con pgx.Tx) {
	ctx := context.Background()

	// Create agent_provider_allocations table
	_, err := con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS agent_provider_allocations (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			agent_line_code VARCHAR(50) NOT NULL,
			game_provider_code VARCHAR(50) NOT NULL,
			
			-- เปอร์เซ็นต์ที่ agent ตัวนี้ได้รับจาก parent (หรือจาก game provider ถ้าเป็น company)
			received_percentage DECIMAL(5,2) NOT NULL CHECK (received_percentage >= 0 AND received_percentage <= 100),
			
			-- เปอร์เซ็นต์ที่ agent ตัวนี้จะแบ่งให้ child agents
			allocated_percentage DECIMAL(5,2) NOT NULL CHECK (allocated_percentage >= 0 AND allocated_percentage <= 100),
			
			-- เปอร์เซ็นต์ที่ agent ตัวนี้เก็บไว้เอง (received - allocated)  
			retained_percentage DECIMAL(5,2) GENERATED ALWAYS AS (received_percentage - allocated_percentage) STORED,
			
			-- Effective date range
			effective_from TIMESTAMP DEFAULT NOW(),
			effective_to TIMESTAMP,
			
			-- Status and metadata
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
			
			-- Audit fields
			created_by VARCHAR(50),
			updated_by VARCHAR(50),
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW(),
			
			-- Copy template information
			copied_from_agent VARCHAR(50),
			copy_timestamp TIMESTAMP,
			
			-- Business rule constraints
			CONSTRAINT chk_allocation_not_exceed_received 
				CHECK (allocated_percentage <= received_percentage),
			CONSTRAINT chk_retained_non_negative 
				CHECK (retained_percentage >= 0),
			
			-- Foreign key constraints
			CONSTRAINT fk_agent_allocations_agent 
				FOREIGN KEY (agent_line_code) REFERENCES agents(line_code) ON DELETE RESTRICT,
			CONSTRAINT fk_agent_allocations_provider 
				FOREIGN KEY (game_provider_code) REFERENCES game_providers(provider_code) ON DELETE RESTRICT,
			CONSTRAINT fk_agent_allocations_copied_from 
				FOREIGN KEY (copied_from_agent) REFERENCES agents(line_code) ON DELETE SET NULL
		);
	`)
	if err != nil {
		panic(err)
	}

	// Create allocation_templates table for predefined templates
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS allocation_templates (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			template_name VARCHAR(100) NOT NULL,
			template_description TEXT,
			agent_type VARCHAR(20) NOT NULL CHECK (agent_type IN ('AG', 'MA', 'SR', 'SSR', 'Company')),
			
			-- Template can be system-defined or user-created
			template_type VARCHAR(20) DEFAULT 'user' CHECK (template_type IN ('system', 'user')),
			
			-- Template data as JSON
			allocation_data JSONB NOT NULL,
			
			-- Status and metadata
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			is_default BOOLEAN DEFAULT false,
			
			-- Audit fields
			created_by VARCHAR(50),
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW(),
			
			-- Unique constraint for template names per agent type
			CONSTRAINT uk_template_name_agent_type 
				UNIQUE (template_name, agent_type)
		);
	`)
	if err != nil {
		panic(err)
	}

	// Create allocation_history table for audit trail
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS allocation_history (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			agent_line_code VARCHAR(50) NOT NULL,
			game_provider_code VARCHAR(50) NOT NULL,
			
			-- Historical values
			old_received_percentage DECIMAL(5,2),
			new_received_percentage DECIMAL(5,2),
			old_allocated_percentage DECIMAL(5,2),
			new_allocated_percentage DECIMAL(5,2),
			
			-- Change information
			change_type VARCHAR(20) NOT NULL CHECK (change_type IN ('create', 'update', 'delete', 'copy')),
			change_reason TEXT,
			
			-- If this was a copy operation
			copied_from_agent VARCHAR(50),
			
			-- Audit fields
			changed_by VARCHAR(50),
			changed_at TIMESTAMP DEFAULT NOW(),
			
			-- Foreign key constraints
			CONSTRAINT fk_allocation_history_agent 
				FOREIGN KEY (agent_line_code) REFERENCES agents(line_code) ON DELETE RESTRICT,
			CONSTRAINT fk_allocation_history_provider 
				FOREIGN KEY (game_provider_code) REFERENCES game_providers(provider_code) ON DELETE RESTRICT
		);
	`)
	if err != nil {
		panic(err)
	}

	// Create indexes for performance optimization
	indexes := []string{
		// Main table indexes
		`CREATE INDEX IF NOT EXISTS idx_agent_allocations_agent_provider 
		 ON agent_provider_allocations(agent_line_code, game_provider_code)`,
		
		`CREATE INDEX IF NOT EXISTS idx_agent_allocations_status 
		 ON agent_provider_allocations(status) WHERE status = 'active'`,
		
		`CREATE INDEX IF NOT EXISTS idx_agent_allocations_effective 
		 ON agent_provider_allocations(effective_from, effective_to) WHERE status = 'active'`,
		
		`CREATE INDEX IF NOT EXISTS idx_agent_allocations_agent_active 
		 ON agent_provider_allocations(agent_line_code) WHERE status = 'active'`,
		
		`CREATE INDEX IF NOT EXISTS idx_agent_allocations_provider_active 
		 ON agent_provider_allocations(game_provider_code) WHERE status = 'active'`,
		
		// Partial unique constraint for active allocation per agent-provider
		`CREATE UNIQUE INDEX IF NOT EXISTS uk_active_agent_provider_allocation 
		 ON agent_provider_allocations(agent_line_code, game_provider_code) 
		 WHERE status = 'active' AND effective_to IS NULL`,
		
		// Template table indexes
		`CREATE INDEX IF NOT EXISTS idx_allocation_templates_agent_type 
		 ON allocation_templates(agent_type) WHERE status = 'active'`,
		
		`CREATE INDEX IF NOT EXISTS idx_allocation_templates_default 
		 ON allocation_templates(agent_type, is_default) WHERE is_default = true`,
		
		// History table indexes
		`CREATE INDEX IF NOT EXISTS idx_allocation_history_agent 
		 ON allocation_history(agent_line_code)`,
		
		`CREATE INDEX IF NOT EXISTS idx_allocation_history_changed_at 
		 ON allocation_history(changed_at DESC)`,
		
		`CREATE INDEX IF NOT EXISTS idx_allocation_history_change_type 
		 ON allocation_history(change_type)`,
	}

	for _, query := range indexes {
		_, err := con.Exec(ctx, query)
		if err != nil {
			panic(err)
		}
	}

	// Add table comments for documentation
	comments := []string{
		`COMMENT ON TABLE agent_provider_allocations IS 'Stores custom commission allocation percentages for each agent-provider pair'`,
		`COMMENT ON TABLE allocation_templates IS 'Predefined allocation templates that can be applied to agents'`,
		`COMMENT ON TABLE allocation_history IS 'Audit trail for all allocation changes'`,
		
		`COMMENT ON COLUMN agent_provider_allocations.received_percentage IS 'Percentage this agent receives from parent (or game provider if company)'`,
		`COMMENT ON COLUMN agent_provider_allocations.allocated_percentage IS 'Percentage this agent allocates to child agents'`,
		`COMMENT ON COLUMN agent_provider_allocations.retained_percentage IS 'Percentage this agent keeps (received - allocated)'`,
		`COMMENT ON COLUMN agent_provider_allocations.copied_from_agent IS 'Source agent if this allocation was copied from another agent'`,
		
		`COMMENT ON COLUMN allocation_templates.allocation_data IS 'JSON data containing allocation percentages for all providers'`,
		`COMMENT ON COLUMN allocation_templates.template_type IS 'system = built-in templates, user = user-created templates'`,
		`COMMENT ON COLUMN allocation_templates.is_default IS 'Whether this template is the default for the agent type'`,
	}

	for _, comment := range comments {
		_, err := con.Exec(ctx, comment)
		if err != nil {
			panic(err)
		}
	}

	// Insert default system templates
	systemTemplates := []string{
		// Conservative template (keep more, give less)
		`INSERT INTO allocation_templates (
			template_name, template_description, agent_type, template_type, is_default, allocation_data, created_by
		) VALUES (
			'Conservative', 
			'Keep higher margins, allocate conservatively to child agents', 
			'SSR', 
			'system', 
			true,
			'{"SBOBET": {"allocated_percentage": 85.0}, "RWB": {"allocated_percentage": 82.0}, "PGSOFT": {"allocated_percentage": 90.0}}',
			'system'
		)`,
		
		// Aggressive template (give more, keep less)
		`INSERT INTO allocation_templates (
			template_name, template_description, agent_type, template_type, allocation_data, created_by
		) VALUES (
			'Aggressive', 
			'Maximize allocation to child agents for growth', 
			'SSR', 
			'system',
			'{"SBOBET": {"allocated_percentage": 87.0}, "RWB": {"allocated_percentage": 83.5}, "PGSOFT": {"allocated_percentage": 91.5}}',
			'system'
		)`,
		
		// Balanced template
		`INSERT INTO allocation_templates (
			template_name, template_description, agent_type, template_type, allocation_data, created_by
		) VALUES (
			'Balanced', 
			'Balanced approach between retention and allocation', 
			'SSR', 
			'system',
			'{"SBOBET": {"allocated_percentage": 86.0}, "RWB": {"allocated_percentage": 82.5}, "PGSOFT": {"allocated_percentage": 90.5}}',
			'system'
		)`,
	}

	for _, template := range systemTemplates {
		_, err := con.Exec(ctx, template)
		if err != nil {
			panic(err)
		}
	}
}

func (m *CreateAgentAllocationSystem) Down(con pgx.Tx) {
	ctx := context.Background()

	// Drop tables in reverse dependency order
	tables := []string{
		`DROP TABLE IF EXISTS allocation_history CASCADE`,
		`DROP TABLE IF EXISTS allocation_templates CASCADE`,
		`DROP TABLE IF EXISTS agent_provider_allocations CASCADE`,
	}

	for _, query := range tables {
		_, err := con.Exec(ctx, query)
		if err != nil {
			panic(err)
		}
	}
}