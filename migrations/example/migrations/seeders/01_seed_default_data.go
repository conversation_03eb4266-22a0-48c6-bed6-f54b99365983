package seeders

import (
	"context"

	"github.com/jackc/pgx/v5"
	"golang.org/x/crypto/bcrypt"
)

type SeedDefaultData struct{}

func (s *SeedDefaultData) GetName() string {
	return "SeedDefaultData"
}

func (s *SeedDefaultData) Up(con pgx.Tx) {
	ctx := context.Background()
	// Insert default admin roles
	_, err := con.Exec(ctx, `
		INSERT INTO user_roles (id, name, display_name, description, permissions, is_system_role, status) 
		VALUES 
		(1, 'superadmin', 'Super Administrator', 'Full system access', '["*"]', true, 'active'),
		(2, 'admin', 'Administrator', 'Administrative access', '["user.*", "agent.*", "report.*"]', true, 'active'),
		(3, 'manager', 'Manager', 'Management access', '["agent.read", "report.*"]', true, 'active'),
		(4, 'operator', 'Operator', 'Basic operational access', '["agent.read", "report.read"]', true, 'active')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create default superadmin user
	password := "12341234"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		panic(err)
	}

	_, err = con.Exec(ctx, `
		INSERT INTO users (
			username, 
			first_name, 
			last_name, 
			password, 
			user_role_id, 
			permissions,
			status, 
			created_at, 
			updated_at
		)
		VALUES ('superadmin', 'Super', 'Admin', $1, 1, '["*"]', 'active', NOW(), NOW())
		ON CONFLICT (username) DO NOTHING
	`, string(hashedPassword))

	if err != nil {
		panic(err)
	}
}

func (s *SeedDefaultData) Down(con pgx.Tx) {
	ctx := context.Background()
	// Delete default admin user
	_, err := con.Exec(ctx, `DELETE FROM admins WHERE username = 'superadmin'`)
	if err != nil {
		panic(err)
	}

	// Delete default admin roles
	_, err = con.Exec(ctx, `DELETE FROM admin_roles WHERE is_system_role = true`)
	if err != nil {
		panic(err)
	}
}
