package seeders

import (
	"context"
	"fmt"
	"os"

	"github.com/jackc/pgx/v5"
	"golang.org/x/crypto/bcrypt"
)

type SeedDefaultData struct{}

func (s *SeedDefaultData) GetName() string {
	return "SeedDefaultData"
}

func (s *SeedDefaultData) Up(con pgx.Tx) {
	ctx := context.Background()

	// Get schema name from environment
	schemaName := "public"
	if envSchema := os.Getenv("DATABASE_SCHEMA"); envSchema != "" {
		schemaName = envSchema
	}

	// Set search path to use the correct schema
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Errorf("failed to set search_path: %w", err))
	}

	// Insert default user roles
	_, err = con.Exec(ctx, `
		INSERT INTO user_roles (id, position, name, is_2fa, is_lock_ip, is_enable, status)
		VALUES
		(1, 1, 'Super Admin', false, true, true, 'active'),
		(2, 2, 'Admin', true, false, true, 'active'),
		(3, 3, 'Manager', false, false, true, 'active'),
		(4, 4, 'Agent', false, false, true, 'active'),
		(5, 5, 'Member', false, false, true, 'active')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Reset sequence to continue from the highest ID
	_, err = con.Exec(ctx, `
		SELECT setval('user_roles_id_seq', (SELECT MAX(id) FROM user_roles))
	`)
	if err != nil {
		panic(err)
	}

	// Insert default member group types
	_, err = con.Exec(ctx, `
		INSERT INTO member_group_types (id, position, name, badge_bg_color, badge_border_color, status)
		VALUES
		(1, 1, 'vip', '#ffcc00', '#000000', 'active'),
		(2, 2, 'regular', '#ffffff', '#000000', 'active'),
		(3, 3, 'premium', '#0000ff', '#ffffff', 'active')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Reset sequence to continue from the highest ID
	_, err = con.Exec(ctx, `
		SELECT setval('member_group_types_id_seq', (SELECT MAX(id) FROM member_group_types))
	`)
	if err != nil {
		panic(err)
	}

	// Insert default commission groups
	_, err = con.Exec(ctx, `
		INSERT INTO commission_groups (id, is_default, name, turnover_sports, turnover_casino, turnover_fishing, turnover_slot, turnover_lottery, turnover_card, turnover_other, status)
		VALUES
		(1, true, 'New', 0.3, 0.3, 0, 0.3, 0, 0, 0, 'active')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Reset sequence to continue from the highest ID
	_, err = con.Exec(ctx, `
		SELECT setval('commission_groups_id_seq', (SELECT MAX(id) FROM commission_groups))
	`)
	if err != nil {
		panic(err)
	}

	// Insert default member groups
	_, err = con.Exec(ctx, `
		INSERT INTO member_groups (id, is_default, code, member_group_type_id, commission_group_id, name, status)
		VALUES
		(1, true, 'DEFAULT', 2,  1, 'Default member group', 'active'),
		(2, false, 'VIP_GOLD', 1, 1, 'VIP Gold member group', 'active'),
		(3, false, 'PREMIUM_PLUS', 3, 1, 'Premium Plus member group', 'active')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Reset sequence to continue from the highest ID
	_, err = con.Exec(ctx, `
		SELECT setval('member_groups_id_seq', (SELECT MAX(id) FROM member_groups))
	`)
	if err != nil {
		panic(err)
	}

	// Insert default languages
	_, err = con.Exec(ctx, `
		INSERT INTO languages (id, code, name, native_name, is_default, status) 
		VALUES 
		(1, 'en', 'English', 'English', true, 'active'),
		(2, 'th', 'Thai', 'ไทย', false, 'active'),
		(3, 'zh', 'Chinese', '中文', false, 'active'),
		(4, 'ja', 'Japanese', '日本語', false, 'active')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	// Create default super admin user
	password := "12341234"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		panic(err)
	}

	_, err = con.Exec(ctx, `
		INSERT INTO users (
			username, 
			first_name, 
			last_name, 
			password, 
			user_role_name,
			user_role_id, 
			agent_code,
			user_type,
			is_enable,
			status, 
			created_at, 
			updated_at
		)
		VALUES ('superadmin', 'Super', 'Admin', $1, 'Super Admin', 1, 'SUPERADMIN', 'agent', true, 'active', NOW(), NOW())
		ON CONFLICT (username) DO NOTHING
	`, string(hashedPassword))

	if err != nil {
		panic(err)
	}

	// Insert deposit accounts
	_, err = con.Exec(ctx, `
		INSERT INTO deposit_account (
			banking_id,
			payment_method_id,
			algorithm_id,
			auto_bot_id,
			account_name,
			account_name_display,
			account_number,
			phone_number,
			auto_transfer,
			push_bullet_nickname,
			push_bullet_token,
			active,
			inactive,
			created_at,
			updated_at
		) VALUES
		(4, 1, 5, 1, 'นายสมชาย ใจดี', 'สมชาย ใ.', '*************', '**********', true, 'scb_account_1', 'o.abcdefghijklmnopqrstuvwxyz1234567890', true, false, NOW(), NOW())
	`)
	if err != nil {
		panic(err)
	}

	// Reset sequence to continue from the highest ID
	_, err = con.Exec(ctx, `
		SELECT setval('deposit_account_id_seq', (SELECT MAX(id) FROM deposit_account))
	`)
	if err != nil {
		panic(err)
	}

}

func (s *SeedDefaultData) Down(con pgx.Tx) {
	ctx := context.Background()

	// Temporarily disable foreign key checks for safe deletion
	_, err := con.Exec(ctx, `SET session_replication_role = replica`)
	if err != nil {
		panic(err)
	}

	// Delete in reverse order to respect foreign key constraints

	// 1. Delete ALL users that reference the user_roles we're about to delete
	_, err = con.Exec(ctx, `DELETE FROM users WHERE user_role_id IN (1, 2, 3, 4, 5)`)
	if err != nil {
		panic(err)
	}

	// 2. Delete user_role_permissions (they reference user_roles and permissions)
	_, err = con.Exec(ctx, `DELETE FROM user_role_permissions WHERE user_role_id IN (1, 2, 3, 4, 5)`)
	if err != nil {
		panic(err)
	}

	// 3. Delete permissions
	_, err = con.Exec(ctx, `DELETE FROM permissions WHERE key LIKE 'users.%' OR key LIKE 'members.%' OR key LIKE 'system_settings.%' OR key LIKE 'reports.%' OR key IN ('users', 'members', 'system_settings', 'reports')`)
	if err != nil {
		panic(err)
	}

	// 4. Permission groups table removed - no longer needed

	// 5. Delete user_roles (after users are deleted)
	_, err = con.Exec(ctx, `DELETE FROM user_roles WHERE name IN ('Super Admin', 'Admin', 'Manager', 'Agent', 'Member')`)
	if err != nil {
		panic(err)
	}

	// 6. Delete member groups
	_, err = con.Exec(ctx, `DELETE FROM member_groups WHERE name IN ('default', 'vip_gold', 'premium_plus')`)
	if err != nil {
		panic(err)
	}

	// 7. Delete member group types
	_, err = con.Exec(ctx, `DELETE FROM member_group_types WHERE name IN ('vip', 'regular', 'premium')`)
	if err != nil {
		panic(err)
	}

	// 8. Delete languages
	_, err = con.Exec(ctx, `DELETE FROM languages WHERE code IN ('en', 'th', 'zh', 'ja')`)
	if err != nil {
		panic(err)
	}

	// Re-enable foreign key checks
	_, err = con.Exec(ctx, `SET session_replication_role = DEFAULT`)
	if err != nil {
		panic(err)
	}
}
