package seeders

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type SeedPermissions struct{}

func (s *SeedPermissions) GetName() string {
	return "SeedPermissions"
}

func (s *SeedPermissions) Up(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Seeder: Starting permission seeding...")

	// Check if permissions already exist
	var count int64
	err = con.QueryRow(ctx, "SELECT COUNT(*) FROM permissions WHERE status = 'active'").Scan(&count)
	if err != nil {
		panic(fmt.Sprintf("Failed to check existing permissions: %v", err))
	}

	if count > 0 {
		log.Printf("Seeder: Permissions already exist (%d found), skipping seeding", count)
		return
	}

	// Seed permissions (no more permission groups needed)
	permissions := getPermissionTemplates()

	log.Printf("Seeder: Found %d permissions to seed", len(permissions))

	for i, perm := range permissions {
		log.Printf("Seeder: Processing permission %d/%d: %s", i+1, len(permissions), perm.Name)
		err := seedPermission(ctx, con, perm, nil)
		if err != nil {
			log.Printf("Failed to seed permission %s: %v", perm.Name, err)
			continue
		}
	}

	log.Printf("Seeder: Permission seeding completed successfully!")
}

func (s *SeedPermissions) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Seeder: Removing all permissions and resetting sequence...")

	// Delete all permissions (hard delete)
	_, err = con.Exec(ctx, "DELETE FROM permissions")
	if err != nil {
		panic(fmt.Sprintf("Failed to delete permissions: %v", err))
	}

	// Reset the sequence (auto-increment counter)
	_, err = con.Exec(ctx, "ALTER SEQUENCE permissions_id_seq RESTART WITH 1")
	if err != nil {
		panic(fmt.Sprintf("Failed to reset permissions sequence: %v", err))
	}

	log.Printf("Seeder: All permissions deleted and sequence reset to 1!")
}

type PermissionTemplate struct {
	Slug           string
	Name           string
	PermissionID   int
	ID             int
	IsCreate       bool
	IsUpdate       bool
	IsRead         bool
	IsDelete       bool
	IsPermission   bool
	IsTitle        bool
	RoleID         int
	IsView         bool
	IsTabs         bool
	IsButton       bool
	Sort           int
	SubPermissions []PermissionTemplate
}

type ButtonTemplate struct {
	Slug string
	Name string
	Sort int
}

func seedPermission(ctx context.Context, con pgx.Tx, template PermissionTemplate, parentID *int) error {
	level := 1
	if parentID != nil {
		level = 2
	}

	var isTab, isButton int64

	if template.IsButton {
		isButton = 1
	}
	if template.IsTabs {
		isTab = 1
	}

	var permID int
	err := con.QueryRow(ctx, `
		INSERT INTO permissions (parent_id, name, key, description, position, level,
		                        supports_create, supports_view, supports_edit, supports_delete,
		                        is_tab, is_button, enabled, status, created_at, updated_at)
		VALUES ($1, $2, $3, NULL, $4, $5, $6, $7, $8, $9, $10, $11, $12, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		RETURNING id
	`, parentID, template.Name, template.Slug, template.Sort, level,
		template.IsCreate, template.IsRead, template.IsUpdate, template.IsDelete,
		isTab, isButton, true).Scan(&permID)

	if err != nil {
		fmt.Println(err)
		return fmt.Errorf("failed to insert permission %s: %w", template.Name, err)
	}

	log.Printf("Created permission: %s (ID: %d)", template.Name, permID)

	// Seed sub-permissions (from SubPermissions array)
	for _, subPerm := range template.SubPermissions {
		err := seedPermission(ctx, con, subPerm, &permID)
		if err != nil {
			log.Printf("Failed to seed sub-permission %s: %v", subPerm.Name, err)
		}
	}

	return nil
}

func getPermissionTemplates() []PermissionTemplate {
	// Fallback to hardcoded data
	return getAllPermissions()
}

func getAllPermissions() []PermissionTemplate {
	return []PermissionTemplate{
		// 1. แดชบอร์ด
		{
			Slug: "dashboard", Name: "แดชบอร์ด", Sort: 1, IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "dashboard.finance", Name: "การเงิน", Sort: 1, IsTabs: true},
				{Slug: "dashboard.bot", Name: "บอท", Sort: 2, IsTabs: true},
			},
		},

		// 2. รายงานผู้ใช้
		{
			Slug: "users", Name: "รายงานผู้ใช้", Sort: 3,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "users.edit_user", Name: "แก้ไขผู้ใช้", Sort: 1, IsButton: true},
				{Slug: "users.change_password", Name: "เปลี่ยนรหัสผ่าน", Sort: 2, IsButton: true},
				{Slug: "users.edit_bank_info", Name: "แก้ไขข้อมูลธนาคาร", Sort: 3, IsButton: true},
				{Slug: "users.edit_partner", Name: "แก้ไขพันธมิตร", Sort: 4, IsButton: true},
				{Slug: "users.adjust_credit", Name: "เพิ่ม/ลด เครดิต", Sort: 5, IsButton: true},
				{Slug: "users.deposit_withdraw", Name: "ฝาก/ถอน", Sort: 6, IsButton: true},
				{Slug: "users.suspend_user", Name: "ระงับการใช้งาน", Sort: 7, IsButton: true},
				{Slug: "users.delete_user", Name: "ลบผู้ใช้", Sort: 8, IsButton: true},
				{Slug: "users.upload_image", Name: "อัพโหลดรูปภาพ", Sort: 9, IsButton: true},
				{Slug: "users.show_all", Name: "แสดงทั้งหมด", Sort: 10, IsButton: true},
				{Slug: "users.show_partner", Name: "แสดงพันธมิตร", Sort: 11, IsButton: true},
				{Slug: "users.show_phone", Name: "แสดงเบอร์โทรศัพท์", Sort: 12, IsButton: true},
				{Slug: "users.manage_promotion_users", Name: "จัดการผู้ใช้ติดเงื่อนไข", Sort: 13, IsButton: true},
				{Slug: "users.edit_address", Name: "แก้ไขที่อยู่", Sort: 14, IsButton: true},
				{Slug: "users.set_bet_limit", Name: "ตั้งค่า bet limit", Sort: 15, IsButton: true},
				{Slug: "users.call_customer", Name: "โทรหาลูกค้า", Sort: 16, IsButton: true},
				{Slug: "users.show_account_number", Name: "แสดงเลขที่บัญชี", Sort: 17, IsButton: true},
				{Slug: "users.adjust_points_tickets", Name: "เพิ่ม/ลด พอยต์/ตั๋ว", Sort: 18, IsButton: true},
				{Slug: "users.send_message", Name: "ส่งข้อความ", Sort: 19, IsButton: true},
				{Slug: "users.lock_user", Name: "ล็อคผู้ใช้งาน", Sort: 20, IsButton: true},
				{Slug: "users.popup_user_info", Name: "ป๊อปอัพข้อมูลผู้ใช้", Sort: 21, IsButton: true},
				{Slug: "users.adjust_commission_credit", Name: "เพิ่ม/ลด เครดิต Commission", Sort: 22, IsButton: true},
				{Slug: "users.adjust_affiliate_credit", Name: "เพิ่ม/ลด เครดิตเพื่อนชวนเพื่อน", Sort: 23, IsButton: true},
				{Slug: "users.import_users", Name: "นำเข้าผู้ใช้", Sort: 24, IsButton: true},
				{Slug: "users.instant_approve", Name: "อนุมัติทันที", Sort: 25, IsButton: true},
			},
		},

		// 3. เพิ่ม/ลด เครดิต
		{
			Slug: "user_adjust_credit", Name: "เพิ่ม/ลด เครดิต", Sort: 4,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 4. สมัครสมาชิก (OTP)
		{
			Slug: "report_register", Name: "สมัครสมาชิก (OTP)", Sort: 5,
			IsRead: true,
		},

		// 5. ตั้งค่ากลุ่ม
		{
			Slug: "usergroup", Name: "ตั้งค่ากลุ่ม", Sort: 6,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "usergroup.user_groups", Name: "กลุ่มผู้ใช้", Sort: 1, IsTabs: true},
				{Slug: "usergroup.group_types", Name: "ประเภทกลุ่ม", Sort: 2, IsTabs: true},
				{Slug: "usergroup.commission_groups", Name: "กลุ่มคอมมิชชั่น", Sort: 3, IsTabs: true},
			},
		},

		// 6. กลุ่มเพื่อนชวนเพื่อน
		{
			Slug: "affiliategroups", Name: "กลุ่มเพื่อนชวนเพื่อน", Sort: 7,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 7. ผู้ใช้ในกลุ่มเพื่อนชวนเพื่อน
		{
			Slug: "affiliategroups_users", Name: "ผู้ใช้ในกลุ่มเพื่อนชวนเพื่อน", Sort: 8,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 8. ผู้ใช้ในกลุ่มคอมมิชชั่น
		{
			Slug: "commissiongroups_users", Name: "ผู้ใช้ในกลุ่มคอมมิชชั่น", Sort: 9,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 9. แบล็คลิสต์
		{
			Slug: "blacklist", Name: "แบล็คลิสต์", Sort: 10,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 10. ประวัติการเปลี่ยนแปลงข้อมูล
		{
			Slug: "report_log_users", Name: "ประวัติการเปลี่ยนแปลงข้อมูล", Sort: 11, IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "report_log_users.user_groups", Name: "กลุ่มผู้ใช้", Sort: 1, IsTabs: true},
				{Slug: "report_log_users.group_types", Name: "ประเภทกลุ่ม", Sort: 2, IsTabs: true},
			},
		},

		// 11. ยูสเซอร์สำหรับเข้าเล่น
		{
			Slug: "user_gaming_accounts", Name: "ยูสเซอร์สำหรับเข้าเล่น", Sort: 12,
			IsRead: true,
		},

		// 12. ผู้ใช้ต้องสงสัย
		{
			Slug: "hunt_promotion", Name: "ผู้ใช้ต้องสงสัย", Sort: 13,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 13. ติดตามลูกค้า
		{
			Slug: "follow_up_user", Name: "ติดตามลูกค้า", Sort: 15,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "follow_up_user.new_customers", Name: "ลูกค้าใหม่", Sort: 1, IsTabs: true},
				{Slug: "follow_up_user.existing_customers", Name: "ลูกค้าเก่า", Sort: 2, IsTabs: true},
			},
		},

		// 14. ติดตามผลลัพท์
		{
			Slug: "follow_up_result", Name: "ติดตามผลลัพท์", Sort: 16,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "follow_up_result.new_customers", Name: "ลูกค้าใหม่", Sort: 1, IsTabs: true},
				{Slug: "follow_up_result.existing_customers", Name: "ลูกค้าเก่า", Sort: 2, IsTabs: true},
			},
		},

		// 15. แท็ก
		{
			Slug: "follow_up_tag", Name: "แท็ก", Sort: 17,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 16. การเงิน
		{
			Slug: "finance", Name: "การเงิน", Sort: 18,
			IsRead: true,
		},

		// 17. ฝาก
		{
			Slug: "finance_deposit", Name: "ฝาก", Sort: 19,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "finance_deposit.cancel", Name: "ยกเลิก", Sort: 1, IsButton: true},
				{Slug: "finance_deposit.upload_slip", Name: "อัพโหลดสลิป", Sort: 2, IsButton: true},
				{Slug: "finance_deposit.transfer_evidence", Name: "หลักฐานการโอนเงิน", Sort: 3, IsButton: true},
				{Slug: "finance_deposit.edit_account_number", Name: "แก้ไขเลขที่บัญชี", Sort: 4, IsButton: true},
				{Slug: "finance_deposit.create_bill_note", Name: "สร้างบิลโน๊ต", Sort: 5, IsButton: true},
				{Slug: "finance_deposit.instant_approve", Name: "อนุมัติทันที", Sort: 6, IsButton: true},
				{Slug: "finance_deposit.blind_eye", Name: "ปิดตา", Sort: 7, IsButton: true},
				{Slug: "finance_deposit.match_transaction_n", Name: "จับคู่รายการ (N)", Sort: 8, IsButton: true},
				{Slug: "finance_deposit.record_expense_o", Name: "บันทึกค่าใช้จ่าย (O)", Sort: 9, IsButton: true},
				{Slug: "finance_deposit.note_r", Name: "หมายเหตุ (R)", Sort: 10, IsButton: true},
				{Slug: "finance_deposit.manage_atm_transfer", Name: "จัดการยอดโอนตู้", Sort: 11, IsButton: true},
				{Slug: "finance_deposit.refund", Name: "คืนเงิน", Sort: 12, IsButton: true},
				{Slug: "finance_deposit.approve_floating_amount", Name: "อนุมัติยอดลอย", Sort: 13, IsButton: true},
				{Slug: "finance_deposit.approve_match", Name: "อนุมัติ match", Sort: 14, IsButton: true},
				{Slug: "finance_deposit.check_credit", Name: "ตรวจสอบเครดิต", Sort: 15, IsButton: true},
				{Slug: "finance_deposit.create_bot_transaction", Name: "สร้างธุรกรรมแทนบอท", Sort: 16, IsButton: true},
			},
		},

		// 18. ถอน
		{
			Slug: "finance_withdrawal", Name: "ถอน", Sort: 20,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "finance_withdrawal.try_again", Name: "ลองอีกครั้ง", Sort: 1, IsButton: true},
				{Slug: "finance_withdrawal.cancel", Name: "ยกเลิก", Sort: 2, IsButton: true},
				{Slug: "finance_withdrawal.upload_slip", Name: "อัพโหลดสลิป", Sort: 3, IsButton: true},
				{Slug: "finance_withdrawal.create_bill_note", Name: "สร้างบิลโน๊ต", Sort: 4, IsButton: true},
				{Slug: "finance_withdrawal.approve", Name: "อนุมัติ", Sort: 5, IsButton: true},
				{Slug: "finance_withdrawal.instant_approve", Name: "อนุมัติทันที", Sort: 6, IsButton: true},
				{Slug: "finance_withdrawal.transfer_evidence", Name: "หลักฐานการโอนเงิน", Sort: 7, IsButton: true},
				{Slug: "finance_withdrawal.wait_vip_check", Name: "รอวีไอพีตรวจสอบ", Sort: 8, IsButton: true},
				{Slug: "finance_withdrawal.blind_eye", Name: "ปิดตา", Sort: 9, IsButton: true},
				{Slug: "finance_withdrawal.match_transaction_n", Name: "จับคู่รายการ (N)", Sort: 10, IsButton: true},
				{Slug: "finance_withdrawal.record_expense_o", Name: "บันทึกค่าใช้จ่าย (O)", Sort: 11, IsButton: true},
				{Slug: "finance_withdrawal.note_r", Name: "หมายเหตุ (R)", Sort: 12, IsButton: true},
				{Slug: "finance_withdrawal.manage_atm_transfer", Name: "จัดการยอดโอนตู้", Sort: 13, IsButton: true},
				{Slug: "finance_withdrawal.refund", Name: "คืนเงิน", Sort: 14, IsButton: true},
				{Slug: "finance_withdrawal.check_payment_gateway", Name: "ตรวจสอบรายการ Payment Gateway", Sort: 15, IsButton: true},
				{Slug: "finance_withdrawal.reserve_job", Name: "จองงาน", Sort: 16, IsButton: true},
			},
		},

		// 19. โอนเงิน
		{
			Slug: "transfer_list", Name: "โอนเงิน", Sort: 21,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "transfer_list.retry", Name: "ทำซ้ำ", Sort: 1, IsButton: true},
				{Slug: "transfer_list.cancel", Name: "ยกเลิก", Sort: 2, IsButton: true},
				{Slug: "transfer_list.confirm", Name: "ยืนยัน", Sort: 3, IsButton: true},
				{Slug: "transfer_list.cancel_2", Name: "ยกเลิก", Sort: 4, IsButton: true},
				{Slug: "transfer_list.create_deposit_bill", Name: "สร้างบิลโน๊ต ฝาก", Sort: 5, IsButton: true},
				{Slug: "transfer_list.create_withdrawal_bill", Name: "สร้างบิลโน๊ต ถอน", Sort: 6, IsButton: true},
				{Slug: "transfer_list.create_saving_bill", Name: "สร้างบิลโน๊ต บัญชีพัก", Sort: 7, IsButton: true},
				{Slug: "transfer_list.blind_eye", Name: "ปิดตา", Sort: 8, IsButton: true},
				{Slug: "transfer_list.match_transaction_n", Name: "จับคู่รายการ (N)", Sort: 9, IsButton: true},
				{Slug: "transfer_list.record_expense_o", Name: "บันทึกค่าใช้จ่าย (O)", Sort: 10, IsButton: true},
				{Slug: "transfer_list.note_r", Name: "หมายเหตุ (R)", Sort: 11, IsButton: true},
				{Slug: "transfer_list.external_transfer_t", Name: "โอนเงินนอกระบบ (T)", Sort: 12, IsButton: true},
				{Slug: "transfer_list.manage_atm_transfer", Name: "จัดการยอดโอนตู้", Sort: 13, IsButton: true},
				{Slug: "transfer_list.refund", Name: "คืนเงิน", Sort: 14, IsButton: true},
			},
		},

		// 20. รอฝาก
		{
			Slug: "depositwaiting", Name: "รอฝาก", Sort: 22,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true, IsTabs: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "depositwaiting.pending_amount", Name: "ยอดรอฝาก", Sort: 1, IsTabs: true},
				{Slug: "depositwaiting.expense_amount", Name: "ยอดใช้จ่าย", Sort: 2, IsTabs: true},
				{Slug: "depositwaiting.blind_eye", Name: "ปิดตา", Sort: 3, IsButton: true},
				{Slug: "depositwaiting.match_transaction_n", Name: "จับคู่รายการ (N)", Sort: 4, IsButton: true},
				{Slug: "depositwaiting.record_expense_o", Name: "บันทึกค่าใช้จ่าย (O)", Sort: 5, IsButton: true},
				{Slug: "depositwaiting.note_r", Name: "หมายเหตุ (R)", Sort: 6, IsButton: true},
			},
		},

		// 21. ธุรกรรมไม่จับคู่
		{
			Slug: "transaction_not_match", Name: "ธุรกรรมไม่จับคู่", Sort: 23,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "transaction_not_match.blind_eye", Name: "ปิดตา", Sort: 1, IsButton: true},
				{Slug: "transaction_not_match.match_transaction_n", Name: "จับคู่รายการ (N)", Sort: 2, IsButton: true},
				{Slug: "transaction_not_match.record_expense_o", Name: "บันทึกค่าใช้จ่าย (O)", Sort: 3, IsButton: true},
				{Slug: "transaction_not_match.note_r", Name: "หมายเหตุ (R)", Sort: 4, IsButton: true},
				{Slug: "transaction_not_match.external_transfer_t", Name: "โอนเงินนอกระบบ (T)", Sort: 5, IsButton: true},
			},
		},

		// 22. เปรียบเทียบยอดจำลอง
		{
			Slug: "report_system_balance", Name: "เปรียบเทียบยอดจำลอง", Sort: 24,
			IsRead: true,
		},

		// 23. รายงานเงินเข้าออกบัญชี
		{
			Slug: "bank_account_detail", Name: "รายงานเงินเข้าออกบัญชี", Sort: 25,
			IsRead: true,
		},

		// 24. เปรียบเทียบธุรกรรม
		{
			Slug: "compare_transaction", Name: "เปรียบเทียบธุรกรรม", Sort: 26,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 25. รายงาน
		{
			Slug: "report", Name: "รายงาน", Sort: 27,
			IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "report.show_all", Name: "แสดงทั้งหมด", Sort: 1, IsButton: true},
			},
		},

		// 26. สรุปรายการเดิมพัน
		{
			Slug: "summary_bet_transaction", Name: "สรุปรายการเดิมพัน", Sort: 28,
			IsRead: true,
		},

		// 27. สรุปรายการเดิมพัน (รายละเอียด)
		{
			Slug: "summary_bet_transaction_agent", Name: "สรุปรายการเดิมพัน (รายละเอียด)", Sort: 29,
			IsRead: true,
		},

		// 28. การเดิมพันรายบุคคล
		{
			Slug: "summary_bet_transaction_by_user", Name: "การเดิมพันรายบุคคล", Sort: 31,
			IsRead: true,
		},

		// 29. ยอดรายบุคคล
		{
			Slug: "report_summary_by_users", Name: "ยอดรายบุคคล", Sort: 32,
			IsRead: true,
		},

		// 30. กะการทำงาน
		{
			Slug: "report_shifts", Name: "กะการทำงาน", Sort: 33, IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "report_shifts.work_shifts", Name: "กะการทำงาน", Sort: 1, IsTabs: true},
				{Slug: "report_shifts.shift_history", Name: "ประวัติการตัดกะ", Sort: 2, IsTabs: true},
				{Slug: "report_shifts.balance_history", Name: "ประวัติการตัดยอด", Sort: 3, IsTabs: true},
			},
		},

		// 31. รายงานโปรโมชั่น
		{
			Slug: "report_promotions", Name: "รายงานโปรโมชั่น", Sort: 34, IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "report_promotions.promotion_report", Name: "รายงานโปรโมชั่น", Sort: 1, IsTabs: true},
				{Slug: "report_promotions.deposit_after_promo", Name: "รายงานจำนวนคนฝากหลังรับโปร", Sort: 2, IsTabs: true},
				{Slug: "report_promotions.activity_turn", Name: "รายงานกิจกรรมเทิร์น", Sort: 3, IsTabs: true},
				{Slug: "report_promotions.user_rewards", Name: "รายงานรางวัลผู้ใช้", Sort: 4, IsTabs: true},
				{Slug: "report_promotions.return_loss", Name: "รายงานคืนยอดเสีย", Sort: 5, IsTabs: true},
			},
		},

		// 32. เพื่อนชวนเพื่อน
		{
			Slug: "report_affiliate", Name: "เพื่อนชวนเพื่อน", Sort: 35,
			IsRead: true,
		},

		// 33. ย้ายพันธมิตร
		{
			Slug: "report_change_partner", Name: "ย้ายพันธมิตร", Sort: 36,
			IsRead: true,
		},

		// 34. ช่องทางสมัครสมาชิก
		{
			Slug: "report_member", Name: "ช่องทางสมัครสมาชิก", Sort: 37, IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "report_member.registration_channels", Name: "ช่องทางสมัครสมาชิก", Sort: 1, IsTabs: true},
				{Slug: "report_member.member_and_deposit", Name: "สมาชิกและฝาก", Sort: 2, IsTabs: true},
				{Slug: "report_member.repeat_deposit", Name: "จำนวนคนฝากเล่นซ้ำ", Sort: 3, IsTabs: true},
			},
		},

		// 35. การเข้าสู่ระบบ
		{
			Slug: "log_authen", Name: "การเข้าสู่ระบบ", Sort: 38, IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "log_authen.customer_login", Name: "รายงานเข้าสู่ระบบ (ลูกค้า)", Sort: 1, IsTabs: true},
				{Slug: "log_authen.admin_login", Name: "ผู้ดูแลระบบ", Sort: 2, IsTabs: true},
				{Slug: "log_authen.locked_users", Name: "ผู้ใช้ที่ถูกล็อค", Sort: 3, IsTabs: true},
			},
		},

		// 36. รายงานเดินบัญชี
		{
			Slug: "report_log_web", Name: "รายงานเดินบัญชี", Sort: 39,
			IsRead: true,
		},

		// 37. รายงานเดินบัญชีแบบกลุ่ม
		{
			Slug: "report_log_bet_transaction", Name: "รายงานเดินบัญชีแบบกลุ่ม", Sort: 40,
			IsRead: true,
		},

		// 38. กราฟช่วงเวลาฝากถอน
		{
			Slug: "report_web", Name: "กราฟช่วงเวลาฝากถอน", Sort: 42,
			IsRead: true,
		},

		// 39. รายงานพันธมิตร
		{
			Slug: "report_partner", Name: "รายงานพันธมิตร", Sort: 43, IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "report_partner.overview", Name: "ภาพรวม", Sort: 1, IsTabs: true},
				{Slug: "report_partner.details", Name: "รายละเอียด", Sort: 2, IsTabs: true},
			},
		},

		// 40. ตรวจสอบ
		{
			Slug: "report_transaction_audit", Name: "ตรวจสอบ", Sort: 44,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 41. รายงานสรุปรายได้เพื่อนชวนเพื่อนและคอมมิชชั่น
		{
			Slug: "report_summary_wallet_log", Name: "รายงานสรุปรายได้เพื่อนชวนเพื่อนและคอมมิชชั่น", Sort: 45, IsRead: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "report_summary_wallet_log.affiliate_income", Name: "รายงานสรุปรายได้เพื่อนชวนเพื่อน", Sort: 1, IsTabs: true},
				{Slug: "report_summary_wallet_log.commission_income", Name: "รายงานสรุปรายได้คอมมิชชั่น", Sort: 2, IsTabs: true},
			},
		},

		// 42. รายงานสรุปผล ยอดคอมมิชชั่น/ยอดเพื่อนชวนเพื่อน
		{
			Slug: "summary_commission_affiliate", Name: "รายงานสรุปผล ยอดคอมมิชชั่น/ยอดเพื่อนชวนเพื่อน", Sort: 46,
			IsRead: true,
		},

		// 43. รายงานสรุป KPI
		{
			Slug: "report_summary_kpi", Name: "รายงานสรุป KPI", Sort: 47,
			IsRead: true,
		},

		// 44. รายงานสรุปข้อมูลสมาชิก
		{
			Slug: "report_summary_user", Name: "รายงานสรุปข้อมูลสมาชิก", Sort: 48,
			IsRead: true,
		},

		// 45. ดาวน์โหลดรายงาน
		{
			Slug: "export_report", Name: "ดาวน์โหลดรายงาน", Sort: 49,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 46. รายงานโทรหาลูกค้า
		{
			Slug: "summary_voip", Name: "รายงานโทรหาลูกค้า", Sort: 50,
			IsRead: true,
		},

		// 47. รายงานสรุปค่าใช้จ่ายเครดิต
		{
			Slug: "report_summary_credits", Name: "รายงานสรุปค่าใช้จ่ายเครดิต", Sort: 51,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 48. โปรโมชั่น
		{
			Slug: "promotions_promotions", Name: "โปรโมชั่น", Sort: 53,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 49. กลุ่มโปรโมชั่น
		{
			Slug: "promotions_promotion_groups", Name: "กลุ่มโปรโมชั่น", Sort: 54,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 50. จัดการกิจกรรม
		{
			Slug: "management_event", Name: "จัดการกิจกรรม", Sort: 55,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 51. ผู้ใช้ติดเงื่อนไข
		{
			Slug: "promotions_user_promotions", Name: "ผู้ใช้ติดเงื่อนไข", Sort: 56,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 52. คืนยอดเสีย
		{
			Slug: "return_loss_list", Name: "คืนยอดเสีย", Sort: 58,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "return_loss_list.set_user_group", Name: "ตั้งค่ากลุ่มผู้ใช้", Sort: 1, IsButton: true},
				{Slug: "return_loss_list.repair_return_loss", Name: "ซ่อมคืนยอดเสีย", Sort: 2, IsButton: true},
			},
		},

		// 53. รายงาน คืนยอดเสีย
		{
			Slug: "report_audit_return_loss", Name: "รายงาน คืนยอดเสีย", Sort: 59,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 54. จัดการการแจ้งเตือน
		{
			Slug: "notification_list", Name: "จัดการการแจ้งเตือน", Sort: 60,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 55. บัญชีฝาก
		{
			Slug: "bank_deposit", Name: "บัญชีฝาก", Sort: 64,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 56. บัญชีถอน
		{
			Slug: "bank_withdrawal", Name: "บัญชีถอน", Sort: 65,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 57. บัญชีพัก
		{
			Slug: "bank_saving", Name: "บัญชีพัก", Sort: 66,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 58. บัญชี Payment Gateway
		{
			Slug: "paymentgateway", Name: "บัญชี Payment Gateway", Sort: 68,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "paymentgateway.deposit_status", Name: "สถานะการฝาก", Sort: 1, IsButton: true},
				{Slug: "paymentgateway.withdrawal_status", Name: "สถานะการถอน", Sort: 2, IsButton: true},
				{Slug: "paymentgateway.transfer_status", Name: "สถานะการโอน", Sort: 3, IsButton: true},
				{Slug: "paymentgateway.decimal", Name: "ทศนิยม", Sort: 4, IsButton: true},
				{Slug: "paymentgateway.status", Name: "สถานะ", Sort: 5, IsButton: true},
				{Slug: "paymentgateway.add_payment_method", Name: "เพิ่มวิธีการชำระเงิน", Sort: 6, IsButton: true},
				{Slug: "paymentgateway.free_fee", Name: "ฟรีค่าธรรมเนียม", Sort: 7, IsButton: true},
				{Slug: "paymentgateway.status_2", Name: "สถานะ", Sort: 8, IsButton: true},
				{Slug: "paymentgateway.lobby", Name: "Lobby", Sort: 9, IsButton: true},
				{Slug: "paymentgateway.delete", Name: "ลบ", Sort: 10, IsButton: true},
				{Slug: "paymentgateway.save", Name: "บันทึก", Sort: 11, IsButton: true},
			},
		},

		// 59. ธนาคาร
		{
			Slug: "banks", Name: "ธนาคาร", Sort: 69,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 60. บอท
		{
			Slug: "bot", Name: "บอท", Sort: 70,
			IsRead: true,
		},

		// 61. บอทธนาคาร
		{
			Slug: "botbank", Name: "บอทธนาคาร", Sort: 71,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 62. จัดการผู้ดูแลระบบ
		{
			Slug: "admins", Name: "จัดการผู้ดูแลระบบ", Sort: 73,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "admins.reset_2fa", Name: "รีเซ็ต 2FA", Sort: 1, IsButton: true},
				{Slug: "admins.edit_history", Name: "ประวัติการแก้ไข", Sort: 2, IsButton: true},
			},
		},

		// 63. จัดการบทบาทหน้าที่
		{
			Slug: "roles", Name: "จัดการบทบาทหน้าที่", Sort: 74,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 64. บัญชี
		{
			Slug: "partner_accounts", Name: "บัญชี", Sort: 77,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "partner_accounts.popup_partner_info", Name: "ป๊อปอัพข้อมูลพันธมิตร", Sort: 1, IsButton: true},
			},
		},

		// 65. ช่องทาง
		{
			Slug: "partner_groups", Name: "ช่องทาง", Sort: 78,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 66. ตั้งค่าเว็บไซต์
		{
			Slug: "website_setting", Name: "ตั้งค่าเว็บไซต์", Sort: 80,
			IsRead: true,
		},

		// 67. แบนเนอร์
		{
			Slug: "image_slides", Name: "แบนเนอร์", Sort: 84,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 68. ผู้ให้บริการ SMS
		{
			Slug: "sms_provider", Name: "ผู้ให้บริการ SMS", Sort: 85,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 69. ผู้ให้ผู้บริการ VoIP
		{
			Slug: "voip_provider", Name: "ผู้ให้ผู้บริการ VoIP", Sort: 86,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 70. ช่องทางการติดต่อ
		{
			Slug: "contact_channels", Name: "ช่องทางการติดต่อ", Sort: 87,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 71. ตั้งค่าไลฟ์แชท
		{
			Slug: "config_live_chat", Name: "ตั้งค่าไลฟ์แชท", Sort: 88,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 72. ตั้งค่า BO
		{
			Slug: "config_bo", Name: "ตั้งค่า BO", Sort: 89,
		},

		// 73. Bet Limit หวย
		{
			Slug: "config_bo_lotto", Name: "Bet Limit หวย", Sort: 90,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 74. Bet Limit กีฬา
		{
			Slug: "config_bo_sport", Name: "Bet Limit กีฬา", Sort: 91,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 75. แนะนำการใช้งาน
		{
			Slug: "user_guide", Name: "แนะนำการใช้งาน", Sort: 93,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 76. รีวิว
		{
			Slug: "review", Name: "รีวิว", Sort: 94,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "review.customer_reviews", Name: "รีวิวจากลูกค้า", Sort: 1, IsTabs: true},
				{Slug: "review.website_reviews", Name: "รีวิวจากเว็บไซต์", Sort: 2, IsTabs: true},
			},
		},

		// 77. ตั้งค่าประเภทเกม
		{
			Slug: "gametype_setting", Name: "ตั้งค่าประเภทเกม", Sort: 96,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 78. ตั้งค่าค่ายเกม
		{
			Slug: "provider_setting", Name: "ตั้งค่าค่ายเกม", Sort: 97,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 79. จัดเรียงเกม
		{
			Slug: "game_setting", Name: "จัดเรียงเกม", Sort: 98,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 80. มินิเกม
		{
			Slug: "mini_game", Name: "มินิเกม", Sort: 99,
			IsRead: true,
		},

		// 81. รายการรางวัล
		{
			Slug: "reward_item", Name: "รายการรางวัล", Sort: 101,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 82. ล็อคอินประจำวัน
		{
			Slug: "daily_login", Name: "ล็อคอินประจำวัน", Sort: 102,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 83. ภารกิจ
		{
			Slug: "mission", Name: "ภารกิจ", Sort: 103,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 84. วงล้อเสี่ยงโชค
		{
			Slug: "spinner", Name: "วงล้อเสี่ยงโชค", Sort: 104,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "spinner.add_item", Name: "เพิ่มรายการ", Sort: 1, IsButton: true},
				{Slug: "spinner.edit", Name: "แก้ไข", Sort: 2, IsButton: true},
				{Slug: "spinner.delete", Name: "ลบ", Sort: 3, IsButton: true},
				{Slug: "spinner.history", Name: "ประวัติ", Sort: 4, IsButton: true},
				{Slug: "spinner.simulate_reward", Name: "จำลองรางวัล", Sort: 5, IsButton: true},
				{Slug: "spinner.delete_2", Name: "ลบ", Sort: 6, IsButton: true},
				{Slug: "spinner.add", Name: "เพิ่ม", Sort: 7, IsButton: true},
			},
		},

		// 85. รายงานการใช้งาน
		{
			Slug: "report_log_action", Name: "รายงานการใช้งาน", Sort: 105,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 86. รายงานภารกิจผู้ใช้
		{
			Slug: "report_mission", Name: "รายงานภารกิจผู้ใช้", Sort: 106,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 87. รายงานล็อคอินประจำวันผู้ใช้
		{
			Slug: "report_daily_login", Name: "รายงานล็อคอินประจำวันผู้ใช้", Sort: 107,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 88. จัดการกิจกรรม
		{
			Slug: "config_event_list", Name: "จัดการกิจกรรม", Sort: 109,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 89. ตั้งค่าบอล
		{
			Slug: "config_football", Name: "ตั้งค่าบอล", Sort: 111,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 90. รายการทายผลบอล
		{
			Slug: "list_predict_football", Name: "รายการทายผลบอล", Sort: 112,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 91. ตั้งค่ามวย
		{
			Slug: "config_boxing", Name: "ตั้งค่ามวย", Sort: 114,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 92. รายการทายผลมวย
		{
			Slug: "list_predict_boxing", Name: "รายการทายผลมวย", Sort: 115,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 93. ตั้งค่าหวย
		{
			Slug: "config_lottery", Name: "ตั้งค่าหวย", Sort: 117,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 94. รายการทายผลหวย
		{
			Slug: "list_predict_lottery", Name: "รายการทายผลหวย", Sort: 118,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 95. ตั้งค่าประกาศ
		{
			Slug: "config_info", Name: "ตั้งค่าประกาศ", Sort: 120,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 96. รายการกิจกรรม
		{
			Slug: "config_reward", Name: "รายการกิจกรรม", Sort: 122,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 97. ตั้งค่ากำหนดเอง
		{
			Slug: "config_custom", Name: "ตั้งค่ากำหนดเอง", Sort: 124,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 98. รายการทายผลกำหนดเอง
		{
			Slug: "list_predict_custom", Name: "รายการทายผลกำหนดเอง", Sort: 125,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 99. ซัพพอร์ต
		{
			Slug: "support", Name: "ซัพพอร์ต", Sort: 126,
			IsRead: true,
		},

		// 100. ธุรกรรม
		{
			Slug: "transaction_support", Name: "ธุรกรรม", Sort: 127,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 101. ตั้งค่าแจ้งเตือนแชทกลุ่ม
		{
			Slug: "notify_group_chat", Name: "ตั้งค่าแจ้งเตือนแชทกลุ่ม", Sort: 129,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 102. ตั้งค่าการจำกัด IP
		{
			Slug: "lobby_lock_ip", Name: "ตั้งค่าการจำกัด IP", Sort: 130,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 103. ตั้งค่าเว็บไซต์
		{
			Slug: "web_setting", Name: "ตั้งค่าเว็บไซต์", Sort: 132,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 104. ตั้งค่าทั่วไป
		{
			Slug: "general_setting", Name: "ตั้งค่าทั่วไป", Sort: 133,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 105. ตั้งค่าธีมโปรโมชั่น
		{
			Slug: "theme_promotion_setting", Name: "ตั้งค่าธีมโปรโมชั่น", Sort: 134,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 106. ตั้งค่าธีมเว็บ
		{
			Slug: "theme_web_setting", Name: "ตั้งค่าธีมเว็บ", Sort: 135,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 107. ภาพประกอบเว็บ
		{
			Slug: "image_setting", Name: "ภาพประกอบเว็บ", Sort: 136,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 108. ตั้งค่า App ดาวน์โหลด
		{
			Slug: "app_download_setting", Name: "ตั้งค่า App ดาวน์โหลด", Sort: 137,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 109. ตั้งค่าธีมเว็บแอดมิน
		{
			Slug: "theme_webadmin", Name: "ตั้งค่าธีมเว็บแอดมิน", Sort: 138,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 110. รายงานค่าใช้จ่ายในเว็บ (O)
		{
			Slug: "web_expenses", Name: "รายงานค่าใช้จ่ายในเว็บ (O)", Sort: 146,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "web_expenses.web_expense_report", Name: "รายงานค่าใช้จ่ายในเว็บ", Sort: 1, IsTabs: true},
				{Slug: "web_expenses.expense_categories", Name: "หัวข้อรายงานค่าใช้จ่าย", Sort: 2, IsTabs: true},
			},
		},

		// 111. ตั้งค่า Live แจกรางวัล
		{
			Slug: "event_live", Name: "ตั้งค่า Live แจกรางวัล", Sort: 147,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
		},

		// 112. บัญชีตรวจสอบ
		{
			Slug: "bank_account_audit", Name: "บัญชีตรวจสอบ", Sort: 149,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "bank_account_audit.enable_audit", Name: "เปิดใช้งาน: การตรวจสอบบัญชี", Sort: 1, IsButton: true},
			},
		},

		// 113. ตั้งค่า respond.io
		{
			Slug: "setting_webhook", Name: "ตั้งค่า respond.io", Sort: 154,
			IsRead: true,
		},

		// 114. รายงานตรวจสอบสลิปการฝากเงิน
		{
			Slug: "support_respond_io", Name: "รายงานตรวจสอบสลิปการฝากเงิน", Sort: 155,
			IsCreate: true, IsRead: true, IsUpdate: true, IsDelete: true,
			SubPermissions: []PermissionTemplate{
				{Slug: "support_respond_io.show_details", Name: "แสดงรายละเอียด", Sort: 1, IsButton: true},
			},
		},
	}
}
