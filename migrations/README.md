# PGX Native Migration System

This migration system uses PGX native Go code for database migrations instead of SQL files, providing better type safety, error handling, and transaction management.

## Features

- **PGX Native**: Uses native PGX transactions for better PostgreSQL compatibility
- **Atomic Migrations**: Each migration runs in a single transaction
- **Backup Support**: Automatic backups before applying migrations in production
- **Dry Run**: Preview what migrations would run without executing them
- **Validation**: Built-in migration validation
- **Seeders**: Separate seeder system for default data
- **Schema Support**: Multi-schema support via environment variables

## Directory Structure

```
migrations/
├── list/                           # Migration files
│   ├── 01_create_init_tables.go    # Initial system tables
│   ├── 02_create_banking_system.go # Banking and payment system
│   ├── 03_create_faq_system.go     # FAQ management
│   ├── 04_create_member_system.go  # Member management system
│   └── 05_create_contact_sms_system.go # Contact and SMS system
├── seeders/                        # Seeder files
│   └── 01_seed_default_data.go     # Default system data
├── migrations.go                   # Migration management system
└── README.md                       # This file
```

## Migration Commands

### Run All Pending Migrations
```bash
go run cmd/migration/main.go local up
```

### Rollback Migrations
```bash
# Rollback 1 migration (default)
go run cmd/migration/main.go local down

# Rollback 3 migrations
go run cmd/migration/main.go local down 3
```

### Check Migration Status
```bash
go run cmd/migration/main.go local status
```

### Validate Migrations
```bash
go run cmd/migration/main.go local validate
```

### Dry Run (Preview)
```bash
go run cmd/migration/main.go local dry-run
```

### Run Seeders
```bash
# Run all seeders
go run cmd/migration/main.go local seed all

# Run specific seeder
go run cmd/migration/main.go local seed SeedDefaultData
```

## Migration Structure

Each migration must implement the `Migration` interface:

```go
type Migration interface {
    GetName() string
    Up(con pgx.Tx)
    Down(con pgx.Tx)
}
```

### Example Migration

```go
package list

import (
    "context"
    "fmt"
    "github.com/jackc/pgx/v5"
    "log"
    "os"
)

type CreateExampleTable struct{}

func (m *CreateExampleTable) GetName() string {
    return "CreateExampleTable"
}

func (m *CreateExampleTable) Up(con pgx.Tx) {
    ctx := context.Background()
    
    // Get schema name from environment
    schemaName := os.Getenv("DATABASE_SCHEMA")
    if schemaName == "" {
        schemaName = "public"
    }

    // Set search_path
    _, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
    if err != nil {
        panic(fmt.Sprintf("Failed to set search_path: %v", err))
    }

    // Create table
    _, err = con.Exec(ctx, `
        CREATE TABLE IF NOT EXISTS example_table (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    `)
    if err != nil {
        panic(err)
    }

    log.Printf("Migration: Example table created successfully")
}

func (m *CreateExampleTable) Down(con pgx.Tx) {
    ctx := context.Background()
    
    schemaName := os.Getenv("DATABASE_SCHEMA")
    if schemaName == "" {
        schemaName = "public"
    }

    _, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
    if err != nil {
        panic(fmt.Sprintf("Failed to set search_path: %v", err))
    }

    // Drop table
    _, err = con.Exec(ctx, "DROP TABLE IF EXISTS example_table")
    if err != nil {
        panic(err)
    }
}
```

## Seeder Structure

Each seeder must implement the `Seeder` interface:

```go
type Seeder interface {
    GetName() string
    Up(con pgx.Tx)
    Down(con pgx.Tx)
}
```

## Database Tables Created

### Core System Tables
- `users` - System users (admins, agents)
- `user_roles` - User role definitions
- `user_role_permissions` - Role-based permissions
- `user_audit_logs` - User activity tracking
- `user_2fa` - Two-factor authentication

### Permission System
- `permission_groups` - Permission group categories
- `permissions` - Hierarchical permission system

### Member System
- `members` - Website members/players
- `member_groups` - Member categorization
- `member_group_types` - VIP/membership types
- `member_audit_logs` - Member activity tracking
- `commission_groups` - Commission rate groups
- `referral_groups` - Referral program groups

### Banking & Payment System
- `banking` - Bank information
- `payment_method` - Payment methods
- `deposit_account` - Deposit accounts
- `withdraw_account` - Withdrawal accounts
- `holding_account` - Holding accounts
- `payment_gateway_account` - Payment gateway configs
- `algorithm` - Processing algorithms
- `auto_bot` - Automation settings

### Communication System
- `contact` - Contact information
- `sms_provider` - SMS service providers
- `sms_provider_name` - SMS provider names
- `otps` - One-time passwords

### Content Management  
- `faqs` - Frequently asked questions
- `banners` - Website banners

### System Configuration
- `system_settings` - System configuration
- `languages` - Supported languages
- `platforms` - Platform information
- `allowed_ips` - IP whitelist
- `login_attempts` - Login attempt tracking
- `channels` - Marketing channels

## Environment Variables

- `DATABASE_SCHEMA` - Database schema name (default: "public")
- `ENVIRONMENT` - Environment name (affects backup behavior)

## Safety Features

1. **Transaction Safety**: All migrations run in transactions with automatic rollback on failure
2. **Production Backups**: Automatic backups in production environment
3. **Validation**: SQL validation prevents dangerous operations in production
4. **Dry Run**: Preview changes before applying
5. **Rollback Support**: All migrations include rollback functionality

## Adding New Migrations

1. Create a new migration file in `migrations/list/`
2. Implement the `Migration` interface
3. Add the migration to `GetAllMigrations()` in `migrations.go`
4. Test with dry-run before applying

## Adding New Seeders

1. Create a new seeder file in `migrations/seeders/`
2. Implement the `Seeder` interface  
3. Add the seeder to `GetAllSeeders()` in `migrations.go`
4. Test in development environment first

## Best Practices

1. **Always test migrations** in development first
2. **Use transactions** for data consistency
3. **Include rollback logic** in Down() methods
4. **Add proper indexing** for performance
5. **Use meaningful names** for migrations
6. **Keep migrations atomic** - one logical change per migration
7. **Backup production** before major changes
8. **Validate SQL** before production deployment