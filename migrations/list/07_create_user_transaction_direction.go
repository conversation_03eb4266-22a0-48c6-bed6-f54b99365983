package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateUserTransactionDirection struct{}

func (m *CreateUserTransactionDirection) GetName() string {
	return "CreateUserTransactionDirection"
}

func (m *CreateUserTransactionDirection) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating user transaction direction table")

	// Create user_transaction_direction table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS user_transaction_direction (
			id SERIAL PRIMARY KEY,
			name VARCHAR(100) NOT NULL UNIQUE,
			detail TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Insert default transaction directions
	_, err = con.Exec(ctx, `
		INSERT INTO user_transaction_direction (id, name, detail, created_at) VALUES
		(1, 'DEPOSIT', 'ฝาก', '2025-08-02 09:00:00'),
		(2, 'WITHDRAW', 'ถอน', '2025-08-02 09:00:00')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	log.Printf("Migration: User transaction direction table created successfully")
}

func (m *CreateUserTransactionDirection) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop table
	_, err = con.Exec(ctx, "DROP TABLE IF EXISTS user_transaction_direction")
	if err != nil {
		panic(err)
	}
}
