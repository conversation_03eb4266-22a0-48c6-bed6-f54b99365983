-- ================================
-- LOOKUP/REFERENCE TABLES (No dependencies)
-- ================================

CREATE TABLE promotion_web_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255) NULL,
    label_en VARCHAR(255) NULL
);

INSERT INTO promotion_web_type (id, name, label_th, label_en) VALUES
(1, 'NEW_MEMBER_FREE', 'โปรโมชั่นสมัครใหม่แจกฟรี', 'new member free'),
(2, 'NEW_MEMBER_CONDITION', 'โปรโมชั่นสมัครใหม่ตามมเงื่อนไข', 'new member condition'),
(3, 'DEPOSIT_MINIMUM_PER_DAY', 'โปรโมชั่นฝากขั้นต่ำต่อวัน', 'deposit minimum per day'),
(4, 'FIRST_DEPOSIT', 'โปรโมชั่นฝากครั้งแรก', 'first deposit'),
(5, 'DEPOSIT_PER_DAY', 'โปรโมชั่นฝากทั้งวัน', 'deposit per day'),
(6, 'DEPOSIT_BY_TIME', 'โปรโมชั่นฝากตามช่วงเวลา', 'deposit by time'),
(7, 'FIRST_DEPOSIT_OF_DAY', 'โปรโมชั้นฝากครั้งแรกของวัน', 'first deposit of the day');

CREATE TABLE promotion_web_status (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255) NULL,
    label_en VARCHAR(255) NULL
);

INSERT INTO promotion_web_status (id, name, label_th, label_en) VALUES
(1, 'DISABLE_WEB', 'ปิดการแสดงผลหน้าเว็บไซต์แต่เปิดใช้งานอยู่', 'disactive web'),
(2, 'ACTIVE', 'ใช้งาน', 'active'),
(3, 'CANCELED', 'ยกเลิก', 'canceled'),
(4, 'SHOW_ONLY', 'เปิดแสดงแต่ไม่ใช้งาน', 'show only'),
(5, 'ONLY_URL', 'เฉพาะลูกค้าที่เข้าจากลิ้งก์', 'use link');

CREATE TABLE promotion_web_date_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255) NULL,
    label_en VARCHAR(255) NULL
);

INSERT INTO promotion_web_date_type (id, name, label_th, label_en) VALUES
(1, 'FIXED_DATE', 'เริ่มต้น-สิ้นสุด', 'fixed date'),
(2, 'NON_FIXED_DATE', 'ไม่กำหนดระยะเวลา', 'non fixed date');

CREATE TABLE promotion_web_bonus_condition (
    id BIGSERIAL PRIMARY KEY,
    syntax VARCHAR(10) NULL,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255) NULL,
    label_en VARCHAR(255) NULL
);

INSERT INTO promotion_web_bonus_condition (id, syntax, name, label_th, label_en) VALUES
(1, '>=', 'MORE_THAN_OR_EQUAL', 'มากกว่าหรือเท่ากับ', 'more than or equal'),
(2, '<=', 'LESS_THAN_OR_EQUAL', 'น้อยกว่าหรือเท่ากับ', 'less than or equal');

CREATE TABLE promotion_web_bonus_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255) NULL,
    label_en VARCHAR(255) NULL
);

INSERT INTO promotion_web_bonus_type (id, name, label_th, label_en) VALUES
(1, 'PERCENT', 'เปอร์เซ็นต์', 'percent'),
(2, 'FIXED RATE', 'จำนวนเงิน', 'fixed rate');

CREATE TABLE promotion_web_turnover_type (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255) NULL,
    label_en VARCHAR(255) NULL
);

INSERT INTO promotion_web_turnover_type (id, name, label_th, label_en) VALUES
(1, 'ALL', 'ทุกเกม', 'all'),
(2, 'SPORT', 'กีฬา', 'sport'),
(3, 'CASINO', 'คาสิโน', 'casino'),
(4, 'SLOT', 'สล็อต', 'slot'),
(5, 'P2P', 'P2P', 'p2p'),
(6, 'LOTTERY', 'ลอตเตอรี่', 'lottery'),
(7, 'FINANCIAL', 'การเงิน', 'financial');

CREATE TABLE promotion_web_user_status (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255) NULL,
    label_en VARCHAR(255) NULL
);

INSERT INTO promotion_web_user_status (id, name, label_th, label_en) VALUES
(1, 'ON_PROCESS', 'รอผ่านเงื่อนไข', 'on process'),
(2, 'SUCCESS', 'สำเร็จ', 'success'),
(3, 'CANCELED', 'ยกเลิก', 'canceled'),
(4, 'WITHDRAW_ON_PROCESS', 'อยู่ระหว่างเงื่อนไขถอน', 'withdraw on process');

CREATE TABLE promotion_web_register_member_status (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    label_th VARCHAR(255) NOT NULL,
    label_en VARCHAR(255) NOT NULL,
    deleted_at TIMESTAMP NULL
);

INSERT INTO promotion_web_register_member_status (id, name, label_th, label_en, deleted_at) VALUES
(1, 'REGISTER_PENDING', 'รอการยืนยัน', 'register pending', NULL),
(2, 'REGISTER_CONFIRM', 'ยืนยัน', 'register confirm', NULL),
(3, 'REGISTER_REJECT', 'ปฏิเสธ', 'register reject', NULL);

CREATE TABLE promotion_return_turn_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO promotion_return_turn_type (id, name, created_at) VALUES
(1, 'คืนยอดเสียเมื่อยอดเทิร์นเกิน', '2023-10-06 08:15:30');

CREATE TABLE promotion_return_turn_status (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO promotion_return_turn_status (id, name, created_at) VALUES
(1, 'PENDING', '2023-10-09 06:52:18'),
(2, 'READY', '2023-10-09 06:52:18'),
(3, 'TAKEN', '2023-10-09 06:52:18'),
(4, 'EXPIRED', '2023-10-09 06:52:18');

CREATE TABLE promotion_return_turn_cut_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO promotion_return_turn_cut_type (id, name, created_at) VALUES
(1, 'รายวัน', '2023-10-06 08:15:31'),
(2, 'รายสัปดาห์', '2023-10-06 08:15:31');

CREATE TABLE promotion_return_loser_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO promotion_return_loser_type (id, name, created_at) VALUES
(1, 'คืนยอดเสียเมื่อยอดเสียเกิน', '2023-10-06 08:15:30');

CREATE TABLE promotion_return_loser_status (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO promotion_return_loser_status (id, name, created_at) VALUES
(1, 'PENDING', '2023-10-09 06:52:18'),
(2, 'READY', '2023-10-09 06:52:18'),
(3, 'TAKEN', '2023-10-09 06:52:18'),
(4, 'EXPIRED', '2023-10-09 06:52:18');

CREATE TABLE promotion_return_cut_type (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO promotion_return_cut_type (id, name, created_at) VALUES
(1, 'รายวัน', '2023-10-06 08:15:31'),
(2, 'รายสัปดาห์', '2024-05-21 06:50:11');

-- ================================
-- MAIN PROMOTION TABLE (depends on lookup tables)
-- ================================

CREATE TABLE promotion_web (
    id BIGSERIAL PRIMARY KEY,
    priority_order BIGINT NOT NULL DEFAULT 0,
    promotion_web_type_id BIGINT NOT NULL,
    promotion_web_status_id BIGINT NOT NULL,
    condition_detail VARCHAR(255) NULL,
    image_url VARCHAR(255) NULL,
    name VARCHAR(255) NOT NULL,
    short_description VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    promotion_web_date_type_id BIGINT NOT NULL,
    start_date DATE NULL,
    end_date DATE NULL,
    free_bonus_amount DECIMAL(10,2) NULL,
    privilege_per_day INTEGER NULL,
    able_withdraw_morethan DECIMAL(10,2) NULL,
    promotion_web_bonus_condition_id BIGINT NULL,
    bonus_condition_amount DECIMAL(10,2) NULL,
    promotion_web_bonus_type_id BIGINT NULL,
    bonus_type_amount DECIMAL(10,2) NULL,
    bonus_type_amount_max DECIMAL(10,2) NULL,
    able_withdraw_pertime DECIMAL(10,2) NULL,
    promotion_web_turnover_type_id BIGINT NULL,
    turnover_amount DECIMAL(10,2) NULL,
    monday BOOLEAN NULL,
    tuesday BOOLEAN NULL,
    wednesday BOOLEAN NULL,
    thursday BOOLEAN NULL,
    friday BOOLEAN NULL,
    saturday BOOLEAN NULL,
    sunday BOOLEAN NULL,
    time_start TIME NULL,
    time_end TIME NULL,
    hidden_url_link VARCHAR(255) NOT NULL DEFAULT '',
    pg_hard_credit_free_spin INTEGER NOT NULL DEFAULT 0,
    pg_hard_code_game_free_spin VARCHAR(255) NOT NULL DEFAULT '',
    pg_hard_is_active BOOLEAN NOT NULL DEFAULT FALSE,
    created_by_user_id BIGINT NULL,
    updated_by_user_id BIGINT NULL,
    canceled_by_user_id BIGINT NULL,
    deleted_by_user_id BIGINT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    CONSTRAINT fk_promotion_web_type FOREIGN KEY (promotion_web_type_id) REFERENCES promotion_web_type(id),
    CONSTRAINT fk_promotion_web_status FOREIGN KEY (promotion_web_status_id) REFERENCES promotion_web_status(id),
    CONSTRAINT fk_promotion_web_bonus_condition FOREIGN KEY (promotion_web_bonus_condition_id) REFERENCES promotion_web_bonus_condition(id),
    CONSTRAINT fk_promotion_web_bonus_type FOREIGN KEY (promotion_web_bonus_type_id) REFERENCES promotion_web_bonus_type(id),
    CONSTRAINT fk_promotion_web_turnover_type FOREIGN KEY (promotion_web_turnover_type_id) REFERENCES promotion_web_turnover_type(id),
    CONSTRAINT fk_promotion_web_date_type FOREIGN KEY (promotion_web_date_type_id) REFERENCES promotion_web_date_type(id)
);

CREATE INDEX idx_promotion_web_type_id ON promotion_web (promotion_web_type_id);
CREATE INDEX idx_promotion_web_status_id ON promotion_web (promotion_web_status_id);
CREATE INDEX idx_promotion_web_bonus_condition_id ON promotion_web (promotion_web_bonus_condition_id);
CREATE INDEX idx_promotion_web_bonus_type_id ON promotion_web (promotion_web_bonus_type_id);
CREATE INDEX idx_promotion_web_turnover_type_id ON promotion_web (promotion_web_turnover_type_id);
CREATE INDEX idx_promotion_web_date_type_id ON promotion_web (promotion_web_date_type_id);

-- ================================
-- USER-PROMOTION RELATIONSHIP TABLES
-- ================================

CREATE TABLE promotion_web_user (
    id BIGSERIAL PRIMARY KEY,
    promotion_web_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    promotion_web_user_status_id BIGINT NOT NULL,
    total_deposit_amount DECIMAL(10,2) NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NULL,
    canceled_by_user_id BIGINT NULL,
    deleted_by_user_id BIGINT NULL,
    approve_credit_by_user_id BIGINT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    canceled_at TIMESTAMP NULL,
    approve_credit_at TIMESTAMP NULL,
    CONSTRAINT fk_promotion_web_user_status FOREIGN KEY (promotion_web_user_status_id) REFERENCES promotion_web_user_status(id)
);

CREATE TABLE promotion_web_user_confirm (
    id BIGSERIAL PRIMARY KEY,
    action_key VARCHAR(255) NOT NULL,
    promotion_web_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    promotion_web_user_id BIGINT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    CONSTRAINT fk_promotion_web_user_confirm_web FOREIGN KEY (promotion_web_id) REFERENCES promotion_web(id),
    CONSTRAINT fk_promotion_web_user_confirm_user FOREIGN KEY (promotion_web_user_id) REFERENCES promotion_web_user(id),
    CONSTRAINT action_key_unique UNIQUE (action_key)
);

CREATE TABLE promotion_web_register_member (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    promotion_id BIGINT NOT NULL,
    register_status BIGINT NOT NULL DEFAULT 1,
    register_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    CONSTRAINT fk_promotion_web_register_member FOREIGN KEY (promotion_id) REFERENCES promotion_web(id),
    CONSTRAINT fk_promotion_web_register_member_status FOREIGN KEY (register_status) REFERENCES promotion_web_register_member_status(id)
);

-- ================================
-- SPECIALIZED PROMOTION TABLES
-- ================================

CREATE TABLE promotion_web_pg_hard_game (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL DEFAULT '',
    game_code_id VARCHAR(255) NOT NULL DEFAULT '',
    image_url VARCHAR(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE promotion_web_pg_hard_game_user (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    promotion_id BIGINT NOT NULL,
    promotion_web_user_id BIGINT NOT NULL,
    total_deposit_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    pg_hard_credit_free_spin DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    bonus_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_ready BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by_id BIGINT NULL,
    CONSTRAINT fk_promotion_web_pg_hard_game_user_web FOREIGN KEY (promotion_id) REFERENCES promotion_web(id),
    CONSTRAINT fk_promotion_web_pg_hard_game_user_user FOREIGN KEY (promotion_web_user_id) REFERENCES promotion_web_user(id)
);

CREATE TABLE promotion_web_lock_credit (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    promotion_web_user_id BIGINT NULL,
    promotion_id BIGINT NOT NULL,
    bonus_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_locked BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_promotion_web_lock_credit_web FOREIGN KEY (promotion_id) REFERENCES promotion_web(id),
    CONSTRAINT fk_promotion_web_lock_credit_user FOREIGN KEY (promotion_web_user_id) REFERENCES promotion_web_user(id)
);

-- ================================
-- LOGGING TABLE
-- ================================

CREATE TABLE promotion_web_user_log (
    id BIGSERIAL PRIMARY KEY,
    json_request TEXT NOT NULL,
    json_payload TEXT NOT NULL,
    log_type VARCHAR(255) NOT NULL,
    status VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- ================================
-- RETURN/CASHBACK SETTINGS TABLES
-- ================================

CREATE TABLE promotion_return_turn_setting (
    id BIGSERIAL PRIMARY KEY,
    return_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_type_id BIGINT NOT NULL DEFAULT 1,
    cut_type_id BIGINT NOT NULL DEFAULT 1,
    min_turn_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    max_return_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    credit_expire_days INTEGER NOT NULL DEFAULT 0,
    calc_on_sport BOOLEAN NOT NULL DEFAULT TRUE,
    return_sport_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    calc_on_casino BOOLEAN NOT NULL DEFAULT TRUE,
    return_casino_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    calc_on_game BOOLEAN NOT NULL DEFAULT TRUE,
    return_game_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    calc_on_lottery BOOLEAN NOT NULL DEFAULT TRUE,
    return_lottery_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    calc_on_p2p BOOLEAN NOT NULL DEFAULT TRUE,
    return_p2p_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    calc_on_financial BOOLEAN NOT NULL DEFAULT TRUE,
    return_financial_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    detail TEXT NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_promotion_return_turn_setting_type FOREIGN KEY (return_type_id) REFERENCES promotion_return_turn_type(id),
    CONSTRAINT fk_promotion_return_turn_setting_cut FOREIGN KEY (cut_type_id) REFERENCES promotion_return_turn_cut_type(id)
);

CREATE TABLE promotion_return_setting (
    id BIGSERIAL PRIMARY KEY,
    return_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_type_id BIGINT NOT NULL DEFAULT 1,
    cut_type_id BIGINT NOT NULL DEFAULT 1,
    min_loss_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    max_return_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    credit_expire_days INTEGER NOT NULL DEFAULT 0,
    calc_on_sport BOOLEAN NOT NULL DEFAULT TRUE,
    calc_on_casino BOOLEAN NOT NULL DEFAULT TRUE,
    calc_on_game BOOLEAN NOT NULL DEFAULT TRUE,
    calc_on_lottery BOOLEAN NOT NULL DEFAULT TRUE,
    calc_on_p2p BOOLEAN NOT NULL DEFAULT TRUE,
    calc_on_financial BOOLEAN NOT NULL DEFAULT TRUE,
    detail TEXT NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_promotion_return_setting_type FOREIGN KEY (return_type_id) REFERENCES promotion_return_loser_type(id),
    CONSTRAINT fk_promotion_return_setting_cut FOREIGN KEY (cut_type_id) REFERENCES promotion_return_cut_type(id)
);

-- ================================
-- RETURN/CASHBACK TRANSACTION TABLES
-- ================================

CREATE TABLE promotion_return_turn (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    status_id BIGINT NOT NULL DEFAULT 1,
    daily_key VARCHAR(255) NOT NULL DEFAULT '',
    of_date DATE NOT NULL,
    total_turn_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_turn_sport DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_sport_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_turn_casino DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_casino_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_turn_game DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_game_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_turn_lottery DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_lottery_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_turn_p2p DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_p2p_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_turn_financial DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_financial_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    game_detail VARCHAR(50) NOT NULL DEFAULT '',
    return_type_id BIGINT NOT NULL DEFAULT 1,
    cut_type_id BIGINT NOT NULL DEFAULT 1,
    min_turn_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    max_return_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    credit_expire_days INTEGER NOT NULL DEFAULT 0,
    return_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    calc_at TIMESTAMP NULL,
    take_at TIMESTAMP NULL,
    taken_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_promotion_return_turn_status FOREIGN KEY (status_id) REFERENCES promotion_return_turn_status(id),
    CONSTRAINT fk_promotion_return_turn_type FOREIGN KEY (return_type_id) REFERENCES promotion_return_turn_type(id),
    CONSTRAINT fk_promotion_return_turn_cut FOREIGN KEY (cut_type_id) REFERENCES promotion_return_turn_cut_type(id),
    CONSTRAINT promotion_return_turn_daily_key_unique UNIQUE (daily_key)
);

CREATE INDEX idx_promotion_return_turn_status_id ON promotion_return_turn (status_id);
CREATE INDEX idx_promotion_return_turn_user_id ON promotion_return_turn (user_id);

CREATE TABLE promotion_return_loser (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    status_id BIGINT NOT NULL DEFAULT 1,
    daily_key VARCHAR(255) NOT NULL DEFAULT '',
    of_date DATE NOT NULL,
    total_loss_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_loss_sport DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_loss_casino DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_loss_game DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_loss_lottery DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_loss_p2p DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_loss_financial DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    return_percent DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    game_detail VARCHAR(255) NOT NULL DEFAULT '',
    return_type_id BIGINT NOT NULL DEFAULT 1,
    cut_type_id BIGINT NOT NULL DEFAULT 1,
    min_loss_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    max_return_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    credit_expire_days INTEGER NOT NULL DEFAULT 0,
    return_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    calc_at TIMESTAMP NULL,
    take_at TIMESTAMP NULL,
    taken_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_promotion_return_loser_status FOREIGN KEY (status_id) REFERENCES promotion_return_loser_status(id),
    CONSTRAINT fk_promotion_return_loser_type FOREIGN KEY (return_type_id) REFERENCES promotion_return_loser_type(id),
    CONSTRAINT fk_promotion_return_loser_cut FOREIGN KEY (cut_type_id) REFERENCES promotion_return_cut_type(id),
    CONSTRAINT promotion_return_loser_daily_key_unique UNIQUE (daily_key)
);

CREATE INDEX idx_promotion_return_loser_status_id ON promotion_return_loser (status_id);
CREATE INDEX idx_promotion_return_loser_user_id ON promotion_return_loser (user_id);

-- ================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- ================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_promotion_web_updated_at
    BEFORE UPDATE ON promotion_web
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_web_user_updated_at
    BEFORE UPDATE ON promotion_web_user
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_web_user_confirm_updated_at
    BEFORE UPDATE ON promotion_web_user_confirm
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_web_register_member_updated_at
    BEFORE UPDATE ON promotion_web_register_member
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_web_pg_hard_game_user_updated_at
    BEFORE UPDATE ON promotion_web_pg_hard_game_user
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_web_user_log_updated_at
    BEFORE UPDATE ON promotion_web_user_log
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_return_turn_setting_updated_at
    BEFORE UPDATE ON promotion_return_turn_setting
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_return_setting_updated_at
    BEFORE UPDATE ON promotion_return_setting
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_return_turn_updated_at
    BEFORE UPDATE ON promotion_return_turn
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promotion_return_loser_updated_at
    BEFORE UPDATE ON promotion_return_loser
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();