package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type SeedThemeSettingData struct{}

func (m *SeedThemeSettingData) GetName() string {
	return "SeedThemeSettingData"
}

func (m *SeedThemeSettingData) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public" // default schema
	}

	log.Printf("Migration: Using schema '%s'", schemaName)

	// Set search_path to use the specified schema
	log.Printf("Migration: Setting search_path to '%s'", schemaName)
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path to %s: %v", schemaName, err))
	}
	log.Printf("Migration: search_path set successfully")

	// Verify current schema
	var currentSchema string
	err = con.QueryRow(ctx, "SELECT current_schema()").Scan(&currentSchema)
	if err == nil {
		log.Printf("Migration: Current schema is '%s'", currentSchema)
	}

	// Check if theme_setting table exists and has data
	var count int
	err = con.QueryRow(ctx, "SELECT COUNT(*) FROM theme_setting").Scan(&count)
	if err != nil {
		panic(fmt.Sprintf("Failed to check theme_setting table: %v", err))
	}

	// Only insert if table is empty
	if count == 0 {
		log.Printf("Migration: Seeding theme_setting with default data")

		// Insert default theme setting: id=1, theme_value="theme-a", updated_by=1 (admin user)
		_, err = con.Exec(ctx, `
			INSERT INTO theme_setting (id, theme_value, updated_at, updated_by) 
			VALUES (1, 'theme-a', CURRENT_TIMESTAMP, 1)
		`)
		if err != nil {
			panic(fmt.Sprintf("Failed to seed theme_setting data: %v", err))
		}

		// Reset sequence to start from 2 for future inserts
		_, err = con.Exec(ctx, `SELECT setval('theme_setting_id_seq', 1, true)`)
		if err != nil {
			panic(fmt.Sprintf("Failed to reset theme_setting sequence: %v", err))
		}

		log.Printf("Migration: theme_setting seeded successfully with default theme 'theme-a'")
	} else {
		log.Printf("Migration: theme_setting table already contains %d records, skipping seed", count)
	}
}

func (m *SeedThemeSettingData) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Remove the seeded data
	_, err = con.Exec(ctx, `DELETE FROM theme_setting WHERE id = 1 AND theme_value = 'theme-a'`)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove seeded theme_setting data: %v", err))
	}

	log.Printf("Migration: theme_setting seed data removed successfully")
}
