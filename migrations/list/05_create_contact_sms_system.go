package list

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"log"
	"os"
)

type CreateContactSMSSystem struct{}

func (m *CreateContactSMSSystem) GetName() string {
	return "CreateContactSMSSystem"
}

func (m *CreateContactSMSSystem) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating contact and SMS system tables")

	// Create contact table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS contact (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			image_url VARCHAR(255) NULL,
			link VARCHAR(255) NULL,
			account VARCHAR(255) NULL,
			main_contract BOOLEAN NOT NULL DEFAULT FALSE,
			brithday BOOLEAN NOT NULL DEFAULT FALSE,
			first_color VARCHAR(7) NULL,
			second_color VARCHAR(7) NULL,
			active BOOLEAN NOT NULL DEFAULT FALSE,
			inactive BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create sms_provider_name table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS sms_provider_name (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create sms_provider table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS sms_provider (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			provider_name_id INTEGER NOT NULL,
			api_key VARCHAR(255) NOT NULL,
			secret_key VARCHAR(255) NOT NULL,
			otp_sender VARCHAR(255) NOT NULL,
			sms_sender VARCHAR(255) NOT NULL,
			prefix_otp VARCHAR(255) NOT NULL,
			balance FLOAT NULL,
			is_sms BOOLEAN NOT NULL DEFAULT FALSE,
			is_otp BOOLEAN NOT NULL DEFAULT FALSE,
			active BOOLEAN NOT NULL DEFAULT FALSE,
			inactive BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT fk_sms_provider_name
			FOREIGN KEY (provider_name_id) REFERENCES sms_provider_name(id)
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create indexes for better performance
	indexes := []string{
		// Contact indexes
		"CREATE INDEX IF NOT EXISTS idx_contact_name ON contact (name)",
		"CREATE INDEX IF NOT EXISTS idx_contact_active ON contact (active)",
		"CREATE INDEX IF NOT EXISTS idx_contact_main_contract ON contact (main_contract)",
		"CREATE INDEX IF NOT EXISTS idx_contact_created_at ON contact (created_at)",

		// SMS provider indexes
		"CREATE INDEX IF NOT EXISTS idx_sms_provider_name ON sms_provider (name)",
		"CREATE INDEX IF NOT EXISTS idx_sms_provider_provider_name_id ON sms_provider (provider_name_id)",
		"CREATE INDEX IF NOT EXISTS idx_sms_provider_active ON sms_provider (active)",
		"CREATE INDEX IF NOT EXISTS idx_sms_provider_is_sms ON sms_provider (is_sms)",
		"CREATE INDEX IF NOT EXISTS idx_sms_provider_is_otp ON sms_provider (is_otp)",
		"CREATE INDEX IF NOT EXISTS idx_sms_provider_created_at ON sms_provider (created_at)",

		// SMS provider name indexes
		"CREATE INDEX IF NOT EXISTS idx_sms_provider_name_name ON sms_provider_name (name)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(err)
		}
	}

	log.Printf("Migration: Contact and SMS system tables created successfully")
}

func (m *CreateContactSMSSystem) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop tables in reverse order
	tables := []string{
		"DROP TABLE IF EXISTS sms_provider",
		"DROP TABLE IF EXISTS sms_provider_name",
		"DROP TABLE IF EXISTS contact",
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			panic(err)
		}
	}
}