package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type AddMemberRemark struct{}

func (m *AddMemberRemark) GetName() string {
	return "AddMemberRemark"
}

func (m *AddMemberRemark) Up(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Adding remark and created_by fields to members table...")

	// Add remark column to members table
	_, err = con.Exec(ctx, `
        ALTER TABLE members 
        ADD COLUMN IF NOT EXISTS remark TEXT NULL,
        ADD COLUMN IF NOT EXISTS created_by INT NULL,
        ALTER COLUMN delete_by DROP NOT NULL
    `)
	if err != nil {
		panic(fmt.Sprintf("Failed to add remark and created_by columns and fix delete_by: %v", err))
	}

	log.Printf("Migration: Remark and created_by fields added to members table and delete_by made nullable successfully")
}

func (m *AddMemberRemark) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Removing remark and created_by fields from members table...")

	// Remove remark and created_by columns from members table
	_, err = con.Exec(ctx, `
        ALTER TABLE members 
        DROP COLUMN IF EXISTS remark,
        DROP COLUMN IF EXISTS created_by,
        ALTER COLUMN delete_by SET NOT NULL
    `)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove remark and created_by columns and revert delete_by: %v", err))
	}

	log.Printf("Migration: Remark and created_by fields removed from members table and delete_by reverted to NOT NULL successfully")
}
