package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type CreateMemberSystem struct{}

func (m *CreateMemberSystem) GetName() string {
	return "CreateMemberSystem"
}

func (m *CreateMemberSystem) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating member system tables")

	// Note: members table is now created in 01_create_init_tables.go

	// Update login_attempts table with new columns
	_, err = con.Exec(ctx, `
		ALTER TABLE login_attempts
		ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE
	`)
	if err != nil {
		panic(err)
	}

	_, err = con.Exec(ctx, `
		ALTER TABLE login_attempts
		ADD COLUMN IF NOT EXISTS is_member BOOLEAN NOT NULL DEFAULT FALSE
	`)
	if err != nil {
		panic(err)
	}

	_, err = con.Exec(ctx, `
		ALTER TABLE login_attempts
		ADD COLUMN IF NOT EXISTS is_cleared BOOLEAN NOT NULL DEFAULT FALSE
	`)
	if err != nil {
		panic(err)
	}

	// Create member_audit_logs table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS member_audit_logs (
			id SERIAL PRIMARY KEY,
			member_id INT NOT NULL,
			username VARCHAR(255) NOT NULL,
			action VARCHAR(50) NOT NULL,
			old_values TEXT,
			new_values TEXT,
			changed_by INT NOT NULL,
			changed_by_name VARCHAR(255) NOT NULL,
			changed_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create otps table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS otps (
			id SERIAL PRIMARY KEY,
			type VARCHAR(10) NOT NULL CHECK (type IN ('phone', 'email')),
			purpose VARCHAR(30) NOT NULL,
			recipient VARCHAR(255) NOT NULL,
			reference VARCHAR(6) NOT NULL,
			code VARCHAR(6) NOT NULL,
			member_id INT NULL,
			is_used BOOLEAN NOT NULL DEFAULT FALSE,
			expires_at TIMESTAMPTZ NOT NULL,
			used_at TIMESTAMPTZ NULL,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create channels table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS channels (
			id SERIAL PRIMARY KEY,
			name VARCHAR(100) UNIQUE NOT NULL,
			platform_id VARCHAR(50) NOT NULL,
			description TEXT,
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_by VARCHAR(255) NOT NULL,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create commission_groups table first (needed by member_groups)
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS commission_groups (
			id SERIAL PRIMARY KEY,
			name VARCHAR(100) UNIQUE NOT NULL,
			is_default BOOLEAN DEFAULT FALSE,
			turnover_sports DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_sports >= 0 AND turnover_sports <= 100),
			turnover_casino DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_casino >= 0 AND turnover_casino <= 100),
			turnover_fishing DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_fishing >= 0 AND turnover_fishing <= 100),
			turnover_slot DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_slot >= 0 AND turnover_slot <= 100),
			turnover_lottery DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_lottery >= 0 AND turnover_lottery <= 100),
			turnover_card DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_card >= 0 AND turnover_card <= 100),
			turnover_other DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_other >= 0 AND turnover_other <= 100),
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Note: member_group_types table is now created in 01_create_init_tables.go

	// Note: member_groups table is now created in 01_create_init_tables.go

	// Create member_group_withdrawal_approvals table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS member_group_withdrawal_approvals (
			id SERIAL PRIMARY KEY,
			member_group_id INT NOT NULL,
			min_amount DECIMAL(15, 2) DEFAULT 0.00 CHECK (min_amount >= 0),
			max_amount DECIMAL(15, 2) DEFAULT 0.00 CHECK (max_amount >= 0),
			user_role_id INT NOT NULL,
			order_index INT NOT NULL CHECK (order_index > 0),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
			-- Note: Foreign key constraints removed as referenced tables are now in 01_create_init_tables.go
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create member_group_deposit_accounts table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS member_group_deposit_accounts (
			id SERIAL PRIMARY KEY,
			member_group_id INT NOT NULL,
			account_deposit_id INT NOT NULL,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
			-- Note: Foreign key constraint removed as member_groups table is now in 01_create_init_tables.go
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create referral_groups table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS referral_groups (
			id SERIAL PRIMARY KEY,
			name VARCHAR(100) NOT NULL,
			turnover_sports DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_sports >= 0 AND turnover_sports <= 100),
			turnover_casino DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_casino >= 0 AND turnover_casino <= 100),
			turnover_fishing DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_fishing >= 0 AND turnover_fishing <= 100),
			turnover_slot DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_slot >= 0 AND turnover_slot <= 100),
			turnover_lottery DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_lottery >= 0 AND turnover_lottery <= 100),
			turnover_card DECIMAL(5, 2) DEFAULT 0.00 CHECK (turnover_card >= 0 AND turnover_card <= 100),
			is_default BOOLEAN DEFAULT FALSE,
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Add system settings for commission and referral
	_, err = con.Exec(ctx, `
		INSERT INTO system_settings (key, value, description, created_at, updated_at)
		VALUES
		('commission_auto_approval_threshold', '10000.00', 'อนุมัติการโอนรายได้คอมมิสชั่นอัตโนมัติ เมื่อจำนวนเงินไม่เกิน (บาท)', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
		('commission_min_withdraw_amount', '100.00', 'จำนวนถอนขั้นต่ำสำหรับคอมมิสชั่น (บาท)', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
		('referral_auto_approval_threshold', '5000.00', 'อนุมัติการโอนรายได้เพื่อนชวนเพื่อนอัตโนมัติ เมื่อจำนวนเงินไม่เกิน (บาท)', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
		('referral_min_withdraw_amount', '50.00', 'จำนวนถอนขั้นต่ำสำหรับเพื่อนชวนเพื่อน (บาท)', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		ON CONFLICT (key) DO NOTHING
	`)
	if err != nil {
		panic(err)
	}

	log.Printf("Migration: Member system tables created successfully")

	// Create all necessary indexes
	log.Printf("Migration: Creating indexes...")
	indexes := []string{
		// Members indexes
		"CREATE INDEX IF NOT EXISTS idx_members_id ON members (id)",
		"CREATE INDEX IF NOT EXISTS idx_members_status ON members (status)",
		"CREATE INDEX IF NOT EXISTS idx_members_username ON members (username)",
		"CREATE INDEX IF NOT EXISTS idx_members_gender ON members (gender)",
		"CREATE INDEX IF NOT EXISTS idx_members_created_at ON members (created_at)",
		"CREATE INDEX IF NOT EXISTS idx_members_member_group_id ON members (member_group_id)",

		// Login attempts indexes
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_is_admin ON login_attempts (is_admin)",
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_is_member ON login_attempts (is_member)",
		"CREATE INDEX IF NOT EXISTS idx_login_attempts_user_type ON login_attempts (is_admin, is_member)",

		// Member audit logs indexes
		"CREATE INDEX IF NOT EXISTS idx_member_audit_logs_member_id ON member_audit_logs (member_id)",
		"CREATE INDEX IF NOT EXISTS idx_member_audit_logs_username ON member_audit_logs (username)",
		"CREATE INDEX IF NOT EXISTS idx_member_audit_logs_action ON member_audit_logs (action)",
		"CREATE INDEX IF NOT EXISTS idx_member_audit_logs_changed_at ON member_audit_logs (changed_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_member_audit_logs_changed_by ON member_audit_logs (changed_by)",

		// OTPs indexes
		"CREATE INDEX IF NOT EXISTS idx_otps_recipient_purpose ON otps (recipient, purpose)",
		"CREATE INDEX IF NOT EXISTS idx_otps_reference ON otps (reference)",
		"CREATE INDEX IF NOT EXISTS idx_otps_expires_at ON otps (expires_at)",
		"CREATE INDEX IF NOT EXISTS idx_otps_created_at ON otps (created_at)",
		"CREATE INDEX IF NOT EXISTS idx_otps_is_used ON otps (is_used)",

		// Channels indexes
		"CREATE INDEX IF NOT EXISTS idx_channels_name ON channels (name)",
		"CREATE INDEX IF NOT EXISTS idx_channels_platform_id ON channels (platform_id)",
		"CREATE INDEX IF NOT EXISTS idx_channels_status ON channels (status)",
		"CREATE INDEX IF NOT EXISTS idx_channels_created_by ON channels (created_by)",
		"CREATE INDEX IF NOT EXISTS idx_channels_created_at ON channels (created_at)",

		// Note: Member groups and member_group_types indexes are now in 01_create_init_tables.go

		// Other indexes
		"CREATE INDEX IF NOT EXISTS idx_commission_groups_name ON commission_groups (name)",
		"CREATE INDEX IF NOT EXISTS idx_commission_groups_status ON commission_groups (status)",
		"CREATE INDEX IF NOT EXISTS idx_referral_groups_name ON referral_groups (name)",
		"CREATE INDEX IF NOT EXISTS idx_referral_groups_status ON referral_groups (status)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(err)
		}
	}

	log.Printf("Migration: Member system tables created successfully")
}

func (m *CreateMemberSystem) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop tables in reverse order (considering dependencies)
	tables := []string{
		"DROP TABLE IF EXISTS referral_groups",
		"DROP TABLE IF EXISTS member_group_deposit_accounts",
		"DROP TABLE IF EXISTS member_group_withdrawal_approvals",
		"DROP TABLE IF EXISTS commission_groups",
		"DROP TABLE IF EXISTS member_groups CASCADE",
		"DROP TABLE IF EXISTS member_group_types CASCADE",
		"DROP TABLE IF EXISTS channels",
		"DROP TABLE IF EXISTS otps",
		"DROP TABLE IF EXISTS member_audit_logs",
		// Note: members, member_groups, member_group_types are now in 01_create_init_tables.go
	}

	for _, dropSQL := range tables {
		_, err := con.Exec(ctx, dropSQL)
		if err != nil {
			panic(err)
		}
	}

	// Remove added columns from login_attempts
	_, err = con.Exec(ctx, `
		ALTER TABLE login_attempts 
		DROP COLUMN IF EXISTS is_admin,
		DROP COLUMN IF EXISTS is_member,
		DROP COLUMN IF EXISTS is_cleared
	`)
	if err != nil {
		panic(err)
	}
}
