package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type AddIsTabAndIsButtonToPermissions struct{}

func (m *AddIsTabAndIsButtonToPermissions) GetName() string {
	return "AddIsTabAndIsButtonToPermissions"
}

func (m *AddIsTabAndIsButtonToPermissions) Up(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Adding is_tab and is_button fields to permissions table...")

	// Add is_tab and is_button columns to permissions table
	_, err = con.Exec(ctx, `
        ALTER TABLE permissions
        ADD COLUMN IF NOT EXISTS is_tab SMALLINT NOT NULL DEFAULT 0,
        ADD COLUMN IF NOT EXISTS is_button SMALLINT NOT NULL DEFAULT 0
    `)
	if err != nil {
		panic(fmt.Sprintf("Failed to add is_tab and is_button columns to permissions table: %v", err))
	}

	log.Printf("Migration: is_tab and is_button fields added to permissions table successfully")
}

func (m *AddIsTabAndIsButtonToPermissions) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Removing is_tab and is_button fields from permissions table...")

	// Remove is_tab and is_button columns from permissions table
	_, err = con.Exec(ctx, `
        ALTER TABLE permissions
        DROP COLUMN IF EXISTS is_tab,
        DROP COLUMN IF EXISTS is_button
    `)
	if err != nil {
		panic(fmt.Sprintf("Failed to remove is_tab and is_button columns from permissions table: %v", err))
	}

	log.Printf("Migration: is_tab and is_button fields removed from permissions table successfully")
}
