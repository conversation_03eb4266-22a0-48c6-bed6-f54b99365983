package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type RemovePermissionGroups struct{}

func (m *RemovePermissionGroups) GetName() string {
	return "RemovePermissionGroups"
}

func (m *RemovePermissionGroups) Up(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Removing permission groups system...")

	// 1. Drop foreign key constraint first
	_, err = con.Exec(ctx, `
		ALTER TABLE permissions 
		DROP CONSTRAINT IF EXISTS permissions_group_id_fkey
	`)
	if err != nil {
		log.Printf("Warning: Could not drop foreign key constraint: %v", err)
	}

	// 2. Remove group_id column from permissions table
	_, err = con.Exec(ctx, `
		ALTER TABLE permissions 
		DROP COLUMN IF EXISTS group_id
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop group_id column: %v", err))
	}

	// 3. Drop permission_groups table entirely
	_, err = con.Exec(ctx, `
		DROP TABLE IF EXISTS permission_groups CASCADE
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to drop permission_groups table: %v", err))
	}

	// 4. Category column not needed - removed for simplicity

	// 5. Add enabled column for sub-permissions (tabs and buttons)
	_, err = con.Exec(ctx, `
		ALTER TABLE permissions
		ADD COLUMN IF NOT EXISTS enabled BOOLEAN DEFAULT true
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add enabled column: %v", err))
	}

	// 6. Create indexes for better performance - category index not needed

	_, err = con.Exec(ctx, `
		CREATE INDEX IF NOT EXISTS idx_permissions_enabled
		ON permissions(enabled, parent_id) WHERE status = 'active'
	`)
	if err != nil {
		log.Printf("Warning: Could not create enabled index: %v", err)
	}

	// 7. No category updates needed since we're not using categories

	log.Printf("Migration: Permission groups system removed successfully!")
}

func (m *RemovePermissionGroups) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Restoring permission groups system...")

	// 1. Recreate permission_groups table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS permission_groups (
			id SERIAL PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			key VARCHAR(100) NOT NULL UNIQUE,
			description TEXT,
			position INTEGER,
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to recreate permission_groups table: %v", err))
	}

	// 2. Insert default permission group
	_, err = con.Exec(ctx, `
		INSERT INTO permission_groups (name, key, description, position, status, created_at, updated_at)
		VALUES ('Default', 'default', 'Default permission group', 1, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
		ON CONFLICT (key) DO NOTHING
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to insert default permission group: %v", err))
	}

	// 3. Add group_id column back to permissions
	_, err = con.Exec(ctx, `
		ALTER TABLE permissions 
		ADD COLUMN IF NOT EXISTS group_id INTEGER DEFAULT 1
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to add group_id column: %v", err))
	}

	// 4. Add foreign key constraint
	_, err = con.Exec(ctx, `
		ALTER TABLE permissions 
		ADD CONSTRAINT permissions_group_id_fkey 
		FOREIGN KEY (group_id) REFERENCES permission_groups(id)
	`)
	if err != nil {
		log.Printf("Warning: Could not add foreign key constraint: %v", err)
	}

	// 5. Remove enabled column (category was never added)
	// Category column removal not needed since it was never added

	_, err = con.Exec(ctx, `
		ALTER TABLE permissions
		DROP COLUMN IF EXISTS enabled
	`)
	if err != nil {
		log.Printf("Warning: Could not drop enabled column: %v", err)
	}

	// 6. Drop indexes - category index was never created

	_, err = con.Exec(ctx, `
		DROP INDEX IF EXISTS idx_permissions_enabled
	`)
	if err != nil {
		log.Printf("Warning: Could not drop enabled index: %v", err)
	}

	log.Printf("Migration: Permission groups system restored!")
}
