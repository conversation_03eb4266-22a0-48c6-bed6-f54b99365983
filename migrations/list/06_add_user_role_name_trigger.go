package list

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v5"
)

type AddUserRoleNameTrigger struct{}

func (m *AddUserRoleNameTrigger) GetName() string {
	return "AddUserRoleNameTrigger"
}

func (m *AddUserRoleNameTrigger) Up(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating user role name sync trigger...")

	// Create trigger function to update user_role_name in users table when user_roles.name changes
	_, err = con.Exec(ctx, `
		CREATE OR REPLACE FUNCTION update_user_role_name()
		RETURNS TRIGGER AS $$
		BEGIN
			-- Update user_role_name in users table when user_roles.name is updated
			UPDATE users 
			SET user_role_name = NEW.name,
				updated_at = CURRENT_TIMESTAMP
			WHERE user_role_id = NEW.id;
			
			RETURN NEW;
		END;
		$$ LANGUAGE plpgsql;
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create trigger function: %v", err))
	}

	// Create trigger on user_roles table
	_, err = con.Exec(ctx, `
		CREATE TRIGGER trigger_update_user_role_name
		AFTER UPDATE OF name ON user_roles
		FOR EACH ROW
		WHEN (OLD.name IS DISTINCT FROM NEW.name)
		EXECUTE FUNCTION update_user_role_name();
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to create trigger: %v", err))
	}

	// Update existing users to sync their user_role_name with current user_roles.name
	_, err = con.Exec(ctx, `
		UPDATE users 
		SET user_role_name = ur.name,
			updated_at = CURRENT_TIMESTAMP
		FROM user_roles ur 
		WHERE users.user_role_id = ur.id 
		AND (users.user_role_name IS NULL OR users.user_role_name != ur.name);
	`)
	if err != nil {
		panic(fmt.Sprintf("Failed to sync existing user role names: %v", err))
	}

	log.Printf("Migration: User role name sync trigger created successfully")
}

func (m *AddUserRoleNameTrigger) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Dropping user role name sync trigger...")

	// Drop trigger
	_, err = con.Exec(ctx, "DROP TRIGGER IF EXISTS trigger_update_user_role_name ON user_roles")
	if err != nil {
		panic(fmt.Sprintf("Failed to drop trigger: %v", err))
	}

	// Drop function
	_, err = con.Exec(ctx, "DROP FUNCTION IF EXISTS update_user_role_name()")
	if err != nil {
		panic(fmt.Sprintf("Failed to drop function: %v", err))
	}

	log.Printf("Migration: User role name sync trigger dropped successfully")
}
