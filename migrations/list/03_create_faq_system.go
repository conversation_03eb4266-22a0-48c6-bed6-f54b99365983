package list

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"log"
	"os"
)

type CreateFAQSystem struct{}

func (m *CreateFAQSystem) GetName() string {
	return "CreateFAQSystem"
}

func (m *CreateFAQSystem) Up(con pgx.Tx) {
	ctx := context.Background()
	// Get schema name from environment or use default
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	// Set search_path
	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	log.Printf("Migration: Creating FAQ system table")

	// Create faqs table
	_, err = con.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS faqs (
			id SERIAL PRIMARY KEY,
			position INT NULL,
			title VARCHAR(255) NOT NULL,
			answer TEXT NOT NULL,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		panic(err)
	}

	// Create indexes for better performance
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_faqs_position ON faqs (position)",
		"CREATE INDEX IF NOT EXISTS idx_faqs_status ON faqs (status)",
		"CREATE INDEX IF NOT EXISTS idx_faqs_created_at ON faqs (created_at)",
	}

	for _, indexSQL := range indexes {
		_, err = con.Exec(ctx, indexSQL)
		if err != nil {
			panic(err)
		}
	}

	log.Printf("Migration: FAQ system table created successfully")
}

func (m *CreateFAQSystem) Down(con pgx.Tx) {
	ctx := context.Background()
	schemaName := os.Getenv("DATABASE_SCHEMA")
	if schemaName == "" {
		schemaName = "public"
	}

	_, err := con.Exec(ctx, fmt.Sprintf("SET search_path TO %s", schemaName))
	if err != nil {
		panic(fmt.Sprintf("Failed to set search_path: %v", err))
	}

	// Drop faqs table
	_, err = con.Exec(ctx, "DROP TABLE IF EXISTS faqs")
	if err != nil {
		panic(err)
	}
}