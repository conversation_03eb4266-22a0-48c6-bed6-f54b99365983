import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '30s', target: 10 },
    { duration: '1m', target: 20 },
    { duration: '30s', target: 0 },
  ],
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:8080';

export default function () {
  // Test health endpoint
  let healthResponse = http.get(`${BASE_URL}/health`);
  check(healthResponse, {
    'health status is 200': (r) => r.status === 200,
    'health response contains status': (r) => r.json('status') === 'ok',
  });

  // Test readiness endpoint
  let readyResponse = http.get(`${BASE_URL}/health/ready`);
  check(readyResponse, {
    'ready status is 200': (r) => r.status === 200,
  });

  // Test liveness endpoint
  let liveResponse = http.get(`${BASE_URL}/health/live`);
  check(liveResponse, {
    'live status is 200': (r) => r.status === 200,
  });

  // Test user creation
  let userData = {
    email: `test-${Math.random()}@example.com`,
    password: 'password123',
    first_name: 'Test',
    last_name: 'User',
  };

  let createUserResponse = http.post(
    `${BASE_URL}/api/v1/users`,
    JSON.stringify(userData),
    {
      headers: { 'Content-Type': 'application/json' },
    }
  );

  check(createUserResponse, {
    'create user status is 201': (r) => r.status === 201,
    'create user response is success': (r) => r.json('success') === true,
    'user has ID': (r) => r.json('data.id') !== null,
  });

  if (createUserResponse.status === 201) {
    let userId = createUserResponse.json('data.id');

    // Test get user
    let getUserResponse = http.get(`${BASE_URL}/api/v1/users/${userId}`);
    check(getUserResponse, {
      'get user status is 200': (r) => r.status === 200,
      'get user email matches': (r) => r.json('data.email') === userData.email,
    });

    // Test update user
    let updateData = {
      first_name: 'Updated',
    };

    let updateUserResponse = http.put(
      `${BASE_URL}/api/v1/users/${userId}`,
      JSON.stringify(updateData),
      {
        headers: { 'Content-Type': 'application/json' },
      }
    );

    check(updateUserResponse, {
      'update user status is 200': (r) => r.status === 200,
      'updated first name matches': (r) => r.json('data.first_name') === 'Updated',
    });

    // Test list users
    let listUsersResponse = http.get(`${BASE_URL}/api/v1/users?limit=10&offset=0`);
    check(listUsersResponse, {
      'list users status is 200': (r) => r.status === 200,
      'list users has pagination': (r) => r.json('data.pagination') !== null,
    });

    // Test delete user
    let deleteUserResponse = http.del(`${BASE_URL}/api/v1/users/${userId}`);
    check(deleteUserResponse, {
      'delete user status is 200': (r) => r.status === 200,
    });
  }

  sleep(1);
}