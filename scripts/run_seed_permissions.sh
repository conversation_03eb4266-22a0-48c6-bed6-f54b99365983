#!/bin/bash

# Permission Seed Script Runner
# This script runs the permission seed data independently

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '#' | awk '/=/ {print $1}')
fi

# Database connection parameters
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-blacking_api}
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD:-password}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🌱 Running Permission Seed Script...${NC}"
echo "Database: $DB_HOST:$DB_PORT/$DB_NAME"
echo "User: $DB_USER"
echo ""

# Check if psql is available
if ! command -v psql &> /dev/null; then
    echo -e "${RED}❌ psql command not found. Please install PostgreSQL client.${NC}"
    exit 1
fi

# Test database connection
echo -e "${YELLOW}🔍 Testing database connection...${NC}"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to connect to database. Please check your connection parameters.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Database connection successful${NC}"

# Run the seed script
echo -e "${YELLOW}🌱 Seeding permission data...${NC}"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f scripts/seed_permissions.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Permission seed completed successfully!${NC}"
    echo ""
    echo -e "${YELLOW}📊 Summary:${NC}"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
        SELECT 
            'Permission Groups' as type,
            COUNT(*) as count
        FROM permission_groups 
        WHERE status = 'active'
        UNION ALL
        SELECT 
            'Main Permissions' as type,
            COUNT(*) as count
        FROM permissions 
        WHERE status = 'active' AND parent_id IS NULL
        UNION ALL
        SELECT 
            'Sub-Permissions' as type,
            COUNT(*) as count
        FROM permissions 
        WHERE status = 'active' AND parent_id IS NOT NULL;
    "
else
    echo -e "${RED}❌ Permission seed failed!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Permission system is ready to use!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Use the permission management APIs to assign permissions to user roles"
echo "2. Test the permission matrix functionality"
echo "3. Configure user role permissions as needed"
