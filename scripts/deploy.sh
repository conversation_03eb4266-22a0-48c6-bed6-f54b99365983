#!/bin/bash

# Deployment script for blacking-api
set -e

ENVIRONMENT=${1:-staging}
IMAGE_TAG=${2:-latest}

echo "Deploying blacking-api to $ENVIRONMENT with tag $IMAGE_TAG..."

# Build Docker image
echo "Building Docker image..."
docker build -t blacking-api:$IMAGE_TAG .

# Tag for registry (replace with your registry)
REGISTRY="your-registry.com"
docker tag blacking-api:$IMAGE_TAG $REGISTRY/blacking-api:$IMAGE_TAG

# Push to registry
echo "Pushing to registry..."
docker push $REGISTRY/blacking-api:$IMAGE_TAG

# Deploy based on environment
case $ENVIRONMENT in
  "staging")
    echo "Deploying to staging environment..."
    # Add staging deployment commands here
    # kubectl apply -f deployments/kubernetes/staging/
    ;;
  "production")
    echo "Deploying to production environment..."
    # Add production deployment commands here
    # kubectl apply -f deployments/kubernetes/production/
    ;;
  *)
    echo "Unknown environment: $ENVIRONMENT"
    echo "Usage: $0 [staging|production] [image_tag]"
    exit 1
    ;;
esac

echo "Deployment completed successfully!"