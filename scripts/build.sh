#!/bin/bash

# Build script for blacking-api
set -e

echo "Building blacking-api..."

# Create bin directory if it doesn't exist
mkdir -p bin

# Build server
echo "Building server..."
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-w -s" -o bin/server cmd/server/main.go

# Build migration tool
echo "Building migration tool..."
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-w -s" -o bin/migrate cmd/migration/main.go

# Build worker
echo "Building worker..."
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags="-w -s" -o bin/worker cmd/worker/main.go

echo "Build completed successfully!"
echo "Binaries created in bin/ directory:"
ls -la bin/