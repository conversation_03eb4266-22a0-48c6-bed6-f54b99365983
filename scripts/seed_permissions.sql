-- Permission Groups and Permissions Seed Data
-- This script can be run independently to seed permission data
-- Use key-based linking to avoid conflicts with existing user_role_permissions

-- Clear existing data (optional - uncomment if you want to reset)
-- DELETE FROM user_role_permissions;
-- DELETE FROM permissions;
-- DELETE FROM permission_groups;

-- Insert Permission Groups
INSERT INTO permission_groups (name, key, description, position, status, created_at, updated_at) VALUES
('การจัดการผู้ใช้', 'user_management', 'การจัดการผู้ใช้งานและสิทธิ์', 1, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การตั้งค่าระบบ', 'system_settings', 'การตั้งค่าระบบและการกำหนดค่า', 2, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การจัดการเนื้อหา', 'content_management', 'การจัดการเนื้อหาและสื่อ', 3, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การจัดการเกม', 'game_management', 'การจัดการเกมและผู้ให้บริการ', 4, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การจัดการการเงิน', 'financial_management', 'การจัดการการเงินและธุรกรรม', 5, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การจัดการโปรโมชั่น', 'promotion_management', 'การจัดการโปรโมชั่นและโบนัส', 6, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การจัดการรายงาน', 'report_management', 'การจัดการรายงานและสถิติ', 7, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การจัดการความปลอดภัย', 'security_management', 'การจัดการความปลอดภัยและการเข้าถึง', 8, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การสื่อสาร', 'communication', 'การสื่อสารและการแจ้งเตือน', 9, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('การวิเคราะห์', 'analytics', 'การวิเคราะห์และแดชบอร์ด', 10, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (key) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    position = EXCLUDED.position,
    updated_at = CURRENT_TIMESTAMP;

-- Insert Permissions
INSERT INTO permissions (group_id, parent_id, name, key, description, position, level, supports_create, supports_view, supports_edit, supports_delete, status, created_at, updated_at) VALUES
-- User Management Group
((SELECT id FROM permission_groups WHERE key = 'user_management'), NULL, 'ผู้ใช้งาน', 'users', 'จัดการผู้ใช้งานในระบบ', 1, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'user_management'), NULL, 'บทบาทผู้ใช้', 'user_roles', 'จัดการบทบาทและสิทธิ์ผู้ใช้', 2, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'user_management'), NULL, 'สิทธิ์ผู้ใช้', 'user_permissions', 'จัดการสิทธิ์การเข้าถึง', 3, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'user_management'), NULL, 'บันทึกการใช้งาน', 'user_audit_logs', 'ดูบันทึกการใช้งานของผู้ใช้', 4, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'user_management'), NULL, 'IP ที่อนุญาต', 'allowed_ips', 'จัดการ IP ที่อนุญาตให้เข้าถึง', 5, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- System Settings Group  
((SELECT id FROM permission_groups WHERE key = 'system_settings'), NULL, 'จำกัดการเข้าสู่ระบบ', 'login_attempt_limit', 'ตั้งค่าจำกัดการเข้าสู่ระบบ', 1, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'system_settings'), NULL, 'การตั้งค่าระบบ', 'system_settings', 'การตั้งค่าระบบทั่วไป', 2, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'system_settings'), NULL, 'การตั้งค่าทั่วไป', 'general_settings', 'การตั้งค่าทั่วไปของระบบ', 3, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'system_settings'), NULL, 'การตั้งค่า SEO', 'seo_settings', 'การตั้งค่า SEO และเมตาดาต้า', 4, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'system_settings'), NULL, 'ภาษา', 'languages', 'จัดการภาษาที่รองรับ', 5, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Content Management Group
((SELECT id FROM permission_groups WHERE key = 'content_management'), NULL, 'แบนเนอร์', 'banners', 'จัดการแบนเนอร์และโฆษณา', 1, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'content_management'), NULL, 'ข่าวสาร', 'news', 'จัดการข่าวสารและบทความ', 2, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'content_management'), NULL, 'ประกาศ', 'announcements', 'จัดการประกาศและข้อมูลสำคัญ', 3, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'content_management'), NULL, 'หน้าเว็บ', 'pages', 'จัดการหน้าเว็บสถิต', 4, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'content_management'), NULL, 'เมนู', 'menus', 'จัดการเมนูและการนำทาง', 5, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Game Management Group
((SELECT id FROM permission_groups WHERE key = 'game_management'), NULL, 'เกม', 'games', 'จัดการเกมในระบบ', 1, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'game_management'), NULL, 'หมวดหมู่เกม', 'game_categories', 'จัดการหมวดหมู่ของเกม', 2, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'game_management'), NULL, 'ผู้ให้บริการเกม', 'game_providers', 'จัดการผู้ให้บริการเกม', 3, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'game_management'), NULL, 'การตั้งค่าเกม', 'game_settings', 'ตั้งค่าเกมและพารามิเตอร์', 4, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Financial Management Group
((SELECT id FROM permission_groups WHERE key = 'financial_management'), NULL, 'การฝากเงิน', 'deposits', 'จัดการการฝากเงินของผู้ใช้', 1, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'financial_management'), NULL, 'การถอนเงิน', 'withdrawals', 'จัดการการถอนเงินของผู้ใช้', 2, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'financial_management'), NULL, 'รายการธุรกรรม', 'transactions', 'ดูรายการธุรกรรมทั้งหมด', 3, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'financial_management'), NULL, 'วิธีการชำระเงิน', 'payment_methods', 'จัดการวิธีการชำระเงิน', 4, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'financial_management'), NULL, 'บัญชีธนาคาร', 'bank_accounts', 'จัดการบัญชีธนาคาร', 5, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'financial_management'), NULL, 'รายงานการเงิน', 'financial_reports', 'ดูรายงานการเงิน', 6, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Promotion Management Group
((SELECT id FROM permission_groups WHERE key = 'promotion_management'), NULL, 'โปรโมชั่น', 'promotions', 'จัดการโปรโมชั่นและแคมเปญ', 1, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'promotion_management'), NULL, 'โบนัส', 'bonuses', 'จัดการโบนัสและรางวัล', 2, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'promotion_management'), NULL, 'คูปอง', 'coupons', 'จัดการคูปองส่วนลด', 3, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'promotion_management'), NULL, 'โปรแกรมสะสมแต้ม', 'loyalty_programs', 'จัดการโปรแกรมสะสมแต้ม', 4, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Report Management Group
((SELECT id FROM permission_groups WHERE key = 'report_management'), NULL, 'รายงานผู้ใช้', 'user_reports', 'ดูรายงานเกี่ยวกับผู้ใช้', 1, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'report_management'), NULL, 'รายงานเกม', 'game_reports', 'ดูรายงานเกมและการเล่น', 2, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'report_management'), NULL, 'รายงานระบบ', 'system_reports', 'ดูรายงานระบบและประสิทธิภาพ', 3, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Security Management Group
((SELECT id FROM permission_groups WHERE key = 'security_management'), NULL, 'การตั้งค่า 2FA', '2fa_settings', 'จัดการการยืนยันตัวตนสองขั้นตอน', 1, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'security_management'), NULL, 'บันทึกความปลอดภัย', 'security_logs', 'ดูบันทึกความปลอดภัย', 2, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'security_management'), NULL, 'รายชื่อ IP ที่อนุญาต', 'ip_whitelist', 'จัดการรายชื่อ IP ที่อนุญาต', 3, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'security_management'), NULL, 'การจัดการเซสชั่น', 'session_management', 'จัดการเซสชั่นผู้ใช้', 4, 1, FALSE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Communication Group
((SELECT id FROM permission_groups WHERE key = 'communication'), NULL, 'การแจ้งเตือน', 'notifications', 'จัดการการแจ้งเตือน', 1, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'communication'), NULL, 'ข้อความ', 'messages', 'จัดการข้อความและการสื่อสาร', 2, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'communication'), NULL, 'แม่แบบอีเมล', 'email_templates', 'จัดการแม่แบบอีเมล', 3, 1, TRUE, TRUE, TRUE, TRUE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'communication'), NULL, 'การตั้งค่า SMS', 'sms_settings', 'ตั้งค่าการส่ง SMS', 4, 1, FALSE, TRUE, TRUE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Analytics Group
((SELECT id FROM permission_groups WHERE key = 'analytics'), NULL, 'แดชบอร์ด', 'dashboard', 'ดูแดชบอร์ดหลัก', 1, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'analytics'), NULL, 'การวิเคราะห์', 'analytics', 'ดูข้อมูลการวิเคราะห์', 2, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'analytics'), NULL, 'สถิติ', 'statistics', 'ดูสถิติต่างๆ', 3, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'analytics'), NULL, 'ตัวชี้วัดประสิทธิภาพ', 'performance_metrics', 'ดูตัวชี้วัดประสิทธิภาพ', 4, 1, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (key) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    position = EXCLUDED.position,
    supports_create = EXCLUDED.supports_create,
    supports_view = EXCLUDED.supports_view,
    supports_edit = EXCLUDED.supports_edit,
    supports_delete = EXCLUDED.supports_delete,
    updated_at = CURRENT_TIMESTAMP;

-- Add some example sub-permissions (dashboard sub-permissions as shown in the image)
INSERT INTO permissions (group_id, parent_id, name, key, description, position, level, supports_create, supports_view, supports_edit, supports_delete, status, created_at, updated_at) VALUES
-- Dashboard sub-permissions
((SELECT id FROM permission_groups WHERE key = 'analytics'), (SELECT id FROM permissions WHERE key = 'dashboard'), 'การเงิน', 'dashboard_finance', 'ดูข้อมูลการเงินในแดชบอร์ด', 1, 2, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
((SELECT id FROM permission_groups WHERE key = 'analytics'), (SELECT id FROM permissions WHERE key = 'dashboard'), 'บอท', 'dashboard_bot', 'ดูข้อมูลบอทในแดชบอร์ด', 2, 2, FALSE, TRUE, FALSE, FALSE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (key) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    position = EXCLUDED.position,
    supports_create = EXCLUDED.supports_create,
    supports_view = EXCLUDED.supports_view,
    supports_edit = EXCLUDED.supports_edit,
    supports_delete = EXCLUDED.supports_delete,
    updated_at = CURRENT_TIMESTAMP;

-- Display summary
SELECT
    'Permission Groups' as type,
    COUNT(*) as count
FROM permission_groups
WHERE status = 'active'
UNION ALL
SELECT
    'Permissions' as type,
    COUNT(*) as count
FROM permissions
WHERE status = 'active'
UNION ALL
SELECT
    'Sub-Permissions' as type,
    COUNT(*) as count
FROM permissions
WHERE status = 'active' AND parent_id IS NOT NULL
UNION ALL
SELECT
    'User Roles' as type,
    COUNT(*) as count
FROM user_roles
WHERE status = 'active'
UNION ALL
SELECT
    'User Role Permissions' as type,
    COUNT(*) as count
FROM user_role_permissions
WHERE status = 'active';
