#!/bin/bash

# Migration script for blacking-api using go-migrations
set -e

ENVIRONMENT=${1:-local}
ACTION=${2:-up}

echo "Running database migrations for $ENVIRONMENT environment..."

case $ACTION in
  "up")
    echo "Running migrations up..."
    go run cmd/migration/main.go $ENVIRONMENT
    ;;
  "down")
    echo "Rolling back migrations..."
    echo "Note: Rollback functionality needs to be implemented in the migration runner"
    echo "To rollback, modify cmd/migration/main.go to set IsRollback: true in config"
    ;;
  "force"|"version")
    echo "Action '$ACTION' is not supported with go-migrations library"
    echo "Please check the migration status manually by examining the migrations table"
    ;;
  *)
    echo "Unknown action: $ACTION"
    echo "Usage: $0 [environment] [up|down]"
    echo "Note: down, force, and version actions require manual implementation"
    exit 1
    ;;
esac

echo "Migration completed successfully!"