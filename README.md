# Blacking API

A production-ready Go REST API built with clean architecture principles, featuring comprehensive user management, robust error handling, and extensive testing capabilities.

## 🏗️ Architecture

This project follows Clean Architecture principles with the following structure:

```
├── cmd/                    # Application entry points
│   ├── server/            # HTTP server
│   ├── worker/            # Background worker
│   └── migration/         # Database migration tool
├── internal/              # Private application code
│   ├── config/           # Configuration management
│   ├── domain/           # Business entities and logic
│   ├── handler/          # HTTP request handlers
│   ├── middleware/       # HTTP middlewares
│   ├── repository/       # Data access layer
│   ├── router/           # API routing
│   ├── service/          # Business logic services
│   └── util/             # Helper utilities
├── pkg/                   # Public packages
│   ├── errors/           # Custom error types
│   ├── logger/           # Structured logging
│   └── validator/        # Input validation
├── infrastructure/        # External service integrations
│   ├── database/         # Database connections
│   ├── cache/            # Redis/caching
│   └── messaging/        # Message queues
├── migrations/           # Database migrations
├── test/                 # Test utilities and data
└── scripts/              # Build and deployment scripts
```

## 🚀 Features

- **Clean Architecture**: Separation of concerns with dependency injection
- **Configuration Management**: Multi-environment support with Viper
- **Database Layer**: PostgreSQL with repository pattern and migrations
- **Error Handling**: Structured error responses with custom error types
- **Logging**: JSON structured logging with request tracing
- **Testing**: Unit, integration, and API tests with K6
- **Docker Support**: Multi-stage builds and docker-compose setup
- **API Documentation**: OpenAPI/Swagger specifications
- **Security**: Input validation, password hashing, and secure defaults
- **Observability**: Health checks, metrics, and request tracing

## 🛠️ Prerequisites

- Go 1.21+
- PostgreSQL 12+
- Redis 6+ (optional)
- Docker & Docker Compose
- Make

## 🏃‍♂️ Quick Start

### Using Docker Compose (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd blacking-api
   ```

2. **Start all services**:
   ```bash
   make docker-run
   ```

3. **Test the API**:
   ```bash
   curl http://localhost:8080/health
   ```

### Local Development

1. **Setup development environment**:
   ```bash
   make dev-setup
   ```

2. **Start dependencies**:
   ```bash
   docker-compose up -d postgres redis
   ```

3. **Run migrations**:
   ```bash
   make migrate-up ENV=local
   ```

4. **Start the server**:
   ```bash
   make run ENV=local
   ```

## 📝 Configuration

Configuration is managed through environment variables in the `.env` file:

- `local.yaml` - Local development
- `development.yaml` - Development environment
- `staging.yaml` - Staging environment
- `production.yaml` - Production environment

### Environment Variables

You can override any configuration using environment variables with the `BLACKING_` prefix:

```bash
export BLACKING_DATABASE_HOST=localhost
export BLACKING_DATABASE_PORT=5432
export BLACKING_JWT_SECRET=your-secret-key
```

## 🗄️ Database

### Migrations

Create a new migration:
```bash
make migrate-create name=add_user_roles
```

Run migrations:
```bash
make migrate-up ENV=local
```

### Schema

The database includes:
- Users table with authentication
- Automatic timestamp updates
- Indexes for performance
- UUID primary keys

## 🔧 Development

### Available Make Commands

```bash
make help                 # Show all available commands
make build               # Build the application
make run                 # Run the server locally
make test                # Run tests
make test-coverage       # Run tests with coverage
make lint                # Run linter
make fmt                 # Format code
make docker-build        # Build Docker image
make docker-run          # Run with Docker Compose
make dev-setup           # Setup development environment
```

### Running Tests

```bash
# Unit tests
make test

# Test coverage
make test-coverage

# API tests (requires running server)
make api-test

# Load tests
make load-test
```

## 📚 API Documentation

### Health Endpoints

- `GET /health` - Service health check
- `GET /health/ready` - Readiness check
- `GET /health/live` - Liveness check

### User Management

- `POST /api/v1/users` - Create user
- `GET /api/v1/users` - List users (with pagination)
- `GET /api/v1/users/{id}` - Get user by ID
- `PUT /api/v1/users/{id}` - Update user
- `DELETE /api/v1/users/{id}` - Delete user
- `PATCH /api/v1/users/{id}/activate` - Activate user
- `PATCH /api/v1/users/{id}/deactivate` - Deactivate user
- `PATCH /api/v1/users/{id}/suspend` - Suspend user
- `PATCH /api/v1/users/{id}/password` - Change password

### Example Requests

**Create User:**
```bash
curl -X POST http://localhost:8080/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "John",
    "last_name": "Doe"
  }'
```

**List Users:**
```bash
curl "http://localhost:8080/api/v1/users?limit=10&offset=0"
```

## 🐳 Docker

### Build and Run

```bash
# Build image
make docker-build

# Run all services
make docker-run

# View logs
make docker-logs

# Stop services
make docker-stop
```

### Production Deployment

```bash
# Build production image
make build-prod
make docker-build

# Deploy (customize scripts/deploy.sh)
./scripts/deploy.sh staging
```

## 🧪 Testing

### Test Structure

- `internal/domain/user/user_test.go` - Domain logic tests
- `acceptance-test/api_test.js` - K6 API tests
- `test/testdata/` - Test fixtures
- `test/mocks/` - Mock implementations

### Running Different Test Types

```bash
# Unit tests
go test ./...

# Integration tests
go test -tags=integration ./...

# Benchmarks
make benchmark

# Race condition detection
make test-race
```

## 🔒 Security

- Password hashing with bcrypt
- Input validation on all endpoints
- SQL injection prevention with prepared statements
- Structured error responses (no sensitive data leakage)
- Non-root Docker user
- Security scanning with gosec

## 📊 Monitoring

### Logging

Structured JSON logging with:
- Request ID tracing
- Performance metrics
- Error context
- User actions

### Health Checks

- Database connectivity
- Redis connectivity (if enabled)
- Application readiness
- Kubernetes-compatible endpoints

## 🚀 Deployment

### Staging/Production

1. **Configure environment**:
   Update the `.env` file with appropriate environment variables

2. **Build and deploy**:
   ```bash
   ./scripts/deploy.sh staging
   ```

3. **Run migrations**:
   ```bash
   ./scripts/migrate.sh staging up
   ```

### Kubernetes

Kubernetes manifests are available in `deployments/kubernetes/`:

```bash
kubectl apply -f deployments/kubernetes/staging/
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run `make check` to verify code quality
6. Submit a pull request

### Code Standards

- Follow Go conventions
- Write tests for new features
- Update documentation
- Use meaningful commit messages

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed information
4. Contact the development team

## 🔧 Troubleshooting

### Common Issues

**Database connection failed:**
```bash
# Check PostgreSQL is running
docker-compose ps postgres

# Check configuration
cat .env
```

**Migration errors:**
```bash
# Check migration status
make migrate-version

# Force to specific version if needed
./scripts/migrate.sh local force 1
```

**Build failures:**
```bash
# Clean and rebuild
make clean
make deps
make build
```

### Debug Mode

Run with debug logging:
```bash
APP_ENV=local BLACKING_LOG_LEVEL=debug make run
```

---

Built with ❤️ using Go, PostgreSQL, and Docker.